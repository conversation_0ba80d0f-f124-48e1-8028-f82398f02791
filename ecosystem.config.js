module.exports = {
  apps: [
    {
      name: process.env.PROD_APP_NAME || 'app-prod',
      script: 'npm',
      args: 'start',
      cwd: process.env.PROD_DEPLOY_PATH || '/var/www/app',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        PORT: process.env.PROD_PORT || 5000,
        NEXT_TELEMETRY_DISABLED: 1
      },
      env_file: `${process.env.PROD_DEPLOY_PATH || '/var/www/app'}/${process.env.ENV_FILE_NAME || '.env'}`,
      time: true,
      autorestart: true,
      watch: false,
      max_memory_restart: process.env.PROD_MEMORY_LIMIT || '1G',
      node_args: `--max-old-space-size=${process.env.PROD_NODE_MEMORY || '2048'}`
    },
    {
      name: process.env.DEV_APP_NAME || 'app-dev',
      script: 'npm',
      args: 'start',
      cwd: process.env.DEV_DEPLOY_PATH || '/var/www/app-dev',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        PORT: process.env.DEV_PORT || 5001,
        NEXT_TELEMETRY_DISABLED: 1
      },
      env_file: `${process.env.DEV_DEPLOY_PATH || '/var/www/app-dev'}/${process.env.ENV_FILE_NAME || '.env'}`,
      time: true,
      autorestart: true,
      watch: false,
      max_memory_restart: process.env.DEV_MEMORY_LIMIT || '1G',
      node_args: `--max-old-space-size=${process.env.DEV_NODE_MEMORY || '2048'}`
    }
  ]
};