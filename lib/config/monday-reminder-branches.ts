/**
 * Configuration for Monday Reservation Reminder SMS
 * 
 * This file contains the branch IDs that should receive Monday reservation reminders.
 * The SMS is sent on Saturday at 7:00 PM (48 hours before Monday) for reservations
 * that were created more than 1 day ago.
 * 
 * To add or remove branches, simply update the MONDAY_REMINDER_BRANCH_IDS array below.
 */

export const MONDAY_REMINDER_BRANCH_IDS: string[] = [
  '683f322bbc628c7f57f1e70e' // Repentigny 
  // Add your branch IDs here - uncomment and replace with actual branch IDs
  // To find branch IDs, check your database branches collection

  // Example:
  // '507f1f77bcf86cd799439011', // Branch Name 1
  // '507f1f77bcf86cd799439012', // Branch Name 2
  // '507f1f77bcf86cd799439013', // Branch Name 3

  // Instructions:
  // 1. Get branch IDs from your database
  // 2. Uncomment the lines above and replace with real IDs
  // 3. Add a comment with the branch name for easy identification
  // 4. Save the file - the cron job will automatically pick up changes on next restart
];

/**
 * SMS Template used for Monday reminders:
 * 
 * <PERSON><PERSON><PERSON> [name],
 * Petit rappel de votre expérience VIP – dégustation 5 services
 * 📅 Le [date] à [hour]
 * 📍 [branch.address]
 * 
 * 🎁 Votre présence vous rend admissible au tirage hebdomadaire de 2 500 $ en produits!
 * 🧊 Et vous repartez avec 25 $ de produits, même sans achat.
 * 
 * ✅ Merci de confirmer votre présence.
 * À bientôt!
 * — Équipe AMQ
 */

/**
 * Schedule: Saturday 7:00 PM (Cron: 0 19 * * 6)
 * Timezone: America/Toronto
 *
 * Conditions:
 * - Reservation is for next Monday
 * - Reservation was created more than 1 day ago
 * - Reservation is in one of the configured branches
 * - Reservation status is 'new'
 */
