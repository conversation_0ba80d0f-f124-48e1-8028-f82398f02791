import { default as dbConnect } from '../lib/db';
import { default as Permission } from '../models/Permission';
import { EVENT_PERMISSIONS } from '../types/permission-codes';

async function createViewAllEventsPermission() {
  try {
    await dbConnect();

    // Create the new VIEW_ALL_EVENTS permission
    const permissionToCreate = {
      name: 'View All Events',
      code: EVENT_PERMISSIONS.VIEW_ALL_EVENTS,
      description: 'Can view all events in the system (shows "All Events" tab in sidebar)'
    };

    // Insert or update the permission
    const permission = await Permission.findOneAndUpdate(
      { code: permissionToCreate.code },
      permissionToCreate,
      { upsert: true, new: true }
    );

    console.log(`Created/Updated permission: ${permission.name} (${permission.code})`);
    console.log('VIEW_ALL_EVENTS permission created successfully');
  } catch (error) {
    console.error('Error creating VIEW_ALL_EVENTS permission:', error);
  } finally {
    process.exit(0);
  }
}

createViewAllEventsPermission();
