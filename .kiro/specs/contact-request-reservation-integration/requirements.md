# Requirements Document

## Introduction

This feature enhances the contact request system to provide seamless integration with the reservation creation process. It enables tracking of reservations created from contact requests, prevents duplicate reservations, and provides visual indicators for reservation sources and status relationships.

## Requirements

### Requirement 1

**User Story:** As a user creating a reservation from a contact request, I want the contact request source to be automatically linked to the reservation, so that I can track the effectiveness of different marketing channels.

#### Acceptance Criteria

1. WHEN a user clicks "Create Reservation" from a contact request THEN the system SHALL pass the contactRequestSourceId as a URL parameter
2. WHEN the reservation creation page loads with a contactRequestSourceId parameter THEN the system SHALL store this ID for use during reservation creation
3. WHEN a reservation is created from a contact request THEN the system SHALL save the contactRequestSourceId in the reservation record
4. WHEN a reservation is created from a contact request THEN the system SHALL set the source field to 'contact_request'
5. WHEN the Reservation model is updated THEN the system SHALL add a contactRequestSourceId field that references ContactRequestSource

### Requirement 2

**User Story:** As a system, I want to automatically update the contact request status when a reservation is created, so that the contact request lifecycle is properly tracked.

#### Acceptance Criteria

1. WHEN a reservation is successfully created from a contact request THEN the system SHALL find the associated PendingReservation record
2. WHEN the PendingReservation is found THEN the system SHALL update it with the new reservation ID
3. WHEN the PendingReservation is updated THEN the system SHALL set the PendingReservation as archived
4. WHEN these updates occur THEN the system SHALL update the updatedAt timestamp
5. NOTE: The status is no longer automatically changed to 'reserved' as this status is being phased out

### Requirement 3

**User Story:** As a user viewing contact requests, I want to see which contact requests have associated reservations, so that I can easily navigate to the reservation details.

#### Acceptance Criteria

1. WHEN displaying contact requests that have an associated reservation THEN the system SHALL show a link to the reservation edit page
2. WHEN the link is displayed THEN it SHALL navigate to /reservations/[id]/edit
3. WHEN the contact request has a reservation THEN the system SHALL display a visual indicator (icon or badge)
4. WHEN the user clicks the reservation link THEN it SHALL open in the same tab
5. WHEN the reservation link is displayed THEN it SHALL show appropriate text like "View Reservation"

### Requirement 4

**User Story:** As a user viewing contact requests, I want to be warned about potential duplicate reservations, so that I can avoid creating duplicate bookings.

#### Acceptance Criteria

1. WHEN displaying a contact request THEN the system SHALL check if any existing reservation has the same phone number
2. WHEN a matching phone number is found AND the reservation status is not in final states (sales, cancelled, etc.) THEN the system SHALL display a danger icon
3. WHEN the danger icon is displayed THEN it SHALL have a tooltip explaining the duplicate phone number situation
4. WHEN the tooltip is shown THEN it SHALL include a link to the existing reservation
5. WHEN the link in the tooltip is clicked THEN it SHALL navigate to the existing reservation's edit page

### Requirement 5

**User Story:** As a user viewing the reservations list, I want to visually identify reservations that came from contact requests, so that I can understand the source of each booking.

#### Acceptance Criteria

1. WHEN displaying reservations with source 'contact_request' THEN the system SHALL show a visual indicator (icon and slight tint)
2. WHEN the user hovers over the contact request indicator THEN the system SHALL display a tooltip with source information
3. WHEN the tooltip is displayed THEN it SHALL show the contact request source details (source type, influencer, tag)
4. WHEN the source information is displayed THEN it SHALL be formatted in a user-friendly manner
5. WHEN no source information is available THEN the system SHALL show a generic "Contact Request" label

### Requirement 6

**User Story:** As a system, I want to ensure data consistency between contact requests and reservations, so that the relationship is maintained accurately.

#### Acceptance Criteria

1. WHEN a reservation is created from a contact request THEN the system SHALL validate that the contact request exists
2. WHEN the contact request source is referenced THEN the system SHALL validate that the source exists
3. WHEN updating the PendingReservation THEN the system SHALL link it to the reservation and archive it (status no longer automatically changed)
4. WHEN any validation fails THEN the system SHALL log the error but continue with reservation creation
5. WHEN the reservation is successfully created THEN the system SHALL return the reservation ID for further processing

### Requirement 7

**User Story:** As a developer, I want the Reservation model to support contact request integration, so that the system can track reservation sources properly.

#### Acceptance Criteria

1. WHEN updating the Reservation model THEN the system SHALL add a contactRequestSourceId field
2. WHEN the contactRequestSourceId field is added THEN it SHALL be optional and reference ContactRequestSource
3. WHEN updating the source field enum THEN the system SHALL add 'contact_request' as a valid option
4. WHEN querying reservations THEN the system SHALL support filtering by contact request source

### Requirement 8

**User Story:** As a user viewing contact requests, I want to see analytics about contact request performance and conversion rates, so that I can understand the effectiveness of the contact request system.

#### Acceptance Criteria

1. WHEN viewing the contact requests page THEN the system SHALL display a collapsible stats section that is collapsed by default
2. WHEN the stats section is expanded THEN the system SHALL show contact request summary statistics
3. WHEN displaying contact request stats THEN the system SHALL show total contact requests count
4. WHEN displaying conversion stats THEN the system SHALL show contact requests to reservation conversion rate
5. WHEN displaying reservation performance THEN the system SHALL show how many converted reservations have presence status
6. WHEN displaying sales performance THEN the system SHALL show how many converted reservations have sales status
7. WHEN calculating presence status THEN the system SHALL check ReservationStatus model for statuses indicating customer presence
8. WHEN calculating sales status THEN the system SHALL check ReservationStatus model for statuses indicating successful sales

### Requirement 9

**User Story:** As a user, I want the system to handle edge cases gracefully, so that reservation creation is not blocked by contact request integration issues.

#### Acceptance Criteria

1. WHEN the contact request source cannot be found THEN the system SHALL create the reservation without the source reference
2. WHEN the PendingReservation cannot be updated THEN the system SHALL log the error but complete the reservation creation
3. WHEN the PendingReservation update fails THEN the system SHALL continue with reservation creation (status updates no longer required)
4. WHEN any contact request integration step fails THEN the system SHALL not prevent the reservation from being created
5. WHEN errors occur THEN the system SHALL log detailed error information for debugging