# Implementation Plan

- [x] 1. Update Reservation model to support contact request integration
  - Add contactRequestSourceId field as optional ObjectId reference to ContactRequestSource
  - Update source field enum to include 'contact_request' option
  - Add database indexes for efficient querying of contact request sources
  - Test model validation with new fields
  - _Requirements: 1.5, 7.1, 7.2, 7.3, 7.4_

- [x] 2. Update PendingReservation model to track reservation relationships
  - Add reservationId field as optional ObjectId reference to Reservation
  - Add database index for efficient reservation lookup
  - Test model updates and relationship queries
  - _Requirements: 2.1, 6.1_

- [x] 3. Enhance reservation creation API to handle contact request integration
  - Modify POST /api/reservations to accept contactRequestId and contactRequestSourceId parameters
  - Implement logic to set source to 'contact_request' when contactRequestSourceId is provided
  - Add contactRequestSourceId to reservation creation data
  - Implement PendingReservation status update after successful reservation creation
  - Add error handling to ensure reservation creation continues even if contact request updates fail
  - _Requirements: 1.1, 1.3, 1.4, 2.1, 2.2, 2.3, 2.4, 2.5, 6.4, 8.1, 8.2, 8.3, 8.4_

- [x] 4. Create utility function for finding 'reserved' status
  - Implement function to find ContactRequestStatus with code 'reserved'
  - Add fallback handling if 'reserved' status doesn't exist
  - Test status lookup and fallback scenarios
  - _Requirements: 2.3, 8.3_

- [x] 5. Enhance contact requests API to include reservation information
  - Modify GET /api/contact-requests to populate reservationId field
  - Add logic to detect duplicate phone numbers in existing reservations
  - Include duplicate reservation information in API response
  - Add hasReservation boolean flag to response data
  - _Requirements: 3.1, 4.1, 4.2_

- [x] 6. Create contact request analytics API endpoint
  - Implement GET /api/contact-requests/stats endpoint for analytics data
  - Add logic to calculate total contact requests and conversion rates
  - Implement presence rate calculation using ReservationStatus model
  - Implement sales rate calculation using ReservationStatus model
  - Add error handling and performance optimization for stats queries
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7, 8.8_

- [x] 7. Create collapsible analytics section for contact requests page
  - Implement ContactRequestStats component with collapsible functionality
  - Add analytics cards for total requests, conversion rate, presence rate, and sales rate
  - Implement lazy loading of stats data when section is expanded
  - Add loading states and error handling for analytics display
  - Style analytics section to be collapsed by default
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6_

- [x] 8. Update contact requests page to show reservation links
  - Add ReservationLinkCell component to display reservation links when available
  - Implement navigation to /reservations/[id]/edit when reservation exists
  - Add visual indicator (icon or badge) for contact requests with reservations
  - Update table columns to include reservation status information
  - Integrate ContactRequestStats component into the page layout
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 8.1_

- [x] 9. Implement duplicate phone number warning system
  - Create DuplicateWarningCell component with danger icon
  - Implement tooltip showing duplicate reservation information
  - Add links to existing reservations in tooltip
  - Filter duplicates to only show non-'reserved' status reservations
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 10. Enhance reservation creation page to handle contact request parameters 
  - Update URL parameter handling to capture contactRequestId and contactRequestSourceId
  - Update passed source to 'contact_request'
  - Store contact request parameters in form state for submission
  - Pass contact request parameters to reservation creation API
  - Ensure existing functionality remains unaffected
  - _Requirements: 1.1, 1.2_

- [x] 11. Create contact request source indicator for reservations list
  - Implement ContactRequestSourceIndicator component for reservations with source 'contact_request'
  - Add visual styling (icon and slight tint) similar to invitation source indicators
  - Create tooltip component to display contact request source information
  - Implement hover functionality to show source details
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 12. Implement contact request source information fetching
  - Implement ContactRequestSourceTooltip component with source information display
  - Add error handling for missing or inaccessible source information
  - Format source information in user-friendly manner (source type, influencer, tag, hits)
  - _Requirements: 5.2, 5.3, 5.4, 5.5_

- [x] 13. Implement phone number comparison utility for duplicate detection
  - Use existing normalizePhoneNumber function from lib/twilio.ts
  - Implement phonesMatch utility function for accurate phone number comparison
  - Add validation to ensure phone numbers are properly normalized before comparison
  - _Requirements: 4.1, 4.2_

- [-] 14. Add comprehensive error handling and validation
  - Implement graceful error handling for contact request integration failures
  - Add validation for contact request source references
  - Ensure reservation creation continues even when contact request updates fail
  - Add detailed logging for debugging contact request integration issues
  - Create user-friendly error messages for integration failures
  - _Requirements: 6.1, 6.2, 6.3, 9.1, 9.2, 9.3, 9.4, 9.5_

- [x] 15. Update reservation creation form to handle contact request source
  - Modify handleOpenReservationDialog to pass contactRequestId , contactRequestSourceId and source='contact_request'
  - Update URL parameter construction to include new contact request parameters
  - Ensure form pre-filling works correctly with contact request data
  - Test end-to-end flow from contact request to reservation creation
  - _Requirements: 1.1, 1.2, 1.3_

- [-] 16. Update UI styling and consistency
  - Ensure contact request source indicators match existing invitation source styling
  - Implement consistent tooltip styling across the application
  - Add appropriate icons and visual cues for different source types
  - Test responsive design for new UI components
  - _Requirements: 5.1, 5.2_