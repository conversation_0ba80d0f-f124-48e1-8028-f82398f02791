# Design Document

## Overview

The Contact Request Reservation Integration system enhances the existing contact request and reservation management by creating seamless connections between contact requests and the reservations created from them. This integration provides source tracking, status management, duplicate prevention, and visual indicators to improve the user experience and data integrity.

The system builds upon the existing contact-requests-sources infrastructure and extends the reservation creation workflow to maintain relationships between contact requests and reservations.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Contact Requests Page] --> B[Create Reservation Button]
    B --> C[Reservation Creation Page]
    C --> D[Enhanced Reservation API]
    D --> E[Reservation Model Updates]
    D --> F[PendingReservation Updates]
    F --> G[Status Management]
    H[Reservations List] --> I[Source Indicators]
    I --> J[Contact Request Source Info]
    A --> K[Duplicate Detection]
    K --> L[Phone Number Matching]
    L --> M[Warning Indicators]
```

### System Components

1. **Enhanced Contact Requests Page**
   - Reservation link display for completed contact requests
   - Duplicate phone number detection and warnings
   - Visual indicators for reservation status

2. **Enhanced Reservation Creation**
   - Contact request source parameter handling
   - Automatic source assignment and tracking
   - PendingReservation status updates

3. **Enhanced Reservations Display**
   - Contact request source visual indicators
   - Source information tooltips
   - Consistent styling with invitation sources

4. **Data Model Extensions**
   - Reservation model with contact request source reference
   - Enhanced source field options
   - Relationship maintenance

## Components and Interfaces

### Database Model Updates

#### Enhanced Reservation Model

```typescript
interface IReservation {
  // ... existing fields
  source?: 'invitation' | 'direct' | 'other' | 'amq_website' | 'contact_request';
  contactRequestSourceId?: mongoose.Types.ObjectId; // New field
  // ... rest of existing fields
}

const reservationSchema = new Schema<IReservation>({
  // ... existing schema
  source: {
    type: String,
    enum: ['invitation', 'direct', 'other', 'amq_website', 'contact_request'], // Updated enum
    default: 'direct'
  },
  contactRequestSourceId: {
    type: Schema.Types.ObjectId,
    ref: 'ContactRequestSource',
    required: false
  },
  // ... rest of existing schema
});
```

#### Enhanced PendingReservation Model

```typescript
interface IPendingReservation extends Document {
  // ... existing fields
  reservationId?: mongoose.Types.ObjectId; // New field for linking
  // ... rest of existing fields
}

const PendingReservationSchema = new Schema<IPendingReservation>({
  // ... existing schema
  reservationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Reservation',
    required: false
  },
  // ... rest of existing schema
});
```

### API Enhancements

#### Enhanced Reservation Creation API

**POST /api/reservations**

Enhanced to handle contact request integration:

```typescript
interface ReservationCreateRequest {
  // ... existing fields
  contactRequestId?: string; // New parameter
  contactRequestSourceId?: string; // New parameter
}

// Enhanced processing logic:
async function createReservation(data: ReservationCreateRequest) {
  // 1. Create reservation with contact request source if provided
  const reservation = await Reservation.create({
    ...data,
    source: data.contactRequestSourceId ? 'contact_request' : data.source,
    contactRequestSourceId: data.contactRequestSourceId
  });

  // 2. Update PendingReservation if contactRequestId provided
  if (data.contactRequestId) {
    await updatePendingReservationStatus(data.contactRequestId, reservation._id);
  }

  return reservation;
}

async function updatePendingReservation(contactRequestId: string, reservationId: string) {
  // Update PendingReservation - no longer changes status to 'reserved'
  // Only links to reservation and archives the contact request
  await PendingReservation.findByIdAndUpdate(contactRequestId, {
    reservationId: reservationId,
    isArchived: true,
    updatedAt: new Date()
  });
}
```

#### Contact Request Stats API

**GET /api/contact-requests/stats**

New endpoint to provide analytics for contact request performance:

```typescript
interface ContactRequestStatsResponse {
  totalContactRequests: number;
  convertedToReservations: number;
  conversionRate: number;
  reservationsWithPresence: number;
  presenceRate: number;
  reservationsWithSales: number;
  salesRate: number;
}

// Stats calculation logic:
async function getContactRequestStats() {
  // Get all contact requests
  const totalContactRequests = await PendingReservation.countDocuments();
  
  // Get contact requests that have been converted to reservations
  const convertedRequests = await PendingReservation.find({ 
    reservationId: { $exists: true, $ne: null } 
  }).populate('reservationId');
  
  const convertedToReservations = convertedRequests.length;
  const conversionRate = totalContactRequests > 0 
    ? Math.round((convertedToReservations / totalContactRequests) * 100) 
    : 0;
  
  // Get reservation statuses to identify presence and sales statuses
  const reservationStatuses = await ReservationStatus.find();
  const presenceStatusCodes = reservationStatuses
    .filter(status => status.code.includes('present') || status.code.includes('arrived'))
    .map(status => status.code);
  const salesStatusCodes = reservationStatuses
    .filter(status => status.code.includes('sold') || status.code.includes('vendu'))
    .map(status => status.code);
  
  // Count reservations with presence and sales
  const reservationsWithPresence = await Reservation.countDocuments({
    _id: { $in: convertedRequests.map(req => req.reservationId) },
    status: { $in: presenceStatusCodes }
  });
  
  const reservationsWithSales = await Reservation.countDocuments({
    _id: { $in: convertedRequests.map(req => req.reservationId) },
    status: { $in: salesStatusCodes }
  });
  
  const presenceRate = convertedToReservations > 0 
    ? Math.round((reservationsWithPresence / convertedToReservations) * 100) 
    : 0;
  
  const salesRate = convertedToReservations > 0 
    ? Math.round((reservationsWithSales / convertedToReservations) * 100) 
    : 0;
  
  return {
    totalContactRequests,
    convertedToReservations,
    conversionRate,
    reservationsWithPresence,
    presenceRate,
    reservationsWithSales,
    salesRate
  };
}
```

#### Enhanced Contact Requests API

**GET /api/contact-requests**

Enhanced to include reservation information and duplicate detection:

```typescript
interface ContactRequestResponse {
  // ... existing fields
  reservationId?: string;
  hasReservation: boolean;
  duplicatePhoneReservations?: Array<{
    _id: string;
    customerInfo: {
      client1Name: string;
      phone: string;
    };
    status: string;
  }>;
}

// Enhanced query logic:
async function getContactRequests() {
  const requests = await PendingReservation.find()
    .populate('statusId')
    .populate('reservationId');

  // Check for duplicate phone numbers
  for (const request of requests) {
    const duplicateReservations = await Reservation.find({
      'customerInfo.phone': normalizePhoneNumber(request.phone),
      _id: { $ne: request.reservationId }
    }).select('_id customerInfo.client1Name customerInfo.phone status');

    request.duplicatePhoneReservations = duplicateReservations.filter(
      res => res.status !== 'reserved'
    );
  }

  return requests;
}
```

### Frontend Component Enhancements

#### Enhanced Contact Requests Page

**Location:** `/app/contact-requests/page.tsx`

**New Components:**
- `ContactRequestStats` - Collapsible stats section with analytics cards
- `ReservationLinkCell` - Shows link to reservation when available
- `DuplicateWarningCell` - Shows warning icon for duplicate phone numbers
- Enhanced table columns for reservation status

```typescript
// Contact Request Stats Component
const ContactRequestStats = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(false);

  const fetchStats = async () => {
    if (stats) return; // Only fetch once
    
    setLoading(true);
    try {
      const response = await fetch('/api/contact-requests/stats');
      if (!response.ok) throw new Error('Failed to fetch stats');
      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error('Error fetching contact request stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded && !stats) {
      fetchStats();
    }
  };

  return (
    <div className="mb-6">
      <Button
        variant="ghost"
        onClick={handleToggle}
        className="flex items-center gap-2 p-0 h-auto font-medium text-muted-foreground hover:text-foreground"
      >
        {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
        Contact Request Analytics
      </Button>
      
      {isExpanded && (
        <div className="mt-4 grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {loading ? (
            <div className="col-span-full flex justify-center py-8">
              <Loader2 className="animate-spin" />
            </div>
          ) : stats ? (
            <>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Contact Requests</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.totalContactRequests}</div>
                  <p className="text-xs text-muted-foreground">All time</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.conversionRate}%</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.convertedToReservations} of {stats.totalContactRequests} converted
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Presence Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.presenceRate}%</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.reservationsWithPresence} showed up
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Sales Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.salesRate}%</div>
                  <p className="text-xs text-muted-foreground">
                    {stats.reservationsWithSales} resulted in sales
                  </p>
                </CardContent>
              </Card>
            </>
          ) : (
            <div className="col-span-full text-center py-8 text-muted-foreground">
              Failed to load statistics
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// New table cell components
const ReservationLinkCell = ({ contactRequest }: { contactRequest: PendingReservation }) => {
  if (!contactRequest.reservationId) return null;
  
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={() => router.push(`/reservations/${contactRequest.reservationId}/edit`)}
    >
      <Calendar className="h-4 w-4 mr-2" />
      View Reservation
    </Button>
  );
};

const DuplicateWarningCell = ({ duplicates }: { duplicates: any[] }) => {
  if (!duplicates?.length) return null;
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <AlertTriangle className="h-4 w-4 text-destructive" />
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-2">
            <p>Duplicate phone number found:</p>
            {duplicates.map(dup => (
              <div key={dup._id}>
                <Link href={`/reservations/${dup._id}/edit`} className="text-blue-600 hover:underline">
                  {dup.customerInfo.client1Name} - {dup.status}
                </Link>
              </div>
            ))}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
```

**Enhanced Create Reservation Function:**

```typescript
const handleOpenReservationDialog = (contactRequest: PendingReservation) => {
  const params = new URLSearchParams({
    name: contactRequest.name || '',
    email: contactRequest.email || '',
    phone: contactRequest.phone || '',
    postal: contactRequest.postal || '',
    partnerId: '6872602078792b6fc225243b', 
    hideAgentSelection: 'true',
    source: 'contact_request',
    contactRequestId: contactRequest._id, // New parameter
    contactRequestSourceId: contactRequest.source || '' // New parameter
  });

  const url = `/reservations/new?${params.toString()}`;
  window.open(url, '_blank');
};
```

#### Enhanced Reservation Creation Page

**Location:** `/app/reservations/new/page.tsx`

**Enhancements:**
- Handle contactRequestId and contactRequestSourceId URL parameters
- Pass these parameters to the reservation creation API

```typescript
// Enhanced useEffect for URL parameters
useEffect(() => {
  if (searchParams) {
    const contactRequestId = searchParams.get('contactRequestId');
    const contactRequestSourceId = searchParams.get('contactRequestSourceId');
    
    // Store these for form submission
    setFormData(prev => ({
      ...prev,
      contactRequestId,
      contactRequestSourceId,
      // ... other existing parameter handling
    }));
  }
}, [searchParams]);

// Enhanced form submission
const handleSubmit = async (e: React.FormEvent) => {
  // ... existing validation

  const reservationData = {
    // ... existing data
    contactRequestId: formData.contactRequestId,
    contactRequestSourceId: formData.contactRequestSourceId,
  };

  // ... rest of existing submission logic
};
```

#### Enhanced Reservations List Page

**Location:** `/app/reservations/page.tsx`

**New Components:**
- `ContactRequestSourceIndicator` - Visual indicator for contact request sources
- Enhanced source tooltip with contact request information

```typescript
const ContactRequestSourceIndicator = ({ reservation }: { reservation: Reservation }) => {
  if (reservation.source !== 'contact_request') return null;
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger>
          <div className="flex items-center space-x-1">
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              <MessageSquare className="h-3 w-3 mr-1" />
              Contact Request
            </Badge>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <ContactRequestSourceTooltip 
            sourceId={reservation.contactRequestSourceId}
          />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

const ContactRequestSourceTooltip = ({ sourceId }: { sourceId?: string }) => {
  const [sourceInfo, setSourceInfo] = useState(null);
  
  useEffect(() => {
    if (sourceId) {
      fetchContactRequestSource(sourceId).then(setSourceInfo);
    }
  }, [sourceId]);
  
  if (!sourceInfo) {
    return <div>Contact Request Source</div>;
  }
  
  return (
    <div className="space-y-2">
      <div className="font-semibold">Contact Request Source</div>
      <div>Source: {sourceInfo.source}</div>
      <div>Influencer: {sourceInfo.influencer}</div>
      <div>Tag: {sourceInfo.tag}</div>
      <div>Hits: {sourceInfo.hits}</div>
    </div>
  );
};
```

### Utility Functions

#### Phone Number Normalization

```typescript
// Enhanced phone number normalization for duplicate detection
export function normalizePhoneNumber(phone: string | undefined | null): string {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Remove leading 1 if present (North American format)
  return digits.startsWith('1') ? digits.slice(1) : digits;
}

// Phone number comparison utility
export function phonesMatch(phone1: string, phone2: string): boolean {
  const normalized1 = normalizePhoneNumber(phone1);
  const normalized2 = normalizePhoneNumber(phone2);
  
  return normalized1 === normalized2 && normalized1.length >= 10;
}
```

#### Contact Request Source Fetching

```typescript
// Utility to fetch contact request source information
export async function fetchContactRequestSource(sourceId: string) {
  try {
    const response = await fetch(`/api/contact-request-sources/${sourceId}`);
    if (!response.ok) throw new Error('Failed to fetch source');
    return await response.json();
  } catch (error) {
    console.error('Error fetching contact request source:', error);
    return null;
  }
}
```

## Data Models

### Database Schema Updates

#### Reservation Schema Enhancement

```javascript
// Add to existing reservation schema
const reservationSchema = new Schema({
  // ... existing fields
  source: {
    type: String,
    enum: ['invitation', 'direct', 'other', 'amq_website', 'contact_request'],
    default: 'direct'
  },
  contactRequestSourceId: {
    type: Schema.Types.ObjectId,
    ref: 'ContactRequestSource',
    required: false
  },
  // ... rest of existing fields
});

// Add index for contact request source queries
reservationSchema.index({ contactRequestSourceId: 1 });
reservationSchema.index({ source: 1, contactRequestSourceId: 1 });
```

#### PendingReservation Schema Enhancement

```javascript
// Add to existing pending reservation schema
const PendingReservationSchema = new Schema({
  // ... existing fields
  reservationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Reservation',
    required: false
  },
  // ... rest of existing fields
});

// Add index for reservation lookup
PendingReservationSchema.index({ reservationId: 1 });
```

## Error Handling

### API Error Scenarios

**Reservation Creation Errors:**
- Contact request not found (log warning, continue)
- Contact request source not found (log warning, continue)
- Reserved status not found (use fallback or skip)
- PendingReservation update failure (log error, continue)

**Contact Request Display Errors:**
- Reservation lookup failure (show without link)
- Duplicate detection failure (show without warning)
- Source information fetch failure (show generic label)

### Frontend Error Handling

```typescript
// Graceful error handling for contact request integration
const handleReservationCreation = async (contactRequest: PendingReservation) => {
  try {
    // Attempt to create reservation with contact request integration
    await createReservationWithContactRequest(contactRequest);
  } catch (error) {
    // Log error but don't block user workflow
    console.error('Contact request integration error:', error);
    
    // Show user-friendly message
    toast({
      title: 'Reservation Created',
      description: 'Reservation created successfully. Some contact request linking may be incomplete.',
      variant: 'default'
    });
  }
};
```



## Security Considerations

### Data Validation

- Validate contact request source references exist
- Sanitize phone numbers for comparison
- Validate reservation status transitions
- Ensure proper user permissions for all operations

### Access Control

- Maintain existing permission requirements
- Ensure contact request source information is only accessible to authorized users
- Validate user permissions for reservation creation and updates

## Performance Considerations

### Database Optimization

- Add indexes for contact request source queries
- Optimize duplicate phone number detection queries
- Use efficient population for related data
- Consider caching for frequently accessed source information

### Frontend Performance

- Lazy load contact request source information
- Debounce duplicate detection checks
- Optimize tooltip rendering for large lists
- Use efficient re-rendering strategies

## Deployment and Configuration

### Environment Configuration

No additional environment variables required. The system uses existing database connections and API endpoints.

### Monitoring and Logging

- Log contact request integration successes and failures
- Monitor duplicate detection accuracy
- Track reservation source distribution
- Alert on integration errors that might indicate system issues