# Design Document

## Overview

The Contact Requests Sources Management System is a comprehensive feature that enables authenticated users to create, manage, and track contact request sources with unique alphanumeric tags. The system integrates with Bitly API for URL shortening and provides analytics through webhook integration to monitor campaign performance and engagement metrics.

The system consists of a new sources management page, API endpoints for CRUD operations, database models, Bitly integration, webhook handling, and enhanced contact requests display with source information.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[User Interface] --> B[Sources Management Page]
    A --> C[Contact Requests Page]
    B --> D[Sources API Endpoints]
    C --> E[Enhanced Contact Requests API]
    D --> F[ContactRequestSource Model]
    E --> G[PendingReservation Model]
    D --> H[Bitly Integration Service]
    I[Bitly Webhooks] --> J[Webhook Handler]
    J --> F
    K[Permission System] --> D
    K --> B
```

### System Components

1. **Frontend Components**
   - Sources Management Page (`/contact-requests/sources`)
   - Source Creation Dialog
   - Statistics Dashboard
   - Enhanced Contact Requests Table with Source Column

2. **Backend Services**
   - Contact Request Sources API (`/api/contact-request-sources`)
   - Bitly Integration Service
   - Webhook Handler (`/api/webhooks/bitly`)
   - Enhanced Contact Requests API

3. **Database Layer**
   - ContactRequestSource Collection
   - Enhanced PendingReservation Model

4. **External Integrations**
   - Bitly API for URL shortening
   - Bitly Webhooks for click tracking

## Components and Interfaces

### Database Models

#### ContactRequestSource Model

```typescript
interface IContactRequestSource extends Document {
  tag: string;                    // 6-character alphanumeric unique identifier
  source: string;                 // Source type (facebook, instagram, tiktok, youtube)
  influencer: string;             // Influencer name (AMQ, Dominique Paquet)
  bitlyUrl: string;              // Generated Bitly shortened URL
  hits: number;                  // Click count from webhooks
  webhookData: WebhookEvent[];   // Array of webhook event data
  createdAt: Date;
  updatedAt: Date;
}

interface WebhookEvent {
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  referrer?: string;
  eventType: string;
  rawData: any;
}
```

#### Enhanced PendingReservation Model

The existing model already includes the `source` field with proper validation.

### API Endpoints

#### Contact Request Sources API

**Base URL:** `/api/contact-request-sources`

**GET /api/contact-request-sources**
- Purpose: Retrieve all sources with filtering and pagination
- Authentication: Required (MANAGE_CONTACT_REQUESTS_SOURCES permission)
- Query Parameters:
  - `search`: Filter by tag, source, or influencer
  - `source`: Filter by source type
  - `influencer`: Filter by influencer
  - `page`, `limit`: Pagination
- Response: Paginated list of sources with statistics

**POST /api/contact-request-sources**
- Purpose: Create new source with Bitly integration
- Authentication: Required (MANAGE_CONTACT_REQUESTS_SOURCES permission)
- Body: `{ tag, source, influencer }`
- Process:
  1. Validate tag uniqueness and format
  2. Create source record
  3. Call Bitly API to generate shortened URL
  4. Update source with Bitly URL
  5. Return complete source with Bitly URL
- Response: Created source with Bitly URL

**GET /api/contact-request-sources/stats**
- Purpose: Retrieve aggregated statistics
- Authentication: Required (MANAGE_CONTACT_REQUESTS_SOURCES permission)
- Response: Statistics object with totals, averages, and top performers

#### Webhook Handler

**POST /api/webhooks/bitly**
- Purpose: Handle Bitly click events
- Authentication: Bitly webhook signature verification
- Process:
  1. Verify webhook signature
  2. Extract source tag from URL
  3. Find corresponding source
  4. Increment hits counter
  5. Store webhook data
- Response: 200 OK

**GET /api/contact-request-sources/analytics**
- Purpose: Retrieve comprehensive analytics using Bitly API data
- Authentication: Required (MANAGE_CONTACT_REQUESTS_SOURCES permission)
- Process:
  1. Fetch all sources from database
  2. Call Bitly API for each source to get detailed click metrics
  3. Aggregate data by time periods, geography, devices
  4. Calculate performance trends and comparisons
- Response: Enhanced analytics with Bitly metrics

#### Enhanced Contact Requests API

**GET /api/contact-requests**
- Enhanced to include:
  - Source field in response data
  - Complete sources list for frontend reference
  - Source information for tooltips

### Frontend Components

#### Sources Management Page

**Location:** `/app/contact-requests/sources/page.tsx`

**Features:**
- Enhanced statistics dashboard with Bitly analytics at top
- Create source button with dialog
- Grouped source list by source type
- Search and filter functionality
- Pagination support
- Real-time analytics refresh

**Components:**
- `SourcesStatsCard` - Display key metrics with Bitly data
- `AnalyticsDashboard` - Comprehensive analytics with charts and trends
- `GeographicAnalytics` - Click distribution by location using Bitly data
- `TimeSeriesChart` - Click trends over time periods
- `PerformanceComparison` - Source performance comparison
- `CreateSourceDialog` - Source creation form
- `SourcesList` - Grouped and filtered source display
- `SourceCard` - Individual source display with enhanced metrics

**Enhanced Statistics Features:**
- Total clicks across all sources (from Bitly API)
- Click trends by time periods (hourly, daily, weekly, monthly)
- Geographic distribution of clicks (country, region, city)
- Top performing sources with detailed metrics
- Performance comparison between source types and influencers
- Real-time vs historical data comparison
- Click velocity and engagement patterns
- Referrer analysis (where clicks are coming from)

#### Source Creation Dialog

**Features:**
- Pre-filled unique tag with regenerate button
- Source type dropdown (facebook, instagram, tiktok, youtube)
- Influencer dropdown (AMQ, Dominique Paquet)
- Form validation
- Success dialog with Bitly URL and clipboard copy

#### Enhanced Contact Requests Page

**Enhancements:**
- New "Source" column in table
- Source tag display with tooltip
- Tooltip shows complete source information

### Services and Utilities

#### Bitly Integration Service

**Location:** `/lib/services/bitly.ts`

**API Reference:** https://dev.bitly.com/api-reference

```typescript
class BitlyService {
  private accessToken: string;
  private baseUrl = 'https://api-ssl.bitly.com/v4';

  async createBitlink(longUrl: string, options?: BitlyCreateOptions): Promise<BitlyCreateResponse>;
  async getBitlinkClicks(bitlink: string, params?: ClicksParams): Promise<BitlyClicksResponse>;
  async getBitlinkClicksSummary(bitlink: string, params?: ClicksParams): Promise<BitlyClicksSummaryResponse>;
  async getBitlinkCountries(bitlink: string, params?: MetricsParams): Promise<BitlyMetricsResponse>;
  async getBitlinkCities(bitlink: string, params?: MetricsParams): Promise<BitlyCitiesResponse>;
  async getBitlinkDevices(bitlink: string, params?: MetricsParams): Promise<BitlyDevicesResponse>;
  async getBitlinkReferrers(bitlink: string, params?: MetricsParams): Promise<BitlyMetricsResponse>;
  async getBitlink(bitlink: string): Promise<BitlyCreateResponse>;
  async updateBitlink(bitlink: string, updates: BitlyUpdateRequest): Promise<BitlyCreateResponse>;
  private async makeRequest(endpoint: string, options: RequestInit): Promise<any>;
}

interface BitlyCreateOptions {
  domain?: string;        // Default: "bit.ly"
  group_guid?: string;    // Recommended to always include
  title?: string;
  tags?: string[];
}

interface BitlyCreateRequest {
  long_url: string;       // Required
  domain?: string;        // Default: "bit.ly"
  group_guid?: string;
  title?: string;
  tags?: string[];
}

interface BitlyCreateResponse {
  references?: any;
  link: string;           // The full short URL (e.g., "https://bit.ly/abc123")
  id: string;            // The Bitlink ID (e.g., "bit.ly/abc123")
  long_url: string;      // The original long URL
  title?: string;
  archived: boolean;
  created_at: string;    // ISO timestamp
  created_by?: string;
  client_id?: string;
  custom_bitlinks?: string[];
  tags?: string[];
}

interface BitlyUpdateRequest {
  title?: string;
  archived?: boolean;
  tags?: string[];
}

interface ClicksParams {
  unit: 'minute' | 'hour' | 'day' | 'week' | 'month';  // Required
  units: number;          // Required, -1 for all units
  unit_reference?: string; // ISO-8601 timestamp, URL encoded
}

interface MetricsParams extends ClicksParams {
  size?: number;          // Default: 50, max quantity returned
}

interface BitlyClicksResponse {
  link_clicks: Array<{
    clicks: number;
    date: string;
  }>;
  units: number;
  unit: string;
  unit_reference: string;
}

interface BitlyClicksSummaryResponse {
  total_clicks: number;
  units: number;
  unit: string;
  unit_reference: string;
}

interface BitlyMetricsResponse {
  unit: string;
  units: number;
  facet: string;
  unit_reference: string;
  metrics: Array<{
    clicks: number;
    value: string;        // Country code, referrer, etc.
  }>;
}

interface BitlyCitiesResponse {
  unit: string;
  units: number;
  facet: string;
  unit_reference: string;
  metrics: Array<{
    clicks: number;
    city: string;
    subregion: string;
    region: string;
    country: string;
  }>;
  other_metrics: {
    other_city_clicks: number;
    no_city_clicks: number;
  };
}

interface BitlyDevicesResponse {
  unit: string;
  units: number;
  facet: string;
  unit_reference: string;
  metrics: Array<{
    clicks: number;
    device_type: string;
  }>;
}
```

**Key API Endpoints:**
- `POST /v4/shorten` - Simple URL shortening (legacy endpoint)
- `POST /v4/bitlinks` - Create bitlink with full options (recommended)
- `GET /v4/bitlinks/{bitlink}` - Retrieve bitlink information
- `PATCH /v4/bitlinks/{bitlink}` - Update bitlink properties
- `GET /v4/bitlinks/{bitlink}/clicks` - Get click data over time
- `GET /v4/bitlinks/{bitlink}/clicks/summary` - Get total clicks summary
- `GET /v4/bitlinks/{bitlink}/countries` - Get clicks by country
- `GET /v4/bitlinks/{bitlink}/cities` - Get clicks by city with geographic data
- `GET /v4/bitlinks/{bitlink}/devices` - Get clicks by device type
- `GET /v4/bitlinks/{bitlink}/referrers` - Get clicks by referrer
- `GET /v4/bitlinks/{bitlink}/referrer_name` - Get clicks by referrer name
- `GET /v4/bitlinks/{bitlink}/referring_domains` - Get clicks by referring domain

**Authentication & Headers:**
- Authorization: `Bearer {TOKEN}` (required for all requests)
- Content-Type: `application/json` (for POST/PATCH requests)
- All timestamps should be URL encoded (replace '+' with '%2B' and ':' with '%3A')

**Best Practices:**
- Always include `group_guid` in bitlink creation for better organization
- Use the full `/v4/bitlinks` endpoint instead of `/v4/shorten` for new implementations
- Implement proper error handling and retry logic for API failures
- Cache frequently accessed metrics to avoid rate limiting
- Use appropriate time units and ranges for analytics queries

#### Tag Generation Utility

**Location:** `/lib/utils/tag-generator.ts`

```typescript
export function generateUniqueTag(): string;
export async function ensureTagUniqueness(tag: string): Promise<boolean>;
```

## Data Models

### ContactRequestSource Schema

```javascript
const ContactRequestSourceSchema = new Schema({
  tag: {
    type: String,
    required: true,
    unique: true,
    validate: {
      validator: (v) => /^[A-Za-z0-9]{6}$/.test(v),
      message: 'Tag must be exactly 6 alphanumeric characters'
    }
  },
  source: {
    type: String,
    required: true,
    enum: ['facebook', 'instagram', 'tiktok', 'youtube']
  },
  influencer: {
    type: String,
    required: true,
    enum: ['AMQ', 'Dominique Paquet']
  },
  bitlyUrl: {
    type: String,
    required: true
  },
  hits: {
    type: Number,
    default: 0
  },
  webhookData: [{
    timestamp: { type: Date, default: Date.now },
    ipAddress: String,
    userAgent: String,
    referrer: String,
    eventType: String,
    rawData: Schema.Types.Mixed
  }]
}, { timestamps: true });
```

## Error Handling

### API Error Responses

**Standard Error Format:**
```typescript
interface ApiError {
  error: string;
  message: string;
  code?: string;
  details?: any;
}
```

**Error Scenarios:**
- Permission denied (403)
- Tag already exists (409)
- Bitly API failures (502)
- Validation errors (400)
- Webhook signature verification failures (401)

### Frontend Error Handling

- Toast notifications for user feedback
- Graceful degradation for API failures
- Retry mechanisms for transient errors
- Loading states during operations

## Testing Strategy

### Unit Tests

**Backend Tests:**
- Model validation and schema tests
- API endpoint functionality
- Bitly service integration
- Tag generation utilities
- Webhook processing logic

**Frontend Tests:**
- Component rendering and interactions
- Form validation and submission
- Statistics calculation and display
- Error handling scenarios

### Integration Tests

- End-to-end source creation flow
- Bitly API integration testing
- Webhook event processing
- Permission-based access control
- Database operations and queries

### API Testing

- Postman collections for all endpoints
- Authentication and authorization testing
- Error scenario validation
- Performance and load testing

## Security Considerations

### Authentication and Authorization

- All API endpoints protected by MANAGE_CONTACT_REQUESTS_SOURCES permission
- Session-based authentication for web requests
- Webhook signature verification for Bitly events

### Data Validation

- Input sanitization for all user inputs
- Tag format validation (6 alphanumeric characters)
- Enum validation for source and influencer fields
- SQL injection prevention through Mongoose ODM

### External API Security

- Secure storage of Bitly API credentials
- Rate limiting for Bitly API calls
- Error handling to prevent information disclosure
- Webhook signature verification

## Performance Considerations

### Database Optimization

- Indexes on frequently queried fields (tag, source, createdAt)
- Efficient aggregation queries for statistics
- Pagination for large datasets
- Connection pooling and query optimization

### Caching Strategy

- Statistics caching with TTL
- Source list caching for dropdown options
- API response caching where appropriate

### Frontend Performance

- Lazy loading for large source lists
- Debounced search functionality
- Optimistic UI updates
- Efficient re-rendering with React keys

## Deployment and Configuration

### Environment Variables

```bash
BITLY_API_KEY=your_bitly_api_key
BITLY_WEBHOOK_SECRET=your_webhook_secret
BASE_URL=https://www.alimentationmonquartier.com
```

### Database Migrations

- Create ContactRequestSource collection
- Add indexes for performance
- Seed initial permission data if needed

### Monitoring and Logging

- API request/response logging
- Bitly integration monitoring
- Webhook processing logs
- Error tracking and alerting