# Requirements Document

## Introduction

This feature introduces a comprehensive contact requests sources management system that allows authenticated users to generate, manage, and track contact request sources with unique alphanumeric tags. The system integrates with Bitly API for URL shortening and provides analytics through webhook integration to track engagement and performance metrics.

## Requirements

### Requirement 1

**User Story:** As an authenticated user with proper permissions, I want to access a contact requests sources management page, so that I can manage and track different sources of contact requests.

#### Acceptance Criteria

1. WHEN a user navigates to /contact-requests/sources THEN the system SHALL display the sources management page
2. WHEN a user without the MANAGE_CONTACT_REQUESTS_SOURCES permission tries to access the page THEN the system SHALL redirect them to an access denied page
3. WHEN the page loads THEN the system SHALL display a list of existing sources grouped by source type
4. WHEN the page loads THEN the system SHALL display statistics at the top of the page showing total sources, total hits, and engagement metrics

### Requirement 2

**User Story:** As an authenticated user with MANAGE_CONTACT_REQUESTS_SOURCES permission, I want to create new contact request sources with unique tags, so that I can track different marketing channels and campaigns.

#### Acceptance Criteria

1. WHEN a user with MANAGE_CONTACT_REQUESTS_SOURCES permission clicks the create source button THEN the system SHALL open a dialog with a form
2. WHEN the dialog opens THEN the system SHALL pre-fill the tag field with a 6-character alphanumeric unique string
3. WHEN a user clicks the regenerate icon THEN the system SHALL generate a new unique 6-character alphanumeric tag
4. WHEN the form is displayed THEN the system SHALL show a dropdown for source selection with options: facebook, instagram, tiktok, youtube
5. WHEN the form is displayed THEN the system SHALL show a dropdown for influencer selection with options: AMQ, Dominique Paquet
6. WHEN a user submits the form THEN the system SHALL validate that the tag is unique and 6 characters alphanumeric
7. WHEN the form is valid THEN the system SHALL save the source to the database and call Bitly API to generate a shortened URL

### Requirement 3

**User Story:** As a user creating a source, I want to receive a shortened URL automatically, so that I can easily share the tracking link for my campaigns.

#### Acceptance Criteria

1. WHEN a source is successfully created THEN the system SHALL call Bitly API to create a shortened URL for https://www.alimentationmonquartier.com/invitation?source={tag}
2. WHEN implementing Bitly integration THEN the system SHALL use Context7 MCP to fetch Bitly API documentation for proper implementation
3. WHEN the Bitly API call succeeds THEN the system SHALL store the shortened URL in the database
3. WHEN the API response is received THEN the system SHALL close the creation form
4. WHEN the form closes THEN the system SHALL display an alert dialog containing the generated Bitly URL
5. WHEN the alert dialog opens THEN the system SHALL automatically copy the URL to the user's clipboard
6. WHEN the alert dialog is displayed THEN the system SHALL show a copy icon that allows manual copying of the URL

### Requirement 4

**User Story:** As a user, I want to view and search through existing contact request sources, so that I can manage and analyze my tracking campaigns.

#### Acceptance Criteria

1. WHEN the sources list is displayed THEN the system SHALL group sources by the 'source' field (facebook, instagram, etc.)
2. WHEN the page loads THEN the system SHALL display a search bar for filtering sources
3. WHEN a user types in the search bar THEN the system SHALL filter sources by tag, source type, or influencer name
4. WHEN the page loads THEN the system SHALL display filter options for source type and influencer
5. WHEN filters are applied THEN the system SHALL update the list to show only matching sources
6. WHEN sources are displayed THEN each source SHALL show tag, source type, influencer, hits count, and creation date

### Requirement 5

**User Story:** As a system administrator, I want webhook integration with Bitly to track URL clicks, so that I can monitor campaign performance and engagement.

#### Acceptance Criteria

1. WHEN a Bitly URL is clicked THEN Bitly SHALL send a webhook to the system
2. WHEN the webhook is received THEN the system SHALL increment the 'hits' field for the corresponding source
3. WHEN the webhook is received THEN the system SHALL store the webhook data in the webhookData array field
4. WHEN webhook data is stored THEN the system SHALL include timestamp, IP address, user agent, and referrer information
5. WHEN the hits count is updated THEN the system SHALL update the statistics displayed on the management page

### Requirement 6

**User Story:** As a user, I want to see comprehensive statistics about my contact request sources, so that I can analyze campaign performance and make data-driven decisions.

#### Acceptance Criteria

1. WHEN the sources management page loads THEN the system SHALL display total number of sources created
2. WHEN the page loads THEN the system SHALL display total hits across all sources
3. WHEN the page loads THEN the system SHALL display average hits per source
4. WHEN the page loads THEN the system SHALL display top performing sources by hits
5. WHEN the page loads THEN the system SHALL display sources performance grouped by source type
6. WHEN the page loads THEN the system SHALL display recent activity showing latest clicks and source creations

### Requirement 7

**User Story:** As a system, I want to store contact request sources in a dedicated database collection, so that I can maintain data integrity and support future enhancements.

#### Acceptance Criteria

1. WHEN a new source is created THEN the system SHALL store it in a ContactRequestSource collection
2. WHEN storing a source THEN the system SHALL include fields: tag, source, influencer, bitlyUrl, hits, webhookData, createdAt, updatedAt
3. WHEN storing a source THEN the system SHALL ensure the tag field is unique across all sources
4. WHEN storing webhook data THEN the system SHALL append new webhook events to the webhookData array
5. WHEN querying sources THEN the system SHALL support filtering, sorting, and grouping operations

### Requirement 8

**User Story:** As a user viewing contact requests, I want to see the source information for each request, so that I can understand where leads are coming from.

#### Acceptance Criteria

1. WHEN viewing the contact-requests page THEN the system SHALL display a 'source' column showing the source tag
2. WHEN hovering over a source tag THEN the system SHALL display a tooltip with complete source information
3. WHEN the contact-requests API is called THEN the system SHALL return the source field from pendingReservations
4. WHEN the contact-requests API is called THEN the system SHALL return the complete list of sources for frontend reference
5. WHEN displaying source information THEN the system SHALL show source type, influencer, and hit count in the tooltip

### Requirement 9

**User Story:** As a system, I want to protect all contact request sources management operations with proper permissions, so that only authorized users can manage sources.

#### Acceptance Criteria

1. WHEN any contact request sources API endpoint is called THEN the system SHALL verify the user has MANAGE_CONTACT_REQUESTS_SOURCES permission
2. WHEN a user without MANAGE_CONTACT_REQUESTS_SOURCES permission calls the API THEN the system SHALL return a 403 Forbidden error
3. WHEN the sources management page loads THEN the system SHALL check permissions before displaying management controls
4. WHEN permission checks fail THEN the system SHALL redirect to access denied page or hide management functionality
5. WHEN API operations are performed THEN the system SHALL log the user's actions for audit purposes