# Implementation Plan

- [x] 1. Create ContactRequestSource database model and utilities
  - Create Mongoose schema for ContactRequestSource with proper validation
  - Implement tag generation utility with uniqueness checking
  - Add database indexes for performance optimization
  - _Requirements: 7.1, 7.2, 7.3_

- [x] 2. Implement Bitly integration service
  - Create BitlyService class with authentication and proper headers
  - Implement createBitlink method using POST /v4/bitlinks endpoint
  - Implement getBitlinkClicks and getBitlinkClicksSummary methods for analytics
  - Add getBitlinkCountries, getBitlinkCities, and getBitlinkDevices methods for detailed analytics
  - Add proper URL encoding for timestamp parameters
  - Add error handling and retry logic for API failures
  - _Requirements: 3.1, 3.2, 6.1, 6.2, 6.3_

- [x] 3. Create contact request sources API endpoints
  - Implement GET /api/contact-request-sources with filtering and pagination
  - Implement POST /api/contact-request-sources with Bitly integration
  - Implement GET /api/contact-request-sources/stats for basic statistics
  - Implement GET /api/contact-request-sources/analytics for enhanced Bitly analytics
  - Add permission-based access control for all endpoints
  - _Requirements: 1.2, 2.1, 2.6, 2.7, 4.1, 4.2, 4.3, 6.1, 9.1, 9.2_

- [x] 4. Create Bitly webhook handler for click tracking
  - Implement POST /api/webhooks/bitly endpoint
  - Add webhook signature verification for security
  - Implement logic to increment hits counter and store webhook data
  - Add error handling for webhook processing
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [x] 5. Build sources management page UI components
  - Create main sources management page at /contact-requests/sources
  - Implement permission-based access control and redirect logic
  - Create statistics dashboard with key metrics display
  - Build grouped sources list with search and filter functionality
  - Add pagination support for large datasets
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 4.1, 4.2, 4.4, 4.5, 4.6_

- [x] 6. Implement source creation dialog and form
  - Create source creation dialog with form validation
  - Implement tag generation with regenerate functionality
  - Add source type dropdown (facebook, instagram, tiktok, youtube)
  - Add influencer dropdown (AMQ, Dominique Paquet)
  - Implement form submission with Bitly URL generation
  - Create success dialog with URL display and clipboard functionality
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 3.3, 3.4, 3.5, 3.6_

- [] 7. Build enhanced analytics dashboard with Bitly data
  - Create analytics components for comprehensive statistics
  - Implement time series charts for click trends
  - Add geographic analytics for click distribution
  - Build performance comparison views between sources
  - Add real-time data refresh functionality
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6_

- [x] 8. Enhance contact requests page with source information
  - Add source column to contact requests table
  - Implement source tag display with hover tooltips
  - Update contact requests API to include source data
  - Add complete sources list to API response for frontend reference
  - Create tooltip component showing complete source information
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [x] 9. Add comprehensive error handling and validation
  - Implement client-side form validation for all inputs
  - Add server-side validation for API endpoints
  - Create user-friendly error messages and toast notifications
  - Add loading states for all async operations
  - Implement graceful error handling for Bitly API failures
  - _Requirements: 2.6, 3.2, 9.1, 9.2, 9.5_

- [x] 10. Create navigation integration and permissions setup
  - Add navigation link to sources management page from contact requests
  - Implement permission guards for page access
  - Add permission checks to all UI components
  - Create access denied handling and redirects
  - _Requirements: 1.2, 9.3, 9.4_

- [x] 11. Implement comprehensive testing suite
  - Write unit tests for all API endpoints and business logic
  - Create integration tests for Bitly service and webhook handling
  - Add frontend component tests for UI interactions
  - Test permission-based access control scenarios
  - Create end-to-end tests for complete source creation workflow
  - _Requirements: All requirements validation_

- [x] 12. Add environment configuration and deployment setup
  - Configure Bitly API credentials in environment variables
  - Set up webhook URL configuration for production
  - Add database migration scripts if needed
  - Create deployment documentation and configuration
  - _Requirements: System deployment and configuration_