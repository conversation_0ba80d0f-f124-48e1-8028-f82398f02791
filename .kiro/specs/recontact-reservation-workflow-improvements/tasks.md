# Implementation Plan

- [x] 1. Update data models to support bidirectional recontact workflow
  - Add 'recontact' to reservation source enum and create oldReservation field for storing original reservation data
  - Add recontactReservationId field to Reservation model for cross-referencing
  - Add reservationId field to RecontactReservation model for tracking conversions
  - Create database indexes for new reference fields to optimize queries
  - _Requirements: 2.4, 2.6, 2.7, 4.1, 4.2, 4.3, 4.4_

- [x] 2. Enhance recontact transfer service with status integration
  - Modify RecontactTransferService to call status update API before transfer
  - Implement proper error handling and rollback mechanisms for failed status updates
  - Add validation to ensure status update completes before proceeding with transfer
  - Create comprehensive logging for the integrated transfer process
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 9.1, 9.5, 9.6_

- [x] 3. Create recontact to reservation conversion service
  - Implement RecontactConversionService with transaction-based conversion logic
  - Add validation for recontact reservation existence and data integrity
  - Create new reservation with proper source, oldReservation, and recontactReservationId fields
  - Update recontact reservation with new reservationId reference
  - Implement automatic note creation explaining the conversion process
  - _Requirements: 2.1, 2.4, 2.5, 2.6, 2.7, 2.8, 7.1, 7.2, 7.3, 7.4, 7.5, 9.2, 9.3, 9.4_

- [x] 4. Create API endpoint for recontact to reservation conversion
  - Implement POST /api/recontact-reservations/[id]/convert-to-reservation endpoint
  - Add request validation for appointment, customer, and status data
  - Integrate with RecontactConversionService for business logic
  - Implement proper error handling and user-friendly error messages
  - Add authentication and authorization checks
  - _Requirements: 2.3, 3.7, 3.8, 3.9, 9.5_

- [x] 5. Build recontact conversion dialog component
  - Create RecontactConversionDialog component similar to contact request creation dialog
  - Implement customer information pre-filling from recontact reservation data
  - Add editable form fields for customer data updates
  - Integrate branch selection with search functionality
  - Add date and time slot selection using existing availability API
  - Implement reservation status selection dropdown
  - Add form validation and submission handling
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9_

- [x] 6. Create old reservation details dialog component
  - Build OldReservationDetailsDialog for displaying historical reservation data
  - Implement sections for customer info, notes, messages, and status history
  - Add proper formatting and organization of historical data
  - Create responsive layout for different screen sizes
  - Add close functionality and proper dialog management
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6_

- [x] 7. Implement recontact indicator component for reservations table
  - Create RecontactIndicator component to show icon next to customer names
  - Implement hover tooltip with brief old reservation summary
  - Add click handler to open detailed old reservation dialog
  - Integrate with reservations table to display for source 'recontact' reservations
  - Style icon and tooltip for consistent user experience
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [x] 8. Add conversion functionality to recontact reservations page
  - Add conversion icon/button to recontact reservations table
  - Integrate RecontactConversionDialog with recontact reservations page
  - Implement click handler to open conversion dialog with selected recontact reservation
  - Add success handling to refresh table after conversion
  - Handle loading states and error scenarios
  - _Requirements: 2.1, 2.2_

- [x] 9. Implement navigation link for converted recontact reservations
  - Add logic to check for reservationId in recontact reservations display
  - Create navigation link/button to associated reservation edit page
  - Implement visual indication for converted recontact reservations
  - Add hover tooltip showing associated reservation details
  - Style link for clear user understanding
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5_

- [x] 10. Update reservation transfer workflow to use status integration
  - Modify existing recontact transfer UI to use enhanced transfer service
  - Update transfer dialog to ensure status selection before recontact transfer
  - Add proper error handling for status update failures
  - Test integration with existing reservation status change functionality
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [-] 11. Add comprehensive error handling and validation
  - Implement client-side validation for all form inputs in conversion dialog
  - Add server-side validation for API endpoints
  - Create user-friendly error messages for all failure scenarios
  - Add proper loading states and user feedback throughout the workflow
  - Test edge cases and error recovery scenarios
  - _Requirements: 9.5, 9.6_

- [-] 12. Create unit tests for new services and components
  - Write tests for RecontactConversionService business logic
  - Test enhanced RecontactTransferService with status integration
  - Create component tests for RecontactConversionDialog
  - Test OldReservationDetailsDialog component functionality
  - Add tests for API endpoint request/response handling
  - _Requirements: All requirements - testing coverage_

- [-] 13. Perform integration testing and end-to-end validation
  - Test complete recontact transfer workflow with status updates
  - Validate conversion process from recontact to reservation
  - Test UI components integration with backend services
  - Verify data integrity throughout the bidirectional workflow
  - Test error scenarios and recovery mechanisms
  - _Requirements: All requirements - integration validation_