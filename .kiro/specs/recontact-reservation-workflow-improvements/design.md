# Design Document

## Overview

The recontact reservation workflow improvements will enhance the existing system by integrating proper status management, creating a bidirectional conversion system, and improving data tracking and user experience. The design focuses on maintaining data integrity while providing seamless user interactions for managing the complete lifecycle of recontact reservations.

## Architecture

### Current System Analysis

The existing system has:
- **Reservation Model**: Core reservation data with status tracking and audit trails
- **RecontactReservation Model**: Stores recontact date, status, and complete reservation snapshot
- **RecontactTransferService**: Handles one-way transfer from reservation to recontact
- **Status Management API**: `/api/reservations/[id]/status` for status updates with comprehensive logging

### Enhanced Architecture

The enhanced system will add:
- **Bidirectional Conversion**: Support for both reservation → recontact and recontact → reservation
- **Status Integration**: Mandatory status updates before recontact transfers
- **Cross-Reference Tracking**: Bidirectional linking between reservations and recontact reservations
- **UI Components**: Recontact conversion dialog and visual indicators

## Components and Interfaces

### 1. Model Enhancements

#### Reservation Model Updates
```typescript
interface IReservation {
  // ... existing fields
  source: 'invitation' | 'direct' | 'other' | 'amq_website' | 'contact_request' | 'recontact';
  oldReservation?: any; // Complete original reservation data for recontact conversions
  recontactReservationId?: mongoose.Types.ObjectId; // Reference to source recontact reservation
}
```

#### RecontactReservation Model Updates
```typescript
interface IRecontactReservation {
  // ... existing fields
  reservationId?: mongoose.Types.ObjectId; // Reference to converted reservation
}
```

### 2. API Enhancements

#### Enhanced Transfer Service
```typescript
interface RecontactTransferServiceV2 {
  // Enhanced transfer with status integration
  transferToRecontactWithStatus(params: {
    reservationId: string;
    targetStatusId: string;
    recontactDate: Date;
    recontactStatusId: string;
    userId: string;
  }): Promise<TransferResult>;
  
  // New conversion method
  convertToReservation(params: {
    recontactReservationId: string;
    appointmentId: string;
    partnerId: string;
    initialStatusId: string;
    customerInfo: CustomerInfo;
    preferences: ReservationPreferences;
    userId: string;
  }): Promise<ConversionResult>;
}
```

#### New API Endpoints
- `POST /api/recontact-reservations/[id]/convert-to-reservation` - Convert recontact to reservation
- `GET /api/recontact-reservations/[id]/original-data` - Fetch original reservation data for display

### 3. UI Components

#### RecontactConversionDialog Component
```typescript
interface RecontactConversionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  recontactReservation: IRecontactReservation | null;
  onSuccess: () => void;
  statuses: ReservationStatus[];
}
```

Features:
- Pre-filled customer information from original reservation
- Editable form fields for customer data updates
- Branch and appointment selection (similar to contact request dialog)
- Initial status selection
- Validation and error handling

#### OldReservationDetailsDialog Component
```typescript
interface OldReservationDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  oldReservationData: any;
  reservationId: string;
}
```

Features:
- Display complete historical reservation data
- Show notes, messages, and status history
- Formatted presentation of customer and appointment information
- Read-only view with proper data organization

#### RecontactIndicator Component
```typescript
interface RecontactIndicatorProps {
  reservation: IReservation;
  onViewDetails: () => void;
}
```

Features:
- Icon display next to customer name for recontact source reservations
- Hover tooltip with brief old reservation summary
- Click handler to open detailed view

### 4. Service Layer Enhancements

#### StatusIntegratedTransferService
```typescript
class StatusIntegratedTransferService {
  async transferWithStatusUpdate(params: TransferParams): Promise<TransferResult> {
    // 1. Update reservation status via existing status API
    // 2. Wait for status update completion and validation
    // 3. Proceed with recontact transfer
    // 4. Handle rollback if transfer fails
  }
}
```

#### RecontactConversionService
```typescript
class RecontactConversionService {
  async convertToReservation(params: ConversionParams): Promise<ConversionResult> {
    // 1. Validate recontact reservation exists
    // 2. Create new reservation with recontact source
    // 3. Update recontact reservation with new reservation ID
    // 4. Create conversion note
    // 5. Handle transaction rollback on failure
  }
}
```

## Data Models

### Enhanced Reservation Schema
```typescript
const reservationSchema = new Schema({
  // ... existing fields
  source: {
    type: String,
    enum: ['invitation', 'direct', 'other', 'amq_website', 'contact_request', 'recontact'],
    default: 'direct'
  },
  oldReservation: {
    type: Schema.Types.Mixed,
    required: false
  },
  recontactReservationId: {
    type: Schema.Types.ObjectId,
    ref: 'RecontactReservation',
    required: false
  }
});
```

### Enhanced RecontactReservation Schema
```typescript
const recontactReservationSchema = new Schema({
  // ... existing fields
  reservationId: {
    type: Schema.Types.ObjectId,
    ref: 'Reservation',
    required: false
  }
});
```

## Error Handling

### Transfer Error Scenarios
1. **Status Update Failure**: If status update fails, prevent recontact transfer
2. **Validation Errors**: Comprehensive validation with user-friendly messages
3. **Transaction Failures**: Proper rollback mechanisms for partial operations
4. **Concurrent Modifications**: Handle race conditions during status updates

### Conversion Error Scenarios
1. **Invalid Recontact Reservation**: Validate existence and data integrity
2. **Appointment Conflicts**: Check availability before creating reservation
3. **Data Corruption**: Validate original reservation data completeness
4. **User Permission Issues**: Ensure proper authorization for conversions

## Testing Strategy

### Unit Tests
- Model validation and schema constraints
- Service layer business logic
- API endpoint request/response handling
- Error handling and edge cases

### Integration Tests
- End-to-end transfer workflow with status updates
- Conversion process with database transactions
- API endpoint integration with authentication
- UI component interaction with backend services

### User Acceptance Tests
- Complete recontact transfer workflow
- Conversion dialog functionality and validation
- Historical data display and navigation
- Error scenarios and user feedback

## Performance Considerations

### Database Optimization
- Indexes on new reference fields (recontactReservationId, reservationId)
- Efficient queries for cross-referenced data
- Proper transaction scoping to minimize lock time

### UI Performance
- Lazy loading of historical data in dialogs
- Efficient re-rendering of reservation lists
- Optimized API calls for availability data

### Caching Strategy
- Cache reservation status options
- Cache branch and user data for dialogs
- Implement proper cache invalidation

## Security Considerations

### Data Access Control
- Validate user permissions for recontact operations
- Ensure proper authorization for status updates
- Protect sensitive customer data in historical views

### Data Integrity
- Transaction-based operations for consistency
- Validation of all input data before processing
- Audit logging for all recontact operations

### API Security
- Rate limiting for conversion operations
- Input sanitization and validation
- Proper error message handling to prevent information leakage

## Migration Strategy

### Database Migrations
1. Add new fields to existing schemas
2. Create indexes for performance optimization
3. Migrate existing data if needed
4. Validate data integrity post-migration

### Deployment Strategy
1. Deploy backend changes first
2. Update API endpoints with backward compatibility
3. Deploy UI components incrementally
4. Monitor system performance and error rates

## Monitoring and Logging

### Audit Logging
- Log all recontact transfer operations
- Track conversion operations with user attribution
- Monitor status update integration
- Log error scenarios for debugging

### Performance Monitoring
- Track API response times for new endpoints
- Monitor database query performance
- Alert on high error rates or failures
- Track user interaction patterns with new features