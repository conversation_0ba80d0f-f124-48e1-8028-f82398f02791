# Requirements Document

## Introduction

This feature enhances the recontact reservation workflow to provide a more streamlined and complete process for managing reservations that need to be recontacted. The current system allows transferring reservations to recontact but lacks proper status management integration and the ability to convert recontact reservations back to normal reservations. This enhancement will provide a complete bidirectional workflow with proper status tracking, data preservation, and user experience improvements.

## Requirements

### Requirement 1: Status-Based Recontact Transfer

**User Story:** As a user managing reservations, I want the recontact transfer process to properly update the reservation status before transferring, so that the status change is tracked and audited correctly.

#### Acceptance Criteria

1. WHEN a user initiates a recontact transfer THEN the system SHALL first update the reservation status to the target status via the `/api/reservations/[id]/status` route
2. WHEN the status update is successful THEN the system SHALL proceed with the recontact transfer process
3. IF the status update fails THEN the system SHALL not proceed with the transfer and SHALL display an appropriate error message
4. WHEN the status is updated THEN the system SHALL create proper audit logs and reservation notes as per existing status change functionality

### Requirement 2: Recontact to Reservation Conversion

**User Story:** As a user managing recontact reservations, I want to convert a recontact reservation back to a normal reservation with a new appointment, so that I can reschedule customers who are ready to book again.

#### Acceptance Criteria

1. WHEN a user clicks the conversion icon on a recontact reservation THEN the system SHALL open a reservation creation dialog
2. WHEN the dialog opens THEN the system SHALL display the original customer information from the recontact reservation
3. WHEN the user selects date, time, branch, and initial status THEN the system SHALL validate all required fields are completed
4. WHEN the form is submitted THEN the system SHALL create a new reservation with source set to 'recontact'
5. WHEN the new reservation is created THEN the system SHALL set the oldReservation field to reference the original reservation data from the recontact reservation
6. WHEN the new reservation is created THEN the system SHALL set the recontactReservationId field to reference the recontact reservation ID
7. WHEN the conversion is successful THEN the system SHALL update the recontact reservation to set reservationId field to the newly created reservation ID
8. WHEN the conversion is successful THEN the system SHALL create a reservation note explaining the reservation was created from a recontact reservation

### Requirement 3: Recontact Conversion Dialog

**User Story:** As a user converting a recontact reservation, I want a user-friendly dialog similar to the contact request creation dialog with pre-filled customer information, so that I can easily update customer details and select appointment details and initial status.

#### Acceptance Criteria

1. WHEN the conversion dialog opens THEN the system SHALL display customer information from the recontact reservation in editable form fields pre-filled with existing data
2. WHEN the dialog loads THEN the system SHALL pre-populate all customer information fields (name, email, phone, postal code, etc.) from the original reservation data
3. WHEN selecting a branch THEN the system SHALL provide a searchable dropdown with all available branches
4. WHEN a branch is selected THEN the system SHALL load and display available dates and time slots using the existing availability API
5. WHEN selecting dates THEN the system SHALL provide a calendar interface similar to the new reservation page
6. WHEN selecting time slots THEN the system SHALL display available slots with capacity information and full slot warnings
7. WHEN selecting initial status THEN the system SHALL provide a dropdown of all available reservation statuses
8. WHEN all required fields are completed THEN the system SHALL enable the submit button
9. WHEN the form is submitted THEN the system SHALL show loading state and disable the form during processing

### Requirement 4: Reservation Source Enhancement

**User Story:** As a developer, I want the reservation model to support 'recontact' as a source type, so that reservations created from recontact reservations can be properly identified and tracked.

#### Acceptance Criteria

1. WHEN defining reservation sources THEN the system SHALL include 'recontact' as a valid source option
2. WHEN creating a reservation from a recontact reservation THEN the system SHALL set the source field to 'recontact'
3. WHEN storing the old reservation data THEN the system SHALL add an oldReservation field to store the complete original reservation object
4. WHEN querying reservations THEN the system SHALL be able to filter and identify reservations created from recontact sources

### Requirement 5: Old Reservation Data Display

**User Story:** As a user viewing reservations, I want to see an indicator when a reservation was created from a recontact reservation, so that I can access the historical context and previous interaction data.

#### Acceptance Criteria

1. WHEN displaying reservations with source 'recontact' THEN the system SHALL show a recontact icon next to the customer name
2. WHEN hovering over the recontact icon THEN the system SHALL display a tooltip with a brief summary of the old reservation
3. WHEN the tooltip is displayed THEN the system SHALL show key information like original appointment date, previous status, and creation date
4. WHEN the tooltip contains a "View Details" button THEN clicking it SHALL open a detailed dialog showing complete old reservation data
5. WHEN the detailed dialog opens THEN the system SHALL display all historical data including messages, notes, and previous status changes

### Requirement 6: Old Reservation Details Dialog

**User Story:** As a user, I want to view complete historical data from the original reservation when a reservation was created from recontact, so that I have full context for customer interactions and previous booking attempts.

#### Acceptance Criteria

1. WHEN the old reservation details dialog opens THEN the system SHALL display all customer information from the original reservation
2. WHEN showing historical data THEN the system SHALL include all reservation notes from the original reservation
3. WHEN displaying messages THEN the system SHALL show all SMS/communication history from the original reservation
4. WHEN showing status history THEN the system SHALL display all previous status changes with timestamps and user information
5. WHEN the dialog is displayed THEN the system SHALL provide proper formatting and organization of the historical data
6. WHEN the user closes the dialog THEN the system SHALL return to the main reservations view

### Requirement 7: Automatic Note Creation

**User Story:** As a user, I want automatic documentation when a reservation is created from a recontact reservation, so that there is a clear audit trail of the conversion process.

#### Acceptance Criteria

1. WHEN a new reservation is created from a recontact reservation THEN the system SHALL automatically create a reservation note
2. WHEN creating the automatic note THEN the system SHALL include the recontact reservation ID and conversion timestamp
3. WHEN the note is created THEN the system SHALL reference the original reservation details and recontact reason
4. WHEN the note is saved THEN the system SHALL attribute it to the user who performed the conversion
5. WHEN viewing the reservation notes THEN the conversion note SHALL be clearly identifiable and formatted consistently

### Requirement 8: Recontact Reservation Navigation

**User Story:** As a user viewing recontact reservations, I want to easily navigate to the associated new reservation when a conversion has been completed, so that I can manage the current active reservation.

#### Acceptance Criteria

1. WHEN displaying recontact reservations THEN the system SHALL check if the recontact reservation has an associated reservationId
2. WHEN a recontact reservation has a reservationId THEN the system SHALL display a link or button to navigate to the associated reservation
3. WHEN the user clicks the navigation link THEN the system SHALL open the reservation edit page at `/reservations/[id]/edit`
4. WHEN the link is displayed THEN the system SHALL provide clear visual indication that this recontact reservation has been converted
5. WHEN hovering over the link THEN the system SHALL show a tooltip indicating the associated reservation details

### Requirement 9: Data Integrity and Validation

**User Story:** As a system administrator, I want proper data validation and integrity checks during the recontact workflow, so that data consistency is maintained throughout the process.

#### Acceptance Criteria

1. WHEN transferring to recontact THEN the system SHALL validate that the reservation exists and is not already deleted
2. WHEN converting from recontact THEN the system SHALL validate that the recontact reservation exists and contains valid original reservation data
3. WHEN creating the new reservation THEN the system SHALL validate all appointment and customer data before saving
4. WHEN storing old reservation data THEN the system SHALL ensure the complete original reservation object is preserved
5. IF any validation fails THEN the system SHALL provide clear error messages and prevent data corruption
6. WHEN the process completes THEN the system SHALL ensure all related data (notes, audit logs) are properly linked and consistent