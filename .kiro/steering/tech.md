# Technology Stack

## Framework & Runtime
- **Next.js 15.2.3** - React framework with App Router
- **React 18.2.0** - UI library
- **TypeScript 5.2.2** - Type safety and development experience
- **Node.js** - Server runtime

## Database & Data
- **MongoDB** - Primary database with Mongoose ODM
- **Mongoose 8.10.0** - Object modeling and schema validation

## Authentication & Authorization
- **NextAuth.js 4.24.11** - Authentication system
- **bcryptjs** - Password hashing
- **Role-based permissions** - Custom permission system with roles and direct permissions

## UI & Styling
- **Tailwind CSS 3.3.3** - Utility-first CSS framework
- **shadcn/ui** - Component library built on Radix UI primitives
- **Radix UI** - Headless UI components
- **Framer Motion** - Animation library
- **Lucide React** - Icon library

## Communication & External Services
- **Twilio** - SMS and voice communication
- **Brevo** - Email service integration
- **Firebase Admin** - Push notifications and admin services
- **Google Maps API** - Location services

## Development & Build Tools
- **ESLint** - Code linting
- **PostCSS & Autoprefixer** - CSS processing
- **tsx** - TypeScript execution for scripts
- **Puppeteer** - PDF generation and web scraping

## Common Commands

### Development
```bash
npm run dev          # Start development server on port 5000
npm run build        # Build for production
npm run start        # Start production server on port 5000
npm run lint         # Run ESLint
```

### Database Operations
```bash
npm run migrate-sellers                    # Add sellers to branches
npm run migrate-soft-delete               # Set default soft delete flags
npm run migrate-conversations             # Migrate conversation data
npm run normalize-reservations-phones     # Normalize phone number formats
```

### Testing & Utilities
```bash
npm run test-invoice-automation           # Test invoice automation
npm run generate-missing-commissions      # Generate missing commission records
npm run create-event-commission-types     # Create event-based commission types
npm run send-grand-jour-sms              # Send scheduled SMS messages
```

### Data Management
```bash
npm run seed:invoice-demo                 # Seed demo invoice data
npm run check:demo-data                   # Verify demo data integrity
npm run token-batch-generate              # Generate invitation tokens in batch
npm run token-monitor                     # Monitor token generation process
```

## Environment Configuration
- Uses `.env` files for configuration
- Supports multiple environment templates (`.env.example`, `.env.example.twilio`)
- Server-side environment variables configured in `next.config.js`
- Standalone output mode for deployment