# Project Structure

## Architecture Pattern
- **Next.js App Router** - Modern file-based routing with server/client components
- **API Routes** - RESTful API endpoints in `/app/api/`
- **Component-driven** - Reusable UI components with clear separation of concerns
- **Type-safe** - TypeScript throughout with shared type definitions

## Directory Organization

### Core Application (`/app/`)
```
app/
├── api/                    # API routes (server-side)
├── [feature]/             # Feature-based page organization
├── components/            # Page-specific components
├── lib/                   # Application utilities
├── models/                # Database models (also in root /models/)
├── types/                 # Type definitions (also in root /types/)
├── globals.css            # Global styles
├── layout.tsx             # Root layout
└── page.tsx               # Home page
```

### Shared Resources
```
components/                # Reusable UI components
├── ui/                   # shadcn/ui components
├── shared/               # Cross-feature components
└── [feature]/            # Feature-specific components

models/                   # Mongoose schemas and interfaces
types/                    # TypeScript type definitions
lib/                      # Shared utilities and services
hooks/                    # Custom React hooks
```

### Configuration & Tooling
```
scripts/                  # Database migrations and utilities
docs/                     # Documentation and implementation guides
tests/                    # Test files
language/                 # Internationalization files (en.json, fr.json)
public/                   # Static assets
```

## Naming Conventions

### Files & Directories
- **kebab-case** for directories and most files (`user-management/`, `api-utils.ts`)
- **PascalCase** for React components (`UserForm.tsx`, `ReservationTable.tsx`)
- **camelCase** for utilities and hooks (`usePermissions.ts`, `dateUtils.ts`)

### API Routes
- RESTful structure: `/api/[resource]/[action]`
- CRUD operations: GET, POST, PATCH, DELETE
- Nested resources: `/api/branches/[id]/users`

### Components
- **Feature-based organization** - Components grouped by business domain
- **Atomic design principles** - UI components, shared components, page components
- **Co-location** - Keep related files close (components, hooks, utils)

## Data Layer Patterns

### Models (`/models/`)
- Mongoose schemas with TypeScript interfaces
- Consistent naming: `I[ModelName]` for interfaces, `[ModelName]` for schemas
- Soft delete support with `deletedAt` fields
- Audit fields: `createdAt`, `updatedAt`

### API Structure
- Consistent error handling and response formats
- Permission-based access control
- Request validation using Zod or similar
- Logging for audit trails

### Type Definitions (`/types/`)
- Shared interfaces for API responses
- Form validation schemas
- Permission and role definitions
- Business domain types

## Component Patterns

### UI Components (`/components/ui/`)
- shadcn/ui based components
- Consistent prop interfaces
- Forwarded refs where appropriate
- Compound component patterns for complex UI

### Feature Components
- Business logic components
- Data fetching and state management
- Form handling with react-hook-form
- Permission-based rendering

### Page Components
- Server components by default
- Client components when interactivity needed
- Proper loading and error states
- SEO-friendly metadata

## Development Patterns

### State Management
- React state for local component state
- Server state with SWR or React Query patterns
- Form state with react-hook-form
- Global state with Redux Toolkit (when needed)

### Error Handling
- Try-catch blocks in API routes
- Error boundaries for React components
- Consistent error response formats
- User-friendly error messages with i18n

### Performance
- Server components for static content
- Client components for interactivity
- Image optimization with Next.js Image
- Code splitting by route and feature