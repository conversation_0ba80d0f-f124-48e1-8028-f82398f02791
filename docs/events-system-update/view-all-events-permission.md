# VIEW_ALL_EVENTS Permission Implementation

## Overview
This document describes the implementation of the new `VIEW_ALL_EVENTS` permission that controls access to the "All Events" tab in the events sidebar.

## Changes Made

### 1. Permission Code Addition
- Added `VIEW_ALL_EVENTS: 'VIEW_ALL_EVENTS'` to `EVENT_PERMISSIONS` in `types/permission-codes.ts`

### 2. Permission Utility Function
- Added `canUserViewAllEvents()` function in `lib/utils/permissions-utils.ts`
- Function checks if user is SuperAdmin OR has the `VIEW_ALL_EVENTS` permission

### 3. Events Sidebar Update
- Updated `components/events/EventsSidebar.tsx` to conditionally show "All Events" tab
- "All Events" tab now only appears when user has `VIEW_ALL_EVENTS` permission
- Imported and used the new `canUserViewAllEvents` function

### 4. Database Permission Creation
- Created script `scripts/create-view-all-events-permission.ts` to add the permission to database
- Permission details:
  - **Name**: "View All Events"
  - **Code**: "VIEW_ALL_EVENTS"
  - **Description**: "Can view all events in the system (shows 'All Events' tab in sidebar)"

## Usage

### For Administrators
To grant a user access to the "All Events" tab:
1. Go to user management
2. Assign the `VIEW_ALL_EVENTS` permission to the user's role or directly to the user
3. User will now see the "All Events" tab in the events sidebar

### For Developers
The permission can be checked using:
```typescript
import { canUserViewAllEvents } from '@/lib/utils/permissions-utils';

const hasAccess = canUserViewAllEvents(user);
```

## Backward Compatibility
- Existing users with `VIEW_EVENTS` permission will still have access to other event features
- Only the "All Events" tab visibility is affected by this new permission
- SuperAdmins automatically have access to all events regardless of this permission

## Security Considerations
- This permission provides granular control over who can see the comprehensive events list
- Users without this permission can still access events through other means if they have appropriate permissions (e.g., "My Events" for supervisors)
- The permission follows the existing security model and integrates with the role-based access control system
