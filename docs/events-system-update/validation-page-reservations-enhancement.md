# Validation Page - Linked Reservations Enhancement

## Overview

This task enhances the event validation page (`/events/validation/[id]`) to provide comprehensive linked reservations information and manual reservation management capabilities for PAPs during the validation process.

## Current State

The validation page currently:
- Shows basic personnel information (PAPs, Cooks, Supervisors)
- Displays validation checks for time ranges and percentages
- Allows approval/rejection of event reports
- Does NOT show linked reservation details
- Does NOT provide manual reservation linking capabilities

## Requirements

### 1. Personnel Summary Enhancement

**Location**: Overview tab - Personnel Summary card

**Current Display**:
```
Personnel Summary
- Supervisors: 2
- PAPs: 3  
- Cooks: 1
```

**Enhanced Display**:
```
Personnel Summary
- Supervisors: 2
- PAPs: 3 (15 linked reservations)
- Cooks: 1

Linked Reservations (15):
- <PERSON> | Agent: <PERSON> | Created: Jul 8, 2025
- <PERSON> | Agent: <PERSON> | Created: Jul 8, 2025
- <PERSON> | Agent: <PERSON> | Created: Jul 7, 2025
[... compact list continues ...]
```

**Data Requirements**:
- `reservation.customerInfo.customer1Name`
- `reservation.partnerId.user.name` (agent name)
- `reservation.createdAt`

### 2. PAP Reservation Management

**Location**: Personnel tab - PAPs section

**Current PAP Item Display**:
```
[PAP Name] - [Time Range]
```

**Enhanced PAP Item Display**:
```
[PAP Name] - [Time Range]
[View Linked Reservations] button
```

#### 2.1 PAP Reservations Dialog

**Trigger**: Click "View Linked Reservations" button on any PAP

**Dialog Content**:
- Title: "Linked Reservations - [PAP Name]"
- List of currently linked reservations for this specific PAP
- Each reservation shows: customer name, agent name, created date
- [Add Reservation] button
- [Remove] button for each linked reservation

**Data Source**: `eventReport.paps[index].linkedReservationIds`

#### 2.2 Add Reservation Dialog

**Trigger**: Click "Add Reservation" button in PAP Reservations Dialog

**Dialog Content**:
- Title: "Add Reservation - [PAP Name]"
- Search/filter functionality
- List of ALL reservations for the event (linked and unlinked)
- Visual indicators:
  - ✅ Already linked to this PAP
  - 🔗 Linked to another PAP
  - ⭕ Not linked
- [Link] / [Unlink] buttons for each reservation

## Technical Implementation

### 3. API Enhancements

#### 3.1 Enhanced Event Report Endpoint

**Endpoint**: `GET /api/event-reports/[id]`

**Current Response**: Basic event report data

**Enhanced Response**: Include populated reservation data
```typescript
{
  // ... existing fields
  paps: [{
    userId: { _id, name, email },
    timeRange: { startTime, endTime },
    linkedReservationIds: [ObjectId], // Current
    linkedReservations: [{ // NEW - populated data
      _id: string,
      customerInfo: {
        customer1Name: string
      },
      partnerId: {
        user: {
          name: string
        }
      },
      createdAt: Date
    }]
  }],
  allEventReservations: [{ // NEW - all reservations for this event
    _id: string,
    customerInfo: { customer1Name: string },
    partnerId: { user: { name: string } },
    createdAt: Date,
    linkedToPapId?: string // Which PAP this is linked to (if any)
  }]
}
```

#### 3.2 Manual Reservation Linking Endpoints

**Endpoint**: `POST /api/event-reports/[id]/link-reservation`

**Purpose**: Manually link/unlink reservations to specific PAPs

**Request Body**:
```typescript
{
  papUserId: string,
  reservationId: string,
  action: 'link' | 'unlink'
}
```

**Response**:
```typescript
{
  success: boolean,
  message: string,
  updatedPap: {
    userId: string,
    linkedReservationIds: string[]
  }
}
```

**Validation Rules**:
- Only users with `canUserValidateEventReports` permission
- Report must be in 'submitted' status
- Reservation must belong to the event's time range
- Cannot link same reservation to multiple PAPs

### 4. Frontend Components

#### 4.1 Enhanced Personnel Summary Component

**File**: `app/events/validation/[id]/page.tsx`

**New Component**: `PersonnelSummaryWithReservations`

**Features**:
- Display total linked reservations count
- Compact reservation list (max 5-10 items, with "show more" option)
- Responsive design for mobile/desktop

#### 4.2 PAP Reservations Dialog Component

**New Component**: `PapReservationsDialog`

**Props**:
```typescript
interface PapReservationsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  pap: {
    userId: { _id: string, name: string },
    linkedReservations: Reservation[]
  };
  onReservationUnlink: (reservationId: string) => void;
  onAddReservation: () => void;
}
```

#### 4.3 Add Reservation Dialog Component

**New Component**: `AddReservationDialog`

**Props**:
```typescript
interface AddReservationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  papUserId: string;
  allReservations: ReservationWithLinkStatus[];
  onReservationLink: (reservationId: string, action: 'link' | 'unlink') => void;
}
```

**Features**:
- Search/filter by customer name or agent name
- Visual status indicators
- Bulk operations support (future enhancement)

### 5. Data Flow

#### 5.1 Page Load
1. Fetch enhanced event report data with populated reservations
2. Display personnel summary with reservation counts
3. Prepare PAP items with reservation management buttons

#### 5.2 View PAP Reservations
1. User clicks "View Linked Reservations" on PAP
2. Open dialog showing linked reservations for that PAP
3. Display reservation details in readable format

#### 5.3 Manual Reservation Linking
1. User clicks "Add Reservation" in PAP dialog
2. Open add reservation dialog with all event reservations
3. User selects reservations to link/unlink
4. Send API request to update links
5. Refresh dialog data and close
6. Update main page data

### 6. UI/UX Considerations

#### 6.1 Visual Design
- Use consistent card/dialog styling with existing validation page
- Clear visual indicators for reservation link status
- Responsive design for mobile validation workflows

#### 6.2 Performance
- Lazy load reservation details only when needed
- Implement pagination for large reservation lists
- Cache reservation data to avoid repeated API calls

#### 6.3 User Experience
- Clear confirmation dialogs for link/unlink actions
- Toast notifications for successful operations
- Error handling with user-friendly messages
- Maintain dialog state during operations

### 7. Validation Rules

#### 7.1 Business Rules
- Reservations can only be linked to one PAP at a time
- Reservations must be within event time range
- **Manual linking should not automatically trigger during validation**
- **Approving a report preserves manual reservation links (no automatic relinking)**
- Changes should be logged in event report history

#### 7.2 Permission Requirements
- Only users with `canUserValidateEventReports` permission
- Report must be in 'submitted' status for modifications
- Audit trail for all manual linking operations

## Implementation Priority

### Phase 1: Core Enhancement
1. ✅ API endpoint enhancement for populated reservation data
2. ✅ Personnel summary reservation display
3. ✅ Basic PAP reservations dialog

### Phase 2: Management Features
1. ✅ Add reservation dialog with search/filter
2. ✅ Manual link/unlink functionality
3. ✅ API endpoints for reservation management

### Phase 3: Polish & Optimization
1. ✅ Performance optimizations
2. ✅ Enhanced UI/UX features
3. ✅ Comprehensive error handling

## Acceptance Criteria

### Personnel Summary
- [ ] Shows total count of linked reservations
- [ ] Displays compact list with customer name, agent name, created date
- [ ] Responsive design works on mobile and desktop

### PAP Management
- [ ] Each PAP has "View Linked Reservations" button
- [ ] Dialog shows current linked reservations for specific PAP
- [ ] Add reservation dialog shows all event reservations with status indicators
- [ ] Manual link/unlink operations work correctly
- [ ] Changes are persisted and reflected immediately

### Data Integrity
- [ ] No reservation can be linked to multiple PAPs
- [ ] All operations are logged in audit trail
- [ ] Permission checks prevent unauthorized modifications
- [ ] Validation process doesn't automatically relink reservations

## Testing Requirements

### Unit Tests
- [ ] API endpoint response validation
- [ ] Component rendering with reservation data
- [ ] Link/unlink operation logic

### Integration Tests
- [ ] End-to-end reservation management workflow
- [ ] Permission-based access control
- [ ] Data consistency across operations

### User Acceptance Tests
- [ ] Validation workflow with reservation management
- [ ] Mobile responsiveness
- [ ] Error handling scenarios
