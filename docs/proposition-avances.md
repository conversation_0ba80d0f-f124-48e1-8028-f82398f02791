# Proposition de mise à jour du système pour le suivi des "avances" (notes de crédit)

## Introduction

 Cette fonctionnalité permettra de gérer les montants prépayés par les bénéficiaires qui peuvent être déduits des factures futures.

## Fonctionnalités proposées

### 1. Nouveau type d'élément de facture "Avances"

Nous proposons d'ajouter un nouveau type d'élément de facture spécifique appelé "Avances". Ce type d'élément aura les caractéristiques suivantes :

- Il sera le seul type d'élément à être inséré avec une valeur négative dans les factures
- Lorsqu'un élément de ce type est ajouté à une facture, le montant sera automatiquement enregistré dans une entité séparée pour le suivi

### 2. Enregistrement automatique des avances

Chaque fois qu'un élément de type "Avances" est utilisé dans une facture :

- Le système enregistrera automatiquement le montant dans une base de données dédiée
- Ces informations seront utilisées pour le suivi de l'historique des paiements du bénéficiaire
- Le montant apparaîtra comme une valeur négative dans la facture pour refléter la déduction

### 3. Interface dédiée pour ajouter des avances aux utilisateurs

Nous proposons de créer une interface séparée permettant d'ajouter des avances aux utilisateurs :

- Interface simple et intuitive pour saisir les avances
- Possibilité de saisir la date et le montant de l'avance
- Enregistrement automatique dans le système de suivi des avances

### 4. Importation d'avances dans les factures

Comme pour les commissions actuelles, il sera possible d'importer des avances dans les factures :

- Sélection depuis une liste d'avances disponibles pour le bénéficiaire
- Ajout automatique comme un seul élément de facture de type "Avances"
- Le montant sera automatiquement converti en valeur négative

### 5. Historique des transactions dans la page de facture

Dans la page d'édition de chaque facture, nous ajouterons un panneau latéral contenant l'historique des transactions du bénéficiaire :

- Liste chronologique des transactions (factures et avances)
- Chaque entrée affichera la date et le montant (positif pour les factures, négatif pour les avances)
- Affichage d'un total cumulatif représentant le solde actuel du bénéficiaire

## Processus de fonctionnement

### Ajout d'une avance
1. L'administrateur utilise l'interface dédiée pour ajouter une avance à un utilisateur, ou bien il peut ajouter un élément de facture de type « Avances » directement dans la facture.
2. Le système enregistre cette avance dans la base de données dédiée
3. L’avance est maintenant disponible pour être utilisée dans les factures, ou bien elle est déjà insérée directement dans la facture.

### Utilisation d'une avance dans une facture
1. Lors de la création ou modification d'une facture, l'administrateur peut importer des avances
2. Les avances sélectionnées sont ajoutées comme éléments de facture de type "Avances"
3. Ces éléments apparaissent avec des montants négatifs dans la facture


### Suivi de l'historique
1. Pour chaque facture, un panneau latéral affiche l'historique complet
2. Cet historique inclut toutes les factures et avances de l'utilisateur
3. Un total cumulatif permet de voir le solde actuel du bénéficiaire

## Conclusion

Cette mise en œuvre permettra une gestion plus efficace et transparente des avances dans le système de facturation. Elle améliorera à la fois l'expérience utilisateur et la précision des opérations financières.