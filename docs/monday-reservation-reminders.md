# Monday Reservation Reminder SMS System

This system automatically sends SMS reminders to customers who have reservations scheduled for Monday. The reminders are sent on Saturday at 7:00 PM (48 hours before the reservation).

## How It Works

1. **Schedule**: Every Saturday at 7:00 PM (America/Toronto timezone)
2. **Target**: Reservations scheduled for the following Monday
3. **Conditions**:
   - Reservation must be in one of the configured branches
   - Reservation must have been created more than 1 day ago
   - Reservation status must be 'new'
4. **SMS Content**: VIP experience reminder with weekly draw and product information

## Configuration

### Setting Up Branch IDs

1. Open the file: `lib/config/monday-reminder-branches.ts`
2. Add your branch IDs to the `MONDAY_REMINDER_BRANCH_IDS` array
3. Save the file
4. Restart the application for changes to take effect

Example:
```typescript
export const MONDAY_REMINDER_BRANCH_IDS: string[] = [
  '507f1f77bcf86cd799439011', // Downtown Branch
  '507f1f77bcf86cd799439012', // Mall Branch
  '507f1f77bcf86cd799439013', // Airport Branch
];
```

### Finding Branch IDs

You can find branch IDs by:
1. Checking your MongoDB `branches` collection
2. Looking at existing reservation data in the `preferences.branchId` field
3. Using your admin interface to view branch details

## SMS Template

The SMS sent to customers includes:
- Personal greeting with customer name
- Reminder of VIP tasting experience
- Date and time of reservation
- Branch address
- Information about weekly draw (2,500$ in products)
- 25$ product guarantee
- Request for confirmation

## Technical Details

- **Cron Expression**: `0 19 * * 6` (Saturday 7:00 PM)
- **Timezone**: America/Toronto
- **Database Models**: Uses Reservation, ScheduledSMS, and Branch collections
- **SMS Service**: Integrates with existing Twilio SMS system
- **Error Handling**: Includes retry logic and failure alerts

## Monitoring

The system logs:
- Number of reservations found
- Number of SMS messages scheduled
- Number of reservations skipped (and reasons)
- Any errors encountered during processing

Check your application logs for entries starting with "Monday reservation reminder" to monitor the system.

## Troubleshooting

### No SMS Being Sent
1. Check if branch IDs are correctly configured in the config file
2. Verify reservations exist for next Monday in those branches
3. Ensure reservations were created more than 1 day ago
4. Check application logs for error messages

### Wrong Branch Getting Reminders
1. Verify the branch IDs in the configuration file
2. Check that the branch IDs match exactly with your database
3. Restart the application after making configuration changes

### SMS Content Issues
1. The SMS template is hardcoded in the CronScheduler
2. To modify the message, edit the `smsTemplate` variable in the `executeMondayReservationReminderJob` method
3. Restart the application after making changes

## Disabling the Feature

To disable Monday reservation reminders:
1. Set the environment variable `DISABLE_CRON=true`
2. Or remove all branch IDs from the configuration file
3. Restart the application
