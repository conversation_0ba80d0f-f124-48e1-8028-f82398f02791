# GitHub Actions Deployment Setup

This document explains how to set up and use the GitHub Actions workflow for automated deployment to your VPS servers.

## Overview

The workflow replaces your manual deployment scripts with an automated GitHub Actions workflow that:

1. **Builds** the project in a clean environment
2. **Tests** the build to ensure it's successful
3. **Deploys** to your chosen environment(s) only if the build succeeds

## Workflow Features

- **Manual Trigger**: Deployments are triggered manually via GitHub's web interface
- **Environment Selection**: Choose to deploy to production, development, or both
- **Build Validation**: Only deploys if the build is successful
- **Environment-Specific Deployment**: Deploy to production or development with appropriate configurations
- **Artifact Management**: Builds once, deploys multiple times

## Required GitHub Secrets

You need to set up the following secrets in your GitHub repository:

### Go to: Repository Settings → Secrets and variables → Actions → New repository secret

#### For Production Server:
- `PROD_HOST`: Your production server IP address
- `PROD_USERNAME`: SSH username for production server
- `PROD_PASSWORD`: SSH password for production server access
- `PROD_DEPLOY_PATH`: Production deployment path (e.g., `/var/www/amq_partners`)

#### For Development Server:
- `DEV_HOST`: Your development server IP address
- `DEV_USERNAME`: SSH username for development server
- `DEV_PASSWORD`: SSH password for development server access
- `DEV_DEPLOY_PATH`: Development deployment path (e.g., `/var/www/amq_partners_dev/amq_partners`)

#### For PM2 App Configuration:
- `PROD_APP_NAME`: Production PM2 app name (e.g., `amq-partners-prod`)
- `DEV_APP_NAME`: Development PM2 app name (e.g., `amq-partners-dev`)
- `PROD_PORT`: Production app port (e.g., `5000`)
- `DEV_PORT`: Development app port (e.g., `5001`)

#### For Environment Configuration:
- `PROD_ENV_FILE`: Complete production environment file content (multiline secret)
- `DEV_ENV_FILE`: Complete development environment file content (multiline secret)

#### Optional PM2 Configuration:
- `PROD_MEMORY_LIMIT`: Production memory limit (default: `1G`)
- `DEV_MEMORY_LIMIT`: Development memory limit (default: `1G`)
- `PROD_NODE_MEMORY`: Production Node.js heap size (default: `2048`)
- `DEV_NODE_MEMORY`: Development Node.js heap size (default: `2048`)

## PM2 Ecosystem Configuration

The workflow now uses a proper PM2 ecosystem file (`ecosystem.config.js`) with the following benefits:

### **Production App (`amq-partners-prod`):**
- **Port**: 5000
- **Mode**: Fork (single instance)
- **Environment**: Production
- **Memory Limit**: 1GB with auto-restart
- **Logs**: Organized in `/var/www/amq_partners/logs/`

### **Development App (`amq-partners-dev`):**
- **Port**: 5001
- **Mode**: Fork (single instance)
- **Environment**: Development
- **Memory Limit**: 1GB with auto-restart
- **Logs**: Organized in `/var/www/amq_partners_dev/amq_partners/logs/`

### **Key Features:**
- ✅ **Automatic Environment Loading**: Reads `.env` files automatically
- ✅ **Memory Management**: Auto-restart on memory limit
- ✅ **Proper Logging**: Separate log files for each environment
- ✅ **Fork Mode**: Single instance per environment (no clustering issues)
- ✅ **Auto-restart**: Keeps apps running even after crashes

#### For GitHub Access (Optional - only if you need git operations):
- `GIT_TOKEN`: Your GitHub personal access token
- `GIT_USERNAME`: Your GitHub username

**Note**: These are optional since the workflow now copies built files instead of using git pull.

### Optional Configuration Secrets

- `POST_BUILD_SCRIPT`: Path to a script to run after deployment (e.g., `./scripts/thank_clients.sh`)

## Environment File Configuration

The workflow now uses GitHub secrets to store complete environment files instead of downloading them from servers:

### Setting Up Environment Files as Secrets

1. **For Production Environment (`PROD_ENV_FILE`)**:
   - Go to Repository Settings → Secrets and variables → Actions
   - Click "New repository secret"
   - Name: `PROD_ENV_FILE`
   - Value: Paste your complete production `.env` file content
   - Example content:
     ```
     NODE_ENV=production
     NEXTAUTH_URL=https://your-production-domain.com
     NEXTAUTH_SECRET=your-production-secret-key
     DATABASE_URL=your-production-database-url
     # Add all your production environment variables
     ```

2. **For Development Environment (`DEV_ENV_FILE`)**:
   - Create another secret named `DEV_ENV_FILE`
   - Value: Paste your complete development `.env` file content
   - Example content:
     ```
     NODE_ENV=development
     NEXTAUTH_URL=https://your-dev-domain.com
     NEXTAUTH_SECRET=your-development-secret-key
     DATABASE_URL=your-development-database-url
     # Add all your development environment variables
     ```

### Required Environment Variables

The workflow validates that these critical variables are present in your environment files:
- `NODE_ENV`: Application environment (automatically set to 'production' during build)
- `NEXTAUTH_URL`: Your application's URL for authentication
- `NEXTAUTH_SECRET`: Secret key for NextAuth.js

**Important**: If any required environment variable is missing, the workflow will fail immediately with no fallbacks.

## Setting Up SSH Password Authentication

### 1. Enable Password Authentication on Your Servers
Make sure password authentication is enabled in your SSH configuration:

```bash
# On your server, edit SSH config
sudo nano /etc/ssh/sshd_config
```

Ensure these settings are enabled:
```
PasswordAuthentication yes
PubkeyAuthentication yes  # Keep this for other uses
```

Restart SSH service:
```bash
sudo systemctl restart ssh
```

### 2. Add Passwords to GitHub Secrets
- `PROD_PASSWORD`: Your production server user password
- `DEV_PASSWORD`: Your development server user password

**Security Note**: While password authentication is simpler to set up, SSH keys are more secure. Consider using SSH keys for production environments.

## GitHub Personal Access Token

### 1. Create Token
- Go to GitHub Settings → Developer settings → Personal access tokens → Tokens (classic)
- Click "Generate new token (classic)"
- Select scopes: `repo` (full control of private repositories)
- Copy the generated token

### 2. Add to Secrets (Optional)
- `GIT_TOKEN`: Paste the token
- `GIT_USERNAME`: Your GitHub username (e.g., "mharsikarim")

**Note**: These secrets are now optional since the workflow copies pre-built files instead of using git operations on the server.

## How to Use

### 1. Manual Deployment
1. Go to your repository on GitHub
2. Click on "Actions" tab
3. Select "Build and Deploy" workflow
4. Click "Run workflow"
5. Choose your deployment environment:
   - **Environment**: Choose production or development

**That's it!** All deployment paths, PM2 settings, and environment file names are now configured via GitHub secrets.

### 2. Monitor Progress
- Watch the workflow progress in real-time
- Each step shows detailed logs
- Build must succeed before deployment starts
- Failed deployments won't affect your servers

## ⚠️ Important Safety Notes

**Double-Check Configuration Before Running:**
- **Verify deployment paths** - Wrong paths could overwrite other applications
- **Confirm PM2 process IDs** - Wrong IDs could restart unrelated services
- **Test with development first** - Always test new configurations on development environment
- **No defaults provided** - All paths and IDs must be explicitly specified to prevent accidents

**Best Practices:**
1. **Start with development environment** to test configuration
2. **Use descriptive PM2 process names** instead of numbers when possible
3. **Document your configurations** for each project
4. **Create separate workflows** for different projects to avoid confusion

## Workflow Steps

### Build Job
1. Checkout code from repository
2. Setup Node.js environment
3. Create environment file from GitHub secrets (`PROD_ENV_FILE` or `DEV_ENV_FILE`)
4. Validate required environment variables are present
5. Install dependencies with `npm ci --legacy-peer-deps`
6. Load and validate environment variables (strict validation, no fallbacks)
7. Build project with `npm run build` using environment variables
8. Create deployment artifact (includes built files and environment file)
9. Upload artifact for deployment jobs

### Production Deployment
1. Validate all required production secrets are present
2. Download build artifact
3. Copy built files to production server
4. Create backup of current deployment
5. Extract new build files (includes environment file)
6. Set proper permissions
7. Clean up temporary files
8. Configure PM2 environment variables from GitHub secrets
9. Restart PM2 process using ecosystem configuration

### Development Deployment
1. Validate all required development secrets are present
2. Download build artifact
3. Copy built files to development server
4. Create backup of current deployment
5. Extract new build files (includes environment file)
6. Set proper permissions
7. Clean up temporary files
8. Configure PM2 environment variables from GitHub secrets
9. Restart PM2 process using ecosystem configuration

## Advantages Over Manual Scripts

1. **Build Validation**: Ensures code builds successfully before deployment
2. **Clean Environment**: Each build starts fresh, avoiding local environment issues
3. **Parallel Deployment**: Can deploy to multiple servers simultaneously
4. **Audit Trail**: Complete log of all deployments with timestamps
5. **Rollback Capability**: Easy to see what was deployed when
6. **No Local Dependencies**: No need for local build environment
7. **Security**: SSH keys stored securely in GitHub secrets
8. **Faster Deployments**: No building on servers, just file copy and restart
9. **Automatic Backups**: Previous deployments are backed up with timestamps
10. **Consistent Builds**: Same build deployed to all environments

## Troubleshooting

### Common Issues:

1. **SSH Connection Failed**
   - Verify server IP addresses in secrets
   - Check that password authentication is enabled on the server
   - Ensure the username and password are correct

2. **Permission Denied**
   - Verify SSH username and password are correct
   - Check that user has sudo privileges on the server
   - Ensure password authentication is enabled in `/etc/ssh/sshd_config`

3. **Build Failed**
   - Check the build logs in GitHub Actions
   - Ensure all dependencies are properly listed in package.json
   - Verify Node.js version compatibility
   - Verify that `PROD_ENV_FILE` or `DEV_ENV_FILE` secrets are properly set
   - Check that all required environment variables are present in the environment file

4. **Environment File Issues**
   - Ensure `PROD_ENV_FILE` and `DEV_ENV_FILE` secrets contain complete environment file content
   - Verify that required variables (`NODE_ENV`, `NEXTAUTH_URL`, `NEXTAUTH_SECRET`) are present
   - Check for syntax errors in environment file content (no spaces around `=`, proper line endings)

5. **PM2 Restart Failed**
   - Verify PM2 app names in `PROD_APP_NAME` and `DEV_APP_NAME` secrets
   - Check that PM2 is installed and running on the servers
   - Ensure ecosystem.config.js file is properly configured

## Reusing for Other Next.js Projects

This workflow is now fully configurable and can be used for any Next.js project! Simply:

1. **Copy the workflow file** (`.github/workflows/deploy.yml`) to your other repository
2. **Set up the required secrets** for the new project's servers
3. **Configure the deployment parameters** when running the workflow:
   - Adjust deployment paths for your project structure
   - Set the correct PM2 process IDs
   - Modify PM2 options as needed

### Example GitHub Secrets Configuration:

**AMQ Partners (Current Project):**
```
PROD_DEPLOY_PATH=/var/www/amq_partners
DEV_DEPLOY_PATH=/var/www/amq_partners_dev/amq_partners
PROD_APP_NAME=amq-partners-prod
DEV_APP_NAME=amq-partners-dev
PROD_PORT=5000
DEV_PORT=5001
PROD_ENV_FILE=<complete production .env file content>
DEV_ENV_FILE=<complete development .env file content>
POST_BUILD_SCRIPT=./scripts/thank_clients.sh
```

**Simple Single Server Setup:**
```
PROD_DEPLOY_PATH=/var/www/my-nextjs-app
DEV_DEPLOY_PATH=/var/www/my-nextjs-app-dev
PROD_APP_NAME=my-app-prod
DEV_APP_NAME=my-app-dev
PROD_PORT=3000
DEV_PORT=3001
PROD_ENV_FILE=<complete production .env file content>
DEV_ENV_FILE=<complete development .env file content>
```

**E-commerce Project Example:**
```
PROD_DEPLOY_PATH=/var/www/ecommerce-frontend
DEV_DEPLOY_PATH=/var/www/ecommerce-staging
PROD_APP_NAME=ecommerce-prod
DEV_APP_NAME=ecommerce-staging
PROD_PORT=4000
DEV_PORT=4001
PROD_MEMORY_LIMIT=2G
DEV_MEMORY_LIMIT=1G
PROD_NODE_MEMORY=4096
DEV_NODE_MEMORY=2048
PROD_ENV_FILE=<complete production .env file content>
DEV_ENV_FILE=<complete development .env file content>
```

**Custom Directory Structure:**
- Production path: `/home/<USER>/apps/frontend`
- Development path: `/home/<USER>/apps/frontend-staging`

## Migration from Manual Scripts

Your old deployment scripts (`deploy.sh`) can be kept as backup, but the GitHub Actions workflow will handle all deployments going forward.

The workflow improves upon your manual scripts by:
- Building in a clean environment instead of on the server
- Creating automatic backups before deployment
- Providing configurable paths and PM2 settings
- Supporting parallel deployment to multiple environments
- **Centralized environment management**: Environment files are stored as GitHub secrets
- **Strict validation**: No fallbacks - workflow fails if required variables are missing
- **Secure**: Environment files never exist on servers, only in GitHub secrets
- **Consistent**: Same environment file used for build and runtime
