name: Build and Deploy

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - development

env:
  # Environment variables will be set from GitHub secrets
  NODE_ENV: production
  NEXT_TELEMETRY_DISABLED: 1

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Create environment file from GitHub secrets
      run: |
        if [ "${{ github.event.inputs.environment }}" == "production" ]; then
          if [ -z "$PROD_ENV_CONTENT" ]; then
            echo "❌ Error: PROD_ENV_FILE secret is not set"
            exit 1
          fi
          echo "$PROD_ENV_CONTENT" > .env
          echo "✅ Production environment file created from PROD_ENV_FILE secret"
        elif [ "${{ github.event.inputs.environment }}" == "development" ]; then
          if [ -z "$DEV_ENV_CONTENT" ]; then
            echo "❌ Error: DEV_ENV_FILE secret is not set"
            exit 1
          fi
          echo "$DEV_ENV_CONTENT" > .env
          echo "✅ Development environment file created from DEV_ENV_FILE secret"
        else
          echo "❌ Error: Invalid environment specified"
          exit 1
        fi
      env:
        PROD_ENV_CONTENT: ${{ secrets.PROD_ENV_FILE }}
        DEV_ENV_CONTENT: ${{ secrets.DEV_ENV_FILE }}

    - name: Verify environment file and required variables
      run: |
        if [ ! -f ".env" ]; then
          echo "❌ Error: Environment file .env not found"
          exit 1
        fi

        echo "✅ Environment file .env found"
        echo "File size: $(stat -c%s ".env") bytes"

        # Check for required environment variables
        REQUIRED_VARS=("NODE_ENV" "NEXTAUTH_URL" "NEXTAUTH_SECRET")
        MISSING_VARS=()

        for var in "${REQUIRED_VARS[@]}"; do
          if ! grep -q "^${var}=" .env; then
            MISSING_VARS+=("$var")
          fi
        done

        if [ ${#MISSING_VARS[@]} -ne 0 ]; then
          echo "❌ Error: Missing required environment variables:"
          printf '%s\n' "${MISSING_VARS[@]}"
          echo "Available variables in .env:"
          grep -E '^[A-Z_]+=.*' .env | cut -d'=' -f1 | sort
          exit 1
        fi

        echo "✅ All required environment variables are present"
    

    - name: Install dependencies
      run: npm ci --legacy-peer-deps

    - name: Clear Next.js cache
      run: rm -rf .next

    - name: Load and validate environment variables
      run: |
        # Parse .env file safely without sourcing (to handle special characters)
        echo "Loading environment variables from .env file..."

        # Process each line in .env file
        while IFS= read -r line || [ -n "$line" ]; do
          # Skip empty lines and comments
          if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
          fi

          # Check if line contains = (key=value format)
          if [[ "$line" =~ ^[^=]+= ]]; then
            # Extract key and value
            key=$(echo "$line" | cut -d'=' -f1)
            value=$(echo "$line" | cut -d'=' -f2-)

            # Export the variable
            export "$key"="$value"

            # Add to GITHUB_ENV for subsequent steps (properly escaped)
            echo "$key=$value" >> $GITHUB_ENV

            echo "✅ Loaded: $key"
          else
            echo "⚠️  Skipping invalid line: $line"
          fi
        done < .env

        # Validate critical environment variables are set
        if [ -z "$NEXTAUTH_URL" ]; then
          echo "❌ Error: NEXTAUTH_URL is not set in environment file"
          exit 1
        fi

        if [ -z "$NEXTAUTH_SECRET" ]; then
          echo "❌ Error: NEXTAUTH_SECRET is not set in environment file"
          exit 1
        fi

        # Override NODE_ENV to production for build (regardless of deployment environment)
        export NODE_ENV="production"
        echo "NODE_ENV=production" >> $GITHUB_ENV

        # Set default translation paths if not specified
        export EN_PATH="${EN_PATH:-./src/locales/en.json}"
        export FR_PATH="${FR_PATH:-./src/locales/fr.json}"
        echo "EN_PATH=${EN_PATH}" >> $GITHUB_ENV
        echo "FR_PATH=${FR_PATH}" >> $GITHUB_ENV

        echo "✅ Environment variables validated and loaded"
        echo "NODE_ENV=$NODE_ENV"
        echo "NEXTAUTH_URL=$NEXTAUTH_URL"
        echo "EN_PATH=$EN_PATH"
        echo "FR_PATH=$FR_PATH"

    - name: Build project
      run: |
        echo "Building with NODE_ENV=$NODE_ENV"
        echo "NEXTAUTH_URL=$NEXTAUTH_URL"
        NODE_OPTIONS="--max-old-space-size=4096" npm run build

    - name: Create deployment artifact
      run: |
        # Include the .env file in the deployment artifact
        tar --exclude=node_modules/.cache \
          -czf deployment.tar.gz \
          .next \
          public \
          package.json \
          package-lock.json \
          next.config.js \
          ecosystem.config.js \
          scripts \
          node_modules \
          .env

    - name: Upload build artifact
      uses: actions/upload-artifact@v4
      with:
        name: deployment-artifact
        path: deployment.tar.gz
        retention-days: 1

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.environment == 'production' }}
    environment: production

    steps:
    - name: Validate required secrets
      run: |
        REQUIRED_SECRETS=("PROD_HOST" "PROD_USERNAME" "PROD_PASSWORD" "PROD_DEPLOY_PATH" "PROD_APP_NAME" "PROD_PORT")
        MISSING_SECRETS=()

        for secret in "${REQUIRED_SECRETS[@]}"; do
          case $secret in
            "PROD_HOST") [ -z "${{ secrets.PROD_HOST }}" ] && MISSING_SECRETS+=("$secret") ;;
            "PROD_USERNAME") [ -z "${{ secrets.PROD_USERNAME }}" ] && MISSING_SECRETS+=("$secret") ;;
            "PROD_PASSWORD") [ -z "${{ secrets.PROD_PASSWORD }}" ] && MISSING_SECRETS+=("$secret") ;;
            "PROD_DEPLOY_PATH") [ -z "${{ secrets.PROD_DEPLOY_PATH }}" ] && MISSING_SECRETS+=("$secret") ;;
            "PROD_APP_NAME") [ -z "${{ secrets.PROD_APP_NAME }}" ] && MISSING_SECRETS+=("$secret") ;;
            "PROD_PORT") [ -z "${{ secrets.PROD_PORT }}" ] && MISSING_SECRETS+=("$secret") ;;
          esac
        done

        if [ ${#MISSING_SECRETS[@]} -ne 0 ]; then
          echo "❌ Error: Missing required GitHub secrets for production deployment:"
          printf '%s\n' "${MISSING_SECRETS[@]}"
          exit 1
        fi

        echo "✅ All required production secrets are present"

    - name: Download build artifact
      uses: actions/download-artifact@v4
      with:
        name: deployment-artifact

    - name: Copy deployment artifact to server
      uses: appleboy/scp-action@v1
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USERNAME }}
        password: ${{ secrets.PROD_PASSWORD }}
        port: 22
        source: "deployment.tar.gz"
        target: "/tmp/"

    - name: Deploy to Production Server
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USERNAME }}
        password: ${{ secrets.PROD_PASSWORD }}
        port: 22
        script: |
          cd ${{ secrets.PROD_DEPLOY_PATH }}

          # Create backup of current deployment
          sudo cp -r .next .next.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

          # Extract the new build
          sudo tar -xzf /tmp/deployment.tar.gz -C ${{ secrets.PROD_DEPLOY_PATH }} --overwrite

          # Set proper permissions
          sudo chown -R www-data:www-data ${{ secrets.PROD_DEPLOY_PATH }}
          sudo chmod 755 ./scripts/thank_clients.sh 2>/dev/null || true

          # Clean up
          rm -f /tmp/deployment.tar.gz

          echo "Deployment completed for production"

          # Create logs directory if it doesn't exist
          sudo mkdir -p logs

          # Set environment variables for PM2 ecosystem (using strict values from secrets)
          export PROD_APP_NAME="${{ secrets.PROD_APP_NAME }}"
          export DEV_APP_NAME="${{ secrets.DEV_APP_NAME || 'dev-app' }}"
          export PROD_DEPLOY_PATH="${{ secrets.PROD_DEPLOY_PATH }}"
          export DEV_DEPLOY_PATH="${{ secrets.DEV_DEPLOY_PATH || '/tmp/dev' }}"
          export ENV_FILE_NAME=".env"
          export PROD_PORT="${{ secrets.PROD_PORT }}"
          export DEV_PORT="${{ secrets.DEV_PORT || '3001' }}"
          export PROD_MEMORY_LIMIT="${{ secrets.PROD_MEMORY_LIMIT || '1G' }}"
          export DEV_MEMORY_LIMIT="${{ secrets.DEV_MEMORY_LIMIT || '1G' }}"
          export PROD_NODE_MEMORY="${{ secrets.PROD_NODE_MEMORY || '2048' }}"
          export DEV_NODE_MEMORY="${{ secrets.DEV_NODE_MEMORY || '2048' }}"

          # Start or restart PM2 process using ecosystem file
          if pm2 describe "${{ secrets.PROD_APP_NAME }}" > /dev/null 2>&1; then
            pm2 restart "${{ secrets.PROD_APP_NAME }}" --update-env
          else
            pm2 start ecosystem.config.js --only "${{ secrets.PROD_APP_NAME }}"
          fi

  deploy-development:
    needs: build
    runs-on: ubuntu-latest
    if: ${{ github.event.inputs.environment == 'development' }}
    environment: development

    steps:
    - name: Validate required secrets
      run: |
        REQUIRED_SECRETS=("DEV_HOST" "DEV_USERNAME" "DEV_PASSWORD" "DEV_DEPLOY_PATH" "DEV_APP_NAME" "DEV_PORT")
        MISSING_SECRETS=()

        for secret in "${REQUIRED_SECRETS[@]}"; do
          case $secret in
            "DEV_HOST") [ -z "${{ secrets.DEV_HOST }}" ] && MISSING_SECRETS+=("$secret") ;;
            "DEV_USERNAME") [ -z "${{ secrets.DEV_USERNAME }}" ] && MISSING_SECRETS+=("$secret") ;;
            "DEV_PASSWORD") [ -z "${{ secrets.DEV_PASSWORD }}" ] && MISSING_SECRETS+=("$secret") ;;
            "DEV_DEPLOY_PATH") [ -z "${{ secrets.DEV_DEPLOY_PATH }}" ] && MISSING_SECRETS+=("$secret") ;;
            "DEV_APP_NAME") [ -z "${{ secrets.DEV_APP_NAME }}" ] && MISSING_SECRETS+=("$secret") ;;
            "DEV_PORT") [ -z "${{ secrets.DEV_PORT }}" ] && MISSING_SECRETS+=("$secret") ;;
          esac
        done

        if [ ${#MISSING_SECRETS[@]} -ne 0 ]; then
          echo "❌ Error: Missing required GitHub secrets for development deployment:"
          printf '%s\n' "${MISSING_SECRETS[@]}"
          exit 1
        fi

        echo "✅ All required development secrets are present"

    - name: Download build artifact
      uses: actions/download-artifact@v4
      with:
        name: deployment-artifact

    - name: Copy deployment artifact to server
      uses: appleboy/scp-action@v1
      with:
        host: ${{ secrets.DEV_HOST }}
        username: ${{ secrets.DEV_USERNAME }}
        password: ${{ secrets.DEV_PASSWORD }}
        port: 22
        source: "deployment.tar.gz"
        target: "/tmp/"

    - name: Deploy to Development Server
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.DEV_HOST }}
        username: ${{ secrets.DEV_USERNAME }}
        password: ${{ secrets.DEV_PASSWORD }}
        port: 22
        script: |
          cd ${{ secrets.DEV_DEPLOY_PATH }}

          # Create backup of current deployment
          sudo cp -r .next .next.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

          # Extract the new build
          sudo tar -xzf /tmp/deployment.tar.gz -C ${{ secrets.DEV_DEPLOY_PATH }} --overwrite

          # Set proper permissions
          sudo chown -R www-data:www-data ${{ secrets.DEV_DEPLOY_PATH }}
          sudo chmod 755 ./scripts/thank_clients.sh 2>/dev/null || true

          # Clean up
          rm -f /tmp/deployment.tar.gz

          echo "Deployment completed for development"

          # Create logs directory if it doesn't exist
          sudo mkdir -p logs

          # Set environment variables for PM2 ecosystem (using strict values from secrets)
          export PROD_APP_NAME="${{ secrets.PROD_APP_NAME || 'prod-app' }}"
          export DEV_APP_NAME="${{ secrets.DEV_APP_NAME }}"
          export PROD_DEPLOY_PATH="${{ secrets.PROD_DEPLOY_PATH || '/tmp/prod' }}"
          export DEV_DEPLOY_PATH="${{ secrets.DEV_DEPLOY_PATH }}"
          export ENV_FILE_NAME=".env"
          export PROD_PORT="${{ secrets.PROD_PORT || '3000' }}"
          export DEV_PORT="${{ secrets.DEV_PORT }}"
          export PROD_MEMORY_LIMIT="${{ secrets.PROD_MEMORY_LIMIT || '1G' }}"
          export DEV_MEMORY_LIMIT="${{ secrets.DEV_MEMORY_LIMIT || '1G' }}"
          export PROD_NODE_MEMORY="${{ secrets.PROD_NODE_MEMORY || '2048' }}"
          export DEV_NODE_MEMORY="${{ secrets.DEV_NODE_MEMORY || '2048' }}"

          # Start or restart PM2 process using ecosystem file
          if pm2 describe "${{ secrets.DEV_APP_NAME }}" > /dev/null 2>&1; then
            pm2 restart "${{ secrets.DEV_APP_NAME }}" --update-env
          else
            pm2 start ecosystem.config.js --only "${{ secrets.DEV_APP_NAME }}"
          fi
