'use client';

import { But<PERSON> } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/lib/contexts/language-context';
import { ShieldAlert, Home } from 'lucide-react';

export default function AccessDeniedPage() {
  const router = useRouter();
  const { t } = useLanguage();

  return (
    <div className="flex flex-col items-center justify-center min-h-[80vh] text-center px-4">
      <ShieldAlert className="h-16 w-16 text-red-500 mb-4" />
      <h1 className="text-3xl font-bold mb-2">{t('common.accessDenied')}</h1>
      <p className="text-muted-foreground mb-6">
        {t('common.accessDeniedMessage')}
      </p>
      <div className="flex gap-4">
        <Button onClick={() => router.back()} variant="outline">
          {t('common.goBack')}
        </Button>
        <Button onClick={() => router.push('/')} className="flex items-center gap-2">
          <Home className="h-4 w-4" />
          {t('common.returnHome')}
        </Button>
      </div>
    </div>
  );
} 