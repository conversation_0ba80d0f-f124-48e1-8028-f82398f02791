'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Textarea } from '@/components/ui/textarea';

export default function TestUpdateApi() {
  const { toast } = useToast();
  const [branchId, setBranchId] = useState('67ca21362c8193f30cc7e414');
  const [date, setDate] = useState('2025-04-02');
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);
  const [isError, setIsError] = useState(false);
  const [rawResponse, setRawResponse] = useState('');
  const [collectionName, setCollectionName] = useState('appointments');

  const handleDirectUpdateApi = async () => {
    setIsLoading(true);
    setIsError(false);
    setRawResponse('');
    
    try {
      // Call the update-slots API directly with minimal slots
      const payload = {
        branchId,
        date,
        slots: [
          {
            startHour: "10:00",
            endHour: "11:00",
            enabled: true,
            capacity: 10,
            online: 5,
            home: 3,
            max_capacity_family: 4
          }
        ]
      };
      
      console.log('Sending payload to update-slots API:', payload);

      const res = await fetch('/api/appointments/update-slots', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      // Store raw response
      const responseText = await res.text();
      setRawResponse(responseText);

      // Parse JSON if possible
      let data;
      try {
        data = JSON.parse(responseText);
        setResponse(data);
      } catch (e) {
        console.error('Error parsing JSON response:', e);
        setIsError(true);
        setResponse(null);
      }

      if (res.ok) {
        toast({
          title: 'Success',
          description: 'API call succeeded!',
        });
      } else {
        setIsError(true);
        toast({
          title: 'Error',
          description: data?.error || 'API call failed',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error calling update-slots API:', error);
      setIsError(true);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Show which collection the API is using by checking the source code
  const checkApiCollection = async () => {
    try {
      const res = await fetch('/api/appointments/update-slots');
      // We don't expect this to work (it's a POST endpoint)
      // but we're just checking which collection is used
      console.log('API collection check response:', res);
    } catch (error) {
      console.error('Error checking API collection:', error);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">Test Update Slots API</h1>
      
      <div className="p-4 mb-6 bg-yellow-50 border border-yellow-200 rounded-md">
        <p className="text-sm text-yellow-800">
          <strong>Note:</strong> This API should be using the <code>{collectionName}</code> collection.
          If it's using a different collection, the API needs to be fixed.
        </p>
      </div>
      
      <div className="space-y-4 mb-6 p-4 border rounded-md">
        <div className="space-y-2">
          <Label htmlFor="branchId">Branch ID</Label>
          <Input
            id="branchId"
            value={branchId}
            onChange={(e) => setBranchId(e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="date">Date (YYYY-MM-DD)</Label>
          <Input
            id="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="collection">Collection Name (for reference only)</Label>
          <Input
            id="collection"
            value={collectionName}
            onChange={(e) => setCollectionName(e.target.value)}
            disabled
          />
        </div>
      </div>
      
      <div className="flex space-x-4 mb-6">
        <Button 
          onClick={handleDirectUpdateApi} 
          disabled={isLoading}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {isLoading ? 'Loading...' : 'Test Update Slots API'}
        </Button>
        
        <Button 
          onClick={() => window.location.href = '/appointments'}
          variant="outline"
        >
          Go to Appointments Page
        </Button>
      </div>
      
      {rawResponse && (
        <div className={`p-4 border rounded-md ${isError ? 'bg-red-50 border-red-200' : 'bg-gray-50'}`}>
          <h2 className="text-lg font-semibold mb-2">Raw Response:</h2>
          <Textarea 
            className="font-mono text-xs w-full h-32"
            readOnly
            value={rawResponse}
          />
        </div>
      )}
      
      {response && (
        <div className="p-4 border rounded-md bg-gray-50 mt-4">
          <h2 className="text-lg font-semibold mb-2">Parsed Response:</h2>
          <pre className="whitespace-pre-wrap text-sm bg-white p-4 rounded border">
            {JSON.stringify(response, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
} 