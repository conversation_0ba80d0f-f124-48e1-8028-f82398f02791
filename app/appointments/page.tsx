'use client';

import { useState, useEffect, useCallback } from 'react';
import { format, addWeeks, subWeeks, startOfWeek, endOfWeek } from 'date-fns';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useSession } from 'next-auth/react';
import { ChevronLeft, ChevronRight, Loader2, PanelLeftClose, PanelLeftOpen, Plus } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { WeekCalendar } from './components/week-calendar';
import { BulkCreateDialog } from './components/bulk-create-dialog';
import { useLanguage } from '@/lib/contexts/language-context';
import { usePermissions } from '@/hooks/use-permissions';
import { useAppSelector } from '@/lib/redux/hooks';
import { canUserAccessAppointments } from '@/lib/utils/permissions-utils';

interface Branch {
  _id: string;
  name: string;
}

interface Appointment {
  _id: string;
  branchId: string;
  date: string;
  startHour: string;
  endHour: string;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
  disponibilitiesCount: number;
}

export default function AppointmentsPage() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const { t } = useLanguage();
  const { isLoading: permissionsLoading, error: permissionsError } = usePermissions();
  const permissions = useAppSelector((state: any) => state.permissions);

  // All hooks must be called before any return
  const [currentDate, setCurrentDate] = useState(new Date());
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoadingBranches, setIsLoadingBranches] = useState(true);
  const [branchId, setBranchId] = useState('');
  const [appointments, setAppointments] = useState<{ appointments: Appointment[] }>({ appointments: [] });
  const [isLoading, setIsLoading] = useState(false);
  const [isSidebarVisible, setIsSidebarVisible] = useState(true);
  const [isBulkCreateDialogOpen, setIsBulkCreateDialogOpen] = useState(false);

  // All useCallback, useEffect, etc. hooks here
  const fetchBranches = useCallback(async () => {
    try {
      setIsLoadingBranches(true);
      const response = await fetch('/api/branches');
      if (!response.ok) throw new Error(`Failed to fetch branches: ${response.status}`);
      const data = await response.json();
      setBranches(data);
      if (data.length > 0 && !branchId) {
        setBranchId(data[0]._id);
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
      toast({
        title: 'Error',
        description: 'Failed to load branches. Please refresh the page.',
        variant: 'destructive',
      });
      setBranches([]);
    } finally {
      setIsLoadingBranches(false);
    }
  }, [toast, branchId]);

  useEffect(() => {
    fetchBranches();
  }, [fetchBranches]);

  const fetchAppointments = useCallback(async () => {
    if (!branchId) {
      setAppointments({ appointments: [] });
      return;
    }
    setIsLoading(true);
    try {
      const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 });
      const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 });
      const startDate = format(weekStart, 'yyyy-MM-dd');
      const endDate = format(weekEnd, 'yyyy-MM-dd');
      console.log(`Fetching appointments for date range: ${startDate} to ${endDate}`);
      const url = new URL('/api/calendar-appointments', window.location.origin);
      url.searchParams.append('branchId', branchId);
      url.searchParams.append('startDate', startDate);
      url.searchParams.append('endDate', endDate);
      const response = await fetch(url.toString());
      if (!response.ok) {
        throw new Error('Failed to fetch appointments');
      }
      const data = await response.json();
      setAppointments({ appointments: data?.appointments || [] });
    } catch (error) {
      console.error('Error fetching appointments:', error);
      toast({
        title: 'Error',
        description: 'Failed to load appointments. Please try again.',
        variant: 'destructive',
      });
      setAppointments({ appointments: [] });
    } finally {
      setIsLoading(false);
    }
  }, [branchId, currentDate, toast]);

  useEffect(() => {
    fetchAppointments();
  }, [currentDate, branchId]);

  // Permission check (can be after hooks)
  const hasAppointmentsAccess = canUserAccessAppointments(permissions);

  // Only now do conditional returns
  if (permissionsLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        <span className="ml-4 text-lg">{t('common.loading')}</span>
      </div>
    );
  }

  if (!hasAppointmentsAccess) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">{t('accessDenied.title') || 'Accès refusé'}</h2>
          <p className="text-muted-foreground">{t('accessDenied.description') || 'Vous n\'avez pas la permission d\'accéder à cette page.'}</p>
        </div>
      </div>
    );
  }

  // Navigation handlers
  const goToPrevious = () => {
    setCurrentDate(prev => {
      const newDate = subWeeks(prev, 1);
      return newDate;
    });
    // Fetch will be triggered by the useEffect that watches currentDate
  };

  const goToNext = () => {
    setCurrentDate(prev => {
      const newDate = addWeeks(prev, 1);
      return newDate;
    });
    // Fetch will be triggered by the useEffect that watches currentDate
  };

  return (
    <div className="flex h-full max-w-full overflow-hidden">
      {/* Branches Sidebar with visibility control */}
      <div className={cn(
        "flex-shrink-0 border-r bg-white transition-all duration-300",
        isSidebarVisible ? "w-24 xs:w-32 sm:w-40 lg:w-48" : "w-0 overflow-hidden"
      )}>
        <div className="p-2 border-b">
          <h2 className="text-xs sm:text-sm font-medium truncate">{t('branches.title')}</h2>
        </div>
        <div className="overflow-auto max-h-[calc(100vh-10rem)]">
          {isLoadingBranches ? (
            <div className="p-2 space-y-2">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-8 w-full" />
              ))}
            </div>
          ) : (
            <div className="divide-y">
              {branches.map((branch: Branch) => (
                <div
                  key={branch._id}
                  className={cn(
                    "p-1.5 sm:p-2 cursor-pointer hover:bg-muted/50 transition-colors text-[10px] xs:text-xs sm:text-sm",
                    branchId === branch._id && "bg-muted border-l-2 border-primary"
                  )}
                  onClick={() => setBranchId(branch._id)}
                >
                  <div className="font-medium truncate">{branch.name}</div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 min-w-0 overflow-hidden flex flex-col">
        <div className="p-2 sm:p-3 md:p-4 lg:p-5 overflow-hidden flex-1 w-full">
          <div className="flex flex-col space-y-3 md:flex-row md:space-y-0 md:justify-between md:items-center mb-3 md:mb-4">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsSidebarVisible(!isSidebarVisible)}
                className="h-7 w-7 md:h-8 md:w-8 flex-shrink-0"
              >
                {isSidebarVisible ? (
                  <PanelLeftClose className="h-3.5 w-3.5 md:h-4 md:w-4" />
                ) : (
                  <PanelLeftOpen className="h-3.5 w-3.5 md:h-4 md:w-4" />
                )}
                <span className="sr-only">
                  {isSidebarVisible ? t('common.hide') : t('common.show')} {t('branches.title').toLowerCase()}
                </span>
              </Button>
              <h1 className="text-lg md:text-xl lg:text-2xl font-bold truncate">{t('sidebar.appointments')}</h1>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsBulkCreateDialogOpen(true)}
                className="ml-2 h-7 sm:h-8 text-xs sm:text-sm"
              >
                <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                Création en lot
              </Button>
            </div>
            
            <div className="flex items-center gap-1.5 md:gap-3">
              <div className="text-[10px] xs:text-xs md:text-sm font-medium text-muted-foreground hidden md:block whitespace-nowrap">
                {format(startOfWeek(currentDate, { weekStartsOn: 1 }), 'dd MMM')} - {format(endOfWeek(currentDate, { weekStartsOn: 1 }), 'dd MMM yyyy')}
              </div>
              <div className="flex items-center gap-1 sm:gap-1.5">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={goToPrevious}
                  disabled={isLoading}
                  className="text-[10px] xs:text-xs sm:text-sm px-1 xs:px-1.5 sm:px-2 h-7 sm:h-8"
                >
                  {isLoading ? (
                    <Loader2 className="h-2.5 w-2.5 sm:h-3 sm:w-3 md:h-3.5 md:w-3.5 mr-0.5 md:mr-1 animate-spin" />
                  ) : (
                    <ChevronLeft className="h-2.5 w-2.5 sm:h-3 sm:w-3 md:h-3.5 md:w-3.5 mr-0.5 md:mr-1" />
                  )}
                  <span className="truncate">{t('appointmentsDisponibilities.previousWeek')}</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={goToNext}
                  disabled={isLoading}
                  className="text-[10px] xs:text-xs sm:text-sm px-1 xs:px-1.5 sm:px-2 h-7 sm:h-8"
                >
                  <span className="truncate">{t('appointmentsDisponibilities.nextWeek')}</span>
                  {isLoading ? (
                    <Loader2 className="h-2.5 w-2.5 sm:h-3 sm:w-3 md:h-3.5 md:w-3.5 ml-0.5 md:ml-1 animate-spin" />
                  ) : (
                    <ChevronRight className="h-2.5 w-2.5 sm:h-3 sm:w-3 md:h-3.5 md:w-3.5 ml-0.5 md:ml-1" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-8 md:py-12 space-y-4">
              <Loader2 className="h-6 w-6 md:h-8 md:w-8 animate-spin text-primary" />
              <p className="text-xs md:text-sm text-muted-foreground">Loading calendar appointments...</p>
            </div>
          ) : (
            <div className="w-full overflow-hidden">
              <WeekCalendar 
                currentWeek={currentDate}
                branchId={branchId}
                appointments={appointments}
                onSave={async () => {
                  await fetchAppointments();
                  console.log('Appointment calendar refreshed with latest data');
                }}
                isLoading={isLoading}
                isSidebarOpen={isSidebarVisible}
                onToggleSidebar={() => setIsSidebarVisible(!isSidebarVisible)}
              />
            </div>
          )}
        </div>
      </div>

      {/* Bulk Create Dialog */}
      <BulkCreateDialog
        isOpen={isBulkCreateDialogOpen}
        onClose={() => setIsBulkCreateDialogOpen(false)}
        branches={branches}
        onSuccess={fetchAppointments}
      />
    </div>
  );
} 