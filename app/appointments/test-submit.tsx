'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';

export default function TestSubmit() {
  const { toast } = useToast();
  const [branchId, setBranchId] = useState('67ca21362c8193f30cc7e414');
  const [date, setDate] = useState('2025-04-02');
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);

  const handleTestSubmit = async () => {
    setIsLoading(true);
    try {
      const res = await fetch('/api/appointments/test-submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ branchId, date }),
      });

      const data = await res.json();
      setResponse(data);

      if (res.ok) {
        toast({
          title: 'Success',
          description: 'Test slot created successfully',
        });
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to create test slot',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error testing submit:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateSubmit = async () => {
    setIsLoading(true);
    try {
      // Create a test slot for the update-slots API
      const testSlot = {
        startHour: "10:00",
        endHour: "11:00",
        enabled: true,
        capacity: 10,
        online: 4,
        home: 2,
        max_capacity_family: 4
      };

      const res = await fetch('/api/appointments/update-slots', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          branchId,
          date,
          slots: [testSlot]
        }),
      });

      const data = await res.json();
      setResponse(data);

      if (res.ok) {
        toast({
          title: 'Success',
          description: 'Slot updated successfully',
        });
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to update slot',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error testing update-slots:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-xl">
      <h1 className="text-2xl font-bold mb-6">Test Appointment Slot APIs</h1>
      
      <div className="space-y-4 mb-6 p-4 border rounded-md">
        <div className="space-y-2">
          <Label htmlFor="branchId">Branch ID</Label>
          <Input
            id="branchId"
            value={branchId}
            onChange={(e) => setBranchId(e.target.value)}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="date">Date (YYYY-MM-DD)</Label>
          <Input
            id="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
          />
        </div>
      </div>
      
      <div className="flex space-x-4 mb-6">
        <Button 
          onClick={handleTestSubmit} 
          disabled={isLoading}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {isLoading ? 'Loading...' : 'Test Submit API'}
        </Button>
        
        <Button 
          onClick={handleUpdateSubmit} 
          disabled={isLoading}
          className="bg-green-600 hover:bg-green-700"
        >
          {isLoading ? 'Loading...' : 'Test Update Slots API'}
        </Button>
      </div>
      
      {response && (
        <div className="p-4 border rounded-md bg-gray-50">
          <h2 className="text-lg font-semibold mb-2">Response:</h2>
          <pre className="whitespace-pre-wrap text-sm bg-white p-4 rounded border">
            {JSON.stringify(response, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
} 