'use client';

import { useSelectedLayoutSegment } from 'next/navigation';
import { useEffect } from 'react';
import { useSubMenu } from '@/components/dynamic-sub-menu/sub-menu-provider';
import { useLanguage } from '@/lib/contexts/language-context';

export default function AppointmentsLayoutClient({
  children,
}: {
  children: React.ReactNode;
}) {
  const segment = useSelectedLayoutSegment();
  const { setMenuItems } = useSubMenu();
  const { t } = useLanguage();

  useEffect(() => {
    const menuItems = [
      {
        title: t('sidebar.appointments'),
        href: '/appointments',
        isActive: segment === null,
      },
      {
        title: t('sidebar.calendars'),
        href: '/calendars',
        isActive: segment === 'calendars' || segment?.startsWith('calendars'),
      },
      {
        title: t('sidebar.myCalendar'),
        href: '/mycalendar',
        isActive: segment === 'mycalendar' || segment?.startsWith('mycalendar'),
      }
    ];

    setMenuItems(menuItems);

    return () => {
      setMenuItems([]);
    };
  }, [segment, t, setMenuItems]);

  return (
    <div className="space-y-6">
      <div className="flex-1">{children}</div>
    </div>
  );
} 