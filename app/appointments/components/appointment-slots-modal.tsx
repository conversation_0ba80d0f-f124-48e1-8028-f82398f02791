'use client';

import { useState, useRef } from 'react';
import { format } from 'date-fns';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogTrigger } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Loader2, 
  AlertTriangle, 
  Sun, 
  SunDim, 
  Moon,
  Clock, 
  Plus, 
  Save, 
  X, 
  Trash2, 
  Wand2, 
  Users,
  Settings2,
  Zap,
  Globe,
  Home,
  Users2,
  ChevronDown
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useLanguage } from '@/lib/contexts/language-context';
import { Badge } from "@/components/ui/badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface TimeSlot {
  _id?: string;
  startHour: string;
  endHour: string;
  enabled: boolean;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
}

interface AppointmentSlotsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDay: Date | null;
  onSave: () => Promise<void>;
  isSubmitting: boolean;
  isFetchingSlots?: boolean;
  timeSlots: TimeSlot[];
  setTimeSlots: React.Dispatch<React.SetStateAction<TimeSlot[]>>;
  defaultCapacity: number;
  setDefaultCapacity: (value: number) => void;
  defaultMaxFamily: number;
  setDefaultMaxFamily: (value: number) => void;
  slotType: "even" | "odd";
  setSlotType: (type: "even" | "odd") => void;
  evenHourSlots: TimeSlot[];
  oddHourSlots: TimeSlot[];
  defaultOnline: number;
  setDefaultOnline: (value: number) => void;
  defaultHome: number;
  setDefaultHome: (value: number) => void;
  branchId: string;
}

export function AppointmentSlotsModal({
  isOpen,
  onClose,
  selectedDay,
  onSave,
  isSubmitting,
  isFetchingSlots,
  timeSlots,
  setTimeSlots,
  defaultCapacity,
  setDefaultCapacity,
  defaultMaxFamily,
  setDefaultMaxFamily,
  slotType,
  setSlotType,
  evenHourSlots,
  oddHourSlots,
  defaultOnline,
  setDefaultOnline,
  defaultHome,
  setDefaultHome,
  branchId,
}: AppointmentSlotsModalProps) {
  const { toast } = useToast();
  const { t } = useLanguage();
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [slotsToDelete, setSlotsToDelete] = useState<number>(0);
  const [slotsToDeleteDetails, setSlotsToDeleteDetails] = useState<Array<{
    id: string;
    timeRange: string;
    startHour: string;
    endHour: string;
    capacity?: number;
    online?: number;
    home?: number;
    max_capacity_family?: number;
    hasReservations: boolean;
  }>>([]);
  const [slotsWithCapacityBelowReservations, setSlotsWithCapacityBelowReservations] = useState<Array<{
    id: string;
    timeRange: string;
    startHour: string;
    endHour: string;
    oldCapacity: number;
    newCapacity: number;
    reservationCount: number;
  }>>([]);

  const [newSlotStart, setNewSlotStart] = useState<string>('');
  const [newSlotDuration, setNewSlotDuration] = useState<number>(1);
  const [isSubmittingLocal, setIsSubmittingLocal] = useState<boolean>(false);

  const checkForDeletedSlots = async () => {
    try {
      if (!selectedDay) {
        return false;
      }
      const formattedDate = format(selectedDay, 'yyyy-MM-dd');
      const response = await fetch('/api/appointments/check-changes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          branchId,
          date: formattedDate,
          slots: timeSlots
        }),
      });
      if (!response.ok) {
        console.error(t('appointments.errors.failedToCheckDeletedSlots'));
        return false;
      }
      const data = await response.json();
      // Use the new field
      const slotsWithReservations = (data.slotsToBeDeletedDetailsWithReservations || []).filter((slot: any) => slot.hasReservations);
      const slotsWithoutReservations = (data.slotsToBeDeletedDetailsWithReservations || []).filter((slot: any) => !slot.hasReservations);
      setSlotsToDelete(slotsWithReservations.length + slotsWithoutReservations.length);
      setSlotsToDeleteDetails(data.slotsToBeDeletedDetailsWithReservations || []);
      setSlotsWithCapacityBelowReservations(data.slotsWithCapacityBelowReservations || []);
      // If any slot is being deleted and has reservations, block
      if (slotsWithReservations.length > 0) {
        return 'blocked';
      }
      // If there are deletions but none are blocked, allow (show confirmation)
      if (slotsWithoutReservations.length > 0) {
        return 'deletable';
      }
      // If only capacity decrements, allow
      return false;
    } catch (error) {
      console.error(t('appointments.errors.checkingDeletedSlots'));
      return false;
    }
  };

  const handleSubmit = async () => {
    console.log('🔵 [MODAL] handleSubmit called');
    const logMessages = [];
    try {
      if (!selectedDay) {
        throw new Error(t('appointments.errors.noDaySelected'));
      }
      // Check for deletions first to show confirmation if needed
      const deletionStatus = await checkForDeletedSlots();
      if (deletionStatus === 'blocked') {
        console.log('🔵 [MODAL] Showing delete confirmation - blocked');
        setShowDeleteConfirmation(true);
        return; // Block saving if deletions would affect reservations
      }
      if (deletionStatus === 'deletable') {
        console.log('🔵 [MODAL] Showing delete confirmation - deletable');
        setShowDeleteConfirmation(true);
        // Allow save in dialog
        return;
      }
      // If there are no slots to delete, proceed with submitChanges
      console.log('🔵 [MODAL] No deletions, calling submitChanges');
      await submitChanges();
    } catch (error) {
      logMessages.push(`${t('appointments.errors.inHandleSubmit')}: ${error instanceof Error ? error.message : String(error)}`);
      console.error(t('appointments.errors.inHandleSubmit'));
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : t('appointments.errors.occurred'),
        variant: 'destructive',
      });
    } finally {
      if (logMessages.length > 0) {
        console.log(`${t('appointments.logs.handleSubmitResult')}: ${logMessages[logMessages.length - 1]}`);
      }
    }
  };

  const submitChanges = async () => {
    console.log('🟢 [MODAL] submitChanges called');
    setIsSubmittingLocal(true);
    console.log(t('appointments.logs.submittingChanges'));

    try {
      if (!selectedDay) {
        throw new Error(t('appointments.errors.noDaySelected'));
      }

      console.log('🟢 [MODAL] Calling onSave (parent component)');
      // Call onSave to handle the API call and refresh the calendar data
      // The parent component (week-calendar) will handle the actual API call
      await onSave();

      console.log('🟢 [MODAL] onSave completed, closing dialog');
      // Close the dialog
      onClose();
    } catch (error) {
      console.error(t('appointments.errors.updatingSlots'), error);

      // Show error toast - the parent component (week-calendar) will handle the detailed error
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : t('appointments.errors.failedToUpdate'),
        variant: 'destructive',
      });
    } finally {
      setIsSubmittingLocal(false);
    }
  };

  const handleSlotTypeChange = (type: "even" | "odd") => {
    setSlotType(type);
    setTimeSlots(type === "even" ? evenHourSlots : oddHourSlots);
  };

  const handleAddSlot = () => {
    if (!newSlotStart || newSlotDuration <= 0) {
      toast({
        title: t('common.error'),
        description: t('appointments.errors.invalidStartTimeOrDuration'),
        variant: 'destructive',
      });
      return;
    }

    // Validate the time format (HH:MM)
    if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(newSlotStart)) {
      toast({
        title: t('common.error'),
        description: t('appointments.errors.invalidTimeFormat'),
        variant: 'destructive',
      });
      return;
    }

    // Calculate end time
    const [hours, minutes] = newSlotStart.split(':').map(Number);
    const startDate = new Date();
    startDate.setHours(hours, minutes, 0, 0);

    const endDate = new Date(startDate);
    endDate.setHours(endDate.getHours() + newSlotDuration);

    const endHour = `${endDate.getHours().toString().padStart(2, '0')}:${endDate.getMinutes().toString().padStart(2, '0')}`;

    // Block slot creation before 11 AM and after 9 PM
    if (hours < 11) {
      toast({
        title: t('common.error'),
        description: t('appointments.errors.slotBeforeElevenAM'),
        variant: 'destructive',
      });
      return;
    }

    if (endDate.getHours() > 21) {
      toast({
        title: t('common.error'),
        description: t('appointments.errors.slotAfterNinePM'),
        variant: 'destructive',
      });
      return;
    }

    // Check for overlapping slots
    const isOverlapping = timeSlots.some(slot => {
      const slotStart = slot.startHour.split(':').map(Number);
      const slotEnd = slot.endHour.split(':').map(Number);

      const slotStartDate = new Date();
      slotStartDate.setHours(slotStart[0], slotStart[1], 0, 0);

      const slotEndDate = new Date();
      slotEndDate.setHours(slotEnd[0], slotEnd[1], 0, 0);

      return (
        (startDate >= slotStartDate && startDate < slotEndDate) ||
        (endDate > slotStartDate && endDate <= slotEndDate) ||
        (startDate <= slotStartDate && endDate >= slotEndDate)
      );
    });

    if (isOverlapping) {
      toast({
        title: t('common.error'),
        description: t('appointments.errors.overlappingSlot'),
        variant: 'destructive',
      });
      return;
    }

    // Create the new slot
    const newSlot: TimeSlot = {
      startHour: newSlotStart,
      endHour: endHour,
      enabled: true,
      capacity: defaultCapacity,
      online: defaultOnline,
      home: defaultHome,
      max_capacity_family: defaultMaxFamily
    };

    // Add the new slot and sort by start time
    const updatedSlots = [...timeSlots, newSlot].sort((a, b) => {
      const [aHours, aMinutes] = a.startHour.split(':').map(Number);
      const [bHours, bMinutes] = b.startHour.split(':').map(Number);

      if (aHours !== bHours) {
        return aHours - bHours;
      }
      return aMinutes - bMinutes;
    });

    setTimeSlots(updatedSlots);
    setNewSlotStart('');
  };

  const clearAllSlots = () => {
    setTimeSlots([]);
  };

  const removeSlot = (index: number) => {
    const updatedSlots = timeSlots.filter((_, i) => i !== index);
    setTimeSlots(updatedSlots);
  };

  const updateSlot = (index: number, field: keyof TimeSlot, value: any) => {
    const updatedSlots = timeSlots.map((slot, i) => {
      if (i === index) {
        return { ...slot, [field]: value };
      }
      return slot;
    });
    setTimeSlots(updatedSlots);
  };

  const applyTemplate = (template: string) => {
    let newSlots: TimeSlot[] = [];

    switch (template) {
      case 'morning-1h':
        // Only create slots from 11 AM onwards (was 8 AM)
        for (let hour = 11; hour < 12; hour++) {
          newSlots.push({
            startHour: `${hour.toString().padStart(2, '0')}:00`,
            endHour: `${(hour + 1).toString().padStart(2, '0')}:00`,
            enabled: true,
            capacity: defaultCapacity,
            online: defaultOnline,
            home: defaultHome,
            max_capacity_family: defaultMaxFamily,
          });
        }
        break;
      case 'afternoon-1h':
        for (let hour = 13; hour < 17; hour++) {
          newSlots.push({
            startHour: `${hour.toString().padStart(2, '0')}:00`,
            endHour: `${(hour + 1).toString().padStart(2, '0')}:00`,
            enabled: true,
            capacity: defaultCapacity,
            online: defaultOnline,
            home: defaultHome,
            max_capacity_family: defaultMaxFamily,
          });
        }
        break;
      case 'evening-1h':
        // Only create slots until 9 PM (was 21)
        for (let hour = 17; hour < 21; hour++) {
          newSlots.push({
            startHour: `${hour.toString().padStart(2, '0')}:00`,
            endHour: `${(hour + 1).toString().padStart(2, '0')}:00`,
            enabled: true,
            capacity: defaultCapacity,
            online: defaultOnline,
            home: defaultHome,
            max_capacity_family: defaultMaxFamily,
          });
        }
        break;
      case 'full-day-1h':
        // Only create slots from 11 AM to 9 PM (was 8 AM to 21)
        for (let hour = 11; hour < 21; hour++) {
          newSlots.push({
            startHour: `${hour.toString().padStart(2, '0')}:00`,
            endHour: `${(hour + 1).toString().padStart(2, '0')}:00`,
            enabled: true,
            capacity: defaultCapacity,
            online: defaultOnline,
            home: defaultHome,
            max_capacity_family: defaultMaxFamily,
          });
        }
        break;
      case 'full-day-2h':
        // Only create slots from 11 AM to 9 PM, but ensure end time doesn't exceed 9 PM
        for (let hour = 11; hour < 21; hour += 2) {
          // Skip if the slot would end after 9 PM
          if (hour + 2 > 21) continue;
          newSlots.push({
            startHour: `${hour.toString().padStart(2, '0')}:00`,
            endHour: `${(hour + 2).toString().padStart(2, '0')}:00`,
            enabled: true,
            capacity: defaultCapacity,
            online: defaultOnline,
            home: defaultHome,
            max_capacity_family: defaultMaxFamily,
          });
        }
        break;
      case 'full-day-2h-even':
        // Start from 12 (even hour >= 11) and ensure end time doesn't exceed 9 PM
        for (let hour = 12; hour < 21; hour += 2) {
          // Skip if the slot would end after 9 PM
          if (hour + 2 > 21) continue;
          newSlots.push({
            startHour: `${hour.toString().padStart(2, '0')}:00`,
            endHour: `${(hour + 2).toString().padStart(2, '0')}:00`,
            enabled: true,
            capacity: defaultCapacity,
            online: defaultOnline,
            home: defaultHome,
            max_capacity_family: defaultMaxFamily,
          });
        }
        break;
      case 'full-day-2h-odd':
        // Start from 11 (odd hour >= 11) and ensure end time doesn't exceed 9 PM
        for (let hour = 11; hour < 21; hour += 2) {
          // Skip if the slot would end after 9 PM
          if (hour + 2 > 21) continue;
          newSlots.push({
            startHour: `${hour.toString().padStart(2, '0')}:00`,
            endHour: `${(hour + 2).toString().padStart(2, '0')}:00`,
            enabled: true,
            capacity: defaultCapacity,
            online: defaultOnline,
            home: defaultHome,
            max_capacity_family: defaultMaxFamily,
          });
        }
        break;
    }

    setTimeSlots(newSlots);
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-[95vw] w-[95vw] h-[95vh] flex flex-col">
          <DialogHeader className="flex-shrink-0 pb-2 border-b">
            <div className="flex justify-between items-center">
              <DialogTitle className="text-lg">{t('appointments.modal.title')}</DialogTitle>
              {selectedDay && (
                <Badge variant="outline" className="text-sm font-normal">
                  {t(`calendar.days.${format(selectedDay, 'EEEE').toLowerCase()}`)} {t(`calendar.months.${format(selectedDay, 'MMMM').toLowerCase()}`)} {format(selectedDay, 'd, yyyy')}
                </Badge>
              )}
            </div>
          </DialogHeader>

          {isFetchingSlots ? (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">{t('appointments.modal.loadingSlots')}</p>
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-hidden min-h-0">
              <div className="grid grid-cols-[1fr_2fr] gap-6 p-4 h-full">
                {/* Left side - Controls */}
                <div className="space-y-4 overflow-y-auto pr-2">
                  {/* Quick Templates */}
                  <div className="space-y-3 bg-card rounded-lg p-3 border shadow-sm">
                    <h3 className="font-semibold text-sm flex items-center gap-2">
                      <Zap className="h-3 w-3" />
                      {t('appointments.modal.quickTemplates')}
                    </h3>
                    <div className="grid grid-cols-2 gap-2">
                      <Button 
                        variant="secondary" 
                        size="sm"
                        onClick={() => applyTemplate('morning-1h')}
                        className="justify-start text-xs h-7"
                      >
                        <Sun className="h-3 w-3 mr-1" />
                        {t('appointments.templates.morning1h')}
                      </Button>
                      <Button 
                        variant="secondary"
                        size="sm"
                        onClick={() => applyTemplate('afternoon-1h')}
                        className="justify-start text-xs h-7"
                      >
                        <SunDim className="h-3 w-3 mr-1" />
                        {t('appointments.templates.afternoon1h')}
                      </Button>
                      <Button 
                        variant="secondary"
                        size="sm"
                        onClick={() => applyTemplate('evening-1h')}
                        className="justify-start text-xs h-7"
                      >
                        <Moon className="h-3 w-3 mr-1" />
                        {t('appointments.templates.evening1h')}
                      </Button>
                      <Button 
                        variant="secondary"
                        size="sm"
                        onClick={() => applyTemplate('full-day-1h')}
                        className="justify-start text-xs h-7"
                      >
                        <Clock className="h-3 w-3 mr-1" />
                        {t('appointments.templates.fullDay1h')}
                      </Button>
                      <Button 
                        variant="secondary"
                        size="sm"
                        onClick={() => applyTemplate('full-day-2h-even')}
                        className="justify-start text-xs h-7"
                      >
                        <Clock className="h-3 w-3 mr-1" />
                        {t('appointments.templates.pairHours')}
                      </Button>
                      <Button 
                        variant="secondary"
                        size="sm"
                        onClick={() => applyTemplate('full-day-2h-odd')}
                        className="justify-start text-xs h-7"
                      >
                        <Clock className="h-3 w-3 mr-1" />
                        {t('appointments.templates.oddHours')}
                      </Button>
                    </div>
                  </div>

                  {/* Add New Slot */}
                  <div className="space-y-4 bg-card rounded-lg p-4 border shadow-sm">
                    <h3 className="font-semibold text-lg flex items-center gap-2">
                      <Plus className="h-4 w-4" />
                      {t('appointments.modal.addNewSlot')}
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>{t('appointments.modal.startTime')}</Label>
                        <Select onValueChange={setNewSlotStart}>
                          <SelectTrigger>
                            <SelectValue placeholder={t('appointments.modal.selectStartTime')} />
                          </SelectTrigger>
                          <SelectContent>
                            {Array.from({ length: 10 }, (_, i) => i + 11).map((hour) => (
                              <SelectItem key={hour} value={`${hour.toString().padStart(2, '0')}:00`}>
                                {`${hour.toString().padStart(2, '0')}:00`}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label>{t('appointments.modal.duration')}</Label>
                        <Select onValueChange={(value) => setNewSlotDuration(parseInt(value))}>
                          <SelectTrigger>
                            <SelectValue placeholder={t('appointments.modal.selectDuration')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">{t('appointments.modal.oneHour')}</SelectItem>
                            <SelectItem value="2">{t('appointments.modal.twoHours')}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <Button 
                      onClick={handleAddSlot}
                      className="w-full mt-2"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {t('appointments.modal.addSlot')}
                    </Button>
                  </div>

                  {/* Default Values */}
                  <div className="space-y-4 bg-card rounded-lg p-4 border shadow-sm">
                    <h3 className="font-semibold text-lg flex items-center gap-2">
                      <Settings2 className="h-4 w-4" />
                      {t('appointments.modal.defaultValues')}
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>{t('appointments.modal.defaultCapacity')}</Label>
                        <Input
                          type="number"
                          min="0"
                          value={defaultCapacity}
                          onChange={(e) => setDefaultCapacity(Math.max(0, parseInt(e.target.value) || 0))}
                          className="h-9"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>{t('appointments.modal.defaultOnline')}</Label>
                        <Input
                          type="number"
                          min="0"
                          value={defaultOnline}
                          onChange={(e) => setDefaultOnline(Math.max(0, parseInt(e.target.value) || 0))}
                          className="h-9"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>{t('appointments.modal.defaultHome')}</Label>
                        <Input
                          type="number"
                          min="0"
                          value={defaultHome}
                          onChange={(e) => setDefaultHome(Math.max(0, parseInt(e.target.value) || 0))}
                          className="h-9"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>{t('appointments.modal.maxFamilySize')}</Label>
                        <Input
                          type="number"
                          min="0"
                          value={defaultMaxFamily}
                          onChange={(e) => setDefaultMaxFamily(Math.max(0, parseInt(e.target.value) || 0))}
                          className="h-9"
                        />
                      </div>
                    </div>
                    <Button 
                      variant="secondary"
                      className="w-full mt-4" 
                      onClick={() => {
                        setTimeSlots(prev => prev.map(slot => ({
                          ...slot,
                          capacity: defaultCapacity,
                          online: defaultOnline,
                          home: defaultHome,
                          max_capacity_family: defaultMaxFamily,
                        })));
                      }}
                    >
                      <Wand2 className="h-4 w-4 mr-2" />
                      {t('appointments.modal.applyToAllSlots')}
                    </Button>
                  </div>
                </div>

                {/* Right side - Timeline */}
                <div className="border rounded-lg shadow-sm overflow-hidden bg-card">
                  <div className="p-4 border-b bg-muted/50">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold text-lg flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        {t('appointments.modal.timeline')}
                      </h3>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={clearAllSlots}
                        className="text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        {t('appointments.modal.clearAll')}
                      </Button>
                    </div>
                  </div>
                  
                  <div className="relative h-[calc(100%-5rem)] overflow-y-auto">
                    {/* Time indicators */}
                    <div className="absolute left-0 top-0 bottom-0 w-[60px] border-r bg-muted/30">
                      {Array.from({ length: 10 }, (_, i) => i + 11).map((hour) => (
                        <div
                          key={hour}
                          className="h-16 flex items-center justify-center text-xs font-medium text-muted-foreground border-b border-border/50"
                        >
                          {hour.toString().padStart(2, '0')}:00
                        </div>
                      ))}
                    </div>

                    {/* Slots visualization */}
                    <div className="ml-[60px] relative p-4">
                      {timeSlots.map((slot, index) => {
                        const startHour = parseInt(slot.startHour);
                        const endHour = parseInt(slot.endHour);
                        const duration = endHour - startHour;
                        return (
                          <div
                            key={index}
                            className="absolute left-4 right-4"
                            style={{
                              top: `${(startHour - 11) * 64}px`,
                              height: `${duration * 64 - 12}px`
                            }}
                          >
                            <div className="bg-primary/5 border rounded-md h-full">
                              <div className="flex items-center h-full px-3 gap-3">
                                <span className="font-medium text-xs whitespace-nowrap w-[80px]">
                                  {slot.startHour} - {slot.endHour}
                                </span>
                                
                                <div className="flex items-center gap-4 flex-1">
                                  <div className="flex items-center gap-1">
                                    <Users className="h-3 w-3 text-muted-foreground" />
                                    <Input
                                      type="number"
                                      value={slot.capacity}
                                      onChange={(e) => updateSlot(index, 'capacity', parseInt(e.target.value))}
                                      className="h-6 w-16 text-xs"
                                    />
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Globe className="h-3 w-3 text-muted-foreground" />
                                    <Input
                                      type="number"
                                      value={slot.online}
                                      onChange={(e) => updateSlot(index, 'online', parseInt(e.target.value))}
                                      className="h-6 w-16 text-xs"
                                    />
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Home className="h-3 w-3 text-muted-foreground" />
                                    <Input
                                      type="number"
                                      value={slot.home}
                                      onChange={(e) => updateSlot(index, 'home', parseInt(e.target.value))}
                                      className="h-6 w-16 text-xs"
                                    />
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Users2 className="h-3 w-3 text-muted-foreground" />
                                    <Input
                                      type="number"
                                      value={slot.max_capacity_family}
                                      onChange={(e) => updateSlot(index, 'max_capacity_family', parseInt(e.target.value))}
                                      className="h-6 w-16 text-xs"
                                    />
                                  </div>
                                </div>

                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeSlot(index)}
                                  className="h-6 w-6 p-0"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex-shrink-0 border-t p-4 bg-background">
            <Button variant="outline" onClick={onClose}>
              {t('common.cancel')}
            </Button>
            <Button 
              onClick={handleSubmit} 
              disabled={isSubmittingLocal || isSubmitting || isFetchingSlots}
              className="ml-2"
            >
              {isSubmittingLocal || isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('common.saving')}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {t('common.saveChanges')}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-destructive">
              <AlertTriangle className="h-5 w-5" />
              {t('appointments.deleteDialog.confirmDeletion')}
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-4">
              <p>
                {slotsToDeleteDetails.some(slot => slot.hasReservations)
                  ? t('appointments.deleteDialog.deleteWarningPlural')
                  : t('appointments.deleteDialog.deleteWarningSingular')}
              </p>
              <div className="bg-muted/50 p-3 rounded-md border">
                <p className="font-medium text-destructive mb-2 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  {t('common.warning')}
                </p>
                {slotsToDeleteDetails.some(slot => slot.hasReservations) ? (
                  <>
                    <p className="text-sm">{t('appointments.deleteDialog.affectExisting')}</p>
                    <p className="text-sm font-semibold text-destructive mt-2">
                      {t('appointments.deleteDialog.partialDeleteWarning')}
                    </p>
                  </>
                ) : (
                  <p className="text-sm text-muted-foreground">{t('appointments.deleteDialog.safeToDelete')}</p>
                )}
                {/* Show warning for capacity decrements below reservations */}
                {slotsWithCapacityBelowReservations.length > 0 && (
                  <div className="mt-4">
                    <p className="font-medium text-yellow-700 mb-2 flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-700" />
                      {t('appointments.deleteDialog.capacityBelowReservationsWarning')}
                    </p>
                    <div className="bg-yellow-50 p-2 rounded-md text-xs font-mono max-h-32 overflow-auto border border-yellow-200">
                      <table className="w-full">
                        <thead>
                          <tr className="text-left border-b border-border/50">
                            <th className="p-1.5">{t('common.id')}</th>
                            <th className="p-1.5">{t('appointments.deleteDialog.timeRange')}</th>
                            <th className="p-1.5">{t('appointments.deleteDialog.oldCapacity')}</th>
                            <th className="p-1.5">{t('appointments.deleteDialog.newCapacity')}</th>
                            <th className="p-1.5">{t('appointments.deleteDialog.reservationCount')}</th>
                          </tr>
                        </thead>
                        <tbody>
                          {slotsWithCapacityBelowReservations.map((slot, index) => (
                            <tr key={index} className="border-b border-border/50 last:border-0">
                              <td className="p-1.5 break-all">{slot.id || t('common.unknown')}</td>
                              <td className="p-1.5">{slot.startHour} - {slot.endHour}</td>
                              <td className="p-1.5">{slot.oldCapacity}</td>
                              <td className="p-1.5">{slot.newCapacity}</td>
                              <td className="p-1.5">{slot.reservationCount}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
              {slotsToDeleteDetails.length > 0 && (
                <div>
                  <p className="font-medium mb-2">{t('appointments.deleteDialog.slotsToDelete')}:</p>
                  <div className="bg-muted p-2 rounded-md text-xs font-mono max-h-40 overflow-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-left border-b border-border/50">
                          <th className="p-1.5">{t('common.id')}</th>
                          <th className="p-1.5">{t('appointments.deleteDialog.timeRange')}</th>
                          <th className="p-1.5">{t('common.capacity')}</th>
                          <th className="p-1.5">{t('appointments.deleteDialog.hasReservations')}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {slotsToDeleteDetails.map((slot, index) => (
                          <tr key={index} className="border-b border-border/50 last:border-0">
                            <td className="p-1.5 break-all">{slot.id || t('common.unknown')}</td>
                            <td className="p-1.5">{slot.startHour} - {slot.endHour}</td>
                            <td className="p-1.5">{slot.capacity || t('common.na')}</td>
                            <td className="p-1.5">{slot.hasReservations ? t('common.yes') : t('common.no')}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            {/* Only allow delete if no slot with reservations is being deleted */}
            {slotsToDeleteDetails.some(slot => !slot.hasReservations) && !slotsToDeleteDetails.some(slot => slot.hasReservations) && (
              <Button
                onClick={async () => {
                  setShowDeleteConfirmation(false);
                  // Call submitChanges after closing the dialog
                  await submitChanges();
                }}
                className="bg-destructive hover:bg-destructive/90"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {t('appointments.deleteDialog.confirmDelete')}
              </Button>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 