'use client';

import { use<PERSON><PERSON><PERSON>, useState, useEffect } from 'react';
import {
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  format,
  isSameMonth,
  isToday,
  parseISO
} from 'date-fns';
import { fr } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Loader2, Plus, Trash, Calendar, Users, Home } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { AppointmentSlotsModal } from './appointment-slots-modal';
import { useLanguage } from '@/lib/contexts/language-context';

interface Appointment {
  _id: string;
  branchId: string;
  date: string;
  startHour: string;
  endHour: string;
  capacity: number;
  overbooking: number;
  max_capacity_family: number;
  disponibilitiesCount: number;
}

interface MonthCalendarProps {
  currentMonth: Date;
  branchId: string;
}

interface TimeSlot {
  startHour: string;
  endHour: string;
  enabled: boolean;
  capacity: number;
  overbooking: number;
  max_capacity_family: number;
}

// Define even and odd hour slots
const evenHourSlots: TimeSlot[] = [
  { startHour: "08:00", endHour: "10:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "10:00", endHour: "12:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "12:00", endHour: "14:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "14:00", endHour: "16:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "16:00", endHour: "18:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "18:00", endHour: "20:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "20:00", endHour: "22:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
];

const oddHourSlots: TimeSlot[] = [
  { startHour: "09:00", endHour: "11:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "11:00", endHour: "13:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "13:00", endHour: "15:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "15:00", endHour: "17:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "17:00", endHour: "19:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
  { startHour: "19:00", endHour: "21:00", enabled: true, capacity: 14, overbooking: 14, max_capacity_family: 3 },
];

export function MonthCalendar({ currentMonth, branchId }: MonthCalendarProps) {
  const { toast } = useToast();
  const { t } = useLanguage();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDay, setSelectedDay] = useState<Date | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [slotType, setSlotType] = useState<"even" | "odd">("odd");
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>(oddHourSlots);
  const [defaultCapacity, setDefaultCapacity] = useState(14);
  const [defaultOverbooking, setDefaultOverbooking] = useState(14);
  const [defaultMaxFamily, setDefaultMaxFamily] = useState(3);
  
  // Fetch appointments for the current month
  useEffect(() => {
    const fetchAppointments = async () => {
      if (!branchId) return;
      
      setIsLoading(true);
      try {
        const month = currentMonth.getMonth() + 1; // JavaScript months are 0-indexed
        const year = currentMonth.getFullYear();
        
        const response = await fetch(
          `/api/calendar-appointments?branchId=${branchId}&month=${month}&year=${year}`
        );
        
        if (!response.ok) {
          throw new Error('Failed to fetch appointments');
        }
        
        const data = await response.json();
        console.log('Fetched appointments:', data);
        setAppointments(data || []);
      } catch (error) {
        console.error('Error fetching appointments:', error);
        toast({
          title: 'Error',
          description: 'Failed to load appointments',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAppointments();
  }, [branchId, currentMonth, toast]);
  
  // Generate calendar days
  const calendarDays = useMemo(() => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(monthStart);
    const calendarStart = startOfWeek(monthStart, { weekStartsOn: 1 });
    const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 1 });
    
    return eachDayOfInterval({ start: calendarStart, end: calendarEnd });
  }, [currentMonth]);

  // Group appointments by date
  const appointmentsByDate = useMemo(() => {
    console.log('Grouping appointments:', appointments);
    const grouped: Record<string, Appointment[]> = {};
    
    if (Array.isArray(appointments)) {
      appointments.forEach((appointment: Appointment) => {
        if (!grouped[appointment.date]) {
          grouped[appointment.date] = [];
        }
        grouped[appointment.date].push(appointment);
      });
    }
    
    console.log('Grouped appointments:', grouped);
    return grouped;
  }, [appointments]);

  // Get slots for a specific day
  const getSlotsForDay = (day: Date) => {
    const dateString = format(day, 'yyyy-MM-dd');
    return appointmentsByDate[dateString] || [];
  };

  // Format time slot for display
  const formatTimeSlot = (appointment: Appointment) => {
    const startHour = appointment.startHour.split(':')[0];
    const endHour = appointment.endHour.split(':')[0];
    return `${startHour}-${endHour}`;
  };

  // Handle day click to open dialog
  const handleDayClick = (day: Date) => {
    setSelectedDay(day);
    
    const dateString = format(day, 'yyyy-MM-dd');
    const existingSlots = appointmentsByDate[dateString] || [];
    
    if (existingSlots.length > 0) {
      const firstSlot = existingSlots[0];
      const startHour = parseInt(firstSlot.startHour.split(':')[0]);
      
      if (startHour % 2 === 0) {
        setSlotType("even");
        const mappedSlots = evenHourSlots.map(template => {
          const match = existingSlots.find(
            slot => slot.startHour === template.startHour && slot.endHour === template.endHour
          );
          
          return match 
            ? { 
                startHour: match.startHour, 
                endHour: match.endHour, 
                enabled: true,
                capacity: match.capacity,
                overbooking: match.overbooking, 
                max_capacity_family: match.max_capacity_family 
              }
            : { ...template, enabled: false };
        });
        
        setTimeSlots(mappedSlots);
      } else {
        setSlotType("odd");
        const mappedSlots = oddHourSlots.map(template => {
          const match = existingSlots.find(
            slot => slot.startHour === template.startHour && slot.endHour === template.endHour
          );
          
          return match 
            ? { 
                startHour: match.startHour, 
                endHour: match.endHour, 
                enabled: true,
                capacity: match.capacity,
                overbooking: match.overbooking, 
                max_capacity_family: match.max_capacity_family 
              }
            : { ...template, enabled: false };
        });
        
        setTimeSlots(mappedSlots);
      }
      
      setDefaultCapacity(existingSlots[0].capacity);
      setDefaultOverbooking(existingSlots[0].overbooking);
      setDefaultMaxFamily(existingSlots[0].max_capacity_family);
    } else {
      setSlotType("odd");
      setTimeSlots(oddHourSlots);
      setDefaultCapacity(30);
      setDefaultOverbooking(30);
      setDefaultMaxFamily(4);
    }
    
    setIsDialogOpen(true);
  };

  // Handle slot type change
  const handleSlotTypeChange = (value: string) => {
    const newType = value as "even" | "odd";
    setSlotType(newType);
    setTimeSlots(newType === "even" ? evenHourSlots : oddHourSlots);
  };

  // Apply default values to all enabled slots
  const applyDefaultValues = () => {
    setTimeSlots(prev => prev.map(slot => ({
      ...slot,
      capacity: slot.enabled ? defaultCapacity : slot.capacity,
      overbooking: slot.enabled ? defaultOverbooking : slot.overbooking,
      max_capacity_family: slot.enabled ? defaultMaxFamily : slot.max_capacity_family
    })));
  };

  // Toggle slot enabled state
  const toggleSlotEnabled = (index: number) => {
    const newSlots = [...timeSlots];
    newSlots[index].enabled = !newSlots[index].enabled;
    setTimeSlots(newSlots);
  };

  // Update slot overbooking
  const updateSlotOverbooking = (index: number, value: string) => {
    const newSlots = [...timeSlots];
    newSlots[index].overbooking = parseInt(value) || 0;
    setTimeSlots(newSlots);
  };

  // Update slot max family capacity
  const updateSlotMaxFamily = (index: number, value: string) => {
    const newSlots = [...timeSlots];
    newSlots[index].max_capacity_family = parseInt(value) || 0;
    setTimeSlots(newSlots);
  };

  // Add function to update slot capacity
  const updateSlotCapacity = (index: number, value: string) => {
    const newSlots = [...timeSlots];
    newSlots[index].capacity = parseInt(value) || 0;
    setTimeSlots(newSlots);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!selectedDay || !branchId) return;
    
    setIsSubmitting(true);
    try {
      const dateString = format(selectedDay, 'yyyy-MM-dd');
      
      // Get enabled slots
      const enabledSlots = timeSlots.filter(slot => slot.enabled);
      
      // First, delete all existing slots for this day
      const existingSlots = appointmentsByDate[dateString] || [];
      if (existingSlots.length > 0) {
        const deleteResponse = await fetch(`/api/appointments/bulk-delete`, {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            branchId, 
            date: dateString 
          }),
        });
        
        if (!deleteResponse.ok) {
          throw new Error('Failed to delete existing slots');
        }
      }
      
      // Then create all new slots
      console.log('Creating slots with data:', JSON.stringify({
        branchId,
        date: dateString,
        slots: enabledSlots.map(slot => ({
          startHour: slot.startHour,
          endHour: slot.endHour,
          capacity: slot.capacity,
          overbooking: slot.overbooking,
          max_capacity_family: slot.max_capacity_family
        }))
      }));
      
      const createResponse = await fetch('/api/appointments/bulk-create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          branchId,
          date: dateString,
          slots: enabledSlots.map(slot => ({
            startHour: slot.startHour,
            endHour: slot.endHour,
            capacity: slot.capacity,
            overbooking: slot.overbooking,
            max_capacity_family: slot.max_capacity_family
          })),
        }),
      });
      
      if (!createResponse.ok) {
        const errorData = await createResponse.json();
        throw new Error(errorData.error || 'Failed to create new slots');
      }
      
      // Refresh appointments
      const month = currentMonth.getMonth() + 1;
      const year = currentMonth.getFullYear();
      const refreshResponse = await fetch(
        `/api/calendar-appointments?branchId=${branchId}&month=${month}&year=${year}`
      );
      
      if (refreshResponse.ok) {
        const refreshedData = await refreshResponse.json();
        setAppointments(refreshedData);
      }
      
      toast({
        title: 'Success',
        description: 'Appointment slots updated successfully',
      });
      
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error saving appointment slots:', error);
      toast({
        title: 'Error',
        description: typeof error === 'string' ? error : error instanceof Error ? error.message : 'Failed to save appointment slots',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {/* Calendar header - days of week */}
        <div className="grid grid-cols-7 gap-px bg-gray-200">
          {['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'].map((day) => (
            <div
              key={day}
              className="bg-gray-50 py-2 text-center text-sm font-medium text-gray-500"
            >
              {t(`common.days.short.${day}`)}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-px bg-gray-200">
          {calendarDays.map((day, dayIdx) => {
            const dateString = format(day, 'yyyy-MM-dd');
            const slots = getSlotsForDay(day);
            console.log(`Slots for ${dateString}:`, slots);
            
            return (
              <div
                key={day.toString()}
                className={cn(
                  "relative p-2 text-center border-b border-r cursor-pointer bg-white",
                  "hover:bg-muted/5 transition-all duration-200 group",
                  "hover:shadow-md hover:z-10",
                  !isSameMonth(day, currentMonth) && "bg-muted/5",
                  isToday(day) && "bg-blue-50/50 hover:bg-blue-50"
                )}
                onClick={() => handleDayClick(day)}
              >
                {/* Add edit icon */}
                <div className="absolute top-1 left-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Plus className="h-3 w-3 text-primary" />
                </div>
                
                <div className="flex items-center justify-center mb-1">
                  <time 
                    dateTime={format(day, 'yyyy-MM-dd')} 
                    className={cn(
                      "font-medium text-sm rounded-full w-6 h-6 flex items-center justify-center transition-colors",
                      isToday(day) && "bg-primary text-white",
                      !isToday(day) && "text-gray-600 group-hover:bg-primary/10"
                    )}
                  >
                    {format(day, 'd')}
                  </time>
                </div>
                
                {/* Show number of slots */}
                {slots.length > 0 && (
                  <div className="absolute top-1 right-1">
                    <Badge variant="secondary" className="text-[10px] font-normal bg-primary/5 text-primary hover:bg-primary/10 px-1 py-px">
                      {slots.length} slots
                    </Badge>
                  </div>
                )}
                
                {/* Show time slots */}
                <div className="mt-1 space-y-1">
                  {slots.map((slot, index) => (
                    <div 
                      key={`${slot._id}-${index}`}
                      className="flex flex-col gap-1 text-xs bg-white border shadow-sm hover:shadow rounded-md px-1 py-0.5 transition-all duration-200 group/slot"
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-700 text-[9px]">
                          {slot.startHour.substring(0, 2)}-{slot.endHour.substring(0, 2)}
                        </span>
                        <Badge 
                          variant="outline" 
                          className={cn(
                            "text-[9px] px-1 py-0 h-4 flex items-center gap-0.5",
                            "bg-red-50 text-red-700 border-red-200"
                          )}
                        >
                          <Users className="h-2.5 w-2.5" />
                          {slot.disponibilitiesCount}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-end gap-0.5">
                        <Badge 
                          variant="outline" 
                          className={cn(
                            "text-[9px] px-1 py-0 h-4 flex items-center gap-0.5",
                            "bg-green-50 text-green-700 border-green-200"
                          )}
                        >
                          <Users className="h-2.5 w-2.5" />
                          {slot.capacity}+{slot.overbooking}
                        </Badge>
                        <Badge 
                          variant="outline" 
                          className={cn(
                            "text-[9px] px-1 py-0 h-4 flex items-center gap-0.5",
                            "bg-orange-50 text-orange-700 border-orange-200"
                          )}
                        >
                          <Home className="h-2.5 w-2.5" />
                          {slot.max_capacity_family}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Replace the entire Dialog section with the shared modal */}
      <AppointmentSlotsModal 
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        selectedDay={selectedDay}
        onSave={handleSubmit}
        isSubmitting={isSubmitting}
        timeSlots={timeSlots}
        setTimeSlots={setTimeSlots}
        defaultCapacity={defaultCapacity}
        setDefaultCapacity={setDefaultCapacity}
        defaultOverbooking={defaultOverbooking}
        setDefaultOverbooking={setDefaultOverbooking}
        defaultMaxFamily={defaultMaxFamily}
        setDefaultMaxFamily={setDefaultMaxFamily}
        slotType={slotType}
        setSlotType={setSlotType}
        evenHourSlots={evenHourSlots}
        oddHourSlots={oddHourSlots}
      />
    </>
  );
} 