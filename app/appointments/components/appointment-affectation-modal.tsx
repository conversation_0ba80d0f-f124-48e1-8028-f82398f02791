'use client';

import * as React from 'react';
import { useEffect, useState, useRef } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Building2, Globe, Users, Home, Loader2, User } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface CustomerInfo {
  client1Name: string;
  hasCompanion: boolean;
  client2Name?: string;
  city: string;
  postalCode: string;
  phone: string;
  phone2?: string;
  email: string;
}

interface ChildrenAges {
  age0to5: number;
  age6to12: number;
  age13to17: number;
}

interface Preferences {
  preferredLanguage: string;
  allergies: string[];
  foods: string[];
  hasChildren: boolean;
  childrenAges: ChildrenAges;
  branchId: string;
  visitDate: string;
  visitTime: string;
}

interface Reservation {
  _id: string;
  appointmentId: string;
  partnerId: string;
  type: 'branch' | 'online' | 'home' | 'family';
  status: 'pending' | 'confirmed' | 'cancelled';
  customerInfo: {
    client1Name: string;
    hasCompanion: boolean;
    client2Name?: string;
    city: string;
    postalCode: string;
    phone: string;
    phone2?: string;
    email: string;
  };
  preferences: any;
  createdAt: string;
  updatedAt: string;
}

interface Seller {
  _id: string;
  name: string;
  email?: string;
  phone?: string;
  type: 'branch' | 'online' | 'home' | 'family';
}

interface AppointmentAffectationModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointment: {
    date: string;
    startHour: string;
    endHour: string;
    capacity: number;
    online: number;
    home: number;
    max_capacity_family: number;
    reservationCounts?: {
      branch: number;
      online: number;
      family: number;
      home: number;
    };
    _id?: string;
    branchId: string;
  } | null;
}

// Define item types for drag and drop
const ItemTypes = {
  SELLER: 'seller',
};

export function AppointmentAffectationModal({
  isOpen,
  onClose,
  appointment
}: AppointmentAffectationModalProps) {
  const { toast } = useToast();
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [branchSellers, setBranchSellers] = useState<Seller[]>([]);
  const [availableSellers, setAvailableSellers] = useState<Seller[]>([]);
  const [assignments, setAssignments] = useState<Record<string, Seller | null>>({});

  useEffect(() => {
    if (isOpen && appointment?.branchId) {
      fetchBranchData();
    }
  }, [isOpen, appointment?.branchId, appointment?.date, appointment?.startHour, appointment?.endHour]);

  useEffect(() => {
    if (isOpen && appointment?._id) {
      fetchReservations();
    }
  }, [isOpen, appointment?._id]);

  // Initialize assignments and available sellers when branch sellers are loaded
  useEffect(() => {
    setAvailableSellers([...branchSellers]);
    
    const initialAssignments: Record<string, Seller | null> = {};
    reservations.forEach(reservation => {
      initialAssignments[reservation._id] = null;
    });
    setAssignments(initialAssignments);
  }, [branchSellers, reservations]);

  const fetchBranchData = async () => {
    if (!appointment?.branchId) return;
    try {
      setIsLoading(true);
      const response = await fetch(
        `/api/branch_available_users_for_slot?branchId=${appointment.branchId}&appointmentDate=${appointment.date}&appointmentStartTime=${appointment.startHour}&appointmentEndTime=${appointment.endHour}`
      );
      if (!response.ok) throw new Error('Failed to fetch available branch users');
      const data = await response.json();
      setBranchSellers(data.users);
    } catch (error) {
      console.error('Error fetching branch data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load branch sellers',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchReservations = async () => {
    if (!appointment?._id) return;
    
    setIsLoading(true);
    try {
      const response = await fetch(`/api/appointments/${appointment._id}/affectations`);
      if (!response.ok) throw new Error('Failed to fetch reservations');
      
      const data = await response.json();
      setReservations(data.reservations);
    } catch (error) {
      console.error('Error fetching reservations:', error);
      toast({
        title: 'Error',
        description: 'Failed to load reservations',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Format assignments for API
      const assignmentData = Object.entries(assignments)
        .filter(([_, seller]) => seller !== null)
        .map(([reservationId, seller]) => ({
          reservationId,
          sellerId: seller?._id
        }));
      
      console.log('Saving assignments:', assignmentData);
      
      // Call API to save assignments
      const response = await fetch(`/api/appointments/${appointment?._id}/assign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ assignments: assignmentData }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save assignments');
      }
      
      toast({
        title: 'Success',
        description: 'Assignments saved successfully',
      });
      
      onClose();
    } catch (error) {
      console.error('Error saving assignments:', error);
      toast({
        title: 'Error',
        description: 'Failed to save assignments',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (!appointment) return null;

  // Calculate type counts for the UI
  const countAssignments = () => {
    const counts = {
      branch: { filled: 0, total: appointment.capacity || 0 },
      online: { filled: 0, total: appointment.online || 0 },
      family: { filled: 0, total: appointment.max_capacity_family || 0 },
      home: { filled: 0, total: appointment.home || 0 }
    };
    
    // Count assigned reservations
    Object.entries(assignments).forEach(([reservationId, seller]) => {
      if (seller) {
        const type = seller.type as keyof typeof counts;
        if (counts[type]) {
          counts[type].filled++;
        }
      }
    });
    
    return counts;
  };

  // Get the counts
  const typeCounts = countAssignments();

  // Group reservations by type
  const reservationsByType = {
    branch: reservations.filter(r => r.type === 'branch'),
    online: reservations.filter(r => r.type === 'online'),
    family: reservations.filter(r => r.type === 'family'),
    home: reservations.filter(r => r.type === 'home')
  };

  // Draggable Seller component
  const DraggableSeller = ({ seller }: { seller: Seller }) => {
    const [{ isDragging }, drag] = useDrag({
      type: ItemTypes.SELLER,
      item: { id: seller._id, type: seller.type, name: seller.name },
      collect: (monitor) => ({
        isDragging: !!monitor.isDragging(),
      }),
    });

    return (
      <div 
        ref={drag}
        className={cn(
          "p-2 border rounded-md mb-2 cursor-move bg-white hover:bg-gray-50",
          isDragging && "opacity-50"
        )}
      >
        <div className="flex items-center">
          <div className="font-medium text-sm">{seller.name}</div>
          <Badge className="ml-auto bg-black text-white text-xs py-0 px-2">
            {seller.type}
          </Badge>
        </div>
      </div>
    );
  };

  // Droppable Reservation component
  const DroppableReservation = ({ reservation }: { reservation: Reservation }) => {
    const assignedSeller = assignments[reservation._id];
    
    const [{ isOver, canDrop }, drop] = useDrop({
      accept: ItemTypes.SELLER,
      drop: (item: any) => {
        // Find the seller by ID
        const seller = branchSellers.find(s => s._id === item.id);
        if (!seller) return;
        
        // Check if this seller is already assigned to another reservation
        const isAlreadyAssigned = Object.values(assignments).some(
          s => s && s._id === seller._id
        );
        
        if (isAlreadyAssigned) {
          // If already assigned, remove from previous assignment
          const prevReservationId = Object.entries(assignments).find(
            ([_, s]) => s && s._id === seller._id
          )?.[0];
          
          if (prevReservationId) {
            setAssignments(prev => ({
              ...prev,
              [prevReservationId]: null,
              [reservation._id]: seller
            }));
          }
        } else {
          // If not already assigned, remove from available sellers and assign
          setAvailableSellers(prev => prev.filter(s => s._id !== seller._id));
          
          setAssignments(prev => ({
            ...prev,
            [reservation._id]: seller
          }));
        }
        
        console.log('Assigned seller:', seller.name, 'to reservation:', reservation._id);
      },
      collect: (monitor) => ({
        isOver: !!monitor.isOver(),
        canDrop: !!monitor.canDrop(),
      }),
    });
    
    // If there's an assigned seller, make it draggable
    const [{ isDragging }, drag] = useDrag({
      type: ItemTypes.SELLER,
      item: assignedSeller ? { 
        id: assignedSeller._id, 
        type: assignedSeller.type, 
        name: assignedSeller.name
      } : null,
      canDrag: !!assignedSeller,
      end: (item, monitor) => {
        if (monitor.didDrop()) {
          // Already handled in the drop handler of the new target
        } else {
          // If not dropped on a valid target, return to available sellers
          if (assignedSeller) {
            setAvailableSellers(prev => [...prev, assignedSeller]);
            
            setAssignments(prev => ({
              ...prev,
              [reservation._id]: null
            }));
            
            console.log('Unassigned seller:', assignedSeller.name, 'from reservation:', reservation._id);
          }
        }
      },
      collect: (monitor) => ({
        isDragging: !!monitor.isDragging(),
      }),
    });

    return (
      <div className="p-3 border-b">
        <div className="flex items-center justify-between mb-1">
          <div className="font-medium">
            {reservation.customerInfo.client1Name}
            {reservation.customerInfo.hasCompanion && 
              ` & ${reservation.customerInfo.client2Name}`}
          </div>
          <Badge className="bg-black text-white">
            {reservation.type}
          </Badge>
        </div>
        
        <div className="text-xs text-muted-foreground mb-2">
          <div>📧 {reservation.customerInfo.email}</div>
          <div>📞 {reservation.customerInfo.phone}</div>
        </div>
        
        {/* Drop zone */}
        <div 
          ref={drop}
          className={cn(
            "p-2 border rounded-md transition-colors min-h-[40px] flex items-center justify-center",
            isOver && canDrop && "bg-green-50 border-green-300",
            isOver && !canDrop && "bg-red-50 border-red-300",
            assignedSeller && !isDragging ? "bg-white" : "border-dashed bg-gray-50"
          )}
        >
          {assignedSeller && !isDragging ? (
            <div ref={drag} className="w-full cursor-move">
              <div className="flex items-center">
                <div className="font-medium text-sm">{assignedSeller.name}</div>
                <Badge className="ml-auto bg-black text-white text-xs py-0 px-2">
                  {assignedSeller.type}
                </Badge>
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground text-sm">
              Drop seller here
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle>Affectation des reservations</DialogTitle>
          <div className="text-right">
            <div className="text-sm text-muted-foreground">
              {appointment.date}
            </div>
            <div className="text-lg font-semibold">
              {appointment.startHour} - {appointment.endHour}
            </div>
          </div>
        </DialogHeader>
        
        <div className="grid grid-cols-4 gap-4 mt-4">
          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Building2 className="h-4 w-4" />
              <span>Branch</span>
            </div>
            <div className="text-2xl font-bold">
              {typeCounts.branch.filled}/{typeCounts.branch.total}
            </div>
          </div>
          
          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Globe className="h-4 w-4" />
              <span>Online</span>
            </div>
            <div className="text-2xl font-bold">
              {typeCounts.online.filled}/{typeCounts.online.total}
            </div>
          </div>
          
          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Users className="h-4 w-4" />
              <span>Family</span>
            </div>
            <div className="text-2xl font-bold">
              {typeCounts.family.filled}/{typeCounts.family.total}
            </div>
          </div>
          
          <div className="p-4 border rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Home className="h-4 w-4" />
              <span>Home</span>
            </div>
            <div className="text-2xl font-bold">
              {typeCounts.home.filled}/{typeCounts.home.total}
            </div>
          </div>
        </div>
        
        <DndProvider backend={HTML5Backend}>
          <div className="grid grid-cols-2 gap-4 mt-4">
            {/* Sellers column */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-4 w-4" />
                <span className="font-medium">Sellers ({availableSellers.length})</span>
              </div>
              <div className="border rounded-lg p-4 h-[300px] overflow-auto">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : availableSellers.length > 0 ? (
                  <div>
                    {availableSellers.map((seller) => (
                      <DraggableSeller key={seller._id} seller={seller} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground h-full flex items-center justify-center">
                    No available sellers
                  </div>
                )}
              </div>
            </div>
            
            {/* Reservations column */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-4 w-4" />
                <span className="font-medium">Reservations ({reservations.length})</span>
              </div>
              <div className="border rounded-lg h-[300px] overflow-auto">
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : reservations.length > 0 ? (
                  <div>
                    {Object.entries(reservationsByType).map(([type, typeReservations]) => (
                      typeReservations.length > 0 && (
                        <div key={type}>
                          <div className="p-2 bg-muted/50 border-b">
                            <div className="flex items-center gap-2">
                              {type === 'branch' && <Building2 className="h-4 w-4" />}
                              {type === 'online' && <Globe className="h-4 w-4" />}
                              {type === 'family' && <Users className="h-4 w-4" />}
                              {type === 'home' && <Home className="h-4 w-4" />}
                              <span>{type} ({typeReservations.length}/{typeCounts[type as keyof typeof typeCounts].total})</span>
                            </div>
                          </div>
                          
                          {typeReservations.map((reservation) => (
                            <DroppableReservation key={reservation._id} reservation={reservation} />
                          ))}
                        </div>
                      )
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground h-full flex items-center justify-center">
                    No reservations found
                  </div>
                )}
              </div>
            </div>
          </div>
        </DndProvider>
        
        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onClose}>Close</Button>
          <Button 
            className="bg-black text-white hover:bg-black/90"
            onClick={handleSave}
            disabled={isSaving}
          >
            {isSaving ? "Saving..." : "Save Assignments"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}