'use client';

import * as React from 'react';
import { useMemo, useState, useEffect } from 'react';
import {
  startOfWeek,
  endOfWeek,
  eachDayOfInterval,
  format,
  isToday,
  parseISO,
  isSameMonth
} from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Loader2, Plus, Users, Home, Globe, Building2, PanelLeftClose, PanelLeftOpen, UserCheck, UserX, UserMinus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { AppointmentSlotsModal } from './appointment-slots-modal';
import { AppointmentAffectationModal } from './appointment-affectation-modal';
import { useLanguage } from '@/lib/contexts/language-context';

// Define even and odd hour slots with online and home values
const evenHourSlots = [
  { startHour: "12:00", endHour: "14:00", enabled: true, capacity: 14, online: 0, home: 0, max_capacity_family: 3 },
  { startHour: "14:00", endHour: "16:00", enabled: true, capacity: 14, online: 0, home: 0, max_capacity_family: 3 },
  { startHour: "16:00", endHour: "18:00", enabled: true, capacity: 14, online: 0, home: 0, max_capacity_family: 3 },
  { startHour: "18:00", endHour: "20:00", enabled: true, capacity: 14, online: 0, home: 0, max_capacity_family: 3 },
];

const oddHourSlots = [
  { startHour: "11:00", endHour: "13:00", enabled: true, capacity: 14, online: 0, home: 0, max_capacity_family: 3 },
  { startHour: "13:00", endHour: "15:00", enabled: true, capacity: 14, online: 0, home: 0, max_capacity_family: 3 },
  { startHour: "15:00", endHour: "17:00", enabled: true, capacity: 14, online: 0, home: 0, max_capacity_family: 3 },
  { startHour: "17:00", endHour: "19:00", enabled: true, capacity: 30, online: 0, home: 0, max_capacity_family: 4 },
  { startHour: "19:00", endHour: "21:00", enabled: true, capacity: 30, online: 0, home: 0, max_capacity_family: 4 },
];

interface WeekCalendarProps {
  currentWeek: Date;
  branchId: string;
  appointments: {
    appointments: Array<{
      _id: string;
      branchId: string;
      date: string;
      startHour: string;
      endHour: string;
      capacity: number;
      online: number;
      home: number;
      max_capacity_family: number;
      disponibilitiesCount: number;
    }>;
  };
  onSave: () => void;
  isLoading?: boolean;
  isSidebarOpen: boolean;
  onToggleSidebar: () => void;
}

interface TimeSlot {
  _id?: string;
  startHour: string;
  endHour: string;
  enabled: boolean;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
  disponibilitiesCount?: number;
}

interface SellerAvailability {
  _id: string;
  name: string;
  email: string;
  status: 'present' | 'partially_available' | 'absent';
}

interface AppointmentSellersData {
  appointmentId: string;
  date: string;
  startHour: string;
  endHour: string;
  sellers: {
    present: SellerAvailability[];
    partially_available: SellerAvailability[];
    absent: SellerAvailability[];
    counts: {
      present: number;
      partially_available: number;
      absent: number;
    };
  };
}

interface Appointment {
  _id: string;
  branchId: string;
  date: string;
  startHour: string;
  endHour: string;
  enabled: boolean;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
  disponibilitiesCount: number;
  reservationCounts?: {
    branch: number;
    online: number;
    family: number;
    home: number;
  };
}

// Add this utility function at the top of the file
const getUtilizationColor = (used: number, total: number | boolean) => {
  // Convert boolean to number if needed
  const numericTotal = typeof total === 'boolean' ? (total ? 1 : 0) : total;
  
  if (numericTotal === 0) return "text-white"; // Handle division by zero
  const percentage = (used / numericTotal) * 100;
  
  if (used === 0) return "text-white";
  if (used >= numericTotal) return "!bg-red-500/30 !text-red-200";
  if (percentage > 50) return "!bg-orange-500/30 !text-orange-200";
  return "!bg-blue-500/30 !text-blue-200";
};

export function WeekCalendar({ 
  currentWeek, 
  branchId, 
  appointments, 
  onSave, 
  isLoading = false,
  isSidebarOpen,
  onToggleSidebar 
}: WeekCalendarProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [selectedDay, setSelectedDay] = useState<Date | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFetchingSlots, setIsFetchingSlots] = useState(false);
  const [slotType, setSlotType] = useState<"even" | "odd">("odd");
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [defaultCapacity, setDefaultCapacity] = useState(14);
  const [defaultMaxFamily, setDefaultMaxFamily] = useState(3);
  const [defaultOnline, setDefaultOnline] = useState(0);
  const [defaultHome, setDefaultHome] = useState(0);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [isAffectationModalOpen, setIsAffectationModalOpen] = useState(false);
  const [sellersAvailability, setSellersAvailability] = useState<Record<string, AppointmentSellersData>>({});
  const [isLoadingSellers, setIsLoadingSellers] = useState(false);
  const { t } = useLanguage();

  // Generate week days
  const weekDays = useMemo(() => {
    const weekStart = startOfWeek(currentWeek, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(currentWeek, { weekStartsOn: 1 });
    return eachDayOfInterval({ start: weekStart, end: weekEnd });
  }, [currentWeek]);

  // Group appointments by date
  const appointmentsByDate = useMemo(() => {
    const grouped: Record<string, any[]> = {};
    (appointments?.appointments || []).forEach((appointment) => {
      if (!grouped[appointment.date]) {
        grouped[appointment.date] = [];
      }
      grouped[appointment.date].push(appointment);
    });
    return grouped;
  }, [appointments]);

  // Fetch seller availability for all appointments
  const fetchSellersAvailability = async () => {
    if (!branchId || !appointments?.appointments?.length) return;

    setIsLoadingSellers(true);
    try {
      const response = await fetch('/api/appointments/sellers-availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          branchId,
          appointments: appointments.appointments.map(apt => ({
            _id: apt._id,
            date: apt.date,
            startHour: apt.startHour,
            endHour: apt.endHour
          }))
        })
      });

      if (!response.ok) {
        throw new Error('Failed to fetch sellers availability');
      }

      const data = await response.json();

      // Convert array to map for quick lookup
      const availabilityMap: Record<string, AppointmentSellersData> = {};
      data.appointments.forEach((apt: AppointmentSellersData) => {
        availabilityMap[apt.appointmentId] = apt;
      });

      setSellersAvailability(availabilityMap);
    } catch (error) {
      console.error('Error fetching sellers availability:', error);
      // Don't show toast for this error as it's not critical
    } finally {
      setIsLoadingSellers(false);
    }
  };

  // Fetch seller availability when appointments change
  useEffect(() => {
    fetchSellersAvailability();
  }, [appointments, branchId]);

  // Get slots for a specific day
  const getSlotsForDay = (day: Date) => {
    const dateString = format(day, 'yyyy-MM-dd');
    return appointmentsByDate[dateString] || [];
  };

  // Handle slot click
  const handleSlotClick = (slot: any) => {
    // Instead of using router, use window.open to open in a new tab
    const date = slot.date;
    const appointmentId = slot._id;
    const targetUrl = `/affectations?date=${date}&branchId=${branchId}&appointmentId=${appointmentId}`;
    
    // Open in a new tab
    window.open(targetUrl, '_blank');
  };

  // Handle day click (outside slots)
  const handleDayClick = async (day: Date) => {
    setSelectedDay(day);
    await fetchDaySlots(day);
    setIsDialogOpen(true);
  };

  const fetchDaySlots = async (day: Date) => {
    if (!branchId || !day) return;
    
    setIsFetchingSlots(true);
    try {
      const dateString = format(day, 'yyyy-MM-dd');
      const response = await fetch(`/api/appointments/by-date?branchId=${branchId}&date=${dateString}`);
      if (!response.ok) throw new Error('Failed to fetch day slots');
      const data = await response.json();
      setTimeSlots(data.appointments || []);
    } catch (error) {
      console.error('Error fetching day slots:', error);
      toast({
        title: 'Error',
        description: 'Failed to load appointment slots for the day',
        variant: 'destructive',
      });
      setTimeSlots([]); // Set empty array instead of default slots
    } finally {
      setIsFetchingSlots(false);
    }
  };

  const handleSubmit = async () => {
    console.log('🟡 [PARENT] handleSubmit called');
    const logMessages = [];

    try {
      setIsSubmitting(true);

      if (!selectedDay) {
        toast({
          title: 'Error',
          description: 'Please select a date',
          variant: 'destructive',
        });
        setIsSubmitting(false);
        return;
      }

      // Format the date as yyyy-MM-dd string
      const formattedDate = format(selectedDay, 'yyyy-MM-dd');
      
      // Log for our collection, won't be displayed as popup
      logMessages.push(`Submitting time slots for ${formattedDate} at branch ${branchId}`);

      // Construct slots for API
      const slotsToSubmit = timeSlots.map(slot => ({
        _id: slot._id, // Include ID if it exists (for updates)
        startHour: slot.startHour,
        endHour: slot.endHour,
        enabled: slot.enabled,
        capacity: slot.capacity || 0,
        online: slot.online || false,
        home: slot.home || false,
        max_capacity_family: slot.max_capacity_family || 0
      }));

      logMessages.push(`Preparing to submit ${slotsToSubmit.length} slots`);

      console.log('🟡 [PARENT] Making API call to update-slots');
      // Call the update-slots API
      const response = await fetch('/api/appointments/update-slots', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          branchId,
          date: formattedDate,
          slots: slotsToSubmit,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update appointment slots');
      }

      logMessages.push('API response received successfully');

      // Create a user-friendly message based on the results
      const { created, updated, deleted, unchanged } = data.results;
      let message = 'Changes saved successfully.';
      
      const details = [];
      if (created > 0) details.push(`${created} slot${created !== 1 ? 's' : ''} created`);
      if (updated > 0) details.push(`${updated} slot${updated !== 1 ? 's' : ''} updated`);
      if (deleted > 0) details.push(`${deleted} slot${deleted !== 1 ? 's' : ''} deleted`);
      if (unchanged > 0) details.push(`${unchanged} slot${unchanged !== 1 ? 's' : ''} unchanged`);
      
      if (details.length > 0) {
        message += ` (${details.join(', ')})`;
      }

      // Only show one success toast
      toast({
        title: "Success",
        description: message,
        duration: 2000,
      });

      // Refresh the slots data for the current day
      await fetchDaySlots(selectedDay);

      // Call the parent's onSave method to reload the entire calendar
      // This will refresh the appointments using the date range API
      await onSave();

      // Close dialog after successful submission
      setIsDialogOpen(false);
      
      // Final success message for console only
      logMessages.push(`Operation completed: ${message}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Error submitting time slots:', error);
      logMessages.push(`Error: ${errorMessage}`);
      
      // Only show one error toast
      toast({
        title: "Error",
        description: errorMessage || "Failed to save time slots. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Log only the final message to the console
      if (logMessages.length > 0) {
        console.log(`Final result: ${logMessages[logMessages.length - 1]}`);
      }
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="overflow-x-auto">
          <div className="min-w-[900px] w-full">
            {/* Header row with times - Fixed to align with content below */}
            <div className="grid grid-cols-[80px_repeat(10,1fr)] border-b bg-slate-50">
              <div className="p-2 border-r" /> {/* Empty corner cell with border */}
              <div className="col-span-10 relative h-8">
                {/* First time marker (11:00) aligned to left edge */}
                <div 
                  className="absolute text-xs sm:text-sm text-muted-foreground"
                  style={{
                    left: '0%',
                    transform: 'translateX(4px)',
                  }}
                >
                  11:00
                </div>
                
                {/* Middle time markers (12:00-20:00) */}
                {Array.from({ length: 9 }, (_, i) => {
                  const hour = i + 12;
                  return (
                    <div 
                      key={i}
                      className="absolute text-xs sm:text-sm text-muted-foreground"
                      style={{
                        left: `${((i + 1) / 10) * 100}%`,
                        transform: 'translateX(-50%)',
                      }}
                    >
                      {`${hour.toString().padStart(2, '0')}:00`}
                    </div>
                  );
                })}
                
                {/* Last time marker (21:00) aligned to right edge */}
                <div 
                  className="absolute text-xs sm:text-sm text-muted-foreground"
                  style={{
                    right: '0%',
                    transform: 'translateX(-4px)',
                  }}
                >
                  21:00
                </div>
              </div>
            </div>

            {/* Days and slots */}
            {weekDays.map((day) => {
              const dateStr = format(day, 'yyyy-MM-dd');
              const daySlots = getSlotsForDay(day);

              return (
                <div 
                  key={dateStr}
                  className="grid grid-cols-[80px_repeat(10,1fr)] border-b hover:bg-slate-50/80 transition-colors"
                >
                  {/* Day cell - with border to separate from time slots */}
                  <div className="p-2 border-r flex justify-between items-start bg-white sticky left-0 z-10">
                    <div>
                      <div className="font-medium text-sm">
                        {t(`calendar.days.${format(day, 'EEE').toLowerCase()}`)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {format(day, 'MMM dd')}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDayClick(day);
                      }}
                    >
                      <Plus className="h-4 w-4" />
                      <span className="sr-only">Edit {format(day, 'EEEE')}</span>
                    </Button>
                  </div>

                  {/* Slots container */}
                  <div 
                    className="col-span-10 relative h-24 p-1"
                    onClick={() => handleDayClick(day)}
                  >
                    {daySlots.map((slot) => {
                      // Parse start and end hours (handle both number and string formats)
                      let startHour, endHour;
                      
                      if (typeof slot.startHour === 'string') {
                        startHour = parseInt(slot.startHour.split(':')[0]);
                      } else {
                        startHour = parseInt(String(slot.startHour));
                      }
                      
                      if (typeof slot.endHour === 'string') {
                        endHour = parseInt(slot.endHour.split(':')[0]);
                      } else {
                        endHour = parseInt(String(slot.endHour));
                      }
                      
                      // Skip slots outside our 11-21 range
                      if (endHour <= 11 || startHour >= 21) return null;
                      
                      // Ensure slots are within our display range
                      startHour = Math.max(11, startHour);
                      endHour = Math.min(21, endHour);
                      
                      // Calculate position with a small margin between slots
                      const left = ((startHour - 11) / 10) * 100;
                      const width = ((endHour - startHour) / 10) * 100;
                      
                      return (
                        <div
                          key={slot._id}
                          className="absolute h-[calc(100%-8px)] rounded-md overflow-hidden cursor-pointer transition-all hover:shadow-md"
                          style={{
                            left: `${left}%`,
                            width: `calc(${width}% - 4px)`, // Subtract margin for spacing
                            margin: '0 2px', // Add margin for spacing between slots
                            background: 'linear-gradient(135deg, rgb(17, 24, 39) 0%, rgb(31, 41, 55) 100%)',
                            boxShadow: '0 2px 8px -2px rgba(0, 0, 0, 0.2)',
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSlotClick(slot);
                          }}
                        >
                          <div className="flex flex-col h-full p-1.5 sm:p-2 relative">
                            {/* Stats badges in a grid */}
                            <div className="grid grid-cols-2 gap-1">
                              {/* Branch Capacity - Always displayed */}
                              <Badge
                                variant="outline"
                                className={cn(
                                  "bg-white/10 text-white border-0 h-5 px-1.5 text-[10px] backdrop-blur-sm transition-colors",
                                  getUtilizationColor(slot.reservationCounts?.branch || 0, slot.capacity || 0)
                                )}
                              >
                                <Building2 className="w-3 h-3 mr-1" />
                                {(slot.reservationCounts?.branch || 0)}/{slot.capacity || 0}
                              </Badge>

                              {/* Family - Always displayed */}
                              <Badge
                                variant="outline"
                                className={cn(
                                  "bg-white/10 text-white border-0 h-5 px-1.5 text-[10px] backdrop-blur-sm transition-colors",
                                  getUtilizationColor(slot.reservationCounts?.family || 0, slot.max_capacity_family || 0)
                                )}
                              >
                                <Users className="w-3 h-3 mr-1" />
                                {(slot.reservationCounts?.family || 0)}/{slot.max_capacity_family || 0}
                              </Badge>

                              {/* Online - Only displayed if not 0/0 */}
                              {((slot.reservationCounts?.online || 0) > 0 || (slot.online || 0) > 0) && (
                                <Badge
                                  variant="outline"
                                  className={cn(
                                    "bg-white/10 text-white border-0 h-5 px-1.5 text-[10px] backdrop-blur-sm transition-colors",
                                    getUtilizationColor(slot.reservationCounts?.online || 0, slot.online || 0)
                                  )}
                                >
                                  <Globe className="w-3 h-3 mr-1" />
                                  {(slot.reservationCounts?.online || 0)}/{slot.online || 0}
                                </Badge>
                              )}

                              {/* Home - Only displayed if not 0/0 */}
                              {((slot.reservationCounts?.home || 0) > 0 || (slot.home || 0) > 0) && (
                                <Badge
                                  variant="outline"
                                  className={cn(
                                    "bg-white/10 text-white border-0 h-5 px-1.5 text-[10px] backdrop-blur-sm transition-colors",
                                    getUtilizationColor(slot.reservationCounts?.home || 0, slot.home || 0)
                                  )}
                                >
                                  <Home className="w-3 h-3 mr-1" />
                                  {(slot.reservationCounts?.home || 0)}/{slot.home || 0}
                                </Badge>
                              )}
                            </div>

                            {/* Seller Availability - positioned at bottom right */}
                            <div className="absolute bottom-1.5 right-1.5 flex items-center gap-1">
                              {isLoadingSellers ? (
                                // Loading state
                                <div className="flex items-center gap-1 bg-gray-500/20 text-gray-300 px-2 py-1 rounded text-[10px] font-medium">
                                  <Loader2 className="w-3 h-3 animate-spin" />
                                  <span>{t('common.loading')}</span>
                                </div>
                              ) : sellersAvailability[slot._id] ? (
                                <TooltipProvider>
                                  {/* Present sellers */}
                                  {sellersAvailability[slot._id].sellers.counts.present > 0 && (
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <div className="flex items-center gap-1 bg-green-500/20 text-green-200 px-2 py-1 rounded text-[10px] font-medium cursor-help">
                                          <UserCheck className="w-3 h-3" />
                                          {sellersAvailability[slot._id].sellers.counts.present}
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent
                                        side="top"
                                        className="max-w-xs max-h-[40vh] overflow-hidden p-0 border-0 bg-white shadow-lg rounded-lg"
                                        sideOffset={8}
                                      >
                                        <div className="p-3">
                                          <div className="font-medium text-green-600 mb-2 text-sm">
                                            {t('appointments.sellers.present')} ({sellersAvailability[slot._id].sellers.counts.present})
                                          </div>
                                          <div className="max-h-[30vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                            {sellersAvailability[slot._id].sellers.present.map(seller => (
                                              <div key={seller._id} className="text-xs py-1 text-gray-700 border-b border-gray-100 last:border-b-0">
                                                {seller.name}
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  )}

                                  {/* Partially available sellers */}
                                  {sellersAvailability[slot._id].sellers.counts.partially_available > 0 && (
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <div className="flex items-center gap-1 bg-orange-500/20 text-orange-200 px-2 py-1 rounded text-[10px] font-medium cursor-help">
                                          <UserMinus className="w-3 h-3" />
                                          {sellersAvailability[slot._id].sellers.counts.partially_available}
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent
                                        side="top"
                                        className="max-w-xs max-h-[40vh] overflow-hidden p-0 border-0 bg-white shadow-lg rounded-lg"
                                        sideOffset={8}
                                      >
                                        <div className="p-3">
                                          <div className="font-medium text-orange-600 mb-2 text-sm">
                                            {t('appointments.sellers.partiallyAvailable')} ({sellersAvailability[slot._id].sellers.counts.partially_available})
                                          </div>
                                          <div className="max-h-[30vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                            {sellersAvailability[slot._id].sellers.partially_available.map(seller => (
                                              <div key={seller._id} className="text-xs py-1 text-gray-700 border-b border-gray-100 last:border-b-0">
                                                {seller.name}
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  )}

                                  {/* Absent sellers */}
                                  {sellersAvailability[slot._id].sellers.counts.absent > 0 && (
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <div className="flex items-center gap-1 bg-red-500/20 text-red-200 px-2 py-1 rounded text-[10px] font-medium cursor-help">
                                          <UserX className="w-3 h-3" />
                                          {sellersAvailability[slot._id].sellers.counts.absent}
                                        </div>
                                      </TooltipTrigger>
                                      <TooltipContent
                                        side="top"
                                        className="max-w-xs max-h-[40vh] overflow-hidden p-0 border-0 bg-white shadow-lg rounded-lg"
                                        sideOffset={8}
                                      >
                                        <div className="p-3">
                                          <div className="font-medium text-red-600 mb-2 text-sm">
                                            {t('appointments.sellers.absent')} ({sellersAvailability[slot._id].sellers.counts.absent})
                                          </div>
                                          <div className="max-h-[30vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                            {sellersAvailability[slot._id].sellers.absent.map(seller => (
                                              <div key={seller._id} className="text-xs py-1 text-gray-700 border-b border-gray-100 last:border-b-0">
                                                {seller.name}
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      </TooltipContent>
                                    </Tooltip>
                                  )}
                                </TooltipProvider>
                              ) : null}
                            </div>

                            {/* Time range - always at bottom left */}
                            <div className="absolute bottom-1.5 left-1.5 text-[10px] text-white font-medium">
                              {typeof slot.startHour === 'string' ? slot.startHour : `${slot.startHour}:00`}-
                              {typeof slot.endHour === 'string' ? slot.endHour : `${slot.endHour}:00`}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      <AppointmentSlotsModal 
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        selectedDay={selectedDay}
        onSave={handleSubmit}
        isSubmitting={isSubmitting}
        isFetchingSlots={isFetchingSlots}
        timeSlots={timeSlots}
        setTimeSlots={setTimeSlots}
        defaultCapacity={defaultCapacity}
        setDefaultCapacity={setDefaultCapacity}
        defaultMaxFamily={defaultMaxFamily}
        setDefaultMaxFamily={setDefaultMaxFamily}
        slotType={slotType}
        setSlotType={setSlotType}
        evenHourSlots={evenHourSlots}
        oddHourSlots={oddHourSlots}
        defaultOnline={defaultOnline}
        setDefaultOnline={setDefaultOnline}
        defaultHome={defaultHome}
        setDefaultHome={setDefaultHome}
        branchId={branchId}
      />

      <AppointmentAffectationModal
        isOpen={isAffectationModalOpen}
        onClose={() => {
          setIsAffectationModalOpen(false);
          setSelectedAppointment(null);
        }}
        appointment={selectedAppointment}
      />
    </div>
  );
}
