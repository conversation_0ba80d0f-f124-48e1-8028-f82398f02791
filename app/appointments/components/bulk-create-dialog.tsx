'use client';

import { useState, useEffect } from 'react';
import { format, eachDayOfInterval } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { useToast } from '@/hooks/use-toast';
import { Loader2, Calendar, Users, Clock, Building2, Info } from 'lucide-react';

import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface Branch {
  _id: string;
  name: string;
}

interface TimeSlot {
  value: string;
  label: string;
  startHour: string;
  endHour: string;
}

interface BulkCreateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  branches: Branch[];
  onSuccess: () => void;
}

const TIME_SLOTS: TimeSlot[] = [
  { value: '11-13', label: '11:00 - 13:00', startHour: '11:00', endHour: '13:00' },
  { value: '13-15', label: '13:00 - 15:00', startHour: '13:00', endHour: '15:00' },
  { value: '15-17', label: '15:00 - 17:00', startHour: '15:00', endHour: '17:00' },
  { value: '17-19', label: '17:00 - 19:00', startHour: '17:00', endHour: '19:00' },
  { value: '19-21', label: '19:00 - 21:00', startHour: '19:00', endHour: '21:00' },
];

export function BulkCreateDialog({ isOpen, onClose, branches, onSuccess }: BulkCreateDialogProps) {
  const { toast } = useToast();
  
  // Form state
  const [selectedBranches, setSelectedBranches] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [capacity, setCapacity] = useState([12]);
  const [selectedTimeSlots, setSelectedTimeSlots] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSelectedBranches([]);
      setDateRange(undefined);
      setCapacity([12]);
      setSelectedTimeSlots([]);
    }
  }, [isOpen]);

  // Handle branch selection
  const handleBranchToggle = (branchId: string) => {
    setSelectedBranches(prev => 
      prev.includes(branchId) 
        ? prev.filter(id => id !== branchId)
        : [...prev, branchId]
    );
  };

  // Handle time slot selection
  const handleTimeSlotToggle = (timeSlotValue: string) => {
    setSelectedTimeSlots(prev => 
      prev.includes(timeSlotValue) 
        ? prev.filter(value => value !== timeSlotValue)
        : [...prev, timeSlotValue]
    );
  };

  // Calculate total appointments that will be created
  const calculateTotalAppointments = () => {
    if (!dateRange?.from || !dateRange?.to) return 0;
    
    const days = eachDayOfInterval({
      start: dateRange.from,
      end: dateRange.to
    });
    
    return selectedBranches.length * days.length * selectedTimeSlots.length;
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validation
    if (selectedBranches.length === 0) {
      toast({
        title: 'Erreur de validation',
        description: 'Veuillez sélectionner au moins une succursale.',
        variant: 'destructive',
      });
      return;
    }

    if (!dateRange?.from || !dateRange?.to) {
      toast({
        title: 'Erreur de validation',
        description: 'Veuillez sélectionner une plage de dates.',
        variant: 'destructive',
      });
      return;
    }

    if (selectedTimeSlots.length === 0) {
      toast({
        title: 'Erreur de validation',
        description: 'Veuillez sélectionner au moins un créneau horaire.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate all dates in the range
      const dates = eachDayOfInterval({
        start: dateRange.from,
        end: dateRange.to
      });

      // Prepare appointments data
      const appointments = [];
      
      for (const branchId of selectedBranches) {
        for (const date of dates) {
          for (const timeSlotValue of selectedTimeSlots) {
            const timeSlot = TIME_SLOTS.find(ts => ts.value === timeSlotValue);
            if (timeSlot) {
              // Ensure date formatting is timezone-agnostic
              const formattedDate = format(new Date(date.getFullYear(), date.getMonth(), date.getDate()), 'yyyy-MM-dd');
              appointments.push({
                branchId,
                date: formattedDate,
                startHour: timeSlot.startHour,
                endHour: timeSlot.endHour,
                capacity: capacity[0],
                online: 0,
                home: 0,
                max_capacity_family: 3,
              });
            }
          }
        }
      }

      // Submit to API
      const response = await fetch('/api/appointments/bulk-create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ appointments }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Échec de la création des rendez-vous');
      }

      const result = await response.json();

      const createdCount = result.created || 0;
      const updatedCount = result.updated || 0;

      let description = '';
      if (createdCount > 0 && updatedCount > 0) {
        description = `${createdCount} rendez-vous créés et ${updatedCount} rendez-vous mis à jour avec succès.`;
      } else if (createdCount > 0) {
        description = `${createdCount} rendez-vous créés avec succès.`;
      } else if (updatedCount > 0) {
        description = `${updatedCount} rendez-vous mis à jour avec succès.`;
      } else {
        description = 'Aucun changement effectué.';
      }

      toast({
        title: 'Succès',
        description,
      });

      // Close dialog and refresh data
      onClose();
      onSuccess();

    } catch (error) {
      console.error('Error creating bulk appointments:', error);
      toast({
        title: 'Erreur',
        description: error instanceof Error ? error.message : 'Échec de la création des rendez-vous',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalAppointments = calculateTotalAppointments();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Création et mise à jour en lot de rendez-vous
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            Créez ou mettez à jour plusieurs créneaux de rendez-vous simultanément pour plusieurs succursales et dates.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Informational Alert */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Cette fonction vous permet de créer ou mettre à jour rapidement des créneaux de rendez-vous
              sur plusieurs succursales et dates. Les nouveaux créneaux seront créés avec la capacité choisie,
              et les créneaux existants seront mis à jour intelligemment selon les réservations en cours.
            </AlertDescription>
          </Alert>

          {/* Update Behavior Alert */}
          <Alert className="border-amber-200 bg-amber-50">
            <Info className="h-4 w-4 text-amber-600" />
            <AlertDescription className="text-amber-800">
              <strong>Mise à jour automatique :</strong> Si un créneau existe déjà au même horaire,
              sa capacité sera mise à jour selon ces règles :
              <br />• Si la capacité choisie ≥ nombre de réservations : utilise la capacité choisie
              <br />• Si la capacité choisie &lt; nombre de réservations : utilise le nombre de réservations
              <br />
              <em className="text-xs">Les réservations exclues des affectations sont automatiquement prises en compte.</em>
            </AlertDescription>
          </Alert>

          {/* Branch Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Succursales ({selectedBranches.length} sélectionnée{selectedBranches.length !== 1 ? 's' : ''})
            </Label>
            <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto border rounded-md p-3">
              {branches.map((branch) => (
                <div key={branch._id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`branch-${branch._id}`}
                    checked={selectedBranches.includes(branch._id)}
                    onCheckedChange={() => handleBranchToggle(branch._id)}
                  />
                  <Label 
                    htmlFor={`branch-${branch._id}`}
                    className="text-sm cursor-pointer truncate"
                    title={branch.name}
                  >
                    {branch.name}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Date Range Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Plage de dates
            </Label>
            <DatePickerWithRange
              date={dateRange}
              onDateChange={setDateRange}
              placeholder="Sélectionner les dates"
              className="w-full"
            />
            {dateRange?.from && dateRange?.to && (
              <p className="text-xs text-muted-foreground">
                {eachDayOfInterval({ start: dateRange.from, end: dateRange.to }).length} jour(s) sélectionné(s)
              </p>
            )}
          </div>

          {/* Capacity Slider */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Capacité par créneau: {capacity[0]}
            </Label>
            <div className="px-2">
              <Slider
                value={capacity}
                onValueChange={setCapacity}
                max={50}
                min={1}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>1</span>
                <span>25</span>
                <span>50</span>
              </div>
            </div>
          </div>

          {/* Time Slots Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Créneaux horaires ({selectedTimeSlots.length} sélectionné{selectedTimeSlots.length !== 1 ? 's' : ''})
            </Label>
            <div className="grid grid-cols-1 gap-2">
              {TIME_SLOTS.map((timeSlot) => (
                <div key={timeSlot.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`timeslot-${timeSlot.value}`}
                    checked={selectedTimeSlots.includes(timeSlot.value)}
                    onCheckedChange={() => handleTimeSlotToggle(timeSlot.value)}
                  />
                  <Label 
                    htmlFor={`timeslot-${timeSlot.value}`}
                    className="text-sm cursor-pointer"
                  >
                    {timeSlot.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Summary */}
          {totalAppointments > 0 && (
            <div className="bg-muted/50 rounded-lg p-4 space-y-2">
              <h4 className="font-medium text-sm">Résumé du traitement</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Succursales:</span>
                  <Badge variant="secondary" className="ml-2">{selectedBranches.length}</Badge>
                </div>
                <div>
                  <span className="text-muted-foreground">Jours:</span>
                  <Badge variant="secondary" className="ml-2">
                    {dateRange?.from && dateRange?.to ? 
                      eachDayOfInterval({ start: dateRange.from, end: dateRange.to }).length : 0
                    }
                  </Badge>
                </div>
                <div>
                  <span className="text-muted-foreground">Créneaux:</span>
                  <Badge variant="secondary" className="ml-2">{selectedTimeSlots.length}</Badge>
                </div>
                <div>
                  <span className="text-muted-foreground">Capacité:</span>
                  <Badge variant="secondary" className="ml-2">{capacity[0]}</Badge>
                </div>
              </div>
              <div className="pt-2 border-t">
                <span className="font-medium">Total de rendez-vous à traiter: </span>
                <Badge variant="default" className="ml-2">{totalAppointments}</Badge>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Annuler
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={isSubmitting || totalAppointments === 0}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Création en cours...
              </>
            ) : (
              `Traiter ${totalAppointments} rendez-vous`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}