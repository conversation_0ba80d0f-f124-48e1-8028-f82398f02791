import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import { createContractSigningToken } from '@/lib/utils/contract-token-utils';

export async function POST(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { userId, contractStartDate } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    const result = await createContractSigningToken(
      userId,
      session.user.id, // Admin ID from session
      contractStartDate ? new Date(contractStartDate) : undefined,
      true // Skip role validation for testing purposes
    );

    return NextResponse.json(result);

  } catch (error: any) {
    console.error('Error generating contract token:', error);
    return NextResponse.json(
      { success: false, error: 'Token generation failed', details: error.message },
      { status: 500 }
    );
  }
}
