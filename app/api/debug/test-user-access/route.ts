import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { isValidObjectId } from 'mongoose';
import { userRequiresContract } from '@/lib/utils/contract-data-mapper';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    console.log('Testing user access for userId:', userId);

    // Validate ObjectId format
    if (!isValidObjectId(userId)) {
      return NextResponse.json({
        error: 'Invalid user ID format',
        userId,
        isValidObjectId: false,
        details: 'User ID must be a valid 24-character MongoDB ObjectId'
      }, { status: 400 });
    }

    // Try to fetch user data
    const user = await User.findById(userId).populate('roles').lean();
    
    if (!user) {
      // Let's also try to find any user to see if the database connection works
      const anyUser = await User.findOne().populate('roles').lean();
      
      return NextResponse.json({
        error: 'User not found',
        userId,
        databaseConnected: !!anyUser,
        totalUsers: await User.countDocuments(),
        sampleUser: anyUser ? {
          id: anyUser._id,
          name: anyUser.name,
          email: anyUser.email,
          roles: anyUser.roles
        } : null
      }, { status: 404 });
    }

    // Check if user requires contract
    const requiresContract = userRequiresContract(user);

    return NextResponse.json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        roles: user.roles,
        hasContractFields: !!(user.socialAssurance || user.taxInfo || user.emergencyContact),
        requiresContract,
        contractEligible: requiresContract ? 'Yes - Has PAP or Seller role' : 'No - Missing required roles'
      }
    });

  } catch (error: any) {
    console.error('Error testing user access:', error);
    return NextResponse.json(
      { 
        error: 'Failed to test user access',
        details: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
