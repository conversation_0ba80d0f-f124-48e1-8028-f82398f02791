import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import ContractAuditLog from '@/models/ContractAuditLog';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json({
        success: false,
        message: 'Relationship test failed',
        error: 'userId is required for testing relationships'
      });
    }

    const results = [];

    // Test User -> ContractSigningToken relationship
    try {
      const user = await User.findById(userId).lean();
      const contractToken = await ContractSigningToken.findOne({ userId }).lean();
      
      results.push({
        test: 'User -> ContractSigningToken relationship',
        success: true,
        data: {
          userExists: !!user,
          userContractToken: user?.contractSigningToken,
          contractTokenExists: !!contractToken,
          contractTokenValue: contractToken?.token,
          relationshipConsistent: user?.contractSigningToken === contractToken?.token
        }
      });
    } catch (error) {
      results.push({
        test: 'User -> ContractSigningToken relationship',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test ContractSigningToken population
    try {
      const contractToken = await ContractSigningToken.findOne({ userId })
        .populate('userId', 'name email')
        .populate('createdBy', 'name email')
        .lean();
      
      results.push({
        test: 'ContractSigningToken population',
        success: true,
        data: {
          contractTokenExists: !!contractToken,
          userPopulated: !!contractToken?.userId,
          createdByPopulated: !!contractToken?.createdBy,
          populatedUserData: contractToken?.userId,
          populatedCreatedByData: contractToken?.createdBy
        }
      });
    } catch (error) {
      results.push({
        test: 'ContractSigningToken population',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test ContractAuditLog relationships
    try {
      const auditLogs = await ContractAuditLog.find({ userId })
        .populate('userId', 'name email')
        .populate('performedBy', 'name email')
        .limit(5)
        .lean();
      
      results.push({
        test: 'ContractAuditLog relationships',
        success: true,
        data: {
          auditLogCount: auditLogs.length,
          sampleLog: auditLogs[0] || null,
          allLogsHaveUserId: auditLogs.every(log => !!log.userId),
          populationWorking: auditLogs.length > 0 ? !!auditLogs[0].userId : 'No logs to test'
        }
      });
    } catch (error) {
      results.push({
        test: 'ContractAuditLog relationships',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Test static methods
    try {
      const contractStatus = await ContractSigningToken.findByUser(userId);
      const auditTrail = await ContractAuditLog.getAuditTrail(userId);
      
      results.push({
        test: 'Static methods',
        success: true,
        data: {
          findByUserWorks: !!contractStatus || contractStatus === null,
          getAuditTrailWorks: Array.isArray(auditTrail),
          auditTrailCount: auditTrail?.length || 0
        }
      });
    } catch (error) {
      results.push({
        test: 'Static methods',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    const overallSuccess = results.every(result => result.success);

    return NextResponse.json({
      success: overallSuccess,
      message: overallSuccess ? 'All relationship tests passed' : 'Some relationship tests failed',
      data: {
        testResults: results,
        summary: {
          totalTests: results.length,
          passedTests: results.filter(r => r.success).length,
          failedTests: results.filter(r => !r.success).length
        }
      }
    });

  } catch (error: any) {
    console.error('Error testing relationships:', error);
    return NextResponse.json({
      success: false,
      message: 'Relationship test failed with exception',
      error: error.message
    });
  }
}
