import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import ContractAuditLog from '@/models/ContractAuditLog';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const results = [];

    // Clean up test users (those with debug email patterns)
    try {
      const testUserResult = await User.deleteMany({
        email: { $regex: /^test-\d+@debug\.local$/ }
      });
      
      results.push({
        operation: 'Delete test users',
        success: true,
        deletedCount: testUserResult.deletedCount
      });
    } catch (error) {
      results.push({
        operation: 'Delete test users',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Clean up test audit logs (those with testData metadata)
    try {
      const testAuditResult = await ContractAuditLog.deleteMany({
        'metadata.testData': true
      });
      
      results.push({
        operation: 'Delete test audit logs',
        success: true,
        deletedCount: testAuditResult.deletedCount
      });
    } catch (error) {
      results.push({
        operation: 'Delete test audit logs',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Clean up orphaned contract tokens (where user no longer exists)
    try {
      // Find contract tokens where the referenced user doesn't exist
      const contractTokens = await ContractSigningToken.find({}).lean();
      let orphanedCount = 0;
      
      for (const token of contractTokens) {
        const userExists = await User.findById(token.userId);
        if (!userExists) {
          await ContractSigningToken.deleteOne({ _id: token._id });
          orphanedCount++;
        }
      }
      
      results.push({
        operation: 'Delete orphaned contract tokens',
        success: true,
        deletedCount: orphanedCount
      });
    } catch (error) {
      results.push({
        operation: 'Delete orphaned contract tokens',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Clean up contract fields from users that don't have contract tokens
    try {
      const usersWithContractFields = await User.find({
        $or: [
          { contractSigningToken: { $exists: true, $ne: null } },
          { contractSignedAt: { $exists: true } },
          { contractStartDate: { $exists: true } },
          { contractAdminSignedAt: { $exists: true } },
          { contractCompletedAt: { $exists: true } },
          { contractSignatureData: { $exists: true } },
          { contractAdminSignatureData: { $exists: true } }
        ]
      });

      let cleanedUsers = 0;
      for (const user of usersWithContractFields) {
        const hasContractToken = await ContractSigningToken.findOne({ userId: user._id });
        if (!hasContractToken) {
          await User.updateOne(
            { _id: user._id },
            {
              $unset: {
                contractSigningToken: "",
                contractSignedAt: "",
                contractStartDate: "",
                contractAdminSignedAt: "",
                contractCompletedAt: "",
                contractSignatureData: "",
                contractAdminSignatureData: ""
              }
            }
          );
          cleanedUsers++;
        }
      }
      
      results.push({
        operation: 'Clean orphaned user contract fields',
        success: true,
        cleanedCount: cleanedUsers
      });
    } catch (error) {
      results.push({
        operation: 'Clean orphaned user contract fields',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    const overallSuccess = results.every(result => result.success);
    const totalDeleted = results.reduce((sum, result) => 
      sum + (result.deletedCount || result.cleanedCount || 0), 0
    );

    return NextResponse.json({
      success: overallSuccess,
      message: `Cleanup completed. ${totalDeleted} items cleaned up.`,
      data: {
        operations: results,
        summary: {
          totalOperations: results.length,
          successfulOperations: results.filter(r => r.success).length,
          failedOperations: results.filter(r => !r.success).length,
          totalItemsDeleted: totalDeleted
        }
      }
    });

  } catch (error: any) {
    console.error('Error during cleanup:', error);
    return NextResponse.json({
      success: false,
      message: 'Cleanup failed with exception',
      error: error.message
    });
  }
}
