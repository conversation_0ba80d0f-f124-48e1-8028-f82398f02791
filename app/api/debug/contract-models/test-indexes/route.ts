import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';

export async function POST(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const results = [];

    // Test ContractSigningToken indexes
    try {
      const contractTokenIndexes = await mongoose.connection.collection('contractsigningtokens').indexes();
      results.push({
        collection: 'contractsigningtokens',
        indexes: contractTokenIndexes,
        count: contractTokenIndexes.length
      });
    } catch (error) {
      results.push({
        collection: 'contractsigningtokens',
        error: 'Collection may not exist yet',
        indexes: []
      });
    }

    // Test ContractAuditLog indexes
    try {
      const auditLogIndexes = await mongoose.connection.collection('contractauditlogs').indexes();
      results.push({
        collection: 'contractauditlogs',
        indexes: auditLogIndexes,
        count: auditLogIndexes.length
      });
    } catch (error) {
      results.push({
        collection: 'contractauditlogs',
        error: 'Collection may not exist yet',
        indexes: []
      });
    }

    // Test User contract-related indexes
    try {
      const userIndexes = await mongoose.connection.collection('users').indexes();
      const contractRelatedIndexes = userIndexes.filter(idx => 
        idx.name && (
          idx.name.includes('contractSigningToken') || 
          idx.name.includes('contractCompletedAt')
        )
      );
      results.push({
        collection: 'users',
        contractRelatedIndexes,
        allIndexes: userIndexes,
        contractIndexCount: contractRelatedIndexes.length
      });
    } catch (error) {
      results.push({
        collection: 'users',
        error: error instanceof Error ? error.message : 'Unknown error',
        indexes: []
      });
    }

    // Performance test - simple query timing
    const performanceTests = [];
    
    try {
      const start = Date.now();
      await mongoose.connection.collection('users').findOne({ contractSigningToken: { $exists: true } });
      const duration = Date.now() - start;
      performanceTests.push({
        test: 'User contract token lookup',
        duration: `${duration}ms`,
        success: true
      });
    } catch (error) {
      performanceTests.push({
        test: 'User contract token lookup',
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Index performance test completed',
      data: {
        indexResults: results,
        performanceTests
      }
    });

  } catch (error: any) {
    console.error('Error testing indexes:', error);
    return NextResponse.json({
      success: false,
      message: 'Index test failed with exception',
      error: error.message
    });
  }
}
