import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import { validateContractToken } from '@/lib/utils/contract-token-utils';

export async function POST(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { token } = await request.json();

    if (!token) {
      return NextResponse.json({
        success: false,
        message: 'Token validation test failed',
        error: 'No token provided for testing'
      });
    }

    const result = await validateContractToken(token);

    return NextResponse.json({
      success: result.isValid,
      message: result.isValid ? 'Token validation successful' : 'Token validation failed',
      data: {
        isValid: result.isValid,
        tokenData: result.tokenData,
        user: result.user,
        error: result.error
      }
    });

  } catch (error: any) {
    console.error('Error testing token validation:', error);
    return NextResponse.json({
      success: false,
      message: 'Token validation test failed with exception',
      error: error.message
    });
  }
}
