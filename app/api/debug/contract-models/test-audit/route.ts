import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import ContractAuditLog from '@/models/ContractAuditLog';

export async function POST(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { userId, token } = await request.json();

    if (!userId || !token) {
      return NextResponse.json({
        success: false,
        message: 'Audit logging test failed',
        error: 'userId and token are required for testing'
      });
    }

    // Test audit logging
    const auditEntry = await ContractAuditLog.logAction(
      userId,
      token,
      'user_visited',
      session.user.id,
      '127.0.0.1',
      'Debug Test User Agent',
      { testData: true, timestamp: new Date() }
    );

    // Get recent audit logs for this user
    const recentLogs = await ContractAuditLog.find({ userId })
      .sort({ timestamp: -1 })
      .limit(5)
      .populate('performedBy', 'name email')
      .lean();

    return NextResponse.json({
      success: true,
      message: 'Audit logging test successful',
      data: {
        newAuditEntry: auditEntry,
        recentLogs
      }
    });

  } catch (error: any) {
    console.error('Error testing audit logging:', error);
    return NextResponse.json({
      success: false,
      message: 'Audit logging test failed with exception',
      error: error.message
    });
  }
}
