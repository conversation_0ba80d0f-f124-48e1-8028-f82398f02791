import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import ContractAuditLog from '@/models/ContractAuditLog';
import User from '@/models/User';

export async function GET(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get contract token statistics
    const contractTokens = await ContractSigningToken.countDocuments();
    const completedContracts = await ContractSigningToken.countDocuments({ isCompleted: true });
    
    // Get audit log count
    const auditLogs = await ContractAuditLog.countDocuments();
    
    // Get users with contracts
    const usersWithContracts = await User.countDocuments({
      contractSigningToken: { $exists: true, $ne: null }
    });

    return NextResponse.json({
      contractTokens,
      auditLogs,
      usersWithContracts,
      completedContracts
    });

  } catch (error: any) {
    console.error('Error fetching contract model stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch stats', details: error.message },
      { status: 500 }
    );
  }
}
