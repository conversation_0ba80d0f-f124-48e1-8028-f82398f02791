import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import { createContractSigningToken } from '@/lib/utils/contract-token-utils';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { userId, adminId } = await request.json();

    // Validate user exists
    if (userId) {
      const user = await User.findById(userId);
      if (!user) {
        return NextResponse.json({
          success: false,
          message: 'Test failed: User not found',
          error: `User with ID ${userId} does not exist`
        });
      }
    }

    // Validate admin exists
    if (adminId) {
      const admin = await User.findById(adminId);
      if (!admin) {
        return NextResponse.json({
          success: false,
          message: 'Test failed: Admin not found',
          error: `Admin with ID ${adminId} does not exist`
        });
      }
    }

    // Use session user as admin if not provided
    const effectiveAdminId = adminId || session.user.id;
    
    // Create a test user if no userId provided
    let effectiveUserId = userId;
    if (!effectiveUserId) {
      const testUser = new User({
        name: `Test User ${Date.now()}`,
        email: `test-${Date.now()}@debug.local`,
        password: 'test-password',
        roles: [],
        isActive: true
      });
      await testUser.save();
      effectiveUserId = testUser._id.toString();
    }

    // Test contract creation
    const result = await createContractSigningToken(
      effectiveUserId,
      effectiveAdminId,
      new Date()
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Contract signing token created successfully',
        data: {
          token: result.token,
          userId: effectiveUserId,
          adminId: effectiveAdminId,
          contractSigningToken: result.contractSigningToken
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Contract creation failed',
        error: result.error
      });
    }

  } catch (error: any) {
    console.error('Error testing contract creation:', error);
    return NextResponse.json({
      success: false,
      message: 'Test failed with exception',
      error: error.message
    });
  }
}
