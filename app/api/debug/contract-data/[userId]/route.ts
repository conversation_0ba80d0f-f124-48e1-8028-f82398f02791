import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import ContractAuditLog from '@/models/ContractAuditLog';
import User from '@/models/User';
import { getContractStatus } from '@/lib/utils/contract-token-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { userId } = await params;

    // Get contract signing token
    const contractSigningToken = await ContractSigningToken.findOne({ userId })
      .populate('userId', 'name email')
      .populate('createdBy', 'name email')
      .lean();

    // Get audit logs
    const auditLogs = await ContractAuditLog.find({ userId })
      .populate('performedBy', 'name email')
      .sort({ timestamp: -1 })
      .limit(20)
      .lean();

    // Get contract status
    const contractStatus = await getContractStatus(userId);

    // Get user contract fields
    const user = await User.findById(userId).select(
      'contractSigningToken contractSignedAt contractStartDate contractAdminSignedAt contractCompletedAt contractSignatureData contractAdminSignatureData'
    ).lean();

    const userContractFields = {
      contractSigningToken: user?.contractSigningToken,
      contractSignedAt: user?.contractSignedAt,
      contractStartDate: user?.contractStartDate,
      contractAdminSignedAt: user?.contractAdminSignedAt,
      contractCompletedAt: user?.contractCompletedAt,
      hasContractSignatureData: !!user?.contractSignatureData,
      hasContractAdminSignatureData: !!user?.contractAdminSignatureData
    };

    return NextResponse.json({
      success: true,
      contractSigningToken,
      auditLogs,
      contractStatus,
      userContractFields
    });

  } catch (error: any) {
    console.error('Error fetching contract debug data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contract debug data', details: error.message },
      { status: 500 }
    );
  }
}
