import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import ContractSigningToken from '@/models/ContractSigningToken';
import { getContractSigningEmailTemplate } from '@/lib/utils/contract-email-templates';

export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
  }

  await dbConnect();

  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get user data
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get contract token
    const contractToken = await ContractSigningToken.findOne({ userId });
    if (!contractToken) {
      return NextResponse.json(
        { error: 'No contract token found for user' },
        { status: 404 }
      );
    }

    // Generate signing URL
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const signingUrl = `${baseUrl}/contract-signing/${contractToken.token}`;

    // Generate logo URL
    const logoUrl = `${baseUrl}/amq-logo.png`;

    // Generate email content
    const emailContent = getContractSigningEmailTemplate({ logoUrl })
      .replace(/{{userName}}/g, user.name)
      .replace(/{{signingLink}}/g, signingUrl)
      .replace(/{{contractStartDate}}/g, contractToken.contractStartDate?.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }) || 'Non définie');

    return NextResponse.json({
      success: true,
      emailContent,
      signingUrl,
      userName: user.name,
      contractStartDate: contractToken.contractStartDate
    });

  } catch (error: any) {
    console.error('Error generating email preview:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate email preview',
        details: error.message
      },
      { status: 500 }
    );
  }
}
