import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import { validateContractToken } from '@/lib/utils/contract-token-utils';

export async function POST(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { isValid: false, error: 'Token is required' },
        { status: 400 }
      );
    }

    const result = await validateContractToken(token);

    return NextResponse.json(result);

  } catch (error: any) {
    console.error('Error validating contract token:', error);
    return NextResponse.json(
      { isValid: false, error: 'Token validation failed', details: error.message },
      { status: 500 }
    );
  }
}
