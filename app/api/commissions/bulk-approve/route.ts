import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { COMMISSION_PERMISSIONS } from '@/types/commission-codes';
import CommissionModel from '@/models/Commission';
import { BulkApprovePayload } from '@/app/commissions/types';
import dbConnect from '@/lib/db';
import { getUserPermissions, getUserRoles } from '../../utils/server-permission-utils';
export async function PATCH(req: NextRequest) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if(session &&!session?.user.roles){
    session.user.roles = await getUserRoles(session);
  }
  // Check permission
  if (!session.user.permissions?.includes(COMMISSION_PERMISSIONS.APPROVE_COMMISSIONS)) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  let body: BulkApprovePayload;
  try {
    body = await req.json();
    if (!Array.isArray(body.commissionIds) || body.commissionIds.length === 0) {
      return NextResponse.json({ error: 'No commissionIds provided' }, { status: 400 });
    }
  } catch (e) {
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
  }
  try {
    // Update all commissions in one go
    const result = await CommissionModel.updateMany(
      { _id: { $in: body.commissionIds }, isApproved: false },
      {
        $set: {
          isApproved: true,
          approvedBy: session.user._id,
          approvedAt: new Date(),
          updatedAt: new Date(),
        },
      }
    );
    return NextResponse.json({ success: true, modifiedCount: result.modifiedCount });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to approve commissions' }, { status: 500 });
  }
} 