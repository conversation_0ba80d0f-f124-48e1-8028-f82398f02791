import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import Commission from '@/models/Commission';
import User from '@/models/User';
import Reservation from '@/models/Reservation';
import CommissionType from '@/models/CommissionType';
import { COMMISSION_PERMISSIONS } from '@/types/commission-codes';
import { ObjectId } from 'mongodb';
import { getUserPermissions, getUserRoles } from '../utils/server-permission-utils';
import * as RoleUtils from '@/lib/utils/role-utils';
export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if(session &&!session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
    }
    if(session &&!session?.user.roles){
      session.user.roles = await getUserRoles(session);
    }
    // Check if user has permission to view commissions
    const hasViewAllPermission = session.user.permissions?.includes(COMMISSION_PERMISSIONS.VIEW_COMMISSIONS);
    const hasViewOwnPermission = session.user.permissions?.includes(COMMISSION_PERMISSIONS.VIEW_OWN_COMMISSIONS);
    if (!hasViewAllPermission && !hasViewOwnPermission) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      );
    }

    // Get query parameters for filtering
    const { searchParams } = new URL(request.url);
    const isApproved = searchParams.get('isApproved');
    const userId = searchParams.get('userId');
    const commissionTypeId = searchParams.get('commissionTypeId');
    const createdAt = searchParams.get('createdAt');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const amountGt = searchParams.get('amountGt');
    const amountLt = searchParams.get('amountLt');
    const amountEq = searchParams.get('amountEq');
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit') || '50');
    const page = parseInt(searchParams.get('page') || '1');
    const skip = (page - 1) * limit;
    const branchId = searchParams.get('branchId');

    // Check if user is SuperAdmin or BranchesAdmin
    const isAdmin = RoleUtils.hasAdminPrivileges(session);
    // Build query filters
    const filter: any = {};

    // If user is not SuperAdmin or BranchesAdmin, apply restrictions
    if (!hasViewAllPermission) {
      if(!hasViewOwnPermission){
        return NextResponse.json(
          { error: 'Permission denied' },
          { status: 403 }
        );
      }
      // If userId parameter is provided, check if it matches the current user's ID
      if (userId && userId !== session.user.id) {
        return NextResponse.json(
          { error: 'You can only view your own commissions' },
          { status: 403 }
        );
      }
      // Force filter to only show the user's own approved commissions
      filter.userId = session.user.id;
      filter.isApproved = true;
    } else {

      if (isApproved !== null) {
        filter.isApproved = isApproved === 'true';
      }
      if (userId) {
        // Convert userId string to ObjectId for proper matching
        try {
          filter.userId = new ObjectId(userId);
        } catch (error) {
          console.error('Invalid userId format:', userId);
          return NextResponse.json(
            { error: 'Invalid userId format' },
            { status: 400 }
          );
        }
      }
    }
    // Branch filter: if branchId is provided and not 'all', filter users by branch
    let branchUserIds: string[] | null = null;
    if (branchId && branchId !== 'all') {
      // Find all users with this branchId in their branchIds
      const users = await User.find({ branchIds: branchId }, { _id: 1 }).lean();
      branchUserIds = users.map(u => u._id.toString());

      // If both userId and branchId filters are provided, combine them
      if (userId && filter.userId) {
        // Check if the specified userId is in the branch's user list
        if (branchUserIds.includes(userId)) {
          // Keep the userId filter as is since it's valid for this branch
          // filter.userId already set to ObjectId(userId) above
        } else {
          // The specified user is not in this branch, so no results should be returned
          filter.userId = { $in: [] }; // This will return no results
        }
      } else {
        // Only branch filter is provided, filter to branch users
        // Convert string IDs to ObjectIds for proper matching
        const branchUserObjectIds = branchUserIds.map(id => {
          try {
            return new ObjectId(id);
          } catch (error) {
            console.error('Invalid branchUserId format:', id);
            return null;
          }
        }).filter(Boolean);
        filter.userId = { $in: branchUserObjectIds };
      }
    }

    // Apply common filters for all users
    if (commissionTypeId) {
      filter.commissionTypeId = commissionTypeId;
    }
    if (createdAt) {
      // Parse the date correctly accounting for timezone differences
      // Use the date as provided in YYYY-MM-DD format to create a date in local timezone
      const dateString = createdAt.replace(/-/g, '/');
      const startDate = new Date(dateString);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(dateString);
      endDate.setHours(23, 59, 59, 999);

      filter.createdAt = {
        $gte: startDate,
        $lte: endDate
      };
    } else if (startDate || endDate) {
      filter.createdAt = {};

      if (startDate) {
        const parsedStartDate = new Date(startDate.replace(/-/g, '/'));
        parsedStartDate.setHours(0, 0, 0, 0);
        filter.createdAt.$gte = parsedStartDate;
      }

      if (endDate) {
        const parsedEndDate = new Date(endDate.replace(/-/g, '/'));
        parsedEndDate.setHours(23, 59, 59, 999);
        filter.createdAt.$lte = parsedEndDate;
      }
    }

    // Amount filters
    if (amountEq) {
      filter.amount = parseFloat(amountEq);
    } else {
      if (amountGt) {
        filter.amount = { ...filter.amount, $gte: parseFloat(amountGt) };
      }
      if (amountLt) {
        filter.amount = { ...filter.amount, $lte: parseFloat(amountLt) };
      }
    }

    // Get total count for pagination using aggregation to exclude invalid reservations
    const totalCountPipeline = [
      { $match: filter },
      {
        $lookup: {
          from: "reservations",
          localField: "reservationId",
          foreignField: "_id",
          as: "reservation"
        }
      },
      {
        $match: {
          "reservation": { $ne: [] }, // Reservation must exist
          "reservation.isDeleted": { $ne: true } // Reservation must not be deleted
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "user",
          pipeline: [{ $project: { name: 1, email: 1 } }]
        }
      },
      {
        $lookup: {
          from: "commissiontypes",
          localField: "commissionTypeId",
          foreignField: "_id",
          as: "commissionType",
          pipeline: [{ $project: { name: 1 } }]
        }
      },
      ...(search ? [{
        $match: {
          $or: [
            { "user.name": { $regex: search, $options: "i" } },
            { "user.email": { $regex: search, $options: "i" } },
            { "reservation.customerInfo.client1Name": { $regex: search, $options: "i" } },
            { "reservation.customerInfo.client2Name": { $regex: search, $options: "i" } },
            { "reservation.customerInfo.companyName": { $regex: search, $options: "i" } }
          ]
        }
      }] : []),
      { $count: "total" }
    ];

    const totalResult = await Commission.aggregate(totalCountPipeline);
    const total = totalResult.length > 0 ? totalResult[0].total : 0;

    // Get commissions with populated references, filtering out those with invalid reservations
    const commissionsPipeline = [
      { $match: filter },
      {
        $lookup: {
          from: "reservations",
          localField: "reservationId",
          foreignField: "_id",
          as: "reservation"
        }
      },
      {
        $match: {
          "reservation": { $ne: [] }, // Reservation must exist
          "reservation.isDeleted": { $ne: true } // Reservation must not be deleted
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "userId",
          pipeline: [{ $project: { name: 1, email: 1, branchIds: 1 } }]
        }
      },
      {
        $lookup: {
          from: "commissiontypes",
          localField: "commissionTypeId",
          foreignField: "_id",
          as: "commissionTypeId",
          pipeline: [{ $project: { name: 1, amount: 1 } }]
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "approvedBy",
          foreignField: "_id",
          as: "approvedBy",
          pipeline: [{ $project: { name: 1, email: 1 } }]
        }
      },
      ...(search ? [{
        $match: {
          $or: [
            { "userId.name": { $regex: search, $options: "i" } },
            { "userId.email": { $regex: search, $options: "i" } },
            { "reservation.customerInfo.client1Name": { $regex: search, $options: "i" } },
            { "reservation.customerInfo.client2Name": { $regex: search, $options: "i" } },
            { "reservation.customerInfo.companyName": { $regex: search, $options: "i" } }
          ]
        }
      }] : []),
      { $sort: { createdAt: -1 as -1 } },
      { $skip: skip },
      { $limit: limit },
      {
        $addFields: {
          userId: { $arrayElemAt: ["$userId", 0] },
          reservationId: {
            $mergeObjects: [
              { $arrayElemAt: ["$reservation", 0] },
              {
                _id: { $arrayElemAt: ["$reservation._id", 0] },
                status: { $arrayElemAt: ["$reservation.status", 0] },
                customerInfo: { $arrayElemAt: ["$reservation.customerInfo", 0] },
                preferences: { $arrayElemAt: ["$reservation.preferences", 0] }
              }
            ]
          },
          commissionTypeId: { $arrayElemAt: ["$commissionTypeId", 0] },
          approvedBy: { $arrayElemAt: ["$approvedBy", 0] }
        }
      },
      {
        $project: {
          reservation: 0 // Remove the reservation array, keep only reservationId
        }
      }
    ];

    const validCommissions = await Commission.aggregate(commissionsPipeline);

    // Fetch all commission types
    const commissionTypes = await CommissionType.find({})
      .select('_id name amount')
      .lean();

    return NextResponse.json({
      commissions: validCommissions,
      commissionTypes,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('GET commissions error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch commissions' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if(session &&!session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
    }
    // Check if user has permission to approve commissions
    const hasPermission = session.user.permissions?.includes(COMMISSION_PERMISSIONS.APPROVE_COMMISSIONS);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      );
    }

    const body = await request.json();

    if (!body.commissionId) {
      return NextResponse.json(
        { error: 'Commission ID is required' },
        { status: 400 }
      );
    }

    const commissionId = body.commissionId;
    const isApproved = body.isApproved === true;

    // Check if the commission exists
    const commission = await Commission.findById(commissionId);
    if (!commission) {
      return NextResponse.json(
        { error: 'Commission not found' },
        { status: 404 }
      );
    }

    // Update the commission
    commission.isApproved = isApproved;
    commission.approvedBy = isApproved ? new ObjectId(session.user.id) : undefined;
    commission.approvedAt = isApproved ? new Date() : undefined;
    await commission.save();

    // Return the updated commission with populated references
    const updatedCommission = await Commission.findById(commissionId)
      .populate('userId', 'name email')
      .populate('reservationId', 'customerInfo.client1Name preferences.visitDate preferences.visitTime status')
      .populate('commissionTypeId', 'name amount')
      .populate('approvedBy', 'name email')
      .lean();
    return NextResponse.json(updatedCommission);
  } catch (error) {
    console.error('PATCH commission error:', error);
    return NextResponse.json(
      { error: 'Failed to update commission' },
      { status: 500 }
    );
  }
}