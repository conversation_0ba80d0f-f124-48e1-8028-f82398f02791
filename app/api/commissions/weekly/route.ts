import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import Commission from '@/models/Commission';
import Bonus from '@/models/Bonus';
import mongoose from 'mongoose';
import { COMMISSION_PERMISSIONS } from '@/types/commission-codes';
import { getUserPermissions, getUserRoles } from '../../utils/server-permission-utils';
import { createQuebecDate, QUEBEC_TIMEZONE } from '@/app/commissions/weekly/utils/dateUtils';
// Add interfaces for type safety
interface DayData {
  dayOfWeek: number;
  count: number;
  totalAmount: number;
  commissions: Array<{
    _id: string;
    amount: number;
    createdAt: Date;
  }>;
}

interface UserAggregateResult {
  _id: string;
  userName: string;
  userEmail: string;
  days: DayData[];
  totalCommissions: number;
  totalAmount: number;
}

interface DailyTotal {
  _id: number;
  count: number;
  totalAmount: number;
}

interface SaveBonusData {
  bonuses: Array<{
    userId: string;
    date: string;
    amount: number;
    dayOfWeek: number;
  }>;
}

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    // Check if user has permission to view commissions
    const hasPermission = session.user.permissions?.includes(COMMISSION_PERMISSIONS.VIEW_COMMISSIONS);

    // Check if user is SuperAdmin
    const isSuperAdmin = session.user.roles?.some((role: any) => {
      const roleId = typeof role === 'string' ? role : role._id?.toString();
      return roleId === '67add3214badd3283e873329';
    });

    if (!hasPermission && !isSuperAdmin) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      );
    }

    // Get query parameters for date range and branch filter
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const branchId = searchParams.get('branchId');

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'startDate and endDate parameters are required' },
        { status: 400 }
      );
    }

    // Parse dates and set to beginning and end of day in Quebec timezone
    // This ensures commission filtering respects Quebec local time (EST/EDT)
    const parsedStartDate = createQuebecDate(startDate, false);
    const parsedEndDate = createQuebecDate(endDate, true);

    // Build the base query filter
    const filter: any = {
      createdAt: {
        $gte: parsedStartDate,
        $lte: parsedEndDate
      },
      isApproved: true
    };

    // Execute the aggregation pipeline to get daily counts and totals
    const dailyAggregate = await Commission.aggregate([
      // Match documents within the date range
      { $match: filter },

      // Lookup reservation to validate it exists and is not deleted
      {
        $lookup: {
          from: "reservations",
          localField: "reservationId",
          foreignField: "_id",
          as: "reservation"
        }
      },

      // Filter out commissions with invalid reservations (deleted or non-existent)
      {
        $match: {
          "reservation": { $ne: [] }, // Reservation must exist
          "reservation.isDeleted": { $ne: true } // Reservation must not be deleted
        }
      },

      // Add a field for the day of week (1-7, Sunday-Saturday) using Quebec timezone
      {
        $addFields: {
          dayOfWeek: {
            $dayOfWeek: {
              date: "$createdAt",
              timezone: QUEBEC_TIMEZONE
            }
          } // MongoDB uses 1-7 (Sunday-Saturday)
        }
      },

      // Group by userId and dayOfWeek
      {
        $group: {
          _id: {
            userId: "$userId",
            dayOfWeek: "$dayOfWeek"
          },
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
          commissions: {
            $push: {
              _id: "$_id",
              amount: "$amount",
              createdAt: "$createdAt"
            }
          }
        }
      },

      // Group by userId to collect all days
      {
        $group: {
          _id: "$_id.userId",
          days: {
            $push: {
              dayOfWeek: "$_id.dayOfWeek",
              count: "$count",
              totalAmount: "$totalAmount",
              commissions: "$commissions"
            }
          },
          totalCommissions: { $sum: "$count" },
          totalAmount: { $sum: "$totalAmount" }
        }
      },

      // Lookup to get user details
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "userDetails"
        }
      },

      // Unwind the user details array
      {
        $unwind: {
          path: "$userDetails",
          preserveNullAndEmptyArrays: true
        }
      },

      // Filter by branch if branchId is provided and not "all"
      ...(branchId && branchId !== 'all' ? [{
        $match: {
          "userDetails.branchIds": { $in: [new mongoose.Types.ObjectId(branchId)] }
        }
      }] : []),

      // Project the final output structure
      {
        $project: {
          _id: 1,
          userName: "$userDetails.name",
          userEmail: "$userDetails.email",
          days: 1,
          totalCommissions: 1,
          totalAmount: 1
        }
      },

      // Sort by userName
      { $sort: { userName: 1 } }
    ]) as UserAggregateResult[];

    // Fetch existing bonuses for this date range, filtered by branch if needed
    let bonusQuery: any = {
      date: {
        $gte: parsedStartDate,
        $lte: parsedEndDate
      }
    };

    // If branch filtering is needed, get user IDs from the selected branch first
    if (branchId && branchId !== 'all') {
      const User = mongoose.model('User');
      const branchUsers = await User.find({
        branchIds: { $in: [new mongoose.Types.ObjectId(branchId)] }
      }).select('_id').lean();

      const branchUserIds = branchUsers.map(user => user._id);
      bonusQuery.userId = { $in: branchUserIds };
    }

    const bonuses = await Bonus.find(bonusQuery).lean();

    // For the column totals (all users' totals per day) - filter by branch if needed
    const dailyTotalsAggregation: any[] = [
      // Match documents within the date range
      { $match: filter },

      // Lookup reservation to validate it exists and is not deleted
      {
        $lookup: {
          from: "reservations",
          localField: "reservationId",
          foreignField: "_id",
          as: "reservation"
        }
      },

      // Filter out commissions with invalid reservations (deleted or non-existent)
      {
        $match: {
          "reservation": { $ne: [] }, // Reservation must exist
          "reservation.isDeleted": { $ne: true } // Reservation must not be deleted
        }
      },

      // Add a field for the day of week (1-7, Sunday-Saturday) using Quebec timezone
      {
        $addFields: {
          dayOfWeek: {
            $dayOfWeek: {
              date: "$createdAt",
              timezone: QUEBEC_TIMEZONE
            }
          }
        }
      }
    ];

    // Add branch filtering for daily totals if branchId is provided
    if (branchId && branchId !== 'all') {
      dailyTotalsAggregation.push(
        // Lookup user details for branch filtering
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "userDetails"
          }
        },
        // Unwind user details
        {
          $unwind: {
            path: "$userDetails",
            preserveNullAndEmptyArrays: true
          }
        },
        // Filter by branch
        {
          $match: {
            "userDetails.branchIds": { $in: [new mongoose.Types.ObjectId(branchId)] }
          }
        }
      );
    }

    // Add final aggregation stages
    dailyTotalsAggregation.push(
      // Group by dayOfWeek
      {
        $group: {
          _id: "$dayOfWeek",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" }
        }
      },

      // Sort by dayOfWeek
      { $sort: { _id: 1 } }
    );

    const dailyTotals = await Commission.aggregate(dailyTotalsAggregation) as DailyTotal[];

    // Format the response with properly organized data
    const response = {
      users: dailyAggregate.map(user => {
        // Create an array for all 7 days of the week (Sunday-Saturday)
        const daysArray = Array(7).fill(null).map((_, index) => {
          // MongoDB dayOfWeek is 1-7 where 1=Sunday, so we adjust the index
          const dayIndex = index + 1;

          // Find the day data for this user and day
          const dayData = user.days.find((day: DayData) => day.dayOfWeek === dayIndex);

          // Find bonuses for this user and day
          // We need to adjust the dayOfWeek to match our internal 0-6 range
          const adjustedDayOfWeek = dayIndex - 1;
          const userBonus = bonuses.find(
            bonus =>
              bonus.userId.toString() === user._id.toString() &&
              bonus.dayOfWeek === adjustedDayOfWeek
          );

          const bonusAmount = userBonus ? userBonus.amount : 0;

          if (dayData) {
            return {
              dayOfWeek: dayData.dayOfWeek,
              count: dayData.count,
              amount: dayData.totalAmount,
              bonus: bonusAmount,
              total: dayData.totalAmount + bonusAmount
            };
          }

          // Return a default empty day if no data exists
          return {
            dayOfWeek: dayIndex,
            count: 0,
            amount: 0,
            bonus: bonusAmount,
            total: bonusAmount // total = amount + bonus, with amount=0
          };
        });

        // Calculate total bonus across all days
        const totalBonus = daysArray.reduce((sum, day) => sum + day.bonus, 0);

        return {
          userId: user._id,
          userName: user.userName || "Unknown",
          userEmail: user.userEmail,
          days: daysArray,
          totalCommissions: user.totalCommissions,
          totalAmount: user.totalAmount,
          totalBonus: totalBonus,
          grandTotal: user.totalAmount + totalBonus
        };
      }),

      // Daily totals for all users (including bonuses)
      dailyTotals: Array(7).fill(null).map((_, index) => {
        // MongoDB dayOfWeek is 1-7 where 1=Sunday, so we adjust the index
        const dayIndex = index + 1;

        // Find the total data for this day
        const totalData = dailyTotals.find(total => total._id === dayIndex);

        // Calculate total bonus for this day
        const adjustedDayOfWeek = dayIndex - 1;
        const dayBonuses = bonuses.filter(bonus => bonus.dayOfWeek === adjustedDayOfWeek);
        const totalDayBonus = dayBonuses.reduce((sum, bonus) => sum + bonus.amount, 0);

        if (totalData) {
          return {
            dayOfWeek: totalData._id,
            count: totalData.count,
            amount: totalData.totalAmount,
            bonus: totalDayBonus,
            total: totalData.totalAmount + totalDayBonus
          };
        }

        // Return a default empty day if no data exists
        return {
          dayOfWeek: dayIndex,
          count: 0,
          amount: 0,
          bonus: totalDayBonus,
          total: totalDayBonus
        };
      }),

      // Include bonuses data in the response
      bonuses: bonuses.map(bonus => ({
        userId: bonus.userId.toString(),
        date: bonus.date.toISOString().split('T')[0],
        amount: bonus.amount,
        dayOfWeek: bonus.dayOfWeek
      }))
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching weekly commission data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch weekly commission data' },
      { status: 500 }
    );
  }
}

// POST handler to save bonuses
export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    if (session && !session?.user.roles) {
      session.user.roles = await getUserRoles(session);
    }
    // Check if user has permission to manage commissions
    const hasPermission = session.user.permissions?.includes(COMMISSION_PERMISSIONS.MANAGE_COMMISSIONS);

    // Check if user is SuperAdmin
    const isSuperAdmin = session.user.roles?.some((role: any) => {
      const roleId = typeof role === 'string' ? role : role._id?.toString();
      return roleId === '67add3214badd3283e873329';
    });

    if (!hasPermission && !isSuperAdmin) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const data = await request.json() as SaveBonusData;

    if (!data.bonuses || !Array.isArray(data.bonuses)) {
      return NextResponse.json(
        { error: 'Invalid request: bonuses array is required' },
        { status: 400 }
      );
    }

    // Process each bonus entry
    for (const bonus of data.bonuses) {
      // Validate required fields
      if (!bonus.userId || !bonus.date || bonus.dayOfWeek === undefined) {
        console.warn('Skipping invalid bonus entry:', bonus);
        continue;
      }

      // Create a Quebec timezone Date object for the bonus date
      const bonusDate = createQuebecDate(bonus.date, false);

      // Use findOneAndUpdate with upsert to avoid duplicates
      await Bonus.findOneAndUpdate(
        {
          userId: bonus.userId,
          date: bonusDate,
          dayOfWeek: bonus.dayOfWeek
        },
        {
          userId: bonus.userId,
          date: bonusDate,
          amount: bonus.amount,
          dayOfWeek: bonus.dayOfWeek
        },
        {
          upsert: true,
          new: true
        }
      );
    }

    return NextResponse.json({ success: true, message: 'Bonuses saved successfully' });
  } catch (error) {
    console.error('Error saving bonuses:', error);
    return NextResponse.json(
      { error: 'Failed to save bonuses' },
      { status: 500 }
    );
  }
}