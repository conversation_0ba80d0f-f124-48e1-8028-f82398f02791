import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import Bonus from '@/models/Bonus';
import { Types } from 'mongoose';
import * as RoleUtils from '@/lib/utils/role-utils';
import { getUserPermissions, getUserRoles } from '../../utils/server-permission-utils';
// Define the Bonus document type
interface BonusDocument {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  date: Date;
  amount: number;
  dayOfWeek: number;
  createdAt: Date;
  updatedAt: Date;
}

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    if(session &&!session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
    }
    if(session &&!session?.user.roles){
      session.user.roles = await getUserRoles(session);
    }
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has admin privileges (SuperAdmin or BranchesAdmin)
    const hasAccess = RoleUtils.hasAdminPrivileges(session);

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Access denied. Only administrators can access this resource.' },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build query filter
    const filter: any = {};

    // Add userId filter if provided
    if (userId) {
      filter.userId = userId;
    }

    // Add date range filter if provided
    // Use the same date parsing logic as commissions for consistency
    if (startDate || endDate) {
      filter.date = {};
      if (startDate) {
        const parsedStartDate = new Date(startDate.replace(/-/g, '/'));
        parsedStartDate.setHours(0, 0, 0, 0);
        filter.date.$gte = parsedStartDate;
      }
      if (endDate) {
        const parsedEndDate = new Date(endDate.replace(/-/g, '/'));
        parsedEndDate.setHours(23, 59, 59, 999);
        filter.date.$lte = parsedEndDate;
      }
    }

    // Fetch bonuses with the filter
    const bonuses = await Bonus.find(filter).sort({ date: -1 }).lean() as unknown as BonusDocument[];

    // Format bonuses for the response
    const formattedBonuses = bonuses.map(bonus => ({
      _id: bonus._id.toString(),
      userId: bonus.userId.toString(),
      date: bonus.date.toISOString().split('T')[0],
      amount: bonus.amount,
      dayOfWeek: bonus.dayOfWeek,
      createdAt: bonus.createdAt,
      updatedAt: bonus.updatedAt
    }));

    return NextResponse.json({ bonuses: formattedBonuses });
  } catch (error) {
    console.error('Error fetching bonuses:', error);
    return NextResponse.json(
      { error: 'Failed to fetch bonuses. Please try again later.' },
      { status: 500 }
    );
  }
}