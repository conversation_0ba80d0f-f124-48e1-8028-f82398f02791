import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import AuditLog from '@/models/AuditLog';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserPermissions } from '../../utils/server-permission-utils';
import { AUDIT_PERMISSIONS } from '@/types/permission-codes';

/**
 * Get all unique actions from audit logs for filter dropdown
 */
export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    // Check for audit log access permission
    const hasAuditAccess = session.user.permissions?.some((permission: string) =>
      permission === AUDIT_PERMISSIONS.ACCESS_AUDIT_LOGS
    );

    if (!hasAuditAccess) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get all unique actions from the database
    const actions = await AuditLog.distinct('action');
    
    // Sort actions alphabetically
    actions.sort();

    // Create a mapping of actions to user-friendly labels
    const actionLabels: Record<string, string> = {
      // User Actions
      'USER_LOGIN': 'User Login',
      'USER_LOGOUT': 'User Logout',
      'USER_CREATED': 'User Created',
      'USER_UPDATED': 'User Updated',
      'USER_DELETED': 'User Deleted',
      
      // Invoice Actions
      'INVOICE_CREATED': 'Invoice Created',
      'INVOICE_UPDATED': 'Invoice Updated',
      'INVOICE_DELETED': 'Invoice Deleted',
      'INVOICE_ARCHIVED': 'Invoice Archived',
      'INVOICE_UNARCHIVED': 'Invoice Unarchived',
      'INVOICE_STATUS_CHANGED': 'Invoice Status Changed',
      'INVOICE_SIGNED': 'Invoice Signed',
      'INVOICE_EMAIL_SENT': 'Invoice Email Sent',
      'INVOICE_PDF_GENERATED': 'Invoice PDF Generated',
      
      // Invoice Item Actions
      'INVOICE_ITEM_CREATED': 'Invoice Item Created',
      'INVOICE_ITEM_UPDATED': 'Invoice Item Updated',
      'INVOICE_ITEM_DELETED': 'Invoice Item Deleted',
      
      // Reservation Actions
      'RESERVATION_CREATED': 'Reservation Created',
      'RESERVATION_UPDATED': 'Reservation Updated',
      'RESERVATION_DELETED': 'Reservation Deleted',
      'RESERVATION_STATUS_CHANGED': 'Reservation Status Changed',
      
      // Commission Actions
      'COMMISSION_IMPORTED': 'Commission Imported',
      'BONUS_IMPORTED': 'Bonus Imported',
      'MASS_INVOICE_GENERATED': 'Mass Invoice Generated',
      
      // System Actions
      'SYSTEM_BACKUP': 'System Backup',
      'SYSTEM_RESTORE': 'System Restore',
      'BULK_OPERATION': 'Bulk Operation',
      'DATA_EXPORT': 'Data Export',
      'DATA_IMPORT': 'Data Import',
      
      // Generic Actions
      'CREATED': 'Created',
      'UPDATED': 'Updated',
      'DELETED': 'Deleted',
      'VIEWED': 'Viewed',
      'EXPORTED': 'Exported'
    };

    // Map actions to include labels and categories
    const actionOptions = actions.map(action => ({
      value: action,
      label: actionLabels[action] || action.replace(/_/g, ' '),
      category: getActionCategory(action)
    }));

    // Group actions by category
    const groupedActions = actionOptions.reduce((acc, action) => {
      if (!acc[action.category]) {
        acc[action.category] = [];
      }
      acc[action.category].push(action);
      return acc;
    }, {} as Record<string, typeof actionOptions>);

    return NextResponse.json({
      actions: actionOptions,
      groupedActions,
      totalActions: actions.length
    });

  } catch (error) {
    console.error('Error fetching audit actions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Categorize actions for better organization
 */
function getActionCategory(action: string): string {
  if (action.startsWith('USER_')) return 'User Management';
  if (action.startsWith('INVOICE_')) return 'Invoice Management';
  if (action.startsWith('RESERVATION_')) return 'Reservation Management';
  if (action.startsWith('COMMISSION_') || action.startsWith('BONUS_') || action.includes('MASS_INVOICE')) return 'Financial Operations';
  if (action.startsWith('SYSTEM_') || action.includes('BULK_') || action.includes('DATA_')) return 'System Operations';
  return 'General Actions';
}
