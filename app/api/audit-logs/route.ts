import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import AuditLog from '@/models/AuditLog';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserPermissions } from '../utils/server-permission-utils';
import { canUserAccessBilling } from '@/lib/utils/permissions-utils';
import { AUDIT_PERMISSIONS } from '@/types/permission-codes';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    // Check for audit log access permission
    const hasAuditAccess = session.user.permissions?.some((permission: string) =>
      permission === AUDIT_PERMISSIONS.ACCESS_AUDIT_LOGS
    );

    if (!hasAuditAccess) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100); // Max 100 per page
    const entityType = searchParams.get('entityType');
    const entityId = searchParams.get('entityId');
    const action = searchParams.get('action');
    const userId = searchParams.get('userId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const success = searchParams.get('success');

    // Build query
    const query: any = {};

    if (entityType) {
      query.entityType = entityType;
    }

    if (entityId) {
      query.entityId = entityId;
    }

    if (action) {
      query.action = action;
    }

    if (userId) {
      query.userId = userId;
    }

    if (success !== null && success !== undefined) {
      query.success = success === 'true';
    }

    // Date range filter
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) {
        query.timestamp.$gte = new Date(startDate);
      }
      if (endDate) {
        query.timestamp.$lte = new Date(endDate);
      }
    }

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    // Execute query with pagination
    const [auditLogs, total] = await Promise.all([
      AuditLog.find(query)
        .populate('userId', 'name email')
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      AuditLog.countDocuments(query)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      auditLogs,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    });

  } catch (error) {
    console.error('Error fetching audit logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch audit logs' },
      { status: 500 }
    );
  }
}

// Get audit log statistics
export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    // Check for audit log access permission
    const hasAuditAccess = session.user.permissions?.some((permission: string) =>
      permission === AUDIT_PERMISSIONS.ACCESS_AUDIT_LOGS
    );

    if (!hasAuditAccess) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const { type } = body;

    if (type === 'statistics') {
      // Get various statistics
      const now = new Date();
      const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const [
        totalLogs,
        last24HoursCount,
        last7DaysCount,
        last30DaysCount,
        errorCount,
        actionStats,
        entityStats,
        userStats
      ] = await Promise.all([
        AuditLog.countDocuments(),
        AuditLog.countDocuments({ timestamp: { $gte: last24Hours } }),
        AuditLog.countDocuments({ timestamp: { $gte: last7Days } }),
        AuditLog.countDocuments({ timestamp: { $gte: last30Days } }),
        AuditLog.countDocuments({ success: false }),
        AuditLog.aggregate([
          { $group: { _id: '$action', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ]),
        AuditLog.aggregate([
          { $group: { _id: '$entityType', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ]),
        AuditLog.aggregate([
          { $group: { _id: '$userId', count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 },
          {
            $lookup: {
              from: 'users',
              localField: '_id',
              foreignField: '_id',
              as: 'user'
            }
          },
          {
            $project: {
              count: 1,
              userName: { $arrayElemAt: ['$user.name', 0] },
              userEmail: { $arrayElemAt: ['$user.email', 0] }
            }
          }
        ])
      ]);

      return NextResponse.json({
        statistics: {
          totalLogs,
          last24Hours: last24HoursCount,
          last7Days: last7DaysCount,
          last30Days: last30DaysCount,
          errorCount,
          errorRate: totalLogs > 0 ? (errorCount / totalLogs * 100).toFixed(2) : 0
        },
        topActions: actionStats,
        topEntityTypes: entityStats,
        topUsers: userStats
      });
    }

    return NextResponse.json({ error: 'Invalid request type' }, { status: 400 });

  } catch (error) {
    console.error('Error processing audit logs request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
