import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import AuditLog from '@/models/AuditLog';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserPermissions } from '../../../../utils/server-permission-utils';
import { canUserAccessBilling } from '@/lib/utils/permissions-utils';
import { AUDIT_PERMISSIONS } from '@/types/permission-codes';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ entityType: string; entityId: string }> }
) {
  try {
    await dbConnect();

    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    // Check for audit log access permission
    const hasAuditAccess = session.user.permissions?.some((permission: string) =>
      permission === AUDIT_PERMISSIONS.ACCESS_AUDIT_LOGS
    );

    if (!hasAuditAccess) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { entityType, entityId } = await params;
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const action = searchParams.get('action');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const includeRelated = searchParams.get('includeRelated') === 'true';

    // Build query for the specific entity
    const query: any = {
      $or: [
        { entityType, entityId },
        // Always include logs where this entity is mentioned in relatedEntities
        {
          'relatedEntities': {
            $elemMatch: {
              type: entityType,
              id: entityId
            }
          }
        }
      ]
    };

    if (action) {
      query.action = action;
    }

    // Date range filter
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) {
        query.timestamp.$gte = new Date(startDate);
      }
      if (endDate) {
        query.timestamp.$lte = new Date(endDate);
      }
    }

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    // Execute query
    const [auditLogs, total] = await Promise.all([
      AuditLog.find(query)
        .populate('userId', 'name email')
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      AuditLog.countDocuments(query)
    ]);

    // Get entity-specific statistics
    const entityQuery = {
      $or: [
        { entityType, entityId },
        {
          'relatedEntities': {
            $elemMatch: {
              type: entityType,
              id: entityId
            }
          }
        }
      ]
    };

    const [
      actionStats,
      userStats,
      recentActivity
    ] = await Promise.all([
      // Top actions for this entity
      AuditLog.aggregate([
        { $match: entityQuery },
        { $group: { _id: '$action', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),

      // Top users who modified this entity
      AuditLog.aggregate([
        { $match: entityQuery },
        { $group: { _id: '$userId', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'user'
          }
        },
        {
          $project: {
            count: 1,
            userName: { $arrayElemAt: ['$user.name', 0] },
            userEmail: { $arrayElemAt: ['$user.email', 0] }
          }
        }
      ]),

      // Recent activity (last 7 days)
      AuditLog.find({
        ...entityQuery,
        timestamp: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
      })
        .populate('userId', 'name email')
        .sort({ timestamp: -1 })
        .limit(10)
        .lean()
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      auditLogs,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage
      },
      statistics: {
        totalLogs: total,
        topActions: actionStats,
        topUsers: userStats,
        recentActivity
      },
      entity: {
        type: entityType,
        id: entityId
      }
    });

  } catch (error) {
    console.error('Error fetching entity audit logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch audit logs' },
      { status: 500 }
    );
  }
}
