import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import AuditLog from '@/models/AuditLog';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserPermissions } from '../../utils/server-permission-utils';
import { AUDIT_PERMISSIONS } from '@/types/permission-codes';
import { isValidObjectId } from 'mongoose';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    // Check for audit log access permission
    const hasAuditAccess = session.user.permissions?.some((permission: string) =>
      permission === AUDIT_PERMISSIONS.ACCESS_AUDIT_LOGS
    );

    if (!hasAuditAccess) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;

    // Validate ObjectId
    if (!isValidObjectId(id)) {
      return NextResponse.json({ error: 'Invalid audit log ID' }, { status: 400 });
    }

    // Fetch the specific audit log
    const auditLog = await AuditLog.findById(id)
      .populate('userId', 'name email')
      .lean();

    if (!auditLog) {
      return NextResponse.json({ error: 'Audit log not found' }, { status: 404 });
    }

    // Get related audit logs (same entity, same user, or same action within 24 hours)
    const relatedLogsQuery = {
      _id: { $ne: auditLog._id },
      $or: [
        // Same entity
        {
          entityType: auditLog.entityType,
          entityId: auditLog.entityId
        },
        // Same user within 24 hours
        {
          userId: auditLog.userId,
          timestamp: {
            $gte: new Date(new Date(auditLog.timestamp).getTime() - 24 * 60 * 60 * 1000),
            $lte: new Date(new Date(auditLog.timestamp).getTime() + 24 * 60 * 60 * 1000)
          }
        },
        // Same action type within 1 hour
        {
          action: auditLog.action,
          timestamp: {
            $gte: new Date(new Date(auditLog.timestamp).getTime() - 60 * 60 * 1000),
            $lte: new Date(new Date(auditLog.timestamp).getTime() + 60 * 60 * 1000)
          }
        }
      ]
    };

    const relatedLogs = await AuditLog.find(relatedLogsQuery)
      .populate('userId', 'name email')
      .sort({ timestamp: -1 })
      .limit(10)
      .lean();

    // Get context information
    const context = {
      // Count of logs for this entity
      entityLogCount: auditLog.entityType && auditLog.entityId ? 
        await AuditLog.countDocuments({
          entityType: auditLog.entityType,
          entityId: auditLog.entityId
        }) : 0,
      
      // Count of logs by this user
      userLogCount: auditLog.userId ? 
        await AuditLog.countDocuments({ userId: auditLog.userId }) : 0,
      
      // Count of this action type
      actionLogCount: await AuditLog.countDocuments({ action: auditLog.action }),
      
      // Recent logs from same IP (if available)
      ipLogCount: auditLog.ipAddress ? 
        await AuditLog.countDocuments({ 
          ipAddress: auditLog.ipAddress,
          timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        }) : 0
    };

    return NextResponse.json({
      auditLog,
      relatedLogs,
      context
    });

  } catch (error) {
    console.error('Error fetching audit log:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
