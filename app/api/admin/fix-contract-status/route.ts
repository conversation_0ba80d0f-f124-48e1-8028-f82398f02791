import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import User from '@/models/User';
import ContractAuditLog from '@/models/ContractAuditLog';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    // Check authentication and admin permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const { action, tokenId } = await request.json();

    if (action === 'fix-all') {
      // Fix all contracts that should be completed but aren't marked as such
      const incompleteContracts = await ContractSigningToken.find({
        isCompleted: false,
        adminSignedAt: { $exists: true, $ne: null },
        adminSignatureData: { $exists: true, $ne: null },
        userSignedAt: { $exists: true, $ne: null },
        userSignatureData: { $exists: true, $ne: null }
      });

      console.log(`Found ${incompleteContracts.length} contracts to fix`);

      const results = [];
      for (const contract of incompleteContracts) {
        try {
          // Update the contract
          await ContractSigningToken.findByIdAndUpdate(contract._id, {
            isCompleted: true
          });

          // Update user record if needed
          const user = await User.findById(contract.userId);
          if (user && !user.contractCompletedAt) {
            await User.findByIdAndUpdate(contract.userId, {
              contractCompletedAt: contract.userSignedAt || new Date()
            });
          }

          // Log the fix
          await ContractAuditLog.logAction(
            contract.userId,
            contract.token,
            'status_fixed',
            session.user.id,
            request.headers.get('x-forwarded-for') || 'unknown',
            request.headers.get('user-agent') || 'unknown',
            { reason: 'Manual status fix - contract was fully signed but not marked complete' }
          );

          results.push({
            tokenId: contract._id,
            token: contract.token.substring(0, 8) + '...',
            userId: contract.userId,
            status: 'fixed'
          });

        } catch (error) {
          console.error(`Error fixing contract ${contract._id}:`, error);
          results.push({
            tokenId: contract._id,
            token: contract.token.substring(0, 8) + '...',
            userId: contract.userId,
            status: 'error',
            error: error.message
          });
        }
      }

      return NextResponse.json({
        success: true,
        message: `Fixed ${results.filter(r => r.status === 'fixed').length} contracts`,
        results
      });

    } else if (action === 'fix-single' && tokenId) {
      // Fix a specific contract
      const contract = await ContractSigningToken.findById(tokenId);
      
      if (!contract) {
        return NextResponse.json({ error: 'Contract not found' }, { status: 404 });
      }

      // Check if it should be completed
      const shouldBeCompleted = !!(contract.adminSignedAt && contract.adminSignatureData && 
                                  contract.userSignedAt && contract.userSignatureData);

      if (!shouldBeCompleted) {
        return NextResponse.json({ 
          error: 'Contract is not fully signed yet' 
        }, { status: 400 });
      }

      if (contract.isCompleted) {
        return NextResponse.json({ 
          message: 'Contract is already marked as completed' 
        });
      }

      // Update the contract
      await ContractSigningToken.findByIdAndUpdate(contract._id, {
        isCompleted: true
      });

      // Update user record if needed
      const user = await User.findById(contract.userId);
      if (user && !user.contractCompletedAt) {
        await User.findByIdAndUpdate(contract.userId, {
          contractCompletedAt: contract.userSignedAt || new Date()
        });
      }

      // Log the fix
      await ContractAuditLog.logAction(
        contract.userId,
        contract.token,
        'status_fixed',
        session.user.id,
        request.headers.get('x-forwarded-for') || 'unknown',
        request.headers.get('user-agent') || 'unknown',
        { reason: 'Manual status fix - contract was fully signed but not marked complete' }
      );

      return NextResponse.json({
        success: true,
        message: 'Contract status fixed successfully',
        contract: {
          id: contract._id,
          token: contract.token.substring(0, 8) + '...',
          isCompleted: true
        }
      });

    } else {
      return NextResponse.json({ 
        error: 'Invalid action. Use "fix-all" or "fix-single" with tokenId' 
      }, { status: 400 });
    }

  } catch (error: any) {
    console.error('Error fixing contract status:', error);
    return NextResponse.json(
      {
        error: 'Failed to fix contract status',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find contracts that need fixing
    const incompleteContracts = await ContractSigningToken.find({
      isCompleted: false,
      adminSignedAt: { $exists: true, $ne: null },
      adminSignatureData: { $exists: true, $ne: null },
      userSignedAt: { $exists: true, $ne: null },
      userSignatureData: { $exists: true, $ne: null }
    }).populate('userId', 'name email').populate('createdBy', 'name email');

    return NextResponse.json({
      success: true,
      contractsNeedingFix: incompleteContracts.length,
      contracts: incompleteContracts.map(contract => ({
        id: contract._id,
        token: contract.token.substring(0, 8) + '...',
        user: contract.userId,
        createdBy: contract.createdBy,
        adminSignedAt: contract.adminSignedAt,
        userSignedAt: contract.userSignedAt,
        isCompleted: contract.isCompleted
      }))
    });

  } catch (error: any) {
    console.error('Error checking contract status:', error);
    return NextResponse.json(
      {
        error: 'Failed to check contract status',
        details: error.message
      },
      { status: 500 }
    );
  }
}
