import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import CommissionConfiguration from '@/models/CommissionConfiguration';
import mongoose from 'mongoose';
import { canUserManageCommissionConfig, canUserManageCommissionConfigForBranch } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '../../utils/server-permission-utils';
import dbConnect from '@/lib/db';

// GET - Retrieve current commission configuration
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Check for branch-specific access if branchId is provided
  const url = new URL(request.url);
  const branchId = url.searchParams.get('branchId');

  if (branchId) {
    const canManage = await canUserManageCommissionConfigForBranch(session.user, branchId);
    if (!canManage) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view commission configuration for this branch' },
        { status: 403 }
      );
    }
  } else {
    // General permission check
    if (!canUserManageCommissionConfig(session.user)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view commission configuration' },
        { status: 403 }
      );
    }
  }
  
  try {
  await dbConnect();
    
    // Get active configuration
    const activeConfig = await CommissionConfiguration.findOne({ isActive: true })
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .lean();

    // Get configuration history (last 10 configurations)
    const configHistory = await CommissionConfiguration.find()
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(10)
      .lean();

    return NextResponse.json({
      activeConfig,
      configHistory
    });

  } catch (error) {
    console.error('Error fetching commission configuration:', error);
    return NextResponse.json(
      { error: 'Failed to fetch commission configuration' },
      { status: 500 }
    );
  }
}

// POST - Create new commission configuration
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (!canUserManageCommissionConfig(session.user)) {
    return NextResponse.json(
      { error: 'Insufficient permissions to manage commission configuration' },
      { status: 403 }
    );
  }
  
  try {
  await dbConnect();
    
    const configData = await request.json();
    
    // Validate required fields
    if (
      configData.papCommissionAmount === undefined ||
      configData.cookCommissionPerReservation === undefined ||
      configData.supervisorCommissionPerReservation === undefined ||
      configData.papSchedulingThresholdWeeks === undefined
    ) {
      return NextResponse.json(
        { error: 'Missing required fields: papCommissionAmount, cookCommissionPerReservation, supervisorCommissionPerReservation, papSchedulingThresholdWeeks' },
        { status: 400 }
      );
    }

    // Validate ranges
    if (configData.papCommissionAmount < 0 || configData.papCommissionAmount > 1000) {
      return NextResponse.json(
        { error: 'PAP commission amount must be between 0 and 1000' },
        { status: 400 }
      );
    }

    if (configData.cookCommissionPerReservation < 0 || configData.cookCommissionPerReservation > 1000) {
      return NextResponse.json(
        { error: 'Cook commission per reservation must be between 0 and 1000' },
        { status: 400 }
      );
    }

    if (configData.supervisorCommissionPerReservation < 0 || configData.supervisorCommissionPerReservation > 1000) {
      return NextResponse.json(
        { error: 'Supervisor commission per reservation must be between 0 and 1000' },
        { status: 400 }
      );
    }

    if (configData.papSchedulingThresholdWeeks < 0 || configData.papSchedulingThresholdWeeks > 52) {
      return NextResponse.json(
        { error: 'PAP scheduling threshold must be between 0 and 52 weeks' },
        { status: 400 }
      );
    }

    // Create new configuration
    const newConfig = new CommissionConfiguration({
      papCommissionAmount: configData.papCommissionAmount,
      cookCommissionPerReservation: configData.cookCommissionPerReservation,
      supervisorCommissionPerReservation: configData.supervisorCommissionPerReservation,
      papSchedulingThresholdWeeks: configData.papSchedulingThresholdWeeks,
      isActive: true, // New configuration becomes active
      createdBy: new mongoose.Types.ObjectId(session.user.id)
    });

    await newConfig.save();

    // Populate the response
    const populatedConfig = await CommissionConfiguration.findById(newConfig._id)
      .populate('createdBy', 'name email')
      .lean();

    return NextResponse.json({
      ...populatedConfig,
      message: 'Commission configuration created successfully'
    });

  } catch (error) {
    console.error('Error creating commission configuration:', error);
    return NextResponse.json(
      { error: 'Failed to create commission configuration' },
      { status: 500 }
    );
  }
}

// PUT - Update existing commission configuration
export async function PUT(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (!canUserManageCommissionConfig(session.user)) {
    return NextResponse.json(
      { error: 'Insufficient permissions to manage commission configuration' },
      { status: 403 }
    );
  }
  
  try {
  await dbConnect();
    
    const { configId, ...updateData } = await request.json();
    
    if (!configId || !mongoose.Types.ObjectId.isValid(configId)) {
      return NextResponse.json(
        { error: 'Valid configuration ID is required' },
        { status: 400 }
      );
    }

    // Find existing configuration
    const existingConfig = await CommissionConfiguration.findById(configId);
    if (!existingConfig) {
      return NextResponse.json(
        { error: 'Commission configuration not found' },
        { status: 404 }
      );
    }

    // Update configuration
    const updatedConfig = await CommissionConfiguration.findByIdAndUpdate(
      configId,
      {
        ...updateData,
        updatedBy: new mongoose.Types.ObjectId(session.user.id)
      },
      { new: true }
    )
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email');

    return NextResponse.json({
      ...updatedConfig.toObject(),
      message: 'Commission configuration updated successfully'
    });

  } catch (error) {
    console.error('Error updating commission configuration:', error);
    return NextResponse.json(
      { error: 'Failed to update commission configuration' },
      { status: 500 }
    );
  }
}
