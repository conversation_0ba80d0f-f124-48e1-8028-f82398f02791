import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { CronScheduler } from '@/lib/services/cron-scheduler';
import { canUserManageEvents } from '@/lib/utils/permissions-utils';

/**
 * GET /api/admin/notification-jobs
 * Get status of all notification jobs and statistics
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user can manage events (admin permission)
    if (!canUserManageEvents(session.user)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Get job status and notification statistics
    const [jobsStatus, notificationStats] = await Promise.all([
      CronScheduler.getJobStatus(),
      CronScheduler.getEventNotificationStats()
    ]);

    return NextResponse.json({
      jobs: jobsStatus,
      statistics: notificationStats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting notification jobs status:', error);
    return NextResponse.json(
      { error: 'Failed to get notification jobs status' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/notification-jobs
 * Manually run notification jobs or manage job lifecycle
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user can manage events (admin permission)
    if (!canUserManageEvents(session.user)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, jobName } = body;

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'run':
        if (!jobName) {
          return NextResponse.json(
            { error: 'Job name is required for run action' },
            { status: 400 }
          );
        }

        const validJobNames = [
          'overdueReports',
          'upcomingEvents',
          'validationDeadlines',
          'eventStatusTransitions',
          'commissionCalculation',
          'all'
        ];

        if (!validJobNames.includes(jobName)) {
          return NextResponse.json(
            { error: `Invalid job name. Valid options: ${validJobNames.join(', ')}` },
            { status: 400 }
          );
        }

        // Map job names to CronScheduler methods
        switch (jobName) {
          case 'overdueReports':
            await CronScheduler.triggerOverdueReportsCheck();
            break;
          case 'upcomingEvents':
            await CronScheduler.triggerUpcomingEventsCheck();
            break;
          case 'validationDeadlines':
            await CronScheduler.triggerValidationDeadlinesCheck();
            break;
          case 'eventStatusTransitions':
            await CronScheduler.triggerEventStatusTransitions();
            break;
          case 'commissionCalculation':
            await CronScheduler.triggerCommissionCalculationCheck();
            break;
          case 'all':
            await CronScheduler.triggerAllEventNotifications();
            break;
        }

        return NextResponse.json({
          message: `Job ${jobName} executed successfully`,
          executedAt: new Date().toISOString()
        });

      case 'restart':
        CronScheduler.restartEventNotificationAutomation();
        return NextResponse.json({
          message: 'All event notification jobs restarted successfully',
          restartedAt: new Date().toISOString()
        });

      case 'stop':
        CronScheduler.stopEventNotificationAutomation();
        return NextResponse.json({
          message: 'All event notification jobs stopped successfully',
          stoppedAt: new Date().toISOString()
        });

      case 'start':
        CronScheduler.startEventNotificationAutomation();
        return NextResponse.json({
          message: 'All event notification jobs started successfully',
          startedAt: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { error: `Invalid action: ${action}. Valid actions: run, restart, stop, start` },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error managing notification jobs:', error);
    return NextResponse.json(
      { error: 'Failed to manage notification jobs' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/notification-jobs
 * Update job configuration (future enhancement)
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user can manage events (admin permission)
    if (!canUserManageEvents(session.user)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    // Future enhancement: Allow updating job schedules, enabling/disabling specific jobs
    return NextResponse.json(
      { message: 'Job configuration updates not yet implemented' },
      { status: 501 }
    );

  } catch (error) {
    console.error('Error updating notification jobs configuration:', error);
    return NextResponse.json(
      { error: 'Failed to update notification jobs configuration' },
      { status: 500 }
    );
  }
}
