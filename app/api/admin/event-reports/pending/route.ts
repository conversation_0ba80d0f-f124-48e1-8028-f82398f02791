import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import { EventReport } from '@/models/EventReport';
import { EventType } from '@/models/EventType';
import Branch from '@/models/Branch';
import Partner from '@/models/Partner';
import mongoose from 'mongoose';
import { canUserValidateEventReports } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import dbConnect from '@/lib/db';

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (!canUserValidateEventReports(session.user)) {
    return NextResponse.json(
      { error: 'Insufficient permissions to view pending reports' },
      { status: 403 }
    );
  }

  try {
  await dbConnect();

    // Ensure all models are registered
    EventType;
    Branch;
    Partner;

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const priority = searchParams.get('priority'); // 'urgent', 'normal', 'all'
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build filter for submitted reports
    const filter: any = {
      status: 'submitted'
    };

    // Add priority filtering
    const now = new Date();
    if (priority === 'urgent') {
      // Reports submitted more than 2 days ago
      filter['history.changedAt'] = {
        $lt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000)
      };
      filter['history.action'] = 'Report submitted for validation';
    }

    // Get pending reports with full details
    const reports = await EventReport.find(filter)
      .populate({
        path: 'eventId',
        select: 'name location startDate endDate branchId partnerId eventTypeId',
        populate: [
          { path: 'branchId', select: 'name' },
          { path: 'partnerId', select: 'name' },
          { path: 'eventTypeId', select: 'name code' }
        ]
      })
      .populate('supervisors', 'name email')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email')
      .populate('history.changedBy', 'name email')
      .sort({ 'history.changedAt': 1 }) // Oldest first
      .skip(offset)
      .limit(limit)
      .lean();

    // Add priority and summary information
    const reportsWithMetadata = reports.map(report => {
      // Find submission date
      const submissionEntry = report.history.find((entry: any) => 
        entry.action === 'Report submitted for validation'
      );
      const submissionDate = submissionEntry?.changedAt || report.updatedAt;
      const daysSinceSubmission = (now.getTime() - new Date(submissionDate).getTime()) / (1000 * 60 * 60 * 24);

      // Calculate priority
      let reportPriority = 'normal';
      if (daysSinceSubmission > 3) {
        reportPriority = 'urgent';
      } else if (daysSinceSubmission > 1) {
        reportPriority = 'high';
      }

      // Calculate totals
      const totalPAPs = report.paps?.length || 0;
      const totalCooks = report.cooks?.length || 0;
      const totalPersonnel = totalPAPs + totalCooks;

      // Calculate event duration
      const eventDuration = report.eventEndTime && report.eventStartTime
        ? (new Date(report.eventEndTime).getTime() - new Date(report.eventStartTime).getTime()) / (1000 * 60 * 60)
        : 0;

      return {
        ...report,
        metadata: {
          priority: reportPriority,
          daysSinceSubmission: Math.round(daysSinceSubmission * 10) / 10,
          submissionDate,
          submittedBy: submissionEntry?.changedBy,
          summary: {
            totalPersonnel,
            totalPAPs,
            totalCooks,
            eventDurationHours: Math.round(eventDuration * 10) / 10
          }
        }
      };
    });

    // Get total count for pagination
    const totalCount = await EventReport.countDocuments(filter);

    // Get summary statistics
    const allPendingReports = await EventReport.find({ status: 'submitted' }).lean();
    const urgentCount = allPendingReports.filter(report => {
      const submissionEntry = report.history.find((entry: any) => 
        entry.action === 'Report submitted for validation'
      );
      const submissionDate = submissionEntry?.changedAt || report.updatedAt;
      const daysSinceSubmission = (now.getTime() - new Date(submissionDate).getTime()) / (1000 * 60 * 60 * 24);
      return daysSinceSubmission > 2;
    }).length;

    const summary = {
      total: totalCount,
      urgent: urgentCount,
      normal: totalCount - urgentCount,
      avgWaitTime: allPendingReports.length > 0 
        ? allPendingReports.reduce((sum, report) => {
            const submissionEntry = report.history.find((entry: any) => 
              entry.action === 'Report submitted for validation'
            );
            const submissionDate = submissionEntry?.changedAt || report.updatedAt;
            const daysSinceSubmission = (now.getTime() - new Date(submissionDate).getTime()) / (1000 * 60 * 60 * 24);
            return sum + daysSinceSubmission;
          }, 0) / allPendingReports.length
        : 0
    };

    return NextResponse.json({
      reports: reportsWithMetadata,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      },
      summary: {
        ...summary,
        avgWaitTime: Math.round(summary.avgWaitTime * 10) / 10
      }
    });

  } catch (error) {
    console.error('Error fetching pending reports:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pending reports' },
      { status: 500 }
    );
  }
}

// POST endpoint for bulk operations on pending reports
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (!canUserValidateEventReports(session.user)) {
    return NextResponse.json(
      { error: 'Insufficient permissions to perform bulk operations' },
      { status: 403 }
    );
  }

  try {
  await dbConnect();
    
    const { action, reportIds, data } = await request.json();
    
    if (!action || !reportIds || !Array.isArray(reportIds)) {
      return NextResponse.json(
        { error: 'Action and reportIds array are required' },
        { status: 400 }
      );
    }

    let results = [];

    switch (action) {
      case 'assign_reviewer':
        // Assign a reviewer to multiple reports
        const reviewerId = data?.reviewerId;
        if (!reviewerId) {
          return NextResponse.json(
            { error: 'Reviewer ID is required' },
            { status: 400 }
          );
        }

        for (const reportId of reportIds) {
          const result = await EventReport.findByIdAndUpdate(
            reportId,
            {
              assignedReviewer: new mongoose.Types.ObjectId(reviewerId),
              $push: {
                history: {
                  action: 'Reviewer assigned',
                  changedBy: new mongoose.Types.ObjectId(session.user.id),
                  changedAt: new Date(),
                  newValue: { reviewerId }
                }
              }
            },
            { new: true }
          );
          results.push(result);
        }
        break;

      case 'set_priority':
        // Set priority flag for multiple reports
        const priority = data?.priority;
        if (!priority) {
          return NextResponse.json(
            { error: 'Priority is required' },
            { status: 400 }
          );
        }

        for (const reportId of reportIds) {
          const result = await EventReport.findByIdAndUpdate(
            reportId,
            {
              priority,
              $push: {
                history: {
                  action: `Priority set to ${priority}`,
                  changedBy: new mongoose.Types.ObjectId(session.user.id),
                  changedAt: new Date(),
                  newValue: { priority }
                }
              }
            },
            { new: true }
          );
          results.push(result);
        }
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      action,
      processedCount: results.length,
      results
    });

  } catch (error) {
    console.error('Error performing bulk operation:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}
