import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// Define the ReservationStatus schema
const ReservationStatusSchema = new mongoose.Schema({
  name: String,
  name_en: String,
  code: {
    type: String,
    required: true,
    unique: true
  },
  color: String,
  order: Number,
}, {
  timestamps: true
});

// Create or get the model
const ReservationStatusModel = mongoose.models.ReservationStatus || mongoose.model('ReservationStatus', ReservationStatusSchema);

export async function GET(request: Request) {
  try {
  await dbConnect();
    
    // Get the preferred language from the request headers
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'fr'; // Default to French
    
    const statuses = await ReservationStatusModel.find({})
      .sort({ order: 1 })
      .lean();

    // Format the response based on language preference
    const formattedStatuses = statuses.map(status => ({
      _id: status._id,
      name: lang === 'en' ? status.name_en : status.name,
      code: status.code,
      color: status.color,
      order: status.order
    }));

    return NextResponse.json(formattedStatuses);
  } catch (error) {
    console.error('Error fetching reservation statuses:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reservation statuses' },
      { status: 500 }
    );
  }
} 