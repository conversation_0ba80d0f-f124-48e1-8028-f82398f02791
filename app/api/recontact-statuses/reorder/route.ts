import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import RecontactReservationStatus from '@/models/RecontactReservationStatus';
import dbConnect from '@/lib/db';

// PATCH - Reorder recontact statuses
export async function PATCH(request: NextRequest) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { updates } = body;

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json(
        { message: 'Updates array is required' },
        { status: 400 }
      );
    }

    // Validate updates format
    for (const update of updates) {
      if (!update.id || typeof update.order !== 'number') {
        return NextResponse.json(
          { message: 'Each update must have id and order fields' },
          { status: 400 }
        );
      }
    }

    // Update all statuses in a transaction-like manner
    const updatePromises = updates.map(({ id, order }) =>
      RecontactReservationStatus.findByIdAndUpdate(
        id,
        { 
          order,
          updatedBy: session.user.id
        },
        { new: true }
      )
    );

    const results = await Promise.all(updatePromises);

    // Check if any updates failed
    const failedUpdates = results.filter(result => !result);
    if (failedUpdates.length > 0) {
      return NextResponse.json(
        { message: 'Some statuses could not be updated' },
        { status: 400 }
      );
    }

    return NextResponse.json({ 
      message: 'Statuses reordered successfully',
      updated: results.length
    });
  } catch (error) {
    console.error('Error reordering recontact statuses:', error);
    return NextResponse.json(
      { message: 'Failed to reorder statuses' },
      { status: 500 }
    );
  }
}
