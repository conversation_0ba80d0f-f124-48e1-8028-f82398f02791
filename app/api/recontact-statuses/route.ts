import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import RecontactReservationStatus from '@/models/RecontactReservationStatus';
import dbConnect from '@/lib/db';

// GET - List all recontact statuses
export async function GET() {
  try {
  await dbConnect();

    const statuses = await RecontactReservationStatus.find()
      .sort({ order: 1 })
      .lean();

    return NextResponse.json(statuses);
  } catch (error) {
    console.error('Error fetching recontact statuses:', error);
    return NextResponse.json(
      { message: 'Failed to fetch statuses' },
      { status: 500 }
    );
  }
}

// POST - Create new recontact status
export async function POST(request: NextRequest) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, code, color, order } = body;

    // Validate required fields
    if (!name || !code) {
      return NextResponse.json(
        { message: 'Name and code are required' },
        { status: 400 }
      );
    }

    // Check for duplicate code
    const existingStatus = await RecontactReservationStatus.findOne({ code });
    if (existingStatus) {
      return NextResponse.json(
        { message: 'Status code already exists' },
        { status: 400 }
      );
    }

    // Check for duplicate name
    const existingName = await RecontactReservationStatus.findOne({ name });
    if (existingName) {
      return NextResponse.json(
        { message: 'Status name already exists' },
        { status: 400 }
      );
    }

    const status = new RecontactReservationStatus({
      name,
      code: code.toLowerCase(),
      color: color || '#6E6E6E',
      order: order || 0,
      createdBy: session.user.id,
      updatedBy: session.user.id
    });

    await status.save();

    return NextResponse.json(status, { status: 201 });
  } catch (error) {
    console.error('Error creating recontact status:', error);
    return NextResponse.json(
      { message: 'Failed to create status' },
      { status: 500 }
    );
  }
}
