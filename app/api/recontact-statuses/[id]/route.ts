import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import RecontactReservationStatus from '@/models/RecontactReservationStatus';
import dbConnect from '@/lib/db';

// GET - Get single recontact status
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
  await dbConnect();

    const status = await RecontactReservationStatus.findById(params.id).lean();
    
    if (!status) {
      return NextResponse.json(
        { message: 'Status not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(status);
  } catch (error) {
    console.error('Error fetching recontact status:', error);
    return NextResponse.json(
      { message: 'Failed to fetch status' },
      { status: 500 }
    );
  }
}

// PUT - Update recontact status
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, code, color, order } = body;

    // Validate required fields
    if (!name || !code) {
      return NextResponse.json(
        { message: 'Name and code are required' },
        { status: 400 }
      );
    }

    // Check for duplicate code (excluding current status)
    const existingStatus = await RecontactReservationStatus.findOne({ 
      code, 
      _id: { $ne: params.id } 
    });
    if (existingStatus) {
      return NextResponse.json(
        { message: 'Status code already exists' },
        { status: 400 }
      );
    }

    // Check for duplicate name (excluding current status)
    const existingName = await RecontactReservationStatus.findOne({ 
      name, 
      _id: { $ne: params.id } 
    });
    if (existingName) {
      return NextResponse.json(
        { message: 'Status name already exists' },
        { status: 400 }
      );
    }

    const updatedStatus = await RecontactReservationStatus.findByIdAndUpdate(
      params.id,
      {
        name,
        code: code.toLowerCase(),
        color: color || '#6E6E6E',
        order: order || 0,
        updatedBy: session.user.id
      },
      { new: true, runValidators: true }
    );

    if (!updatedStatus) {
      return NextResponse.json(
        { message: 'Status not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedStatus);
  } catch (error) {
    console.error('Error updating recontact status:', error);
    return NextResponse.json(
      { message: 'Failed to update status' },
      { status: 500 }
    );
  }
}

// DELETE - Delete recontact status
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Check if status is being used by any recontact reservations
    const { default: RecontactReservation } = await import('@/models/RecontactReservation');
    const usageCount = await RecontactReservation.countDocuments({ statusId: params.id });
    
    if (usageCount > 0) {
      return NextResponse.json(
        { message: `Cannot delete status. It is being used by ${usageCount} recontact reservation(s).` },
        { status: 400 }
      );
    }

    const deletedStatus = await RecontactReservationStatus.findByIdAndDelete(params.id);

    if (!deletedStatus) {
      return NextResponse.json(
        { message: 'Status not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Status deleted successfully' });
  } catch (error) {
    console.error('Error deleting recontact status:', error);
    return NextResponse.json(
      { message: 'Failed to delete status' },
      { status: 500 }
    );
  }
}
