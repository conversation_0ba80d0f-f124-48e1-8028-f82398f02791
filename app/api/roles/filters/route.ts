import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Role from '@/models/Role';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SETTINGS_PERMISSIONS } from '@/types/permission-codes';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';

export async function GET() {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    if (!session.user.permissions?.includes(SETTINGS_PERMISSIONS.MANAGE_RESERVATION_SETTINGS)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const roles = await Role.find({})
      .select('name description reservationFilters')
      .lean();

    return NextResponse.json({ roles });
  } catch (error) {
    console.error('GET role filters error:', error);
    return NextResponse.json({ error: 'Failed to fetch role filters' }, { status: 500 });
  }
}
