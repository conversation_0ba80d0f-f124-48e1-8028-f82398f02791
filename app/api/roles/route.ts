import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Role from '@/models/Role';

export async function GET() {
  try {
    await dbConnect();
    const roles = await Role.find({}).populate('permissions');
    return NextResponse.json(roles);
  } catch (error) {
    console.error('GET roles error:', error);
    return NextResponse.json({ error: 'Failed to fetch roles' }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    await dbConnect();
    const body = await request.json();
    
    // Validate permissions array
    if (!body.permissions || !Array.isArray(body.permissions) || body.permissions.length === 0) {
      return NextResponse.json(
        { error: 'At least one permission is required' },
        { status: 400 }
      );
    }

    const role = await Role.create(body);
    const populatedRole = await role.populate('permissions');
    
    return NextResponse.json(populatedRole);
  } catch (error) {
    console.error('Create role error:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A role with this name already exists' },
        { status: 400 }
      );
    }
    return NextResponse.json({ error: 'Failed to create role' }, { status: 500 });
  }
} 