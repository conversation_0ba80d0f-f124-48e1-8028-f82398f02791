import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import ContactRequestSource from '@/models/ContactRequestSource';
import crypto from 'crypto';
import dbConnect from '@/lib/db';

export async function POST(req: NextRequest) {
  try {
  await dbConnect();

    // Get the raw body for signature verification
    const body = await req.text();
    const signature = req.headers.get('x-bitly-signature');

    // Verify webhook signature if secret is configured
    const webhookSecret = process.env.BITLY_WEBHOOK_SECRET;
    if (webhookSecret) {
      if (!signature) {
        console.error('Bitly webhook signature missing but secret is configured');
        return NextResponse.json({ error: 'Signature required' }, { status: 401 });
      }

      const expectedSignature = crypto
        .createHmac('sha256', webhookSecret)
        .update(body)
        .digest('hex');

      const providedSignature = signature.replace('sha256=', '');

      const expectedBuffer = Buffer.from(expectedSignature, 'hex');
      const providedBuffer = Buffer.from(providedSignature, 'hex');

      if (expectedBuffer.length !== providedBuffer.length || !crypto.timingSafeEqual(expectedBuffer, providedBuffer)) {
        console.error('Bitly webhook signature verification failed');
        return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
      }
    }

    // Parse the webhook data
    let webhookData;
    try {
      webhookData = JSON.parse(body);
    } catch (parseError) {
      console.error('Failed to parse webhook body:', parseError);
      return NextResponse.json({ error: 'Invalid JSON' }, { status: 400 });
    }

    // Extract relevant information from webhook
    const {
      event_type,
      bitlink,
      timestamp,
      ip_address,
      user_agent,
      referrer
    } = webhookData;

    if (!bitlink || !event_type) {
      console.error('Missing required webhook fields:', webhookData);
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Find the source by matching the bitlink URL
    let source = await ContactRequestSource.findOne({ bitlyUrl: bitlink });

    if (!source) {
      // Try to find by bitlink without protocol or with different protocols
      const cleanBitlink = bitlink.replace(/^https?:\/\//, '');
      source = await ContactRequestSource.findOne({
        $or: [
          { bitlyUrl: `http://${cleanBitlink}` },
          { bitlyUrl: `https://${cleanBitlink}` },
          { bitlyUrl: cleanBitlink },
          { bitlyUrl: { $regex: cleanBitlink.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), $options: 'i' } }
        ]
      });
    }

    if (!source) {
      console.warn('No source found for bitlink:', bitlink);
      return NextResponse.json({ message: 'Source not found' }, { status: 404 });
    }

    await updateSourceWithWebhook(source, webhookData, ip_address, user_agent, referrer, event_type, timestamp);

    return NextResponse.json({ message: 'Webhook processed successfully' }, { status: 200 });
  } catch (error) {
    console.error('Error processing Bitly webhook:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function updateSourceWithWebhook(
  source: any,
  webhookData: any,
  ipAddress?: string,
  userAgent?: string,
  referrer?: string,
  eventType?: string,
  timestamp?: string
) {
  try {
    // Increment hits counter
    source.hits = (source.hits || 0) + 1;

    // Add webhook data to the array
    const webhookEvent = {
      timestamp: timestamp ? new Date(timestamp) : new Date(),
      ipAddress: ipAddress || undefined,
      userAgent: userAgent || undefined,
      referrer: referrer || undefined,
      eventType: eventType || 'click',
      rawData: webhookData
    };

    source.webhookData = source.webhookData || [];
    source.webhookData.push(webhookEvent);

    // Keep only the last 1000 webhook events to prevent unlimited growth
    if (source.webhookData.length > 1000) {
      source.webhookData = source.webhookData.slice(-1000);
    }

    await source.save();

    console.log(`Updated source ${source.tag} - hits: ${source.hits}, event: ${eventType}`);
  } catch (error) {
    console.error('Error updating source with webhook data:', error);
    throw error;
  }
}