import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Screen from '@/models/Screen';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { NOTIFICATION_PERMISSIONS } from '@/types/permission-codes';
import { withApiLogging } from '../utils/with-api-logging';

export const GET = withApiLogging(async (request: Request) => {
  await dbConnect();
  
  try {
    const screens = await Screen.find({})
      .sort({ name: 1 })
      .lean();

    return NextResponse.json({ screens });
  } catch (error) {
    console.error('Error fetching screens:', error);
    return NextResponse.json(
      { error: 'Failed to fetch screens' },
      { status: 500 }
    );
  }
});

export const POST = withApiLogging(async (request: Request) => {
  await dbConnect();
  
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has permission to manage notifications (which includes managing screens)
    if (!session.user.permissions?.includes(NOTIFICATION_PERMISSIONS.MANAGE_NOTIFICATIONS)) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    const body = await request.json();
    
    // Validate required fields
    if (!body.path || !body.name) {
      return NextResponse.json(
        { error: 'Path and name are required' },
        { status: 400 }
      );
    }

    // Check if screen with this path already exists
    const existingScreen = await Screen.findOne({ path: body.path });
    if (existingScreen) {
      return NextResponse.json(
        { error: 'Screen with this path already exists' },
        { status: 400 }
      );
    }

    const screen = await Screen.create({
      path: body.path,
      name: body.name
    });

    return NextResponse.json(screen);
  } catch (error) {
    console.error('Error creating screen:', error);
    return NextResponse.json(
      { error: 'Failed to create screen' },
      { status: 500 }
    );
  }
});