import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Appointment from '@/models/Appointment';
import AppointmentDisponibility from '@/models/AppointmentDisponibility';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import Reservation from '@/models/Reservation';
import { getExcludedStatusCodes } from '@/lib/utils/reservation-status-utils';

// Define minimal types
type MongoDoc = {
  _id: mongoose.Types.ObjectId;
  [key: string]: any;
};

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const startDate = searchParams.get('startDate'); // Format: 'yyyy-MM-dd'
    const endDate = searchParams.get('endDate');     // Format: 'yyyy-MM-dd'
    
    // For backward compatibility, still support month/year parameters
    const month = searchParams.get('month');
    const year = searchParams.get('year');

    if (!branchId) {
      return NextResponse.json({ error: 'Missing branchId parameter' }, { status: 400 });
    }

    // Validate either date range or month/year is provided
    if (!((startDate && endDate) || (month && year))) {
      return NextResponse.json({ 
        error: 'Either startDate+endDate or month+year parameters are required' 
      }, { status: 400 });
    }

    await dbConnect();

    let dateFilter;
    
    // Use date range if provided, otherwise use month/year
    if (startDate && endDate) {
      dateFilter = {
        date: {
          $gte: startDate,
          $lte: endDate
        }
      };
    } else {
      // Legacy month/year filter
      dateFilter = {
        date: {
          $regex: `^${year}-${month!.padStart(2, '0')}`
        }
      };
    }

   

    const appointments = await Appointment.find({
      branchId,
      ...dateFilter
    }).lean();

    console.log(`Found ${appointments.length} appointments`);

    // Get all appointment IDs
    const appointmentIds = appointments.map((app: MongoDoc) => app._id);

    // Get excluded status codes dynamically
    const excludedStatusCodes = await getExcludedStatusCodes();

    // Fetch reservations for all appointments (excluding those with excludeFromAffectations statuses)
    const reservations = await Reservation.find({
      appointmentId: { $in: appointmentIds },
      status: { $nin: excludedStatusCodes },
      isDeleted: { $ne: true }
    }).lean();

    console.log(`Found ${reservations.length} reservations for these appointments`);

    // Get excluded reservations count for capacity adjustment
    const excludedReservations = await Reservation.aggregate([
      {
        $match: {
          appointmentId: { $in: appointmentIds },
          status: { $in: excludedStatusCodes },
          isDeleted: { $ne: true }
        }
      },
      { $group: { _id: '$appointmentId', count: { $sum: 1 } } }
    ]);

    const excludedMap = Object.fromEntries(excludedReservations.map((r: any) => [r._id.toString(), r.count]));

    // Calculate counts for each appointment using type assertions
    const appointmentsWithCounts = appointments.map((appointment: MongoDoc) => {
      // Filter reservations for this appointment (only non-excluded reservations)
      const appointmentReservations = reservations.filter((res: any) => {
        return res.appointmentId &&
               appointment._id &&
               res.appointmentId.toString() === appointment._id.toString();
      });

      // Count different reservation types (only non-excluded reservations)
      const counts = {
        online: appointmentReservations.filter((res: any) => res.type === 'online').length,
        home: appointmentReservations.filter((res: any) => res.type === 'home').length,
        branch: appointmentReservations.filter((res: any) => res.type === 'branch').length,
        family: appointmentReservations.filter((res: any) =>
          res.type === 'branch' && res.preferences && res.preferences.hasChildren
        ).length,
      };

      // Adjust capacity by subtracting excluded reservations
      const excludedCount = excludedMap[appointment._id.toString()] || 0;
      const adjustedCapacity = Math.max(0, (appointment.capacity || 0) - excludedCount);

      return {
        ...appointment,
        capacity: adjustedCapacity,
        reservationCounts: counts
      };
    });

    return NextResponse.json({ appointments: appointmentsWithCounts });

  } catch (error: any) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 