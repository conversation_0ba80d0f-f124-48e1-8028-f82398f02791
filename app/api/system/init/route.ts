import { NextRequest, NextResponse } from 'next/server';
import { initializeAutomation, getAutomationHealth } from '@/lib/services/automation-init';

// Global flag to prevent multiple initializations
let isInitialized = false;

export async function GET(request: NextRequest) {
  try {
    // Auto-initialize on first request if not already done
    if (!isInitialized) {
      console.log('🚀 Auto-initializing system services...');
      initializeAutomation();
      isInitialized = true;
    }

    // Return current automation health status
    const health = getAutomationHealth();
    
    return NextResponse.json({
      success: true,
      message: 'System initialization check completed',
      automation: health,
      initialized: isInitialized,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('System initialization failed:', error);
    return NextResponse.json({
      success: false,
      error: 'System initialization failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { force } = await request.json();
    
    if (force || !isInitialized) {
      console.log('🔄 Force initializing system services...');
      initializeAutomation();
      isInitialized = true;
    }

    const health = getAutomationHealth();
    
    return NextResponse.json({
      success: true,
      message: 'System services initialized successfully',
      automation: health,
      initialized: isInitialized,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Forced system initialization failed:', error);
    return NextResponse.json({
      success: false,
      error: 'System initialization failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
