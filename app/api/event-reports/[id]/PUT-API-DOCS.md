# Event Report PUT API Documentation

## Endpoint Overview

**PUT** `/api/event-reports/[id]`

Updates an existing event report with new personnel assignments, timing information, and other editable fields. This endpoint is used by supervisors and authorized users to modify event reports before they are submitted for validation.

## Authentication & Authorization

### Required Authentication
- Valid session with authenticated user OR mobile app authentication
- User permissions are automatically loaded if not present in session

### Mobile App Authentication
- Include `isMobile: true` in request body
- Provide `Authorization: Bearer <token>` header with valid user token
- User tokens are stored in the user's `tokens` array field

### Authorization Rules
The endpoint implements a two-tier permission system:

1. **Global Permission**: Users with `EDIT_EVENT_REPORTS` permission can edit any report
2. **Supervisor Permission**: Users who are assigned as supervisors to the specific event can edit that report

### Permission Functions Used
- `canUserEditEventReports(user)` - Checks for global edit permissions
- Supervisor check: Validates if user ID matches any supervisor ID in the report

## Request Parameters

### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | MongoDB ObjectId of the event report to update |

### Request Body
The request body should contain **only the fields to be updated**. All fields are optional and support partial updates:

```typescript
{
  isMobile?: boolean,              // Set to true for mobile app authentication
  eventStartTime?: Date,           // Updated event start time
  eventEndTime?: Date,             // Updated event end time
  paps?: Array<{                   // PAP (Personnel Assignment Personnel) updates
    userId: string,                // MongoDB ObjectId as string
    timeRange: {
      startTime: Date,
      endTime: Date
    }
  }>,
  cooks?: Array<{                  // Cook personnel updates
    userId: string,                // MongoDB ObjectId as string
    timeRange: {
      startTime: Date,
      endTime: Date
    },
    percentage?: number            // Commission percentage (default: 100)
  }>
}
```

**Important Notes:**
- **Partial Updates Supported**: You only need to include the fields you want to change
- **Unchanged Fields Preserved**: Any fields not included in the request will remain unchanged
- **Array Fields**: When updating `paps` or `cooks`, the entire array is replaced (not merged)
- **Change Tracking**: Only the fields you provide will be tracked in the history

## Business Logic & Validation

### Status Validation
- Reports with status `validated` cannot be edited
- Returns 400 error if attempting to edit a validated report

### Automatic Status Transitions
- If current status is `pending`, it automatically updates to `processing` when edited
- This indicates that work has begun on the report

### Change Tracking
All modifications are tracked in the report's history:
- Tracks previous and new values for: `eventStartTime`, `eventEndTime`, `paps`, `cooks`
- Records who made the change and when
- Creates audit trail for compliance

### Data Transformation
- User IDs in `paps` and `cooks` arrays are converted to MongoDB ObjectIds
- Cook percentage defaults to 100 if not provided
- Time ranges are preserved as-is

## Response Format

### Success Response (200)
Returns the updated event report with full population of related data:

```typescript
{
  _id: string,
  eventId: {
    _id: string,
    name: string,
    location: string,
    startDate: Date,
    endDate: Date,
    status: string,
    branchId: { _id: string, name: string },
    partnerId: { _id: string, name: string },
    eventTypeId: { _id: string, name: string, code: string }
  },
  eventStartTime: Date,
  eventEndTime: Date,
  status: 'pending' | 'processing' | 'submitted' | 'validated',
  supervisors: Array<{
    _id: string,
    name: string,
    email: string
  }>,
  paps: Array<{
    userId: {
      _id: string,
      name: string,
      email: string
    },
    timeRange: {
      startTime: Date,
      endTime: Date
    },
    linkedReservationIds?: string[]
  }>,
  cooks: Array<{
    userId: {
      _id: string,
      name: string,
      email: string
    },
    timeRange: {
      startTime: Date,
      endTime: Date
    },
    percentage: number
  }>,
  history: Array<{
    action: string,
    changedBy: {
      _id: string,
      name: string,
      email: string
    },
    changedAt: Date,
    previousValue?: any,
    newValue?: any
  }>,
  linkedReservationIds?: string[],
  validationComments?: string,
  commissions?: CommissionData,
  createdAt: Date,
  updatedAt: Date
}
```

## Error Responses

### 401 Unauthorized
```json
{
  "error": "Unauthorized"
}
```
**Cause**: No valid session found

### 400 Bad Request
```json
{
  "error": "Invalid report ID"
}
```
**Cause**: Invalid or missing MongoDB ObjectId in path parameter

```json
{
  "error": "Cannot edit validated report"
}
```
**Cause**: Attempting to edit a report with status 'validated'

### 403 Forbidden
```json
{
  "error": "Insufficient permissions to edit this report"
}
```
**Cause**: User lacks both global edit permissions and supervisor permissions for this specific report

### 404 Not Found
```json
{
  "error": "Event report not found"
}
```
**Cause**: No event report exists with the provided ID

### 500 Internal Server Error
```json
{
  "error": "Failed to update event report"
}
```
**Cause**: Database error or other server-side issue

## Side Effects

### Automatic Reservation Linking
After every successful update (if report is not validated):
- Triggers automatic reservation linking service
- Links reservations to the event based on updated personnel time ranges
- Failure of linking does not fail the update operation
- Linking errors are logged but don't affect the response

### History Tracking
- Creates new history entry with action "Report updated"
- Records all changed fields with previous and new values
- Associates change with the authenticated user

## Example Usage

### Update Only Event Timing
```javascript
// Only update event start and end times - all other fields remain unchanged
const response = await fetch('/api/event-reports/507f1f77bcf86cd799439011', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    eventStartTime: '2024-01-15T09:30:00Z',
    eventEndTime: '2024-01-15T18:30:00Z'
  })
});
```

### Update Only Personnel Assignments
```javascript
// Only update PAPs and cooks - event timing remains unchanged
const response = await fetch('/api/event-reports/507f1f77bcf86cd799439011', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    paps: [
      {
        userId: '507f1f77bcf86cd799439012',
        timeRange: {
          startTime: '2024-01-15T10:00:00Z',
          endTime: '2024-01-15T18:00:00Z'
        }
      }
    ],
    cooks: [
      {
        userId: '507f1f77bcf86cd799439013',
        timeRange: {
          startTime: '2024-01-15T09:00:00Z',
          endTime: '2024-01-15T19:00:00Z'
        },
        percentage: 100
      }
    ]
  })
});

const updatedReport = await response.json();
```

### Update Only PAPs (Cooks Remain Unchanged)
```javascript
// Only update PAPs - cooks and timing remain unchanged
const response = await fetch('/api/event-reports/507f1f77bcf86cd799439011', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    paps: [
      {
        userId: '507f1f77bcf86cd799439012',
        timeRange: {
          startTime: '2024-01-15T10:00:00Z',
          endTime: '2024-01-15T18:00:00Z'
        }
      }
    ]
  })
});
```

### Mobile Application Usage
```javascript
const response = await fetch('/api/event-reports/507f1f77bcf86cd799439011', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-mobile-token-here'
  },
  body: JSON.stringify({
    isMobile: true,
    eventStartTime: '2024-01-15T09:30:00Z',
    eventEndTime: '2024-01-15T18:30:00Z',
    paps: [
      {
        userId: '507f1f77bcf86cd799439012',
        timeRange: {
          startTime: '2024-01-15T10:00:00Z',
          endTime: '2024-01-15T18:00:00Z'
        }
      }
    ]
  })
});

const updatedReport = await response.json();
```

## Database Collections Affected

- **eventreports** - Primary collection updated
- **reservations** - Potentially updated through automatic linking service

## Performance Considerations

- Uses efficient MongoDB queries with proper indexing
- Population of related data may impact response time for large datasets
- Automatic reservation linking runs asynchronously but may add processing time

## Security Notes

- All user inputs are validated and sanitized
- MongoDB ObjectId validation prevents injection attacks
- Permission checks prevent unauthorized access
- Mobile authentication uses secure token validation
- Audit trail maintains compliance requirements
