import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import { EventReport } from '@/models/EventReport';
import Reservation from '@/models/Reservation';
import { EventReservationLinkingService } from '@/lib/services/event-reservation-linking';
import mongoose from 'mongoose';
import { canUserEditEventReports } from '@/lib/utils/permissions-utils';
import dbConnect from '@/lib/db';

/**
 * GET /api/event-reports/[id]/reservations
 * Get linked reservations for an event report
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
  await dbConnect();
    
    if (!params.id || !mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: 'Invalid event report ID' },
        { status: 400 }
      );
    }

    const report = await EventReport.findById(params.id)
      .populate('eventId', 'name location startDate endDate')
      .populate('supervisors', 'name email')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email');

    if (!report) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Check permissions
    if (!canUserEditEventReports(session.user)) {
      // Check if user is a supervisor of this event
      const isSupervisor = report.supervisors.some((sup: any) => 
        sup._id.toString() === session.user.id
      );
      
      if (!isSupervisor) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
      }
    }

    // Get linked reservations using the service
    const linkingService = new EventReservationLinkingService();
    const linkingResult = await linkingService.linkReservationsToEvent(params.id);

    // Get detailed reservation data
    const reservations = await Reservation.find({
      linkedEventId: report.eventId
    })
    .populate('partnerId', 'name email')
    .populate('appointmentId')
    .sort({ createdAt: -1 })
    .lean();

    // Parse query parameters for filtering
    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');
    const status = searchParams.get('status');
    const frozen = searchParams.get('frozen');

    let filteredReservations = reservations;

    // Apply filters
    if (agentId) {
      filteredReservations = filteredReservations.filter(r => 
        r.partnerId._id.toString() === agentId
      );
    }

    if (status) {
      filteredReservations = filteredReservations.filter(r => r.status === status);
    }

    if (frozen !== null) {
      const isFrozen = frozen === 'true';
      filteredReservations = filteredReservations.filter(r => 
        Boolean(r.linkingFrozen) === isFrozen
      );
    }

    // Create PAP-specific reservation breakdown
    const papReservationBreakdown = report.paps.map((pap: any) => {
      const papReservations = filteredReservations.filter(r =>
        pap.linkedReservationIds?.some((linkedId: any) =>
          linkedId.toString() === (r as any)._id.toString()
        )
      );

      return {
        papId: pap.userId._id,
        papName: pap.userId.name,
        timeRange: pap.timeRange,
        reservations: papReservations,
        summary: {
          count: papReservations.length,
          revenue: papReservations.reduce((sum: number, r: any) =>
            sum + (r.sellingAmount || 0), 0
          ),
          byStatus: papReservations.reduce((acc: any, r: any) => {
            acc[r.status] = (acc[r.status] || 0) + 1;
            return acc;
          }, {})
        }
      };
    });

    return NextResponse.json({
      report: {
        id: report._id,
        eventId: report.eventId,
        status: report.status,
        frozen: report.status === 'validated'
      },
      linkingResult,
      reservations: filteredReservations,
      papReservationBreakdown, // New: PAP-specific breakdown
      summary: {
        total: filteredReservations.length,
        frozen: filteredReservations.filter(r => r.linkingFrozen).length,
        byStatus: filteredReservations.reduce((acc: any, r: any) => {
          acc[r.status] = (acc[r.status] || 0) + 1;
          return acc;
        }, {}),
        byAgent: filteredReservations.reduce((acc: any, r: any) => {
          const agentId = r.partnerId._id.toString();
          acc[agentId] = (acc[agentId] || 0) + 1;
          return acc;
        }, {}),
        totalRevenue: filteredReservations.reduce((sum: number, r: any) =>
          sum + (r.sellingAmount || 0), 0
        )
      }
    });

  } catch (error) {
    console.error('Error fetching event report reservations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reservations' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/event-reports/[id]/reservations
 * Manually link or unlink reservations
 */
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
  await dbConnect();
    
    if (!params.id || !mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: 'Invalid event report ID' },
        { status: 400 }
      );
    }

    const { action, reservationId } = await request.json();

    if (!action || !reservationId) {
      return NextResponse.json(
        { error: 'Missing required fields: action, reservationId' },
        { status: 400 }
      );
    }

    if (!['link', 'unlink'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "link" or "unlink"' },
        { status: 400 }
      );
    }

    const report = await EventReport.findById(params.id);
    if (!report) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Check permissions
    if (!canUserEditEventReports(session.user)) {
      // Check if user is a supervisor of this event
      const isSupervisor = report.supervisors.some((sup: any) => 
        sup._id.toString() === session.user.id
      );
      
      if (!isSupervisor) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
      }
    }

    // Check if report is validated (frozen)
    if (report.status === 'validated') {
      return NextResponse.json(
        { error: 'Cannot modify links for validated reports' },
        { status: 400 }
      );
    }

    const linkingService = new EventReservationLinkingService();

    if (action === 'link') {
      await linkingService.manuallyLinkReservation(
        reservationId,
        report.eventId.toString(),
        session.user.id
      );
    } else {
      await linkingService.manuallyUnlinkReservation(
        reservationId,
        session.user.id
      );
    }

    // Get updated reservation data
    const reservation = await Reservation.findById(reservationId)
      .populate('partnerId', 'name email')
      .lean();

    return NextResponse.json({
      success: true,
      action,
      reservation,
      message: `Reservation ${action}ed successfully`
    });

  } catch (error: any) {
    console.error('Error managing reservation link:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to manage reservation link' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/event-reports/[id]/reservations
 * Refresh all reservation links for an event report
 */
export async function PUT(
  _request: Request,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
  await dbConnect();
    
    if (!params.id || !mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: 'Invalid event report ID' },
        { status: 400 }
      );
    }

    const report = await EventReport.findById(params.id);
    if (!report) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Check permissions
    if (!canUserEditEventReports(session.user)) {
      // Check if user is a supervisor of this event
      const isSupervisor = report.supervisors.some((sup: any) => 
        sup._id.toString() === session.user.id
      );
      
      if (!isSupervisor) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
      }
    }

    // Check if report is validated (frozen)
    if (report.status === 'validated') {
      return NextResponse.json(
        { error: 'Cannot refresh links for validated reports' },
        { status: 400 }
      );
    }

    // Refresh links
    const linkingResult = await report.refreshReservationLinks();

    return NextResponse.json({
      success: true,
      linkingResult,
      message: 'Reservation links refreshed successfully'
    });

  } catch (error: any) {
    console.error('Error refreshing reservation links:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to refresh reservation links' },
      { status: 500 }
    );
  }
}
