import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import { EventReport } from '@/models/EventReport';
import Reservation from '@/models/Reservation';
import mongoose from 'mongoose';
import { canUserValidateEventReports } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';

interface Params {
  params: {
    id: string;
  };
}

interface LinkReservationRequest {
  papUserId: string;
  reservationId: string;
  action: 'link' | 'unlink';
}

export async function POST(request: Request, { params }: Params) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const p =await params;
  
  if (!canUserValidateEventReports(session.user)) {
    return NextResponse.json(
      { error: 'Insufficient permissions to manage event report reservations' },
      { status: 403 }
    );
  }
  
  try {
    await connectToDatabase();
    
    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid report ID' },
        { status: 400 }
      );
    }

    const { papUserId, reservationId, action }: LinkReservationRequest = await request.json();
    
    if (!papUserId || !reservationId || !action) {
      return NextResponse.json(
        { error: 'Missing required fields: papUserId, reservationId, action' },
        { status: 400 }
      );
    }

    if (!['link', 'unlink'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "link" or "unlink"' },
        { status: 400 }
      );
    }

    if (!mongoose.Types.ObjectId.isValid(papUserId) || !mongoose.Types.ObjectId.isValid(reservationId)) {
      return NextResponse.json(
        { error: 'Invalid papUserId or reservationId format' },
        { status: 400 }
      );
    }

    // Get current report
    const report = await EventReport.findById(p.id);
    if (!report) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Check if report is in a state that allows modifications
    if (report.status !== 'submitted') {
      return NextResponse.json(
        { error: `Cannot modify reservations for report with status: ${report.status}` },
        { status: 400 }
      );
    }

    // Find the PAP in the report
    const papIndex = report.paps.findIndex((pap: any) => 
      pap.userId.toString() === papUserId
    );

    if (papIndex === -1) {
      return NextResponse.json(
        { error: 'PAP not found in this event report' },
        { status: 404 }
      );
    }

    // Verify reservation exists
    const reservation = await Reservation.findById(reservationId);
    if (!reservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }

    // Get current linked reservations for this PAP
    const currentLinkedIds = report.paps[papIndex].linkedReservationIds || [];
    const reservationIdStr = reservationId.toString();
    const isCurrentlyLinked = currentLinkedIds.some((id: any) => id.toString() === reservationIdStr);

    let updatedLinkedIds = [...currentLinkedIds];
    let actionPerformed = '';

    if (action === 'link') {
      if (isCurrentlyLinked) {
        return NextResponse.json(
          { error: 'Reservation is already linked to this PAP' },
          { status: 400 }
        );
      }

      // Check if reservation is linked to another PAP in this report
      const linkedToOtherPap = report.paps.some((pap: any, index: number) => 
        index !== papIndex && 
        pap.linkedReservationIds?.some((id: any) => id.toString() === reservationIdStr)
      );

      if (linkedToOtherPap) {
        return NextResponse.json(
          { error: 'Reservation is already linked to another PAP in this report' },
          { status: 400 }
        );
      }

      updatedLinkedIds.push(new mongoose.Types.ObjectId(reservationId));
      actionPerformed = 'linked';
    } else { // unlink
      if (!isCurrentlyLinked) {
        return NextResponse.json(
          { error: 'Reservation is not linked to this PAP' },
          { status: 400 }
        );
      }

      updatedLinkedIds = updatedLinkedIds.filter((id: any) => id.toString() !== reservationIdStr);
      actionPerformed = 'unlinked';
    }

    // Update the report
    const updateData = {
      [`paps.${papIndex}.linkedReservationIds`]: updatedLinkedIds,
      $push: {
        history: {
          action: `Reservation ${actionPerformed} manually during validation`,
          changedBy: new mongoose.Types.ObjectId(session.user.id),
          changedAt: new Date(),
          previousValue: { 
            papUserId,
            reservationId,
            previousLinkedCount: currentLinkedIds.length
          },
          newValue: { 
            papUserId,
            reservationId,
            action: actionPerformed,
            newLinkedCount: updatedLinkedIds.length
          }
        }
      }
    };

    await EventReport.findByIdAndUpdate(p.id, updateData);

    // Get updated PAP data
    const updatedReport = await EventReport.findById(p.id)
      .populate('paps.userId', 'name email')
      .lean();

    if (!updatedReport) {
      return NextResponse.json(
        { error: 'Failed to retrieve updated report' },
        { status: 500 }
      );
    }

    const updatedPap = (updatedReport as any).paps[papIndex];

    return NextResponse.json({
      success: true,
      message: `Reservation ${actionPerformed} successfully`,
      updatedPap: {
        userId: updatedPap.userId._id,
        linkedReservationIds: updatedPap.linkedReservationIds || []
      },
      action: actionPerformed,
      reservationId,
      papUserId
    });

  } catch (error) {
    console.error('Error linking/unlinking reservation:', error);
    return NextResponse.json(
      { error: 'Failed to process reservation linking request' },
      { status: 500 }
    );
  }
}
