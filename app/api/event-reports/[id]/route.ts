import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import { EventReport } from '@/models/EventReport';
import mongoose from 'mongoose';
import { canUserEditEventReports, canUserViewEventReports } from '@/lib/utils/permissions-utils';
import { getUserPermissions, getUserRoles } from '@/app/api/utils/server-permission-utils';
import { transformReportForFrontend, validateTimeRange, validatePersonnelTimeRanges } from '@/app/api/utils/event-report-utils';
import { EventReservationLinkingService } from '@/lib/services/event-reservation-linking';
import { getUserFromToken } from '@/app/api/affectations/utils/mobile_auth_utils';

import dbConnect from '@/lib/db';

interface Params {
  params: {
    id: string;
  };
}

export async function GET(request: Request, { params }: Params) {
  const requestHeaders = request.headers;

  // Check for mobile authentication first
  let session = null;
  const authHeader = requestHeaders.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    session = await getUserFromToken({ isMobile: true }, requestHeaders);
  }

  // Fall back to web session if no mobile session
  if (!session) {
    session = await getServerSession(authOptions);
  }

  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const p = await params;
  
  try {
  await dbConnect();
    
    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid report ID' },
        { status: 400 }
      );
    }

    const report = await EventReport.findById(p.id)
      .populate({
        path: 'eventId',
        select: 'name location startDate endDate status branchId partnerId eventTypeId',
        populate: [
          { path: 'branchId', select: 'name' },
          { path: 'partnerId', select: 'name' },
          { path: 'eventTypeId', select: 'name code' }
        ]
      })
      .populate('supervisors', 'name email')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email')
      .populate('history.changedBy', 'name email')
      .lean();

    if (!report) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Get user ID (handle both web and mobile sessions)
    const userId = session.user.id || session.user._id?.toString();

    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid user session' },
        { status: 401 }
      );
    }

    // Check permissions
    const isSupervisor = (report as any).supervisors?.some((supervisor: any) =>
      supervisor._id.toString() === userId
    ) || false;

    if (!canUserViewEventReports(session.user) && !isSupervisor) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view this report' },
        { status: 403 }
      );
    }

    // Transform the report data to match frontend expectations
    const transformedReport = await transformReportForFrontend(report);

    return NextResponse.json(transformedReport);

  } catch (error) {
    console.error('Error fetching event report:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event report' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request, { params }: Params) {
  const requestBody = await request.json();
  const requestHeaders = request.headers;

  // Check for mobile authentication first
  let session = null;
  const authHeader = requestHeaders.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    session = await getUserFromToken({ isMobile: true }, requestHeaders);
  }

  // Fall back to web session if no mobile session
  if (!session) {
    session = await getServerSession(authOptions);
  }

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Ensure user has roles and permissions loaded
  if (!session.user.roles) {
    session.user.roles = await getUserRoles(session);
  }
  if (!session.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }

  const p = await params;

  try {
  await dbConnect();

    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid report ID' },
        { status: 400 }
      );
    }

    const reportData = requestBody;
    
    // Get current report
    const currentReport = await EventReport.findById(p.id);
    if (!currentReport) {
      return NextResponse.json(
        { error: 'Event report not found' },
        { status: 404 }
      );
    }

    // Get user ID (handle both web and mobile sessions)
    const userId = session.user.id || session.user._id?.toString();

    if (!userId) {
      return NextResponse.json(
        { error: 'Invalid user session' },
        { status: 401 }
      );
    }

    // Check permissions
    const isSupervisor = currentReport.supervisors.some((supervisorId: any) =>
      supervisorId.toString() === userId
    );

    if (!canUserEditEventReports(session.user) && !isSupervisor) {
      return NextResponse.json(
        { error: 'Insufficient permissions to edit this report' },
        { status: 403 }
      );
    }

    // Check if report is in editable state
    if (currentReport.status === 'validated') {
      return NextResponse.json(
        { error: 'Cannot edit validated report' },
        { status: 400 }
      );
    }

    // Validate and normalize event time range if being updated
    if (reportData.eventStartTime || reportData.eventEndTime) {
      try {
        // Use current values if only one is being updated
        const currentStartTime = reportData.eventStartTime ? new Date(reportData.eventStartTime) : currentReport.eventStartTime;
        const currentEndTime = reportData.eventEndTime ? new Date(reportData.eventEndTime) : currentReport.eventEndTime;

        // Check if dates are valid
        if (isNaN(currentStartTime.getTime()) || isNaN(currentEndTime.getTime())) {
          return NextResponse.json(
            { error: 'Invalid date format for event times. Please provide valid ISO date strings.' },
            { status: 400 }
          );
        }

        // Validate that startTime is before endTime
        if (currentStartTime >= currentEndTime) {
          return NextResponse.json(
            { error: 'Event start time must be before end time' },
            { status: 400 }
          );
        }

        // Ensure dates are stored as UTC
        if (reportData.eventStartTime) {
          reportData.eventStartTime = currentStartTime.toISOString();
        }
        if (reportData.eventEndTime) {
          reportData.eventEndTime = currentEndTime.toISOString();
        }

      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid date format for event times. Please provide valid ISO date strings.' },
          { status: 400 }
        );
      }
    }

    // Validate personnel time ranges are within event time range
    const eventStartTime = reportData.eventStartTime || currentReport.eventStartTime;
    const eventEndTime = reportData.eventEndTime || currentReport.eventEndTime;

    if (eventStartTime && eventEndTime) {
      // Collect all personnel from the update data
      const allPersonnel = [];

      if (reportData.paps) {
        allPersonnel.push(...reportData.paps.map((pap: any) => ({
          userId: pap.userId,
          timeRange: pap.timeRange,
          name: 'PAP'
        })));
      }

      if (reportData.cooks) {
        allPersonnel.push(...reportData.cooks.map((cook: any) => ({
          userId: cook.userId,
          timeRange: cook.timeRange,
          name: 'Cook'
        })));
      }

      if (allPersonnel.length > 0) {
        const validationErrors = validatePersonnelTimeRanges(
          eventStartTime,
          eventEndTime,
          allPersonnel
        );

        if (validationErrors.length > 0) {
          return NextResponse.json(
            {
              error: 'Personnel time validation failed',
              details: validationErrors.map(e => e.message)
            },
            { status: 400 }
          );
        }
      }
    }

    // Prepare update data
    const updateData: any = {};
    const historyEntry: any = {
      action: 'Report updated',
      changedBy: new mongoose.Types.ObjectId(userId),
      changedAt: new Date(),
      previousValue: {},
      newValue: {}
    };

    // Track changes for history
    const fieldsToTrack = ['eventStartTime', 'eventEndTime', 'paps', 'cooks'];
    fieldsToTrack.forEach(field => {
      if (reportData[field] !== undefined) {
        historyEntry.previousValue[field] = currentReport[field];
        historyEntry.newValue[field] = reportData[field];
        updateData[field] = reportData[field];
      }
    });

    // Convert user IDs to ObjectIds for paps and cooks
    if (reportData.paps) {
      updateData.paps = reportData.paps.map((pap: any) => ({
        userId: new mongoose.Types.ObjectId(pap.userId),
        timeRange: pap.timeRange
      }));
    }

    if (reportData.cooks) {
      updateData.cooks = reportData.cooks.map((cook: any) => ({
        userId: new mongoose.Types.ObjectId(cook.userId),
        timeRange: cook.timeRange,
        percentage: cook.percentage || 100
      }));
    }

    // Update status to processing if it was pending
    if (currentReport.status === 'pending') {
      updateData.status = 'processing';
    }

    // Add history entry
    updateData.$push = {
      history: historyEntry
    };

    // Update the report
    const updatedReport = await EventReport.findByIdAndUpdate(
      p.id,
      updateData,
      { new: true }
    )
      .populate({
        path: 'eventId',
        select: 'name location startDate endDate status branchId partnerId eventTypeId',
        populate: [
          { path: 'branchId', select: 'name' },
          { path: 'partnerId', select: 'name' },
          { path: 'eventTypeId', select: 'name code' }
        ]
      })
      .populate('supervisors', 'name email')
      .populate('paps.userId', 'name email')
      .populate('cooks.userId', 'name email')
      .populate('history.changedBy', 'name email')
      .lean();

    // Trigger automatic reservation linking after every report update
    // Only if the report is not validated (frozen)
    if (updatedReport && (updatedReport as any).status !== 'validated') {
      try {
        const linkingService = new EventReservationLinkingService();
        await linkingService.linkReservationsToEvent(p.id);
        console.log(`Automatic reservation linking completed for report ${p.id}`);
      } catch (error) {
        console.error('Failed to automatically link reservations:', error);
        // Don't fail the update if linking fails, just log the error
      }
    }

    return NextResponse.json(updatedReport);

  } catch (error) {
    console.error('Error updating event report:', error);
    return NextResponse.json(
      { error: 'Failed to update event report' },
      { status: 500 }
    );
  }
}


