import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function GET() {
  try {
    const enPath = process.env.EN_PATH!;
    const frPath = process.env.FR_PATH!;

    const [enContent, frContent] = await Promise.all([
      fs.readFile(enPath, 'utf-8'),
      fs.readFile(frPath, 'utf-8'),
    ]);

    return NextResponse.json({
      en: JSON.parse(enContent),
      fr: JSON.parse(frContent),
    });
  } catch (error) {
    console.error('Error reading translation files:', error);
    return NextResponse.json(
      { error: 'Failed to read translation files' },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { translations } = await req.json();
    const frPath = process.env.FR_PATH!;

    await fs.writeFile(
      frPath,
      JSON.stringify(translations, null, 2),
      'utf-8'
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving translations:', error);
    return NextResponse.json(
      { error: 'Failed to save translations' },
      { status: 500 }
    );
  }
} 