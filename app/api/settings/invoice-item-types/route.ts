import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import InvoiceItemType from '@/models/InvoiceItemType';

// GET: List all invoice item types
export async function GET() {
  await dbConnect();
  try {
    const items = await InvoiceItemType.find().populate('branchId', 'name').lean();
    // Map to include branchName and remove branchId
    const mapped = items.map(item => {
      if (!item.branchId) {
        return {
          ...item,
          branchName: 'All',
          branchId: 'all',
        };
      }
      return {
        ...item,
        branchName: item.branchId?.name || '',
        branchId: undefined
      };
    });
    return NextResponse.json({ items: mapped });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch invoice item types.' }, { status: 500 });
  }
}

// POST: Create a new invoice item type
export async function POST(req: NextRequest) {
  await dbConnect();
  try {
    const { name, code, branchId, requiresContreFacture, chargeAmount } = await req.json();
    if (!name || !code || branchId === undefined || chargeAmount === undefined || isNaN(chargeAmount)) {
      return NextResponse.json({ error: 'Missing required fields.' }, { status: 400 });
    }
    // If branchId is 'all', do not set it in the document
    const doc: any = { name, code, requiresContreFacture: !!requiresContreFacture, chargeAmount: Number(chargeAmount) };
    if (branchId !== 'all') {
      doc.branchId = branchId;
    }
    const created = await InvoiceItemType.create(doc);
    return NextResponse.json({ item: created });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create invoice item type. ' + error }, { status: 500 });
  }
}

// TODO: PATCH (update by id), DELETE (delete by id) 