import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import InvoiceItemType from '@/models/InvoiceItemType';
import mongoose from 'mongoose';

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  const { id } =await params;
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
  }
  try {
    const item = await InvoiceItemType.findById(id).lean();
    if (!item) return NextResponse.json({ error: 'Not found' }, { status: 404 });
    return NextResponse.json({ item });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch invoice item type.' }, { status: 500 });
  }
}

export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  const { id } =await params;
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return NextResponse.json({ error: 'Invalid ID' }, { status: 400 });
  }
  try {
    const data = await req.json();
    const { name, code, branchId, requiresContreFacture, chargeAmount } = data;
    if (!name || !code || branchId === undefined || chargeAmount === undefined || isNaN(chargeAmount)) {
      return NextResponse.json({ error: 'Missing required fields.' }, { status: 400 });
    }
    const updateDoc: any = { name, code, requiresContreFacture: !!requiresContreFacture, chargeAmount: Number(chargeAmount) };
    if (branchId !== 'all') {
      updateDoc.branchId = branchId;
    } else {
      updateDoc.$unset = { branchId: 1 };
    }
    const updated = await InvoiceItemType.findByIdAndUpdate(
      id,
      updateDoc,
      { new: true }
    ).lean();
    if (!updated) return NextResponse.json({ error: 'Not found' }, { status: 404 });
    return NextResponse.json({ item: updated });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to update invoice item type.' }, { status: 500 });
  }
}

export async function DELETE() {
  return NextResponse.json({ error: 'Delete not allowed.' }, { status: 405 });
} 