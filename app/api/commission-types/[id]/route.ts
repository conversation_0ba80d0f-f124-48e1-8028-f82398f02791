import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import CommissionType from '@/models/CommissionType';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SETTINGS_PERMISSIONS } from '@/types/permission-codes';
import mongoose from 'mongoose';
import { getUserPermissions } from '../../utils/server-permission-utils';

interface Params {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: Params) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    const { id } = params;

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    // Check if user has permission to view settings
    const hasPermission = session.user.permissions?.includes(SETTINGS_PERMISSIONS.EDIT_SETTINGS);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      );
    }

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid commission type ID' },
        { status: 400 }
      );
    }

    const commissionType = await CommissionType.findById(id)
      .populate('statusTrigger', 'name name_en code')
      .populate('eligibleUsers', 'name email')
      .populate('eligibleBranches', 'name')
      .lean();

    if (!commissionType) {
      return NextResponse.json(
        { error: 'Commission type not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(commissionType);
  } catch (error) {
    console.error('GET commission type error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch commission type' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: Params) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    const { id } = await params;

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    // Check if user has permission to edit settings
    const hasPermission = session.user.permissions?.includes(SETTINGS_PERMISSIONS.EDIT_SETTINGS);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      );
    }

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid commission type ID' },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.statusTrigger || !body.recipient || body.amount === undefined) {
      return NextResponse.json(
        { error: 'Name, status trigger, recipient and amount are required' },
        { status: 400 }
      );
    }
    /* THIS IS FOR FUTURE USE , DONT DELETE THIS
    // Validate that at least one recipient is selected
    if (!body.recipient.partner && !body.recipient.branchPAP && !body.recipient.assignedUser) {
      return NextResponse.json(
        { error: 'At least one recipient must be selected' },
        { status: 400 }
      );
    }
    */
    // Prepare update data
    const updateData = {
      name: body.name,
      code: body.code,
      statusTrigger: body.statusTrigger,
      recipient: {
        partner: body.recipient.partner || false,
        branchPAP: body.recipient.branchPAP || false,
        assignedUser: body.recipient.assignedUser || false
      },
      amount: body.amount,
      isAlwaysActive: body.isAlwaysActive ?? true,
      startDate: body.isAlwaysActive ? null : body.startDate,
      endDate: body.isAlwaysActive ? null : body.endDate,

      // Time restrictions
      timeRestricted: body.timeRestricted ?? false,
      startTime: body.timeRestricted ? body.startTime : null,
      endTime: body.timeRestricted ? body.endTime : null,

      // Day restrictions
      dayRestricted: body.dayRestricted ?? false,
      daysOfWeek: body.dayRestricted ? body.daysOfWeek : [],

      // User eligibility restrictions
      userRestricted: body.userRestricted ?? false,
      eligibleUsers: body.userRestricted ? body.eligibleUsers : [],

      // Branch eligibility restrictions
      branchRestricted: body.branchRestricted ?? false,
      eligibleBranches: body.branchRestricted ? body.eligibleBranches : [],
      isAutoApproved: body.isAutoApproved ?? false,
    };

    // Update commission type
    const commissionType = await CommissionType.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('statusTrigger', 'name name_en code');

    if (!commissionType) {
      return NextResponse.json(
        { error: 'Commission type not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(commissionType);
  } catch (error: any) {
    console.error('Update commission type error:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A commission type with this name already exists' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to update commission type' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    const { id } = params;

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    // Check if user has permission to edit settings
    const hasPermission = session.user.permissions?.includes(SETTINGS_PERMISSIONS.EDIT_SETTINGS);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      );
    }

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid commission type ID' },
        { status: 400 }
      );
    }

    const commissionType = await CommissionType.findByIdAndDelete(id);

    if (!commissionType) {
      return NextResponse.json(
        { error: 'Commission type not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('DELETE commission type error:', error);
    return NextResponse.json(
      { error: 'Failed to delete commission type' },
      { status: 500 }
    );
  }
}