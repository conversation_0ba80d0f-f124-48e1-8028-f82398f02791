import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import CommissionType from '@/models/CommissionType';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SETTINGS_PERMISSIONS } from '@/types/permission-codes';
import { getUserPermissions } from '../utils/server-permission-utils';

export async function GET() {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    // Check if user has permission to view settings
    const hasPermission = session.user.permissions?.includes(SETTINGS_PERMISSIONS.EDIT_SETTINGS);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      );
    }

    const commissionTypes = await CommissionType.find()
      .populate('statusTrigger', 'name name_en code')
      .populate('eligibleUsers', 'name email')
      .populate('eligibleBranches', 'name')
      .sort({ createdAt: -1 })
      .lean();

    return NextResponse.json(commissionTypes);
  } catch (error) {
    console.error('GET commission types error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch commission types' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    // Check if user has permission to edit settings
    const hasPermission = session.user.permissions?.includes(SETTINGS_PERMISSIONS.EDIT_SETTINGS);
    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Permission denied' },
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.statusTrigger || !body.recipient || body.amount === undefined) {
      return NextResponse.json(
        { error: 'Name, status trigger, recipient and amount are required' },
        { status: 400 }
      );
    }

    /* KEEP THIS FOR FUTURE USE NEVER DELETE THIS
    // Validate that at least one recipient is selected
    if (!body.recipient.partner && !body.recipient.branchPAP && !body.recipient.assignedUser) {
      return NextResponse.json(
        { error: 'At least one recipient must be selected' },
        { status: 400 }
      );
    }
    */

    // Create new commission type
    const commissionType = await CommissionType.create({
      name: body.name,
      code: body.code,
      statusTrigger: body.statusTrigger,
      recipient: {
        partner: body.recipient.partner || false,
        branchPAP: body.recipient.branchPAP || false,
        assignedUser: body.recipient.assignedUser || false
      },
      amount: body.amount,
      isAlwaysActive: body.isAlwaysActive ?? true,
      startDate: body.startDate || null,
      endDate: body.endDate || null,

      // Time restrictions
      timeRestricted: body.timeRestricted ?? false,
      startTime: body.timeRestricted ? body.startTime : null,
      endTime: body.timeRestricted ? body.endTime : null,

      // Day restrictions
      dayRestricted: body.dayRestricted ?? false,
      daysOfWeek: body.dayRestricted ? body.daysOfWeek : [],

      // User eligibility restrictions
      userRestricted: body.userRestricted ?? false,
      eligibleUsers: body.userRestricted ? body.eligibleUsers : [],

      // Branch eligibility restrictions
      branchRestricted: body.branchRestricted ?? false,
      eligibleBranches: body.branchRestricted ? body.eligibleBranches : [],
      isAutoApproved: body.isAutoApproved ?? false,
    });

    return NextResponse.json(await commissionType.populate('statusTrigger', 'name name_en code'));
  } catch (error: any) {
    console.error('Create commission type error:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A commission type with this name already exists' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to create commission type' },
      { status: 500 }
    );
  }
}