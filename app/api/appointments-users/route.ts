import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Types } from 'mongoose';
import Branch from '@/models/Branch';

export async function GET(request: Request) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is BranchesAgent
    const isBranchesAgent = session.user.roles?.some((role: any) => {
      const roleId = role.id || role._id;
      return roleId?.toString() === '67c032fe0af5117479e27737';
    });

    // Check if user is BranchesAdmin
    const isBranchesAdmin = session.user.roles?.some((role: any) => {
      const roleId = role.id || role._id;
      return roleId?.toString() === '67c032e90af5117479e27731';
    });
    // If BranchesAgent, only return their own user data
    if (isBranchesAgent) {
      return NextResponse.json({
        users: [{
          _id: session.user.id,
          name: session.user.name,
          email: session.user.email
        }]
      });
    }

    // If BranchesAdmin, get all users from their branches
    if (isBranchesAdmin) {
      // First, find all branches where the admin is responsible
      const adminBranches = await Branch.find({
        responsible: session.user.id
      }).lean();

      // Get all user IDs from these branches (both responsible and agents)
      const branchUserIds = adminBranches.reduce((acc: string[], branch) => {
        const responsibleIds = Array.isArray(branch.responsible) ? branch.responsible : [branch.responsible];
        const agentIds = Array.isArray(branch.agents) ? branch.agents : [branch.agents];
        return [...acc, ...responsibleIds, ...agentIds];
      }, []);

      // Remove duplicates
      const uniqueUserIds = Array.from(new Set(branchUserIds));

      // Fetch users
      const branchUsers = await User.find({
        _id: { $in: uniqueUserIds }
      })
      .select('name email roles')
      .populate('roles', 'name')
      .lean();

      return NextResponse.json({ users: branchUsers });
    }

    // Get the IDs for BranchesAdmin and BranchesAgent roles
    const branchesAdminRoleId = '67c032e90af5117479e27731';
    const branchesAgentRoleId = '67c032fe0af5117479e27737';

    // Find users with either BranchesAdmin or BranchesAgent role
    const users = await User.find({
      'roles': {
        $in: [
          new Types.ObjectId(branchesAdminRoleId),
          new Types.ObjectId(branchesAgentRoleId)
        ]
      }
    })
    .select('name email roles')
    .populate('roles', 'name')
    .lean();

    return NextResponse.json({ users });
  } catch (error) {
    console.error('GET appointments users error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
} 