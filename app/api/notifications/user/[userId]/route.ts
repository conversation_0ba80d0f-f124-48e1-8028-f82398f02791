import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Notification from '@/models/Notification';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Types } from 'mongoose';

interface RouteParams {
  params: {
    userId: string;
  };
}

export async function GET(request: Request, { params }: RouteParams) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { userId } = params;
    
    if (!Types.ObjectId.isValid(userId)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      );
    }
    
    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const status = url.searchParams.get('status');
    const unreadOnly = url.searchParams.get('unreadOnly') === 'true';
    
    // Build query
    const query: any = {
      'users.userId': new Types.ObjectId(userId)
    };
    
    if (status) {
      query['users.notificationStatus'] = status;
    }
    
    if (unreadOnly) {
      query['users.notificationStatus'] = { $ne: 'read' };
    }
    
    // Calculate skip for pagination
    const skip = (page - 1) * limit;
    
    // Get notifications for the user
    const notifications = await Notification.find(query)
      .populate('users.userId', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Filter to only show the specific user's status and add user-specific fields
    const userNotifications = notifications.map(notification => {
      const userInfo = notification.users.find(
        (user: any) => user.userId._id.toString() === userId
      );
      
      return {
        ...notification,
        userStatus: userInfo?.notificationStatus || 'unknown',
        // Keep all users for admin purposes, but highlight current user
        currentUser: userInfo
      };
    });
    
    // Get total count for pagination
    const total = await Notification.countDocuments(query);
    
    // Get unread count
    const unreadCount = await Notification.countDocuments({
      'users.userId': new Types.ObjectId(userId),
      'users.notificationStatus': { $ne: 'read' }
    });
    
    return NextResponse.json({
      notifications: userNotifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      unreadCount
    });
  } catch (error) {
    console.error('Error fetching user notifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user notifications' },
      { status: 500 }
    );
  }
}