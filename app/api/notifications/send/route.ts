import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Notification from '@/models/Notification';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Types } from 'mongoose';
import admin from 'firebase-admin';

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
  }
}

export async function POST(request: Request) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    const { notificationId } = body;
    
    if (!notificationId || !Types.ObjectId.isValid(notificationId)) {
      return NextResponse.json(
        { error: 'Valid notification ID is required' },
        { status: 400 }
      );
    }
    
    // Get the notification
    const notification = await Notification.findById(notificationId);
    
    if (!notification) {
      return NextResponse.json(
        { error: 'Notification not found' },
        { status: 404 }
      );
    }
    
    // Extract FCM tokens
    let tokens = notification.messages.tokens || [];
    
      // Get user IDs from the notification
      const userIds = notification.users.map((user: any) => user.userId);
      
      // Fetch FCM tokens from user profiles
      const users = await User.find({
        _id: { $in: userIds },
        fcmTokens: { $exists: true, $ne: [] }
      });
      
      // Extract tokens from users
      const userTokens = users.flatMap((user: any) => user.fcmTokens || []);
      
      // Add tokens to the notification
      tokens = userTokens;
      
      // Update the notification with the tokens
      if (tokens.length > 0) {
        await Notification.updateOne(
          { _id: notification._id },
          { $set: { 'messages.tokens': tokens } }
        );
      } else {
        // Still no tokens found
        return NextResponse.json(
          { error: 'No FCM tokens found for notification users' },
          { status: 400 }
        );
      }
    
    
    // Prepare the message
    const message = {
      tokens: tokens,
      notification: {
        title: notification.messages.notification.title,
        body: notification.messages.notification.body
      },
      data: notification.messages.data
    };
    message.data={"test":"test"};
    // Send the notification
    const response = await admin.messaging().sendEachForMulticast(message);
    console.log(response)
    // Create a map to track which tokens were successful
    const tokenStatusMap = new Map();
    
    // Initialize all tokens as failed
    tokens.forEach((token: string) => {
      tokenStatusMap.set(token, 'failed');
    });
    
    // Update successful tokens in the map
    response.responses.forEach((resp: any, index: number) => {
      if (resp.success) {
        tokenStatusMap.set(tokens[index], 'success');
      } else if (resp.error?.errorInfo?.code === 'messaging/registration-token-not-registered') {
        // Token is invalid
        tokenStatusMap.set(tokens[index], 'invalid');
      }
    });
    
    // Remove invalid tokens from notification's tokens array and user profiles
    const validTokens = tokens.filter((token:any) => {tokenStatusMap.get(token) !== 'invalid'});
    const invalidTokens = tokens.filter((token:any) => {tokenStatusMap.get(token) === 'invalid'});
    
    if (validTokens.length !== tokens.length) {
      // Update notification with only valid tokens
      await Notification.updateOne(
        { _id: notification._id },
        { $set: { 'messages.tokens': validTokens } }
      );
      
      // Remove invalid tokens from user profiles
      if (invalidTokens.length > 0) {
        await User.updateMany(
          { fcmTokens: { $in: invalidTokens } },
          { $pull: { fcmTokens: { $in: invalidTokens } } }
        );
      }
    }
    
    // Update notification status for each user
    const updatePromises = notification.users.map(async (user: any) => {
      // Get tokens associated with this user (this would require additional logic to map tokens to users)
      // For now, we'll use a simplified approach where if any token was successful, mark as sent
      
      // Determine notification status based on token success
      let notificationStatus = 'pending';
      
      // If any token was successful, mark as sent
      if (response.successCount > 0) {
        notificationStatus = 'sent';
      } else if (response.failureCount === tokens.length) {
        // If all tokens failed, mark as failed
        notificationStatus = 'failed';
      }
      
      // Update notification status
      await Notification.updateOne(
        { _id: notification._id, 'users.userId': user.userId },
        { $set: { 'users.$.notificationStatus': notificationStatus } }
      );
    });

    
    await Promise.all(updatePromises);
    
    return NextResponse.json({
      success: true,
      message: 'Push notification sent successfully',
      results: {
        successCount: response.successCount,
        failureCount: response.failureCount,
        responses: response.responses
      }
    });
  } catch (error) {
    console.error('Error sending push notification:', error);
    return NextResponse.json(
      { error: 'Failed to send push notification' },
      { status: 500 }
    );
  }
}