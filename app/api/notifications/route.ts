import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Notification from '@/models/Notification';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Types } from 'mongoose';

export async function GET(request: Request) {
  try {
    await dbConnect();
    
    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const status = url.searchParams.get('status');
    const userId = url.searchParams.get('userId');
    const targetScreenId = url.searchParams.get('targetScreenId');
    
    // Build query
    const query: any = {};
    
    if (status) {
      query['users.notificationStatus'] = status;
    }
    
    if (userId && Types.ObjectId.isValid(userId)) {
      query['users.userId'] = new Types.ObjectId(userId);
    }
    
    if (targetScreenId && Types.ObjectId.isValid(targetScreenId)) {
      query.targetScreenId = new Types.ObjectId(targetScreenId);
    }
    
    // Calculate skip for pagination
    const skip = (page - 1) * limit;
    
    // Get notifications with pagination
    const notifications = await Notification.find(query)
      .populate('users.userId', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count for pagination
    const total = await Notification.countDocuments(query);
    
    return NextResponse.json({
      notifications,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    
    // Validate required fields
    if (!body.messages || !body.messages.notification || !body.messages.notification.title || !body.messages.notification.body) {
      return NextResponse.json(
        { error: 'Messages with notification title and body are required' },
        { status: 400 }
      );
    }
    
    if (!body.users || !Array.isArray(body.users) || body.users.length === 0) {
      return NextResponse.json(
        { error: 'At least one user is required' },
        { status: 400 }
      );
    }
    
    // Validate user IDs
    const invalidUserIds = body.users.filter((user: any) => !Types.ObjectId.isValid(user.userId));
    if (invalidUserIds.length > 0) {
      return NextResponse.json(
        { error: 'Invalid user IDs provided' },
        { status: 400 }
      );
    }
    
    // Fetch FCM tokens from user models
    const userIds = body.users.map((user: any) => new Types.ObjectId(user.userId));
    const usersWithTokens = await User.find(
      { _id: { $in: userIds } },
      { _id: 1, fcmTokens: 1 }
    ).lean();
    
    // Collect all FCM tokens from users
    const fcmTokens = usersWithTokens.reduce((tokens: string[], user: any) => {
      if (user.fcmTokens && Array.isArray(user.fcmTokens) && user.fcmTokens.length > 0) {
        return [...tokens, ...user.fcmTokens];
      }
      return tokens;
    }, []);
    
    // Create notification data
    const notificationData = {
      messages: {
        tokens: fcmTokens, // Use tokens from user models
        notification: {
          title: body.messages.notification.title,
          body: body.messages.notification.body
        },
        data: body.messages.data || {}
      },
      users: body.users.map((user: any) => ({
        userId: new Types.ObjectId(user.userId),
        notificationStatus: user.notificationStatus || 'pending'
      })),
      targetScreenId: body.targetScreenId ? new Types.ObjectId(body.targetScreenId) : undefined
    };
    
    const notification = await Notification.create(notificationData);
    
    // Populate the created notification
    const populatedNotification = await Notification.findById(notification._id)
      .populate('users.userId', 'name email')
      .lean();
    
    return NextResponse.json(populatedNotification, { status: 201 });
  } catch (error) {
    console.error('Error creating notification:', error);
    return NextResponse.json(
      { error: 'Failed to create notification' },
      { status: 500 }
    );
  }
}