import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Notification from '@/models/Notification';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Types } from 'mongoose';

export async function POST(request: Request) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    const { operation, notificationIds, userId, status } = body;
    
    if (!operation) {
      return NextResponse.json(
        { error: 'Operation is required' },
        { status: 400 }
      );
    }
    
    switch (operation) {
      case 'markAsRead':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId is required for markAsRead operation' },
            { status: 400 }
          );
        }
        
        if (!Types.ObjectId.isValid(userId)) {
          return NextResponse.json(
            { error: 'Invalid user ID' },
            { status: 400 }
          );
        }
        
        const query = notificationIds && notificationIds.length > 0
          ? { 
              _id: { $in: notificationIds.map((id: string) => new Types.ObjectId(id)) },
              'users.userId': new Types.ObjectId(userId)
            }
          : { 'users.userId': new Types.ObjectId(userId) };
        
        const markReadResult = await Notification.updateMany(
          query,
          { $set: { 'users.$.notificationStatus': 'read' } }
        );
        
        return NextResponse.json({
          message: 'Notifications marked as read',
          modifiedCount: markReadResult.modifiedCount
        });
      
      case 'updateStatus':
        if (!userId || !status) {
          return NextResponse.json(
            { error: 'userId and status are required for updateStatus operation' },
            { status: 400 }
          );
        }
        
        if (!Types.ObjectId.isValid(userId)) {
          return NextResponse.json(
            { error: 'Invalid user ID' },
            { status: 400 }
          );
        }
        
        const validStatuses = ['pending', 'sent', 'delivered', 'read', 'failed'];
        if (!validStatuses.includes(status)) {
          return NextResponse.json(
            { error: `Invalid status. Must be one of: ${validStatuses.join(', ')}` },
            { status: 400 }
          );
        }
        
        if (!notificationIds || !Array.isArray(notificationIds) || notificationIds.length === 0) {
          return NextResponse.json(
            { error: 'notificationIds array is required for updateStatus operation' },
            { status: 400 }
          );
        }
        
        const updateStatusResult = await Notification.updateMany(
          { 
            _id: { $in: notificationIds.map((id: string) => new Types.ObjectId(id)) },
            'users.userId': new Types.ObjectId(userId)
          },
          { $set: { 'users.$.notificationStatus': status } }
        );
        
        return NextResponse.json({
          message: `Notifications status updated to ${status}`,
          modifiedCount: updateStatusResult.modifiedCount
        });
      
      case 'delete':
        if (!notificationIds || !Array.isArray(notificationIds) || notificationIds.length === 0) {
          return NextResponse.json(
            { error: 'notificationIds array is required for delete operation' },
            { status: 400 }
          );
        }
        
        const deleteResult = await Notification.deleteMany({
          _id: { $in: notificationIds.map((id: string) => new Types.ObjectId(id)) }
        });
        
        return NextResponse.json({
          message: 'Notifications deleted',
          deletedCount: deleteResult.deletedCount
        });
      
      default:
        return NextResponse.json(
          { error: 'Invalid operation. Supported operations: markAsRead, updateStatus, delete' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error performing bulk operation:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}