import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Notification from '@/models/Notification';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Types } from 'mongoose';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function PUT(request: Request, { params }: RouteParams) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { id } = params;
    
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid notification ID' },
        { status: 400 }
      );
    }
    
    const body = await request.json();
    
    // Validate required fields
    if (!body.userId || !body.status) {
      return NextResponse.json(
        { error: 'userId and status are required' },
        { status: 400 }
      );
    }
    
    if (!Types.ObjectId.isValid(body.userId)) {
      return NextResponse.json(
        { error: 'Invalid user ID' },
        { status: 400 }
      );
    }
    
    // Validate status
    const validStatuses = ['pending', 'sent', 'delivered', 'read', 'failed'];
    if (!validStatuses.includes(body.status)) {
      return NextResponse.json(
        { error: `Invalid status. Must be one of: ${validStatuses.join(', ')}` },
        { status: 400 }
      );
    }
    
    // Update the specific user's notification status
    const notification = await Notification.findOneAndUpdate(
      { 
        _id: id,
        'users.userId': new Types.ObjectId(body.userId)
      },
      { 
        $set: { 'users.$.notificationStatus': body.status }
      },
      { new: true, runValidators: true }
    ).populate('users.userId', 'name email');
    
    if (!notification) {
      return NextResponse.json(
        { error: 'Notification or user not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(notification);
  } catch (error) {
    console.error('Error updating notification status:', error);
    return NextResponse.json(
      { error: 'Failed to update notification status' },
      { status: 500 }
    );
  }
}