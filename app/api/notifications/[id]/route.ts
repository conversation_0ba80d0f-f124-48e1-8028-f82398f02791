import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Notification from '@/models/Notification';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Types } from 'mongoose';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: Request, { params }: RouteParams) {
  try {
    await dbConnect();
    
    const { id } = params;
    
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid notification ID' },
        { status: 400 }
      );
    }
    
    const notification = await Notification.findById(id)
      .populate('users.userId', 'name email')
      .lean();
    
    if (!notification) {
      return NextResponse.json(
        { error: 'Notification not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(notification);
  } catch (error) {
    console.error('Error fetching notification:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notification' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request, { params }: RouteParams) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { id } = params;
    
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid notification ID' },
        { status: 400 }
      );
    }
    
    const body = await request.json();
    
    // Build update object
    const updateData: any = {};
    
    if (body.messages) {
      updateData.messages = {};
      
      if (body.messages.tokens) {
        updateData.messages.tokens = body.messages.tokens;
      }
      
      if (body.messages.notification) {
        updateData.messages.notification = body.messages.notification;
      }
      
      if (body.messages.data) {
        updateData.messages.data = body.messages.data;
      }
    }
    
    if (body.users) {
      // Validate user IDs
      const invalidUserIds = body.users.filter((user: any) => !Types.ObjectId.isValid(user.userId));
      if (invalidUserIds.length > 0) {
        return NextResponse.json(
          { error: 'Invalid user IDs provided' },
          { status: 400 }
        );
      }
      
      updateData.users = body.users.map((user: any) => ({
        userId: new Types.ObjectId(user.userId),
        notificationStatus: user.notificationStatus || 'pending'
      }));
    }
    
    if (body.targetScreenId) {
      if (Types.ObjectId.isValid(body.targetScreenId)) {
        updateData.targetScreenId = new Types.ObjectId(body.targetScreenId);
      } else {
        return NextResponse.json(
          { error: 'Invalid targetScreenId' },
          { status: 400 }
        );
      }
    }
    
    const notification = await Notification.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('users.userId', 'name email');
    
    if (!notification) {
      return NextResponse.json(
        { error: 'Notification not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(notification);
  } catch (error) {
    console.error('Error updating notification:', error);
    return NextResponse.json(
      { error: 'Failed to update notification' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request, { params }: RouteParams) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { id } = params;
    
    if (!Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid notification ID' },
        { status: 400 }
      );
    }
    
    const notification = await Notification.findByIdAndDelete(id);
    
    if (!notification) {
      return NextResponse.json(
        { error: 'Notification not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ message: 'Notification deleted successfully' });
  } catch (error) {
    console.error('Error deleting notification:', error);
    return NextResponse.json(
      { error: 'Failed to delete notification' },
      { status: 500 }
    );
  }
}