import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Notification from '@/models/Notification';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Types } from 'mongoose';

export async function GET(request: Request) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse query parameters
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    
    // Build date filter
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.$gte = new Date(startDate);
    }
    if (endDate) {
      dateFilter.$lte = new Date(endDate);
    }
    
    // Build base query
    const baseQuery: any = {};
    if (Object.keys(dateFilter).length > 0) {
      baseQuery.createdAt = dateFilter;
    }
    
    if (userId && Types.ObjectId.isValid(userId)) {
      // Get stats for specific user
      const userQuery = {
        ...baseQuery,
        'users.userId': new Types.ObjectId(userId)
      };
      
      const [
        totalNotifications,
        pendingNotifications,
        sentNotifications,
        deliveredNotifications,
        readNotifications,
        failedNotifications
      ] = await Promise.all([
        Notification.countDocuments(userQuery),
        Notification.countDocuments({
          ...userQuery,
          'users.notificationStatus': 'pending'
        }),
        Notification.countDocuments({
          ...userQuery,
          'users.notificationStatus': 'sent'
        }),
        Notification.countDocuments({
          ...userQuery,
          'users.notificationStatus': 'delivered'
        }),
        Notification.countDocuments({
          ...userQuery,
          'users.notificationStatus': 'read'
        }),
        Notification.countDocuments({
          ...userQuery,
          'users.notificationStatus': 'failed'
        })
      ]);
      
      return NextResponse.json({
        userId,
        totalNotifications,
        statusBreakdown: {
          pending: pendingNotifications,
          sent: sentNotifications,
          delivered: deliveredNotifications,
          read: readNotifications,
          failed: failedNotifications
        },
        unreadCount: totalNotifications - readNotifications
      });
    } else {
      // Get overall system stats
      const [
        totalNotifications,
        totalUsers,
        statusStats,
        recentNotifications
      ] = await Promise.all([
        Notification.countDocuments(baseQuery),
        Notification.aggregate([
          { $match: baseQuery },
          { $unwind: '$users' },
          { $group: { _id: '$users.userId' } },
          { $count: 'uniqueUsers' }
        ]),
        Notification.aggregate([
          { $match: baseQuery },
          { $unwind: '$users' },
          {
            $group: {
              _id: '$users.notificationStatus',
              count: { $sum: 1 }
            }
          }
        ]),
        Notification.find(baseQuery)
          .sort({ createdAt: -1 })
          .limit(5)
          .select('messages.notification.title createdAt users')
          .lean()
      ]);
      
      // Format status stats
      const statusBreakdown = statusStats.reduce((acc: any, stat: any) => {
        acc[stat._id] = stat.count;
        return acc;
      }, {
        pending: 0,
        sent: 0,
        delivered: 0,
        read: 0,
        failed: 0
      });
      
      return NextResponse.json({
        totalNotifications,
        totalUsers: totalUsers[0]?.uniqueUsers || 0,
        statusBreakdown,
        recentNotifications: recentNotifications.map(notification => ({
          id: notification._id,
          title: notification.messages.notification.title,
          createdAt: notification.createdAt,
          userCount: notification.users.length
        }))
      });
    }
  } catch (error) {
    console.error('Error fetching notification stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notification stats' },
      { status: 500 }
    );
  }
}