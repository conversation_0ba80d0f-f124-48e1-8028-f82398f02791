import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Contact } from '@/app/models/Contact';

export async function GET(req: NextRequest) {
  try {
  await dbConnect();
    const contacts = await Contact.find({});
    let updatedCount = 0;
    for (const contact of contacts) {
      if (contact.conversation && Array.isArray(contact.conversation.messages) && contact.conversation.messages.length > 1) {
        // Check current order
        const isDescending = contact.conversation.messages[0].dateCreated > contact.conversation.messages[contact.conversation.messages.length - 1].dateCreated;
        // Switch order
        contact.conversation.messages = [...contact.conversation.messages].reverse();
        // Update lastMessage to match new first message
        await contact.save();
        updatedCount++;
      }
    }
    return NextResponse.json({ updatedContacts: updatedCount });
  } catch (error) {
    console.error('Switch messages sorting migration error:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Migration failed' }, { status: 500 });
  }
} 