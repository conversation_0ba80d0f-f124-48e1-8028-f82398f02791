import { NextRequest, NextResponse } from 'next/server';
import twilio from 'twilio';
import { connectToDatabase } from '@/lib/mongodb';
import { Contact } from '@/app/models/Contact';
import Message from '@/models/Message';
import Reservation from '@/models/Reservation';
import { createTwilioConversation } from '@/lib/conversations/createTwilioConversation';
import dbConnect from '@/lib/db';

const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const conversationNumber = process.env.TWILIO_CONVERSATION_NUMBER;

// Normalize phone for matching
function normalize(phone: string = ''): string {
  return phone.replace(/\D/g, '').replace(/^1/, '');
}

export async function GET(req: NextRequest) {
  if (!accountSid || !authToken || !conversationNumber) {
    return NextResponse.json({ error: 'Twilio credentials or conversation number not set' }, { status: 500 });
  }
  try {
  await dbConnect();
    const client = twilio(accountSid, authToken);
    // DEV ONLY: Delete all Twilio conversations before migration
    if (process.env.NODE_ENV === 'development') {
      const allConvos = await client.conversations.v1.conversations.list({ limit: 10000 });
      for (const convo of allConvos) {
        try {
          await client.conversations.v1.conversations(convo.sid).remove();
          console.log(`[Migration] Deleted Twilio conversation ${convo.sid}`);
        } catch (err) {
          console.error(`[Migration] Failed to delete Twilio conversation ${convo.sid}:`, err);
        }
      }
      console.log(`[Migration] Deleted ${allConvos.length} Twilio conversations.`);
    }
    // Fetch all messages for the conversation number (inbound and outbound)
    const inboundMessages = await client.messages.list({
      to: conversationNumber,
      limit: 10000,
    });
    const outboundMessages = await client.messages.list({
      from: conversationNumber,
      limit: 10000,
    });
    // Merge and deduplicate by SID
    const allMessagesMap = new Map();
    for (const msg of [...inboundMessages, ...outboundMessages]) {
      allMessagesMap.set(msg.sid, msg);
    }
    const allMessages = Array.from(allMessagesMap.values());

    if (process.env.NODE_ENV === 'development') {
      console.log(`[Migration] Fetched ${allMessages.length} Twilio messages (inbound + outbound) for number ${conversationNumber}`);
    }

    // Group messages by contact (other phone number)
    const contactMap = new Map<string, any[]>();
    for (const msg of allMessages) {
      const otherPhone = msg.from === conversationNumber ? msg.to : msg.from;
      if (!otherPhone) continue; // skip if no other phone
      const normalizedOtherPhone = normalize(otherPhone);
      if (!normalizedOtherPhone || normalizedOtherPhone.length !== 10) continue; // skip if not 10 digits
      if (!contactMap.has(normalizedOtherPhone)) contactMap.set(normalizedOtherPhone, []);
      contactMap.get(normalizedOtherPhone)!.push(msg);
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`[Migration] Grouped into ${contactMap.size} contacts`);
    }

    // Fetch all messages from Message collection for mapping
    const dbMessages = await Message.find({});
    const dbMessageMap = new Map(dbMessages.map((m: any) => [m.sid, m]));
    // Fetch all reservations for mapping
    const reservations = await Reservation.find({});
    const reservationMap = new Map<string, any>();
    for (const r of reservations) {
      reservationMap.set(normalize(r.customerInfo.phone), r);
    }

    // Build contacts and messages
    const contacts = [];
    for (const [phone, msgs] of Array.from(contactMap.entries())) {
      const normalizedPhone = phone; // already normalized and validated
      if (!normalizedPhone || normalizedPhone.length !== 10) continue; // skip if not 10 digits
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Migration] Processing contact phone: ${phone}, normalized: ${normalizedPhone}`);
      }
      const reservation = reservationMap.get(normalizedPhone);
      if (process.env.NODE_ENV === 'development') {
        if (reservation) {
          console.log(`[Migration] Found reservation for phone ${normalizedPhone}:`, JSON.stringify(reservation, null, 2));
        } else {
          console.warn(`[Migration] No reservation found for phone ${normalizedPhone}`);
        }
      }
      const contactMessages = msgs
        .filter((msg: any) => {
          const valid = typeof msg.body === 'string' && msg.body.trim().length > 0;
          if (!valid && process.env.NODE_ENV === 'development') {
            console.warn(`[Migration] Skipping message with sid ${msg.sid} for phone ${normalizedPhone} due to missing/empty body.`);
          }
          return valid;
        })
        .map((msg: any) => {
          const dbMsg = dbMessageMap.get(msg.sid);
          return {
            sid: msg.sid,
            body: msg.body,
            author: msg.from,
            dateCreated: msg.dateCreated,
            direction: msg.direction,
            archivedAt: dbMsg?.archivedAt || null,
            archivedBy: dbMsg?.archivedBy || [],
            isRead: dbMsg?.isRead || false,
            reservationId: dbMsg?.reservationId || null,
          };
        })
        // Sort messages by dateCreated descending (newest first)
        .sort((a, b) => new Date(b.dateCreated).getTime() - new Date(a.dateCreated).getTime());
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Migration] Contact ${normalizedPhone} has ${contactMessages.length} messages.`);
      }
      contacts.push({
        phone: normalizedPhone,
        fullname: reservation?.customerInfo?.client1Name || '',
        conversation: {
          sid: '',
          readBy: [],
          archivedBy: [],
          linkedReservationId: reservation?._id?.toString() || null,
          linkedBranch: reservation?.preferences?.branchId || null,
          customerName: reservation?.customerInfo?.client1Name || null,
          messages: contactMessages,
        },
        postalCode: reservation?.customerInfo?.postalCode || '',
        lastMessage: contactMessages[0] || null,
      });
    }

    if (process.env.NODE_ENV === 'development') {
      for (let i = 0; i < Math.min(3, contacts.length); i++) {
        const c = contacts[i];
        console.log(`[Migration] Contact #${i + 1} (${c.phone}) has ${c.conversation.messages.length} messages.`);
        for (let j = 0; j < Math.min(2, c.conversation.messages.length); j++) {
          const m = c.conversation.messages[j];
          console.log(`[Migration]   Message #${j + 1}: sid=${m.sid}, body=${m.body}`);
        }
      }
    }

    if (process.env.NODE_ENV === 'development' && contacts.length > 0) {
      const sampleContact = contacts[0] as any;
      console.log('[Migration] Sample contact:', JSON.stringify(sampleContact, null, 2));
      if (sampleContact.conversation?.messages?.length > 0) {
        console.log('[Migration] Sample message for first contact:', JSON.stringify(sampleContact.conversation.messages[0], null, 2));
      }
    }

    // Insert contacts and create Twilio conversations
    const insertedContacts = await Contact.insertMany(contacts);
    // Twilio conversation creation is temporarily disabled for verification.
    // To re-enable, uncomment the block below:
    
    for (const contact of insertedContacts) {
      try {
        const participants = [
          {
            address: `+1${contact.phone}`,
            proxyAddress: conversationNumber,
          },
        ];
        const result = await createTwilioConversation({
          friendlyName: contact.phone,
          contactId: contact._id.toString(),
          participants,
        });
        contact.conversation.sid = result.sid;
        await contact.save();
        if (process.env.NODE_ENV === 'development') {
          console.log(`[Migration] Created Twilio conversation for contact ${contact.phone} with sid ${result.sid}`);
        }
      } catch (err) {
        console.error(`[Migration] Failed to create Twilio conversation for contact ${contact.phone}:`, err);
      }
    }
    

    return NextResponse.json({ migratedContacts: contacts.length, contacts });
  } catch (error) {
    console.error('Migration error:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Migration failed' }, { status: 500 });
  }
}