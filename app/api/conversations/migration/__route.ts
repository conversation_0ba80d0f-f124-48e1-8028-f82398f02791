import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';

export async function GET(req: NextRequest) {
  try {
    const db=await connectToDatabase();
 
    // Step 1: Aggregate contacts whose normalized phone matches a user's normalized phone
    const matchingContacts = await db?.collection('contacts').aggregate([
      {
        $addFields: {
          normalizedContactPhone: {
            $regexFind: {
              input: {
                $replaceAll: {
                  input: '$phone',
                  find: '[^0-9]',
                  replacement: '',
                },
              },
              regex: /(\d{10})$/,
            },
          },
        },
      },
      {
        $lookup: {
          from: 'users',
          let: { contactPhone: '$normalizedContactPhone.match' },
          pipeline: [
            {
              $addFields: {
                normalizedUserPhone: {
                  $regexFind: {
                    input: {
                      $replaceAll: {
                        input: '$phone',
                        find: '[^0-9]',
                        replacement: '',
                      },
                    },
                    regex: /(\d{10})$/,
                  },
                },
              },
            },
            {
              $match: {
                $expr: {
                  $eq: ['$normalizedUserPhone.match', '$$contactPhone'],
                },
              },
            },
          ],
          as: 'matchedUsers',
        },
      },
      {
        $match: {
          'matchedUsers.0': { $exists: true },
        },
      },
      {
        $project: { _id: 1 },
      },
    ]).toArray();

    const idsToDelete = matchingContacts.map((doc: any) => doc._id);
    let deletedCount = 0;
    if (idsToDelete.length > 0) {
      const deleteResult = await db.collection('contacts').deleteMany({ _id: { $in: idsToDelete } });
      deletedCount = deleteResult.deletedCount || 0;
    }

    return NextResponse.json({ message: `Deleted ${deletedCount} contacts.`, deletedCount });
  } catch (error) {
    console.error('Migration error:', error);
    return NextResponse.json({ error: error instanceof Error ? error.message : 'Migration failed' }, { status: 500 });
  }
}