import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Contact } from '@/app/models/Contact';
import twilio from 'twilio';

const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
const conversationNumber = process.env.TWILIO_CONVERSATION_NUMBER;

// Set to true to fix all discrepancies, ignoring MAX_DISCREPANCIES
const FIX_ALL = false;

export async function GET(req: NextRequest) {
  try {
    const params=req.nextUrl.searchParams;
    const key=params.get('key');
    if(key!==process.env.THIRD_PARTY_API_KEY){
      return NextResponse.json({ message: 'Invalid key' }, { status: 401 });
    }
    if (!accountSid || !authToken || !conversationNumber) {
      return NextResponse.json({ error: 'Twilio credentials or conversation number not set' }, { status: 500 });
    }
  await dbConnect();
    const contacts = await Contact.find({}).sort({createdAt: -1});
    const client = twilio(accountSid, authToken);
    const results: any[] = [];
    let discrepancyCount = 0;
    const MAX_DISCREPANCIES = 10;
    const affectedPhones: string[] = [];
    const affectedContacts: { phone: string, inserted: { body: string, date: string }[] }[] = [];
    for (const contact of contacts) {
      const phone = contact.phone;
      try {
        // Fetch Twilio messages
        const inboundMessages = await client.messages.list({ to: phone, from: conversationNumber, limit: 100 });
        const outboundMessages = await client.messages.list({ from: phone, to: conversationNumber, limit: 100 });
        const twilioMessages = [...inboundMessages, ...outboundMessages];
        const twilioBodies = new Set(twilioMessages.map((m: any) => (m.body || '').trim()));
        // Get stored messages
        const storedMessages = contact.conversation?.messages || [];
        const storedBodies = new Set(storedMessages.map((m: any) => (m.body || '').trim()));
        // Compare by body
        const missingInDb = Array.from(twilioBodies).filter(body => body && !storedBodies.has(body));
        const extraInDb = Array.from(storedBodies).filter(body => body && !twilioBodies.has(body));
        let insertedMessages: { body: string, date: string }[] = [];
        if (missingInDb.length > 0 || extraInDb.length > 0) {
          discrepancyCount++;
          console.log(`[Compare] Phone: ${phone}`);
          console.log(`  Twilio messages: ${twilioMessages.length}, DB messages: ${storedMessages.length}`);
          if (missingInDb.length > 0) {
            console.log(`  Missing in DB: ${missingInDb.length}`);
            for (const body of missingInDb) {
              const msg = twilioMessages.find((m: any) => (m.body || '').trim() === body);
              const date = msg?.dateCreated?.toISOString?.() || msg?.dateCreated || '';
              console.log(`    - "${body}" (${date})`);
              if (msg) {
                // Prepare message object for DB
                const newMsg = {
                  sid: msg.sid || '',
                  body: msg.body || '',
                  author: msg.from || '',
                  dateCreated: msg.dateCreated instanceof Date ? msg.dateCreated : new Date(msg.dateCreated),
                  direction: msg.direction || (msg.from === phone ? 'inbound' : 'outbound-api'),
                  archivedAt: null,
                  archivedBy: [],
                };
                // Insert in correct position (by dateCreated ascending)
                let inserted = false;
                for (let i = 0; i < storedMessages.length; i++) {
                  const dbDate = new Date(storedMessages[i].dateCreated);
                  if (newMsg.dateCreated < dbDate) {
                    storedMessages.splice(i, 0, newMsg);
                    inserted = true;
                    console.log(`      Inserted at position ${i}`);
                    break;
                  }
                }
                if (!inserted) {
                  storedMessages.push(newMsg);
                  console.log(`      Inserted at end (position ${storedMessages.length - 1})`);
                }
                insertedMessages.push({ body: msg.body, date: typeof date === 'string' ? date : (date instanceof Date ? date.toISOString() : String(date)) });
              }
            }
            // Save updated messages array
            await Contact.findByIdAndUpdate(
              contact._id,
              { 'conversation.messages': storedMessages }
            );
            affectedPhones.push(phone);
            if (insertedMessages.length > 0) {
              affectedContacts.push({ phone, inserted: insertedMessages });
            }
          }
          if (extraInDb.length > 0) {
            console.log(`  Extra in DB: ${extraInDb.length}`);
            extraInDb.forEach(body => {
              const msg = storedMessages.find((m: any) => (m.body || '').trim() === body);
              const date = msg?.dateCreated?.toISOString?.() || msg?.dateCreated || '';
              console.log(`    - "${body}" (${date})`);
            });
          }
        }
        results.push({
          phone,
          twilioCount: twilioMessages.length,
          dbCount: storedMessages.length,
          missingInDb,
          extraInDb
        });
        if (discrepancyCount >= MAX_DISCREPANCIES && !FIX_ALL) {
          break;
        }
      } catch (err) {
        console.error(`[Twilio] Error comparing messages for phone ${phone}:`, err);
        results.push({ phone, error: String(err) });
      }
      // Add a slight delay to avoid Twilio rate limiting
      await new Promise(res => setTimeout(res, 150));
    }
    // Print summary to console
    if (affectedContacts.length > 0) {
      console.log('\n=== Migration Summary: Inserted Messages ===');
      affectedContacts.forEach(({ phone, inserted }) => {
        const msgSummary = inserted.map((msg, idx) => {
          let body = (msg.body || '').replace(/\n/g, ' ');
          if (body.length > 40) body = body.slice(0, 37) + '...';
          return `"${body}" (${msg.date})`;
        }).join('; ');
        console.log(`Contact: ${phone} | Inserted ${inserted.length} message(s): ${msgSummary}`);
      });
      console.log('=== End of Migration Summary ===\n');
    } else {
      console.log('\n=== Migration Summary: No contacts affected ===\n');
    }
    return NextResponse.json({ count: contacts.length, results, affectedPhones, affectedContacts });
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      console.error('[Contacts API] Error:', error);
    }
    return NextResponse.json({ error: error.message || 'Failed to compare messages' }, { status: 500 });
  }
}
