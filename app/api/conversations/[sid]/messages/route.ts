import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Contact } from '@/app/models/Contact';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { ObjectId } from 'mongodb';
import { sendSMS } from '@/lib/twilio';
import { canUserViewMessages } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '../../../utils/server-permission-utils';
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;

export async function POST(
  req: NextRequest,
  { params }: { params: { sid: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const p = await params;
  if (!accountSid || !authToken) {
    return NextResponse.json({ error: 'Twilio credentials not set' }, { status: 500 });
  }
  if(!session.user.permissions){
    session.user.permissions=await getUserPermissions(session);
  }
  if(!canUserViewMessages(session.user)){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { sid } = p;
  if (!sid) {
    return NextResponse.json({ error: 'Conversation SID is required' }, { status: 400 });
  }

  try {
    const body = await req.json();
    const { author, message } = body;

    if (!author || !message) {
      return NextResponse.json({ error: 'author and message are required' }, { status: 400 });
    }

    const contact = await Contact.findOne({ 'conversation.sid': sid });
    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }
    const result = await sendSMS(process.env.TWILIO_CONVERSATION_NUMBER || "", "+1"+contact.phone, message,false,session.user);
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.debug('[Conversations API] Created message:', result);
    }

    return NextResponse.json(result, { status: 201 });
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('[Conversations API] Error:', error);
    }
    return NextResponse.json({ error: error.message || 'Failed to add message' }, { status: 500 });
  }
} 