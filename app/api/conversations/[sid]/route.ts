import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Contact, IContact } from '@/app/models/Contact';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserPermissions } from '../../utils/server-permission-utils';
import { canUserViewMessages } from '@/lib/utils/permissions-utils';
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;
import dbConnect from '@/lib/db';

export async function GET(
  req: NextRequest,
  { params }: { params: { sid: string } }
) {
  const p = await params;
  if (!accountSid || !authToken) {
    return NextResponse.json({ error: 'Twilio credentials not set' }, { status: 500 });
  }

  const { sid } = p;
  if (!sid) {
    return NextResponse.json({ error: 'Conversation SID is required' }, { status: 400 });
  }

  const session = await getServerSession(authOptions);
  if(!session){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(!session.user.permissions){
    session.user.permissions=await getUserPermissions(session);
  }
  if(!canUserViewMessages(session.user)){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  // Parse pagination and search params
  const { searchParams } = new URL(req.url);
  const pageSize = parseInt(searchParams.get('pageSize') || '50', 10);
  const q = searchParams.get('q')?.trim();
  const pageToken = searchParams.get('pageToken');

  try {
  await dbConnect();
    // Fetch contact by conversation.sid
    const contact = await Contact.findOne({ 'conversation.sid': sid }).lean() as IContact | null;
    if (!contact) {
      return NextResponse.json({ error: 'Contact not found' }, { status: 404 });
    }
    // Filter messages by search if q is provided
    let allMessages = contact.conversation.messages || [];
    if (q) {
      const qLower = q.toLowerCase();
      allMessages = allMessages.filter((msg: any) =>
        (msg.body && msg.body.toLowerCase().includes(qLower)) ||
        (msg.author && msg.author.toLowerCase().includes(qLower))
      );
    }
    // Filter out messages with author === "auto-user"
    //allMessages = allMessages.filter((msg: any) => msg.author !== "auto-user");
    // Paginate messages (newest last)
    const totalMessages = allMessages.length;
    let start = Math.max(0, totalMessages - pageSize);
    let end = totalMessages;
    if (pageToken) {
      // pageToken is the index to end at (exclusive), for backward pagination
      end = parseInt(pageToken, 10);
      start = Math.max(0, end - pageSize);
    }
    const paginatedMessages = allMessages.slice(start, end);
    const hasNextPage = start > 0;
    const nextPageToken = hasNextPage ? String(start) : null;

    // Merge contact and Twilio data
    const result = {
      sid: contact.conversation.sid,
      friendlyName: contact.fullname || contact.phone,
      dateCreated: contact.createdAt ? new Date(contact.createdAt).toISOString() : '',
      dateUpdated: contact.updatedAt ? new Date(contact.updatedAt).toISOString() : '',
      state: null,
      messages: paginatedMessages, // already in correct order (oldest to newest)
      nextPageToken,
      hasNextPage,
      readBy: contact.conversation.readBy || [],
      archivedBy: contact.conversation.archivedBy || [],
      linkedReservationId: contact.conversation.linkedReservationId || null,
      linkedBranch: contact.conversation.linkedBranch || null,
      customerName: contact.conversation.customerName || null,
      phone: contact.phone || null,
      fullname: contact.fullname || null,
      postalCode: contact.postalCode || null
    };
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.debug('[Conversations API] Conversation detail (paginated):', result);
    }
    return NextResponse.json(result);
  } catch (error: any) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('[Conversations API] Error:', error);
    }
    return NextResponse.json({ error: error.message || 'Failed to fetch conversation' }, { status: 500 });
  }
} 