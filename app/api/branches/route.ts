import { NextResponse } from 'next/server';
import Branch from '@/models/Branch';
import dbConnect from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserViewAllBranches, canUserViewOwnBranches } from '@/lib/utils/permissions-utils';
import { getUserPermissions, getUserRoles } from '@/app/api/utils/server-permission-utils';
import { validateDashboardToken, extractTokenFromRequest } from '@/lib/utils/dashboard-token-utils';

export async function GET(request: Request) {
  try {
    await dbConnect();

    // Check for dashboard token first
    const dashboardToken = extractTokenFromRequest(request as any);
    let isTokenAuth = false;
    let session = null;

    if (dashboardToken) {
      const tokenValidation = await validateDashboardToken(dashboardToken);
      if (tokenValidation.isValid) {
        isTokenAuth = true;
      } else {
        return NextResponse.json({ error: 'Invalid dashboard token' }, { status: 401 });
      }
    } else {
      // Fall back to session authentication
      session = await getServerSession(authOptions);
      if(session &&!session?.user.permissions){
        session.user.permissions = await getUserPermissions(session);
      }
      if (!session?.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      if(session &&!session?.user.roles){
        session.user.roles = await getUserRoles(session);
      }
    }

    const { searchParams } = new URL(request.url);
    const regionId = searchParams.get('regionId');
    const includeDeleted = searchParams.get('includeDeleted') === 'true';

    let query: any = { _id: { $exists: false } };

    if (isTokenAuth) {
      // Token authentication - grant access to all branches
      query = {};
    } else if (session && canUserViewAllBranches(session.user)) {
      query = {};
    } else if (session && canUserViewOwnBranches(session.user)) {
      query = {
        $or: [
          { responsible: session.user.id },
          { agents: session.user.id },
          { sellers: session.user.id },
          { paps: session.user.id }
        ]
      };
    }
    // Exclude deleted branches by default
    if (!includeDeleted) {
      query.deletedAt = null;
    }

    // Add region filter if provided
    if (regionId && regionId !== 'all') {
      query.regionId = regionId;
    }

 
   

    const branches = await Branch.find(query)
      .select('name postalCode city province address phone automatedPhone primaryRequestPhone backupRequestPhone responsible agents sellers paps regionId deletedAt availabilityLimit requestPrompt requestTags')
      .populate('responsible', 'name')
      .populate('agents', 'name')
      .populate('sellers', 'name')
      .populate('paps', 'name')
      .sort({ name: 1 })
      .lean();

    return NextResponse.json(branches);
  } catch (error) {
    console.error('Error fetching branches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch branches' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.address || !body.postalCode || !body.city || !body.province || !body.phone) {
      return NextResponse.json({ 
        error: 'Missing required fields. Name, address, postal code, city, province, and phone are required.' 
      }, { status: 400 });
    }
    
    // Example request body:
    // {
    //   "name": "Branch Name",
    //   "address": "123 Main St",
    //   "city": "Toronto",
    //   "province": "ON",
    //   "postalCode": "M5V 2N4",
    //   "phone": "(*************",
    //   "automatedPhone": "(*************",
    //   "primaryRequestPhone": "(*************",
    //   "backupRequestPhone": "(*************",
    //   "regionId": "60d21b4667d0d8992e610c85",
    //   "responsible": "60d21b4667d0d8992e610c86", // User ID
    //   "agents": ["60d21b4667d0d8992e610c87"], // Array of User IDs
    //   "sellers": ["60d21b4667d0d8992e610c88"], // Array of User IDs
    //   "paps": ["60d21b4667d0d8992e610c89"] // Array of PAP User IDs
    // }
    
    // Deduplicate user arrays to prevent duplicates
    if (body.responsible) {
      body.responsible = Array.from(new Set(body.responsible.map(String)));
    }
    if (body.agents) {
      body.agents = Array.from(new Set(body.agents.map(String)));
    }
    if (body.sellers) {
      body.sellers = Array.from(new Set(body.sellers.map(String)));
    }
    if (body.paps) {
      body.paps = Array.from(new Set(body.paps.map(String)));
    }
    
    const branchData = {
      ...body,
      automatedPhone: body.automatedPhone || '',
      primaryRequestPhone: body.primaryRequestPhone || '',
      backupRequestPhone: body.backupRequestPhone || '',
    };
    
    const newBranch = new Branch(branchData);
    await newBranch.save();
    
    const populatedBranch = await Branch.findById(newBranch._id)
      .populate('responsible', 'name')
      .populate('agents', 'name')
      .populate('sellers', 'name')
      .populate('paps', 'name');
    
    return NextResponse.json(populatedBranch, { status: 201 });
  } catch (error) {
    console.error('Error creating branch:', error);
    return NextResponse.json(
      { error: 'Failed to create branch' },
      { status: 500 }
    );
  }
}