import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Branch from '@/models/Branch';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { isValidObjectId } from 'mongoose';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    const branch = await Branch.findById(params.id)
      .populate('responsible', 'name email')
      .populate('agents', 'name email')
      .populate('sellers', 'name email')
      .populate('paps', 'name email') // Populate paps array
      
    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }
    
    return NextResponse.json(branch);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch branch' }, { status: 500 });
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    const body = await request.json();
    let p=await params;
    const id = await Promise.resolve(p.id);

    // Fetch the existing branch
    const branch = await Branch.findById(id);
    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }

    // Validate that all arrays only contain strings (user IDs)
    const arraysToCheck = ['responsible', 'agents', 'sellers', 'paps'];
    for (const key of arraysToCheck) {
      if (body[key] && Array.isArray(body[key])) {
        const hasObject = body[key].some((item: any) => typeof item !== 'string');
        if (hasObject) {
          return NextResponse.json({ error: `Field '${key}' must be an array of user IDs (strings) only.` }, { status: 400 });
        }
      }
    }
    
    // Handle arrays to prevent duplicates
    if (body.responsible) {
      // Convert to strings to ensure comparison works correctly
      body.responsible = Array.from(new Set(body.responsible.map(String)));
    }
    if (body.agents) {
      body.agents = Array.from(new Set(body.agents.map(String)));
    }
    if (body.sellers) {
      body.sellers = Array.from(new Set(body.sellers.map(String)));
    }
    if (body.paps) {
      body.paps = Array.from(new Set(body.paps.map(String)));
    }
    
    // Merge only provided fields
    Object.assign(branch, body);
    
    // Optionally: handle automatedPhone, primaryRequestPhone, backupRequestPhone defaults
    if (body.automatedPhone !== undefined) branch.automatedPhone = body.automatedPhone;
    if (body.primaryRequestPhone !== undefined) branch.primaryRequestPhone = body.primaryRequestPhone;
    if (body.backupRequestPhone !== undefined) branch.backupRequestPhone = body.backupRequestPhone;

    await branch.save();
    await branch.populate('responsible', 'name email');
    await branch.populate('agents', 'name email');
    await branch.populate('sellers', 'name email');
    await branch.populate('paps', 'name email');

    return NextResponse.json(branch);
  } catch (error) {
    console.error('Error updating branch:', error);
    return NextResponse.json({ error: 'Failed to update branch' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    
    if (!isValidObjectId(params.id)) {
      return NextResponse.json({ error: 'Invalid branch ID' }, { status: 400 });
    }

    // Implement soft delete instead of actual deletion
    const branch = await Branch.findByIdAndUpdate(
      params.id,
      { 
        deletedAt: new Date()
      },
      { new: true }
    );
    
    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Branch deleted successfully' });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to delete branch' }, { status: 500 });
  }
} 