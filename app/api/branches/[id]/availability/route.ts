import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Branch from '@/models/Branch';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { BRANCH_PERMISSIONS } from '@/types/permission-codes';
import { getUserPermissions, getUserRoles } from '@/app/api/utils/server-permission-utils';

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if(session && !session.user.permissions){
      session.user.permissions=await getUserPermissions(session);
    }
    if(session&& !session.user.roles){
      session.user.roles=await getUserRoles(session);
    }
    // Check permission
    if (!session.user.permissions?.includes(BRANCH_PERMISSIONS.EDIT_BRANCHES)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    await dbConnect();
    
    const { availabilityLimit } = await request.json();
    const p=await params;
    const id = p.id;
    
    const branch = await Branch.findByIdAndUpdate(
      id,
      { availabilityLimit },
      { new: true }
    );
    
    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }
    
    return NextResponse.json(branch);
  } catch (error) {
    console.error('Error updating branch availability:', error);
    return NextResponse.json({ error: 'Failed to update branch availability' }, { status: 500 });
  }
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    const branch = await Branch.findById(params.id).select('availabilityLimit');
      
    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }
    
    return NextResponse.json({ availabilityLimit: branch.availabilityLimit });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch branch availability' }, { status: 500 });
  }
} 