import { NextRequest, NextResponse } from 'next/server';
import { Plan } from '@/models/Plan';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
  await dbConnect();
    const branchId = new mongoose.Types.ObjectId(params.id);

    const plan = await Plan.findOne({ branchId });
    if (!plan) {
      return NextResponse.json({ tables: [], lines: [] });
    }

    return NextResponse.json(plan);
  } catch (error) {
    console.error('Error fetching plan:', error);
    return NextResponse.json(
      { error: 'Failed to fetch plan' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = await params;
  await dbConnect();
    const branchId = new mongoose.Types.ObjectId(id);
    const data = await request.json();

    // Transform the lines data to match the schema
    const transformedLines = data.lines.map((line: any) => ({
      points: line.points.reduce((acc: any[], point: number, index: number) => {
        if (index % 2 === 0) {
          acc.push({ x: point, y: line.points[index + 1] });
        }
        return acc;
      }, []),
      color: line.color,
      width: line.width
    }));

    const plan = await Plan.findOneAndUpdate(
      { branchId },
      { 
        branchId,
        tables: data.tables,
        lines: transformedLines,
        name: data.name,
        lastModified: new Date()
      },
      { upsert: true, new: true }
    );

    return NextResponse.json(plan);
  } catch (error) {
    console.error('Error saving plan:', error);
    return NextResponse.json(
      { error: 'Failed to save plan' },
      { status: 500 }
    );
  }
}