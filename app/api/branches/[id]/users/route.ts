import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Branch from '@/models/Branch';
import User from '@/models/User';
import Role from '@/models/Role';
import { UpdateQuery } from 'mongoose';
import { Branch as BranchType } from '@/types/branch';
import { Types } from 'mongoose';

interface UpdateData {
  responsible: string[];
  agents: string[];
  sellers: string[];
  paps: string[];
}

interface PopulatedUser {
  _id: string;
  name: string;
  email: string;
}

interface BranchData {
  _id: Types.ObjectId;
  responsible: PopulatedUser[];
  agents: PopulatedUser[];
  sellers: PopulatedUser[];
  paps: PopulatedUser[];
  [key: string]: any;
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    const p = await params;
    
    // Special case for "all" - just return eligible users by role
    if (p.id === 'all') {
      // Get the role IDs for BranchesAdmin, BranchesAgent, Seller, and PAP
      const roles = await Role.find({
        name: { $in: ['BranchesAdmin', 'BranchesAgent', 'Seller', 'PAP'] }
      }).lean();

      const branchAdminRoleId = roles.find(role => role.name === 'BranchesAdmin')?._id;
      const branchAgentRoleId = roles.find(role => role.name === 'BranchesAgent')?._id;
      const sellerRoleId = roles.find(role => role.name === 'Seller')?._id;
      const papRoleId = roles.find(role => role.name === 'PAP')?._id;

      // Get all eligible users
      const eligibleUsers = await User.find({
        roles: { 
          $in: [branchAdminRoleId, branchAgentRoleId, sellerRoleId, papRoleId] 
        }
      })
      .populate('roles', 'name')
      .select('name email roles')
      .lean();

      // Separate users by role
      const branchAdmins = eligibleUsers.filter(user => 
        user.roles.some((role: any) => role._id.toString() === branchAdminRoleId?.toString())
      );
      const branchAgents = eligibleUsers.filter(user => 
        user.roles.some((role: any) => role._id.toString() === branchAgentRoleId?.toString())
      );
      const branchSellers = eligibleUsers.filter(user => 
        user.roles.some((role: any) => role._id.toString() === sellerRoleId?.toString())
      );
      const branchPAPs = eligibleUsers.filter(user => 
        user.roles.some((role: any) => role._id.toString() === papRoleId?.toString())
      );

      return NextResponse.json({
        eligibleUsers: {
          admins: branchAdmins,
          agents: branchAgents,
          sellers: branchSellers,
          paps: branchPAPs
        },
        currentUsers: {
          admins: [],
          agents: [],
          sellers: [],
          paps: []
        }
      });
    }
    
    // Regular case for specific branch ID
    const branchDoc = await Branch.findById(p.id)
      .select('responsible agents sellers paps')
      .populate('responsible', 'name email')
      .populate('agents', 'name email')
      .populate('sellers', 'name email')
      .populate('paps', 'name email')
      .lean() as unknown as BranchData;

    if (!branchDoc) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }

    // Get the role IDs for BranchesAdmin, BranchesAgent, Seller, and PAP
    const roles = await Role.find({
      name: { $in: ['BranchesAdmin', 'BranchesAgent', 'Seller', 'PAP'] }
    }).lean();

    const branchAdminRoleId = roles.find(role => role.name === 'BranchesAdmin')?._id;
    const branchAgentRoleId = roles.find(role => role.name === 'BranchesAgent')?._id;
    const sellerRoleId = roles.find(role => role.name === 'Seller')?._id;
    const papRoleId = roles.find(role => role.name === 'PAP')?._id;

    // Get all eligible users
    const eligibleUsers = await User.find({
      roles: { 
        $in: [branchAdminRoleId, branchAgentRoleId, sellerRoleId, papRoleId] 
      }
    })
    .populate('roles', 'name')
    .select('name email roles')
    .lean();

    // Separate users by role
    const branchAdmins = eligibleUsers.filter(user => 
      user.roles.some((role: any) => role._id.toString() === branchAdminRoleId?.toString())
    );
    const branchAgents = eligibleUsers.filter(user => 
      user.roles.some((role: any) => role._id.toString() === branchAgentRoleId?.toString())
    );
    const branchSellers = eligibleUsers.filter(user => 
      user.roles.some((role: any) => role._id.toString() === sellerRoleId?.toString())
    );

    return NextResponse.json({
      eligibleUsers: {
        admins: branchAdmins,
        agents: branchAgents,
        sellers: branchSellers
      },
      currentUsers: {
        admins: branchDoc.responsible || [],
        agents: branchDoc.agents || [],
        sellers: branchDoc.sellers || []
      }
    });
  } catch (error) {
    console.error('GET branch users error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch branch users' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const data = await request.json() as UpdateData;
    console.log('Updating branch users with:', { 
      responsible: data.responsible, 
      agents: data.agents, 
      sellers: data.sellers 
    });

    // Remove any potential duplicate IDs
    const uniqueResponsible = Array.from(new Set(data.responsible));
    const uniqueAgents = Array.from(new Set(data.agents))
    const uniqueSellers = Array.from(new Set(data.sellers));

    await dbConnect();
    const p = await params;
    // Update the branch directly with the new data
    const updatedBranch = await Branch.findByIdAndUpdate(
      p.id,
      {
        responsible: uniqueResponsible.map(id => new Types.ObjectId(id)),
        agents: uniqueAgents.map(id => new Types.ObjectId(id)),
        sellers: uniqueSellers.map(id => new Types.ObjectId(id))
      },
      {
        new: true,
        runValidators: true
      }
    )
      .select('name postalCode city province address regionId responsible agents sellers createdAt updatedAt')
      .populate('responsible', 'name email')
      .populate('agents', 'name email')
      .populate('sellers', 'name email')
      .lean();

    if (!updatedBranch) {
      throw new Error('Failed to fetch updated branch');
    }

    // --- Update users' branchIds field ---
    const branchId = p.id;
    // All user IDs that should be associated with this branch after update
    const newUserIds = [
      ...uniqueResponsible,
      ...uniqueAgents,
      ...uniqueSellers
    ];
    // Find all users who currently have this branch in their branchIds
    const allAffectedUsers = await User.find({
      $or: [
        { _id: { $in: newUserIds } },
        { branchIds: branchId }
      ]
    });
    // For each user, update their branchIds array
    await Promise.all(
      allAffectedUsers.map(async (user) => {
        const isNowInBranch = newUserIds.includes(user._id.toString());
        const hasBranch = user.branchIds?.map((id: any) => id.toString()).includes(branchId);
        if (isNowInBranch && !hasBranch) {
          // Add branchId
          user.branchIds = [...(user.branchIds || []), new Types.ObjectId(branchId)];
          await user.save();
        } else if (!isNowInBranch && hasBranch) {
          // Remove branchId
          user.branchIds = (user.branchIds || []).filter((id: any) => id.toString() !== branchId);
          await user.save();
        }
      })
    );
    // --- End update users' branchIds ---

    console.log('Final updated branch:', updatedBranch);
    return NextResponse.json(updatedBranch);
  } catch (error: any) {
    console.error('PUT branch users error:', error);
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);

    return NextResponse.json(
      { 
        error: 'Failed to update branch users',
        details: error.message
      },
      { status: 500 }
    );
  }
}