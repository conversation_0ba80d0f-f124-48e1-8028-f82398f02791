import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import Branch from '@/models/Branch';
import Appointment from '@/models/Appointment';
import AppointmentDisponibility from '@/models/AppointmentDisponibility';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!userId || !startDate || !endDate) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // Step 1: Find all branches where the user is responsible or an agent
    const branchesWithUser = await Branch.find({
      $or: [
        { responsible: userId },
        { agents: userId }
      ]
    }).lean();

    const branchIds = branchesWithUser.map(branch => branch._id);

    // Step 2: Find all appointments for these branches in the date range
    const appointments = await Appointment.find({
      branchId: { $in: branchIds },
      date: { $gte: startDate, $lte: endDate }
    }).sort({ date: 1, startHour: 1 }).lean();

    // Step 3: Find all disponibilities for this user
    const disponibilities = await AppointmentDisponibility.find({
      user_id: userId
    }).lean();

    // Create a map for quick lookup of disponibilities
    const disponibilityMap = {};
    disponibilities.forEach(disp => {
      disponibilityMap[disp.appointment_id.toString()] = disp.disponibility;
    });

    // Add disponibility information to each appointment
    const appointmentsWithDisponibility = appointments.map(appointment => {
      const appointmentId = appointment._id.toString();
      return {
        ...appointment,
        disponibility: disponibilityMap[appointmentId] || false
      };
    });

    return NextResponse.json({
      branches: branchesWithUser,
      appointments: appointmentsWithDisponibility
    });

  } catch (error) {
    console.error('Error fetching user appointments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user appointments' },
      { status: 500 }
    );
  }
} 