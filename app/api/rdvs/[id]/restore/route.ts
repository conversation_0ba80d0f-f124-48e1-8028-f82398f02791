import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import dbConnect from '@/lib/db';
import RDVModel from '@/app/models/RDV';
import { authOptions } from '@/lib/auth';

// POST /api/rdvs/[id]/restore
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const rdv = await RDVModel.findOneAndUpdate(
      { 
        _id: params.id,
        deletedAt: { $exists: true } // Only restore if it's actually deleted
      },
      {
        $unset: { deletedAt: 1, deletedBy: 1 },
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    ).populate('createdBy', 'name email');

    if (!rdv) {
      return NextResponse.json({ error: 'RDV not found or not deleted' }, { status: 404 });
    }

    return NextResponse.json(rdv);
  } catch (error: any) {
    console.error('Failed to restore RDV:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: Object.values(error.errors).map((err: any) => err.message)
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to restore RDV' },
      { status: 500 }
    );
  }
}