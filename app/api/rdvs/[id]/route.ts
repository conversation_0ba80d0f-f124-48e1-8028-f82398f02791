import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import dbConnect from '@/lib/db';
import RDVModel from '@/app/models/RDV';
import { authOptions } from '@/lib/auth';

// GET /api/rdvs/[id]
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const rdv = await RDVModel.findById(params.id)
      .populate('createdBy', 'name email')
      .populate('deletedBy', 'name email');

    if (!rdv) {
      return NextResponse.json({ error: 'RDV not found' }, { status: 404 });
    }

    return NextResponse.json(rdv);
  } catch (error) {
    console.error('Failed to fetch RDV:', error);
    return NextResponse.json(
      { error: 'Failed to fetch RDV' },
      { status: 500 }
    );
  }
}

// PATCH /api/rdvs/[id]
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    const data = await req.json();

    const rdv = await RDVModel.findByIdAndUpdate(
      params.id,
      { ...data, updatedAt: new Date() },
      {
        new: true,
        runValidators: true,
        strict: true,
        lean: false
      }
    ).populate('createdBy', 'name email');

    // Log the data being saved for debugging
    console.log('RDV data being saved:', data);
    console.log('Updated RDV:', rdv);

    if (!rdv) {
      return NextResponse.json({ error: 'RDV not found' }, { status: 404 });
    }

    return NextResponse.json(rdv);
  } catch (error: any) {
    console.error('Failed to update RDV:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: Object.values(error.errors).map((err: any) => err.message)
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update RDV' },
      { status: 500 }
    );
  }
}

// DELETE /api/rdvs/[id]
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Implement soft delete
    const rdv = await RDVModel.findByIdAndUpdate(
      params.id,
      {
        deletedAt: new Date(),
        deletedBy: session.user.id,
        updatedAt: new Date()
      },
      { new: true }
    );

    if (!rdv) {
      return NextResponse.json({ error: 'RDV not found' }, { status: 404 });
    }

    return NextResponse.json(rdv);
  } catch (error) {
    console.error('Failed to delete RDV:', error);
    return NextResponse.json(
      { error: 'Failed to delete RDV' },
      { status: 500 }
    );
  }
}