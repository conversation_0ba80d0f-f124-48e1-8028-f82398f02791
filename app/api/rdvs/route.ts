import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import dbConnect from '@/lib/db';
import RDVModel from '@/app/models/RDV';
import { RDV } from '@/app/types/rdv';
import { authOptions } from '@/lib/auth';

// GET /api/rdvs
export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const searchParams = req.nextUrl.searchParams;
    const showDeleted = searchParams.get('showDeleted') === 'true';
    
    const query = showDeleted 
      ? { deletedAt: { $exists: true } }  // Only show deleted items
      : { deletedAt: { $exists: false } }; // Only show non-deleted items
    
    const rdvs = await RDVModel.find(query)
      .populate('createdBy', 'name email')
      .populate('deletedBy', 'name email')
      .sort({ createdAt: -1 });

    return NextResponse.json(rdvs);
  } catch (error) {
    console.error('Failed to fetch RDVs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch RDVs' },
      { status: 500 }
    );
  }
}

// POST /api/rdvs
export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    const data = await req.json();

    const rdvData = {
      ...data,
      createdBy: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Log the data being saved for debugging
    console.log('RDV data being created:', rdvData);

    const rdv = await RDVModel.create(rdvData);
    const populatedRdv = await rdv.populate('createdBy', 'name email');

    console.log('Created RDV:', populatedRdv);

    return NextResponse.json(populatedRdv, { status: 201 });
  } catch (error: any) {
    console.error('Failed to create RDV:', error);
    
    // Return validation errors if available
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: Object.values(error.errors).map((err: any) => err.message)
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create RDV' },
      { status: 500 }
    );
  }
}