import { NextResponse } from 'next/server';
import { join, resolve } from 'path';
import { createReadStream, statSync, existsSync } from 'fs';
import mime from 'mime-types';

export async function GET(
  request: Request,
  { params }: { params: { path: string[] } }
) {
  try {
    // Construct the file path using public directory
    const publicDir = resolve(process.cwd(), 'public');
    const filePath = join(publicDir, 'uploads', ...params.path);
    
    // Ensure the path is within the uploads directory (security measure)
    if (!filePath.startsWith(join(publicDir, 'uploads'))) {
      console.error('Invalid path access attempt:', filePath);
      return new NextResponse('Invalid path', { status: 403 });
    }
    
    console.log('Attempting to serve file:', filePath);
    
    if (!existsSync(filePath)) {
      console.error('File not found:', filePath);
      return new NextResponse('File not found', { status: 404 });
    }

    const stats = statSync(filePath);
    const mimeType = mime.lookup(filePath) || 'application/octet-stream';
    const fileStream = createReadStream(filePath);

    const headers = new Headers({
      'Content-Type': mimeType,
      'Content-Length': stats.size.toString(),
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    });

    return new NextResponse(fileStream as any, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('Error serving file:', error);
    return new NextResponse('Error serving file', { status: 500 });
  }
} 