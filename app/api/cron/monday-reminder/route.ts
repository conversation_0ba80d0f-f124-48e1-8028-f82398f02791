import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { CronScheduler } from '@/lib/services/cron-scheduler';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get current configuration
    const branchIds = CronScheduler.getMondayReminderBranchIds();
    
    return NextResponse.json({
      success: true,
      configuration: {
        branchIds,
        branchCount: branchIds.length,
        schedule: 'Saturday 7:00 PM (48 hours before Monday)',
        timezone: 'America/Toronto'
      }
    });

  } catch (error) {
    console.error('Error getting Monday reminder configuration:', error);
    return NextResponse.json(
      { error: 'Failed to get configuration' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, branchIds } = body;

    switch (action) {
      case 'trigger':
        // Manually trigger the Monday reminder job
        console.log('Manually triggering Monday reminder job...');
        const result = await CronScheduler.triggerMondayReservationReminderManually();
        
        return NextResponse.json({
          success: true,
          message: 'Monday reminder job executed successfully',
          result
        });

      case 'configure':
        // Update branch IDs configuration
        if (!Array.isArray(branchIds)) {
          return NextResponse.json(
            { error: 'branchIds must be an array' },
            { status: 400 }
          );
        }

        // Validate branch IDs format
        const validBranchIds = branchIds.filter(id => 
          typeof id === 'string' && id.length === 24
        );

        if (validBranchIds.length !== branchIds.length) {
          return NextResponse.json(
            { error: 'All branch IDs must be valid 24-character strings' },
            { status: 400 }
          );
        }

        CronScheduler.updateMondayReminderBranchIds(validBranchIds);
        
        return NextResponse.json({
          success: true,
          message: 'Branch IDs configuration updated successfully',
          configuration: {
            branchIds: validBranchIds,
            branchCount: validBranchIds.length
          }
        });

      case 'restart':
        // Restart the Monday reminder automation
        CronScheduler.restartMondayReservationReminderAutomation();
        
        return NextResponse.json({
          success: true,
          message: 'Monday reminder automation restarted successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: trigger, configure, or restart' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in Monday reminder API:', error);
    return NextResponse.json(
      { 
        error: 'Failed to execute action',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
