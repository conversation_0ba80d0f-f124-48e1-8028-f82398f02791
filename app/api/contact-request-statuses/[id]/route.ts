import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import ContactRequestStatus from '@/models/ContactRequestStatus';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
  await dbConnect();
    const status = await ContactRequestStatus.findById(params.id);
    
    if (!status) {
      return NextResponse.json(
        { error: 'Contact request status not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(status);
  } catch (error) {
    console.error('Error fetching contact request status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contact request status' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
  await dbConnect();
    const data = await req.json();
    
    // Validate required fields
    if (!data.name || !data.name_en || !data.code) {
      return NextResponse.json(
        { error: 'Name, name_en, and code are required' },
        { status: 400 }
      );
    }
    
    const updatedStatus = await ContactRequestStatus.findByIdAndUpdate(
      params.id,
      data,
      { new: true, runValidators: true }
    );
    
    if (!updatedStatus) {
      return NextResponse.json(
        { error: 'Contact request status not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedStatus);
  } catch (error: any) {
    console.error('Error updating contact request status:', error);
    
    // Handle duplicate key errors
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A status with that name or code already exists' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update contact request status' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
  await dbConnect();
    const deletedStatus = await ContactRequestStatus.findByIdAndDelete(params.id);
    
    if (!deletedStatus) {
      return NextResponse.json(
        { error: 'Contact request status not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting contact request status:', error);
    return NextResponse.json(
      { error: 'Failed to delete contact request status' },
      { status: 500 }
    );
  }
} 