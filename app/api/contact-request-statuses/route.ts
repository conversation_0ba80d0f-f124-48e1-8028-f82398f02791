import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import ContactRequestStatus from '@/models/ContactRequestStatus';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';

export async function GET() {
  try {
  await dbConnect();
    const statuses = await ContactRequestStatus.find().sort({ order: 1 });
    return NextResponse.json(statuses);
  } catch (error) {
    console.error('Error fetching contact request statuses:', error);
    return NextResponse.json({ error: 'Failed to fetch contact request statuses' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

  await dbConnect();
    const data = await req.json();
    
    // Validate required fields
    if (!data.name || !data.name_en || !data.code) {
      return NextResponse.json(
        { error: 'Name, name_en, and code are required' },
        { status: 400 }
      );
    }

    // Create the status
    const newStatus = await ContactRequestStatus.create(data);
    return NextResponse.json(newStatus, { status: 201 });
  } catch (error: any) {
    console.error('Error creating contact request status:', error);
    
    // Handle duplicate key errors
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A status with that name or code already exists' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to create contact request status' },
      { status: 500 }
    );
  }
} 