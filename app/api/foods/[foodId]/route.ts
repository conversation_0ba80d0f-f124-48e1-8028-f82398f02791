import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Food from '@/models/Food';
import { hasPermission } from '@/lib/auth/check-permission';
import { FOOD_PERMISSIONS } from '@/types/permission-codes';

export async function PUT(
  request: Request,
  { params }: { params: { foodId: string } }
) {
  try {
    await dbConnect();
    
    if (!await hasPermission(FOOD_PERMISSIONS.EDIT_FOODS)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();

    if (!body.name || !body.name_en) {
      return NextResponse.json(
        { error: 'Name and English name are required' },
        { status: 400 }
      );
    }

    const food = await Food.findByIdAndUpdate(
      params.foodId,
      {
        name: body.name,
        name_en: body.name_en,
      },
      { new: true }
    );

    if (!food) {
      return NextResponse.json(
        { error: 'Food not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(food);
  } catch (error) {
    console.error('Update food error:', error);
    return NextResponse.json(
      { error: 'Failed to update food' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { foodId: string } }
) {
  try {
    await dbConnect();
    
    if (!await hasPermission(FOOD_PERMISSIONS.DELETE_FOODS)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const food = await Food.findByIdAndDelete(params.foodId);

    if (!food) {
      return NextResponse.json(
        { error: 'Food not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Food deleted successfully' });
  } catch (error) {
    console.error('Delete food error:', error);
    return NextResponse.json(
      { error: 'Failed to delete food' },
      { status: 500 }
    );
  }
} 