import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import User from '@/models/User';
import { generateContractHTML } from '@/lib/utils/contract-html-template';
import { mapUserWithExistingSignatures } from '@/lib/utils/contract-data-mapper';

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    // Find the token in the database
    const contractToken = await ContractSigningToken.findOne({ token });

    if (!contractToken) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 404 }
      );
    }

    // Check if admin has signed
    if (!contractToken.adminSignedAt) {
      return NextResponse.json(
        { error: 'Contract not ready for user signature' },
        { status: 400 }
      );
    }

    // Fetch the user data with populated roles
    const user = await User.findById(contractToken.userId).populate('roles');
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Map user data to contract format with existing signatures
    const contractData = mapUserWithExistingSignatures(user);

    // Add contract start date from token
    contractData.contractStartDate = contractToken.contractStartDate?.toISOString();

    // Add admin signature from token
    if (contractToken.adminSignedAt && contractToken.adminSignatureData) {
      contractData.adminSignature = {
        data: contractToken.adminSignatureData,
        date: contractToken.adminSignedAt.toISOString()
      };
    }

    // Add user signature from token if exists
    if (contractToken.userSignedAt && contractToken.userSignatureData) {
      contractData.userSignature = {
        data: contractToken.userSignatureData,
        date: contractToken.userSignedAt.toISOString()
      };
    }

    // Generate HTML content using the same function as debug preview and PDF
    const htmlContent = generateContractHTML(contractData);

    return NextResponse.json({
      success: true,
      htmlContent
    });

  } catch (error: any) {
    console.error('Error generating contract HTML:', error);
    
    // Handle specific error types
    if (error.message?.includes('Contract generation is only available')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Failed to generate contract HTML',
        details: error.message
      },
      { status: 500 }
    );
  }
}
