import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import User from '@/models/User';
import ContractAuditLog from '@/models/ContractAuditLog';

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    // Find the token in the database
    const contractToken = await ContractSigningToken.findOne({ token });

    if (!contractToken) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 404 }
      );
    }

    // Check if admin has signed
    if (!contractToken.adminSignedAt) {
      return NextResponse.json(
        { error: 'Contract not ready for user signature' },
        { status: 400 }
      );
    }

    // Check if this is the first visit
    const isFirstVisit = !contractToken.firstVisitedAt;

    // Fetch the user data with populated roles
    const user = await User.findById(contractToken.userId).populate('roles');
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // If it's the first visit, update the token
    if (isFirstVisit) {
      const now = new Date();
      contractToken.firstVisitedAt = now;
      await contractToken.save();

      // Log audit event
      await ContractAuditLog.logAction(
        contractToken.userId,
        contractToken.token,
        'user_visited',
        undefined,
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        request.headers.get('user-agent') || 'unknown'
      );
    }

    // Prepare contract data
    const contractData = {
      user: {
        name: user.name,
        email: user.email,
        phone: user.phone,
        birthDate: user.birthDate,
        companyName: user.companyName,
        address: user.address,
        city: user.city,
        postalCode: user.postalCode,
        socialAssurance: user.socialAssurance,
        taxInfo: user.taxInfo,
        emergencyContact: user.emergencyContact,
        roles: user.roles
      },
      contractStartDate: contractToken.contractStartDate,
      adminSignature: {
        data: contractToken.adminSignatureData,
        date: contractToken.adminSignedAt
      },
      userSignature: contractToken.userSignedAt ? {
        data: contractToken.userSignatureData,
        date: contractToken.userSignedAt
      } : undefined
    };

    return NextResponse.json({
      success: true,
      tokenInfo: {
        userId: contractToken.userId,
        contractStartDate: contractToken.contractStartDate,
        adminSignedAt: contractToken.adminSignedAt,
        userSignedAt: contractToken.userSignedAt,
        isCompleted: contractToken.isCompleted,
        userSignatureData: contractToken.userSignatureData
      },
      contractData
    });

  } catch (error: any) {
    console.error('Error validating contract token:', error);
    return NextResponse.json(
      {
        error: 'Failed to validate token',
        details: error.message
      },
      { status: 500 }
    );
  }
}
