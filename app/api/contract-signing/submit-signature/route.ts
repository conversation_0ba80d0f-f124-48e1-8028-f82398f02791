import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import User from '@/models/User';
import ContractAuditLog from '@/models/ContractAuditLog';

function getClientIP(request: NextRequest): string {
  return request.headers.get('x-forwarded-for') || 
         request.headers.get('x-real-ip') || 
         'unknown';
}

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { token, signatureData } = await request.json();

    if (!token || !signatureData) {
      return NextResponse.json(
        { error: 'Token and signature data are required' },
        { status: 400 }
      );
    }

    // Find the token in the database
    const contractToken = await ContractSigningToken.findOne({ token });

    if (!contractToken) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 404 }
      );
    }

    // Check if admin has signed
    if (!contractToken.adminSignedAt) {
      return NextResponse.json(
        { error: 'Contract not ready for user signature' },
        { status: 400 }
      );
    }

    // Check if user has already signed
    if (contractToken.userSignedAt) {
      return NextResponse.json(
        { error: 'Contract has already been signed by user' },
        { status: 400 }
      );
    }

    // Update the token with user signature
    const now = new Date();
    const clientIP = getClientIP(request);

    contractToken.userSignedAt = now;
    contractToken.userSignatureData = signatureData;
    contractToken.userSignedFromIP = clientIP;

    // Manually update isCompleted status to ensure it's set correctly
    contractToken.isCompleted = !!(contractToken.adminSignedAt && contractToken.adminSignatureData && now && signatureData);

    await contractToken.save();

    // Update user record with completion status
    await User.findByIdAndUpdate(contractToken.userId, {
      contractSignedAt: now,
      contractSignatureData: signatureData,
      contractCompletedAt: contractToken.isCompleted ? now : undefined
    });

    // Log audit event
    await ContractAuditLog.logAction(
      contractToken.userId,
      contractToken.token,
      'user_signed',
      undefined,
      clientIP,
      request.headers.get('user-agent') || 'unknown'
    );

    // If contract is now complete, log completion
    if (contractToken.isCompleted) {
      await ContractAuditLog.logAction(
        contractToken.userId,
        contractToken.token,
        'completed',
        undefined,
        clientIP,
        request.headers.get('user-agent') || 'unknown'
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Signature submitted successfully',
      tokenInfo: {
        userId: contractToken.userId,
        contractStartDate: contractToken.contractStartDate,
        adminSignedAt: contractToken.adminSignedAt,
        userSignedAt: contractToken.userSignedAt,
        isCompleted: contractToken.isCompleted,
        userSignatureData: contractToken.userSignatureData
      }
    });

  } catch (error: any) {
    console.error('Error submitting signature:', error);
    return NextResponse.json(
      {
        error: 'Failed to submit signature',
        details: error.message
      },
      { status: 500 }
    );
  }
}
