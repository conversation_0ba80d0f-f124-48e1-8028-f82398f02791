import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import User from '@/models/User';
import ContractAuditLog from '@/models/ContractAuditLog';
import { generateContractPDF } from '@/lib/utils/contract-pdf-utils';

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    // Find the token in the database
    const contractToken = await ContractSigningToken.findOne({ token });

    if (!contractToken) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 404 }
      );
    }

    // Fetch the user data with populated roles
    const user = await User.findById(contractToken.userId).populate('roles');
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Prepare contract data for PDF generation
    const contractData = {
      user: {
        name: user.name,
        email: user.email,
        phone: user.phone,
        birthDate: user.birthDate,
        companyName: user.companyName,
        address: user.address,
        city: user.city,
        postalCode: user.postalCode,
        socialAssurance: user.socialAssurance,
        taxInfo: user.taxInfo,
        emergencyContact: user.emergencyContact,
        roles: user.roles
      },
      contractStartDate: contractToken.contractStartDate?.toISOString(),
      adminSignature: contractToken.adminSignedAt ? {
        data: contractToken.adminSignatureData,
        date: contractToken.adminSignedAt?.toISOString()
      } : undefined,
      userSignature: contractToken.userSignedAt ? {
        data: contractToken.userSignatureData,
        date: contractToken.userSignedAt?.toISOString()
      } : undefined
    };

    // Generate PDF
    const pdfBuffer = await generateContractPDF(contractData);
    const pdfBase64 = `data:application/pdf;base64,${pdfBuffer.toString('base64')}`;

    // Generate filename based on contract status
    const userName = user.name.replace(/\s+/g, '_');
    const timestamp = new Date().toISOString().split('T')[0];
    
    let fileName = `contrat_${userName}_${timestamp}`;
    
    if (contractToken.isCompleted) {
      fileName += '_signe';
    } else if (contractToken.adminSignedAt) {
      fileName += '_admin_signe';
    } else {
      fileName += '_brouillon';
    }
    
    fileName += '.pdf';

    // Log audit event
    const auditAction = contractToken.isCompleted ? 'completed' : 'user_visited';
    await ContractAuditLog.logAction(
      contractToken.userId,
      contractToken.token,
      auditAction,
      undefined,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      { 
        action: 'download_contract',
        contractStatus: contractToken.isCompleted ? 'completed' : (contractToken.adminSignedAt ? 'admin_signed' : 'draft')
      }
    );

    return NextResponse.json({
      success: true,
      pdfBase64,
      fileName,
      contractStatus: {
        isCompleted: contractToken.isCompleted,
        hasAdminSignature: !!contractToken.adminSignedAt,
        hasUserSignature: !!contractToken.userSignedAt
      },
      message: 'Contract downloaded successfully'
    });

  } catch (error: any) {
    console.error('Error downloading contract:', error);
    return NextResponse.json(
      {
        error: 'Failed to download contract',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
