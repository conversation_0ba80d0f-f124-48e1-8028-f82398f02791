import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import User from '@/models/User';
import ContractAuditLog from '@/models/ContractAuditLog';
import { generateContractPDF } from '@/lib/utils/contract-pdf-utils';

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    // Find the token in the database
    const contractToken = await ContractSigningToken.findOne({ token });

    if (!contractToken) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 404 }
      );
    }

    // Check if contract is completed
    if (!contractToken.isCompleted) {
      return NextResponse.json(
        { error: 'Contract is not yet completed' },
        { status: 400 }
      );
    }

    // Fetch the user data with populated roles
    const user = await User.findById(contractToken.userId).populate('roles');
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Prepare contract data for PDF generation
    const contractData = {
      user: {
        name: user.name,
        email: user.email,
        phone: user.phone,
        birthDate: user.birthDate,
        companyName: user.companyName,
        address: user.address,
        city: user.city,
        postalCode: user.postalCode,
        socialAssurance: user.socialAssurance,
        taxInfo: user.taxInfo,
        emergencyContact: user.emergencyContact,
        roles: user.roles
      },
      contractStartDate: contractToken.contractStartDate?.toISOString(),
      adminSignature: {
        data: contractToken.adminSignatureData,
        date: contractToken.adminSignedAt?.toISOString()
      },
      userSignature: {
        data: contractToken.userSignatureData,
        date: contractToken.userSignedAt?.toISOString()
      }
    };

    // Generate PDF
    const pdfBuffer = await generateContractPDF(contractData);
    const pdfBase64 = `data:application/pdf;base64,${pdfBuffer.toString('base64')}`;

    // Generate filename
    const fileName = `contrat_${user.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;

    // Log audit event
    await ContractAuditLog.logAction(
      contractToken.userId,
      contractToken.token,
      'completed', // Using 'completed' action for download
      undefined,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      { action: 'download_signed_contract' }
    );

    return NextResponse.json({
      success: true,
      pdfBase64,
      fileName,
      message: 'Contract downloaded successfully'
    });

  } catch (error: any) {
    console.error('Error downloading signed contract:', error);
    return NextResponse.json(
      {
        error: 'Failed to download contract',
        details: error.message
      },
      { status: 500 }
    );
  }
}
