import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import InvoiceItem from '@/models/InvoiceItem';
import Invoice from '@/models/Invoice';
import { getToken } from 'next-auth/jwt';
import * as RoleUtils from '@/lib/utils/role-utils';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';
import mongoose from 'mongoose';
import * as BillingUtils from '@/lib/utils/billing-utils';
import { canUserAccessBilling, canUserManageBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { getItemTaxableStatusForInvoice } from '../../utils/invoice-item-utils';
import InvoiceItemType from '@/models/InvoiceItemType';
import Commission from '@/models/Commission';
import Bonus from '@/models/Bonus';
import { InvoiceAuditLogger } from '@/lib/utils/audit-utils';
import Reservation from '@/models/Reservation';

// Helper function to generate commission title based on invoice item type code
async function generateCommissionTitle(
  invoiceItemType: any,
  commissions: any[]
): Promise<string> {
  const code = invoiceItemType?.code;

  // Helper function to format date as DD/MM/YYYY
  const formatDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  switch (code) {
    case 'commissions':
      // For grouped coupons, include date range
      if (commissions.length > 1) {
        return 'COUPONS';
      }
      return 'COUPONS';

    case 'commission-vente':
      // For commission-vente, get customer name from reservation and add date
      const commission = commissions[0];
      if (commission?.reservationId) {
        try {
          const reservation = await Reservation.findById(commission.reservationId).lean() as any;
          if (reservation?.customerInfo) {
            const client1Name = reservation.customerInfo.client1Name || '';
            const client2Name = reservation.customerInfo.client2Name || '';
            const customerName = client2Name ? `${client1Name} ${client2Name}` : client1Name;
            const commissionDate = commission.createdAt ? new Date(commission.createdAt) : new Date();
            return `Commission sur la vente de ${customerName} le ${formatDate(commissionDate)}`;
          }
        } catch (error) {
          console.error('Error fetching reservation for commission-vente title:', error);
        }
      }
      // Fallback to default name if reservation not found
      return invoiceItemType?.name || 'Commission';

    default:
      // For all other types, use the invoice item type name
      return invoiceItemType?.name + (commissions.length > 1 ? ` (x${commissions.length})` : '');
  }
}

// GET: List all invoice items (admin only)
export async function GET(request: NextRequest) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions   = await getUserPermissions(session);
  }
  if (!session || !canUserAccessBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const invoiceItems = await InvoiceItem.find({});
    return NextResponse.json(invoiceItems);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch invoice items' }, { status: 500 });
  }
}

// POST: Create a new invoice item and associate it with an invoice (admin only)
export async function POST(request: NextRequest) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const session2 = await mongoose.startSession();
  session2.startTransaction();

  try {
    const body = await request.json();
    const { invoiceId, commissionIds, bonusIds, date, itemDate, ...itemData } = body;

    // Client-side workaround: If itemDate is provided, use it as date
    if (itemDate && !itemData.date) {
      itemData.date = itemDate;
    } else if (date) {
      // If date was provided as top-level param, add it back to itemData
      itemData.date = date;
    }

    // Validate invoiceId is provided and is a valid ObjectId
    if (!invoiceId || !mongoose.Types.ObjectId.isValid(invoiceId)) {
      return NextResponse.json({ error: 'Valid invoiceId is required' }, { status: 400 });
    }
    // Batch commissions
    if (Array.isArray(commissionIds) && commissionIds.length > 0) {
      // Fetch all commissions with commission type populated
      const commissions = await Commission.find({ _id: { $in: commissionIds } })
        .populate('commissionTypeId', 'name code')
        .populate('reservationId', 'customerInfo')
        .lean();
      if (!commissions.length) {
        await session2.abortTransaction();
        session2.endSession();
        return NextResponse.json({ error: 'No commissions found' }, { status: 400 });
      }

      // Find the invoice
      const invoice = await Invoice.findById(invoiceId);
      if (!invoice) {
        await session2.abortTransaction();
        session2.endSession();
        return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
      }

      const createdItems = [];

      // Group commissions by commission type to determine invoice item type
      const commissionsByType = new Map();
      for (const commission of commissions) {
        const typeId = commission.commissionTypeId?._id?.toString() || commission.commissionTypeId?.toString();
        if (!commissionsByType.has(typeId)) {
          commissionsByType.set(typeId, []);
        }
        commissionsByType.get(typeId).push(commission);
      }

      // Process each commission type group
      for (const [, commissionsOfType] of Array.from(commissionsByType.entries())) {
        // Find the appropriate invoice item type for this commission type
        let invoiceItemType = await InvoiceItemType.findOne({ code: "Autres" });

        // Try to find a more specific invoice item type based on commission type code
        const commissionTypeCode = commissionsOfType[0]?.commissionTypeId?.code || '';
        if (commissionTypeCode === 'commission-vente') {
          const venteType = await InvoiceItemType.findOne({ code: 'commission-vente' });
          if (venteType) invoiceItemType = venteType;
        } else if (commissionTypeCode === 'commissions') {
          const papType = await InvoiceItemType.findOne({ code: 'commissions' });
          if (papType) invoiceItemType = papType;
        }

        // Group by amount within this commission type
        const groups: Record<string, any[]> = {};
        for (const c of commissionsOfType) {
          if (!groups[c.amount as any]) groups[c.amount as any] = [];
          groups[c.amount as any].push(c);
        }

        for (const [amount, comms] of Object.entries(groups)) {
          const commsArr = comms as any[];
          // Use provided date if present, else set to today
          let itemDate = date ? new Date(date).toISOString() : new Date().toISOString();

          // Generate title based on commission type and grouping
          let title = await generateCommissionTitle(invoiceItemType, commsArr);

          const invoiceItem = await InvoiceItem.create([{
            title,
            quantity: commsArr.length,
            date: itemDate,
            unitPrice: Number(amount),
            taxable: itemData.taxable,
            invoiceitemtypeId: invoiceItemType?._id,
          }], { session: session2 });
          invoice.manualItems.push(invoiceItem[0]._id);
          createdItems.push(invoiceItem[0]);
        }
      }

      await invoice.save({ session: session2 });
      await session2.commitTransaction();
      session2.endSession();
      return NextResponse.json({ created: createdItems }, { status: 201 });
    }
    // Batch bonuses
    if (Array.isArray(bonusIds) && bonusIds.length > 0) {
      // Fetch all bonuses
      const bonuses = await Bonus.find({ _id: { $in: bonusIds } });
      if (!bonuses.length) {
        await session2.abortTransaction();
        session2.endSession();
        return NextResponse.json({ error: 'No bonuses found' }, { status: 400 });
      }
      // Group by amount
      const groups: Record<string, any[]> = {};
      for (const b of bonuses) {
        if (!groups[b.amount as any]) groups[b.amount as any] = [];
        groups[b.amount as any].push(b);
      }
      // Find the invoice
      const invoice = await Invoice.findById(invoiceId);
      if (!invoice) {
        await session2.abortTransaction();
        session2.endSession();
        return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
      }
      // Find InvoiceItemType with code 'bonuses'
      const type = await InvoiceItemType.findOne({ code: 'bonuses' });
      if (!type) {
        await session2.abortTransaction();
        session2.endSession();
        return NextResponse.json({ error: 'InvoiceItemType for bonuses not found' }, { status: 400 });
      }
      const createdItems = [];
      for (const [amount, bons] of Object.entries(groups)) {
        const bonsArr = bons as any[];
        const firstBonus = bonsArr[0];
        // Use provided date if present, else set to today
        let itemDate = date ? new Date(date).toISOString() : new Date().toISOString();

        // Generate title based on quantity and date range
        let title: string;
        if (bonsArr.length > 1) {
          // For grouped bonuses, include date range
          const bonusDates = bonsArr.map((b: any) => b.date).filter(Boolean);
          if (bonusDates.length > 0) {
            // Sort dates from oldest to newest
            const sortedDates = bonusDates.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
            const startDate = new Date(sortedDates[0]);
            const endDate = new Date(sortedDates[sortedDates.length - 1]);

            // Helper function to format date as DD/MM/YYYY
            const formatDate = (date: Date) => {
              const day = date.getDate().toString().padStart(2, '0');
              const month = (date.getMonth() + 1).toString().padStart(2, '0');
              const year = date.getFullYear();
              return `${day}/${month}/${year}`;
            };

            title = `Bonus collecté du ${formatDate(startDate)} au ${formatDate(endDate)}`;
          } else {
            title = `Bonus (x${bonsArr.length})`;
          }
        } else {
          // For single bonus, include the date
          const bonusDate = firstBonus.date ? new Date(firstBonus.date) : new Date();
          const formatDate = (date: Date) => {
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
          };
          title = `Bonus collecté le ${formatDate(bonusDate)}`;
        }

        // Automatically determine taxable status based on invoice's tax type
        const isTaxable = await getItemTaxableStatusForInvoice(invoiceId);

        const invoiceItem = await InvoiceItem.create([{
          title,
          quantity: bonsArr.length,
          date: itemDate,
          unitPrice: Number(amount),
          taxable: isTaxable,
          invoiceitemtypeId: type._id,
        }], { session: session2 });
        invoice.manualItems.push(invoiceItem[0]._id);
        createdItems.push(invoiceItem[0]);
      }
      await invoice.save({ session: session2 });
      await session2.commitTransaction();
      session2.endSession();
      return NextResponse.json({ created: createdItems }, { status: 201 });
    }
    // Fallback: single item logic (legacy)
    // Validate required fields
    if (!itemData.title || !itemData.quantity || !itemData.date || itemData.unitPrice === undefined || !itemData.invoiceitemtypeId) {
      // Create more detailed error message
      const missingFields = [];
      if (!itemData.title) missingFields.push('title');
      if (!itemData.quantity) missingFields.push('quantity');
      if (!itemData.date) missingFields.push('date');
      if (itemData.unitPrice === undefined) missingFields.push('unitPrice');
      if (!itemData.invoiceitemtypeId) missingFields.push('invoiceitemtypeId');

      return NextResponse.json({
        error: `Missing required fields: ${missingFields.join(', ')}`,
        receivedData: { ...itemData, invoiceId }
      }, { status: 400 });
    }

    // Automatically determine taxable status based on invoice's tax type
    const isTaxable = await getItemTaxableStatusForInvoice(invoiceId);

    // Create the invoice item with auto-determined taxable status
    const itemDataWithTaxable = {
      ...itemData,
      taxable: isTaxable
    };
    const invoiceItem = await InvoiceItem.create([itemDataWithTaxable], { session: session2 });

    // Find the invoice and add the item reference to it
    const invoice = await Invoice.findById(invoiceId);
    if (!invoice) {
      await session2.abortTransaction();
      session2.endSession();
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Add the invoice item to the invoice
    invoice.manualItems.push(invoiceItem[0]._id);

    // No need to recalculate totals as they will be calculated on demand
    await invoice.save({ session: session2 });

    await session2.commitTransaction();
    session2.endSession();

    // Log audit event for the invoice item
    await InvoiceAuditLogger.logInvoiceItemCreated(
      request,
      invoiceItem[0]._id.toString(),
      invoiceItem[0].title,
      invoice._id.toString(),
      invoice.invoiceNumber,
      session
    );

    // Also log an audit event directly for the invoice
    const { logAuditEvent } = await import('@/lib/utils/audit-utils');
    await logAuditEvent(request, {
      action: 'INVOICE_UPDATED',
      entityType: 'Invoice',
      entityId: invoice._id.toString(),
      entityName: invoice.invoiceNumber,
      description: `Invoice item "${invoiceItem[0].title}" added to invoice ${invoice.invoiceNumber}`,
      metadata: {
        itemId: invoiceItem[0]._id.toString(),
        itemTitle: invoiceItem[0].title,
        itemQuantity: invoiceItem[0].quantity,
        itemUnitPrice: invoiceItem[0].unitPrice
      },
      relatedEntities: [{
        type: 'InvoiceItem',
        id: invoiceItem[0]._id.toString(),
        name: invoiceItem[0].title
      }]
    }, session);

    return NextResponse.json(invoiceItem[0], { status: 201 });
  } catch (error) {
    await session2.abortTransaction();
    session2.endSession();
    console.error('Create invoice item error:', error);
    return NextResponse.json({ error: 'Failed to create invoice item' }, { status: 500 });
  }
}

// TODO: Add PATCH/DELETE/GET by id routes for individual invoice items