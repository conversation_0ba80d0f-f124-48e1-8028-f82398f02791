import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import InvoiceItem from '@/models/InvoiceItem';
import Invoice from '@/models/Invoice';
import { getToken } from 'next-auth/jwt';
import * as RoleUtils from '@/lib/utils/role-utils';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';
import mongoose from 'mongoose';
import * as BillingUtils from '@/lib/utils/billing-utils';
import { canUserAccessBilling, canUserManageBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions, getUserRoles } from '@/app/api/utils/server-permission-utils';
import { InvoiceAuditLogger } from '@/lib/utils/audit-utils';
import { getItemTaxableStatusForInvoice } from '../../../utils/invoice-item-utils';
// GET: Fetch a single invoice item (admin only)
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session || !canUserAccessBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const invoiceItem = await InvoiceItem.findById(params.id);
    if (!invoiceItem) {
      return NextResponse.json({ error: 'Invoice item not found' }, { status: 404 });
    }
    return NextResponse.json(invoiceItem);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch invoice item' }, { status: 500 });
  }
}

// PATCH: Update an invoice item (admin only)
export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(!session){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(!session.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if(!session.user.roles){
    session.user.roles = await getUserRoles(session);
  }
  if (!canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'forbidden' }, { status: 403 });
  }
  const p=await params;
  const session2 = await mongoose.startSession();
  session2.startTransaction();

  try {
    const body = await request.json();

    // Find the invoice item
    const invoiceItem = await InvoiceItem.findById(p.id);
    if (!invoiceItem) {
      await session2.abortTransaction();
      session2.endSession();
      return NextResponse.json({ error: 'Invoice item not found' }, { status: 404 });
    }

    // Store original data for audit logging
    const originalData = invoiceItem.toObject();

    // Find the invoice this item belongs to
    const invoice = await Invoice.findOne({ manualItems: p.id });
    if (!invoice) {
      await session2.abortTransaction();
      session2.endSession();
      return NextResponse.json({ error: 'Invoice not found for this item' }, { status: 404 });
    }

    // Automatically determine taxable status based on invoice's tax type
    const isTaxable = await getItemTaxableStatusForInvoice(invoice._id.toString());

    // Update allowed fields (taxable is auto-determined, not from frontend)
    const updateFields: Record<string, any> = {};
    if (body.title !== undefined) updateFields.title = body.title;
    if (body.quantity !== undefined) updateFields.quantity = body.quantity;
    if (body.date !== undefined) updateFields.date = new Date(body.date);
    if (body.unitPrice !== undefined) updateFields.unitPrice = body.unitPrice;
    updateFields.taxable = isTaxable; // Always set based on invoice tax type
    if (body.invoiceitemtypeId !== undefined) updateFields.invoiceitemtypeId = body.invoiceitemtypeId;

    // Apply updates
    await InvoiceItem.updateOne({ _id: p.id }, { $set: updateFields }, { session: session2 });

    // Get the updated invoice item
    const updatedItem = await InvoiceItem.findById(p.id, null, { session: session2 });

    await session2.commitTransaction();
    session2.endSession();

    // Log audit event
    if (updatedItem && invoice) {
      await InvoiceAuditLogger.logInvoiceItemUpdated(
        request,
        updatedItem._id.toString(),
        updatedItem.title,
        originalData,
        updatedItem.toObject(),
        invoice.invoiceNumber,
        session
      );

      // Also log directly to the invoice
      const { logAuditEvent } = await import('@/lib/utils/audit-utils');
      await logAuditEvent(request, {
        action: 'INVOICE_UPDATED',
        entityType: 'Invoice',
        entityId: invoice._id.toString(),
        entityName: invoice.invoiceNumber,
        description: `Invoice item "${updatedItem.title}" updated in invoice ${invoice.invoiceNumber}`,
        metadata: {
          itemId: updatedItem._id.toString(),
          itemTitle: updatedItem.title
        },
        relatedEntities: [{
          type: 'InvoiceItem',
          id: updatedItem._id.toString(),
          name: updatedItem.title
        }]
      }, session);
    }

    return NextResponse.json(updatedItem);
  } catch (error) {
    await session2.abortTransaction();
    session2.endSession();
    console.error('Update invoice item error:', error);
    return NextResponse.json({ error: 'Failed to update invoice item' }, { status: 500 });
  }
}

// DELETE: Delete an invoice item (admin only)
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(!session){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(!session.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if(!session.user.roles){
    session.user.roles = await getUserRoles(session);
  }

  if (!canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'forbidden' }, { status: 403 });
  }
  const p=await params;
  const session2 = await mongoose.startSession();
  session2.startTransaction();

  try {
    // Find the invoice item
    const invoiceItem = await InvoiceItem.findById(p.id);
    if (!invoiceItem) {
      await session2.abortTransaction();
      session2.endSession();
      return NextResponse.json({ error: 'Invoice item not found' }, { status: 404 });
    }

    // Find invoices that contain this item
    const invoices = await Invoice.find({ manualItems: p.id });
    const invoiceNumber = invoices.length > 0 ? invoices[0].invoiceNumber : 'Unknown';

    // Update each invoice
    for (const invoice of invoices) {
      // Remove the item reference
      invoice.manualItems = invoice.manualItems.filter((id:any) => !id.equals(p.id));
      // Save invoice
      await invoice.save({ session: session2 });
    }

    // Delete the invoice item
    await InvoiceItem.deleteOne({ _id: p.id }, { session: session2 });

    await session2.commitTransaction();
    session2.endSession();

    // Log audit event
    await InvoiceAuditLogger.logInvoiceItemDeleted(
      request,
      invoiceItem._id.toString(),
      invoiceItem.title,
      invoiceNumber,
      session
    );

    // Also log directly to the invoice if we have one
    if (invoices.length > 0) {
      const { logAuditEvent } = await import('@/lib/utils/audit-utils');
      await logAuditEvent(request, {
        action: 'INVOICE_UPDATED',
        entityType: 'Invoice',
        entityId: invoices[0]._id.toString(),
        entityName: invoiceNumber,
        description: `Invoice item "${invoiceItem.title}" deleted from invoice ${invoiceNumber}`,
        metadata: {
          itemId: invoiceItem._id.toString(),
          itemTitle: invoiceItem.title
        },
        relatedEntities: [{
          type: 'InvoiceItem',
          id: invoiceItem._id.toString(),
          name: invoiceItem.title
        }]
      }, session);
    }

    return NextResponse.json({ message: 'Invoice item deleted successfully' });
  } catch (error) {
    await session2.abortTransaction();
    session2.endSession();
    console.error('Delete invoice item error:', error);
    return NextResponse.json({ error: 'Failed to delete invoice item' }, { status: 500 });
  }
}