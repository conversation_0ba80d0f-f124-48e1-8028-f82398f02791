import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import InvoiceItem from '@/models/InvoiceItem';
import { getToken } from 'next-auth/jwt';
import * as RoleUtils from '@/lib/utils/role-utils';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';
import mongoose from 'mongoose';
import { canUserAccessBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
// GET: Fetch all invoice items by invoiceId (admin only)
export async function GET(request: NextRequest, { params }: { params: { invoiceId: string } }) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  } 
  if (!session || !canUserAccessBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const p=await params;
  // Validate that invoiceId is a valid ObjectId
  if (!mongoose.Types.ObjectId.isValid(p.invoiceId)) {
    return NextResponse.json({ error: 'Invalid invoice ID format' }, { status: 400 });
  }
  
  try {
    const p=await params;
    const invoice = await Invoice.findById(p.invoiceId);
    
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }
    
    // If invoice exists but has no items yet, return an empty array
    if (!invoice.manualItems || invoice.manualItems.length === 0) {
      return NextResponse.json([]);
    }
    
    // Fetch the invoice items separately to avoid population issues
    const invoiceItems = await InvoiceItem.find({
      _id: { $in: invoice.manualItems }
    });
    
    // Transform the items to ensure they have tps and tvq fields as numbers
    const transformedItems = invoiceItems.map(item => {
      const plainItem = item.toObject();
      
      // Ensure basic fields have default values to prevent undefined errors
      const safeItem = {
        ...plainItem,
        quantity: plainItem.quantity ?? 0,
        unitPrice: plainItem.unitPrice ?? 0,
        date: plainItem.date ?? new Date(),
        title: plainItem.title || 'Unnamed Item'
      };
      
      // Handle case where tps and tvq exist (new format)
      if ('tps' in safeItem && 'tvq' in safeItem) {
        return {
          ...safeItem,
          tps: safeItem.tps ?? 0,
          tvq: safeItem.tvq ?? 0
        };
      }
      
      // Handle case where we have the old boolean fields but need to convert to numbers
      if ('tpsApplicable' in safeItem || 'tvqApplicable' in safeItem) {
        const tps = safeItem.tpsApplicable ? safeItem.quantity * safeItem.unitPrice * 0.05 : 0;
        const tvq = safeItem.tvqApplicable ? safeItem.quantity * safeItem.unitPrice * 0.09975 : 0;
        
        // Update the item in the database to use the new fields
        InvoiceItem.updateOne(
          { _id: safeItem._id },
          { 
            $set: { tps, tvq },
            $unset: { tpsApplicable: "", tvqApplicable: "" }
          }
        ).catch(err => console.error(`Error updating invoice item ${safeItem._id}:`, err));
        
        return {
          ...safeItem,
          tps,
          tvq,
          tpsApplicable: undefined,
          tvqApplicable: undefined
        };
      }
      
      // Default case: no tax fields at all
      return {
        ...safeItem,
        tps: 0,
        tvq: 0
      };
    });
    
    return NextResponse.json(transformedItems);
  } catch (error) {
    console.error('Error fetching invoice items:', error);
    return NextResponse.json({ error: 'Failed to fetch invoice items by invoiceId' }, { status: 500 });
  }
} 