import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserManageBilling } from '@/lib/utils/permissions-utils';
import { InvoiceAutomationService } from '@/lib/services/invoice-automation';
import { CronScheduler } from '@/lib/services/cron-scheduler';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { action } = await request.json();

    if (!action) {
      return NextResponse.json({ error: 'Action parameter is required' }, { status: 400 });
    }

    let result;
    let message;

    switch (action) {
      case 'thursday':
        result = await InvoiceAutomationService.thursdayTransition();
        message = `Thursday automation completed: ${result.updatedCount}/${result.totalFound} invoices updated`;
        break;
        
      case 'friday':
        result = await InvoiceAutomationService.fridayTransition();
        message = `Friday automation completed: ${result.updatedCount}/${result.totalFound} invoices updated`;
        break;
        
      case 'late':
        result = await InvoiceAutomationService.lateInvoiceTransition();
        message = result.disabled
          ? `Late invoice check is disabled - no invoices processed`
          : `Late invoice check completed: ${result.updatedCount}/${result.totalFound} invoices updated`;
        break;

      case 'archive':
        result = await InvoiceAutomationService.fridayArchiveTransition();
        message = `Archive automation completed: ${result.archivedCount}/${result.totalFound} invoices archived`;
        break;

      default:
        return NextResponse.json({
          error: 'Invalid action. Valid actions are: thursday, friday, late, archive'
        }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message,
      result: {
        action,
        updatedCount: result.updatedCount || result.archivedCount || 0,
        archivedCount: result.archivedCount,
        totalFound: result.totalFound,
        disabled: result.disabled || false,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('Manual automation trigger failed:', error);
    return NextResponse.json({ 
      error: 'Automation trigger failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const jobStatus = CronScheduler.getJobStatus();
    const nextRunTimes = CronScheduler.getNextRunTimes();

    return NextResponse.json({
      success: true,
      status: jobStatus,
      nextRunTimes,
      serverTime: new Date().toISOString(),
      timezone: 'America/Toronto'
    });
    
  } catch (error) {
    console.error('Failed to get automation status:', error);
    return NextResponse.json({ 
      error: 'Failed to get automation status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
