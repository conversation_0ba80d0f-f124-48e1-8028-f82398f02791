import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserManageBilling } from '@/lib/utils/permissions-utils';
import { CronScheduler } from '@/lib/services/cron-scheduler';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { action } = await request.json();

    switch (action) {
      case 'start':
        CronScheduler.startInvoiceAutomation();
        return NextResponse.json({ 
          success: true, 
          message: 'Invoice automation started successfully',
          status: CronScheduler.getJobStatus()
        });
        
      case 'stop':
        CronScheduler.stopInvoiceAutomation();
        return NextResponse.json({ 
          success: true, 
          message: 'Invoice automation stopped successfully',
          status: CronScheduler.getJobStatus()
        });
        
      case 'restart':
        CronScheduler.restartInvoiceAutomation();
        return NextResponse.json({ 
          success: true, 
          message: 'Invoice automation restarted successfully',
          status: CronScheduler.getJobStatus()
        });
        
      default:
        return NextResponse.json({ 
          error: 'Invalid action. Valid actions are: start, stop, restart' 
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('Automation initialization failed:', error);
    return NextResponse.json({ 
      error: 'Automation initialization failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const status = CronScheduler.getJobStatus();
    const nextRunTimes = CronScheduler.getNextRunTimes();

    return NextResponse.json({
      success: true,
      automation: {
        status,
        nextRunTimes,
        environment: process.env.NODE_ENV,
        automationEnabled: process.env.ENABLE_AUTOMATION === 'true' || process.env.NODE_ENV === 'production'
      },
      serverTime: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Failed to get automation status:', error);
    return NextResponse.json({ 
      error: 'Failed to get automation status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
