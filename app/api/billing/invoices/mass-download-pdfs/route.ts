import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import InvoiceItem from '@/models/InvoiceItem';
import User from '@/models/User';
import TaxType from '@/models/TaxType';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { generateInvoicePDFDataURL } from '@/lib/utils/html-pdf-utils';
import { canUserAccessBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import * as BillingUtils from '@/lib/utils/billing-utils';
import JSZip from 'jszip';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    // Check authorization
    const session = await getServerSession(authOptions);
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    if (!session || !canUserAccessBilling(session.user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { invoiceIds } = await request.json();

    if (!Array.isArray(invoiceIds) || invoiceIds.length === 0) {
      return NextResponse.json({ error: 'No invoice IDs provided' }, { status: 400 });
    }

    // Fetch all invoices with their related data
    const invoices = await Invoice.find({ _id: { $in: invoiceIds } })
      .populate('userId')
      .populate('taxTypeId')
      .lean();

    if (!invoices || invoices.length === 0) {
      console.log('No invoices found for IDs:', invoiceIds);
      return NextResponse.json({ error: 'No invoices found' }, { status: 404 });
    }

    console.log(`Processing ${invoices.length} invoices for PDF generation:`, invoices.map(inv => inv.invoiceNumber));

    // Company info
     const companyInfo = {
      name: "Alimentation Mon Quartier",
      address: "200 rue Principale, local 8",
      city: "St-Sauveur",
      province: "Québec",
      postalCode: "J0R 1R0",
      phone: "**************",
      email: "<EMAIL>",
    };

    // Create ZIP file
    const zip = new JSZip();
    const pdfPromises = [];

    console.log('JSZip initialized successfully');

    for (const invoice of invoices) {
      const pdfPromise = (async () => {
        try {
          // Fetch invoice items using manualItems array
          let items: any[] = [];

          if (invoice.manualItems && invoice.manualItems.length > 0) {
            items = await InvoiceItem.find({ _id: { $in: invoice.manualItems } }).lean();
          }

          if (!items || items.length === 0) {
            console.warn(`No items found for invoice ${invoice.invoiceNumber}. ManualItems: ${invoice.manualItems?.length || 0}`);
            return { success: false, invoiceNumber: invoice.invoiceNumber, error: 'No invoice items found' };
          }

          // Calculate totals
          const subtotal = BillingUtils.calculateSubtotal(items as any);
          const taxType = invoice.taxTypeId;

          if (!taxType) {
            console.warn(`No tax type found for invoice ${invoice.invoiceNumber}`);
            return { success: false, invoiceNumber: invoice.invoiceNumber, error: 'No tax type found' };
          }

          const total = BillingUtils.calculateGrandTotalWithTaxType(items as any, taxType);

          // Check if invoice is signed and get signature data
          let signatureData = undefined;
          let actualSignedAt = undefined;
          let actualSignedFromIP = undefined;
          if (invoice.signedAt && invoice.signatureData) {
            signatureData = invoice.signatureData;
            actualSignedAt = invoice.signedAt;
            actualSignedFromIP = invoice.signedFromIP;
          } else {
            // Check signing token for signature data
            const InvoiceSigningToken = (await import('@/models/InvoiceSigningToken')).default;
            const signingToken = await InvoiceSigningToken.findOne({
              invoiceId: invoice._id,
              signedAt: { $exists: true }
            });
            if (signingToken && signingToken.signatureData) {
              signatureData = signingToken.signatureData;
              actualSignedAt = signingToken.signedAt;
              actualSignedFromIP = signingToken.signedFromIP;
            }
          }

          // Prepare invoice details for HTML PDF generation
          const user = invoice.userId;

          if (!user) {
            console.warn(`No user found for invoice ${invoice.invoiceNumber}`);
            return { success: false, invoiceNumber: invoice.invoiceNumber, error: 'No user found' };
          }

          const invoiceDetails = {
            invoiceNumber: invoice.invoiceNumber,
            title: invoice.title,
            date: invoice.date,
            dueDate: invoice.dueDate,
            status: invoice.status,
            total: total,
            subtotal: subtotal,
            signedAt: actualSignedAt, // Include the actual signed date
            signedFromIP: actualSignedFromIP, // Include the IP address
            issuerInfo: {
              name: (user as any).companyName || user.name || 'Client',
              address: (user as any).address,
              city: (user as any).city,
              province: (user as any).province,
              postalCode: (user as any).postalCode,
              phone: user.phone
            },
            // Include tax registration numbers from invoice
            tpsRegistrationNumber: invoice.tpsRegistrationNumber,
            qstRegistrationNumber: invoice.qstRegistrationNumber,
          };

          // Format tax type for PDF generation
          const taxTypeFormatted = {
            code: (taxType as any).code,
            names: (taxType as any).names,
            percentages: (taxType as any).percentages,
          };

          // Generate PDF using HTML template with signature if available
          const pdfDataUrl = await generateInvoicePDFDataURL(
            items as any,
            invoiceDetails,
            companyInfo,
            taxTypeFormatted,
            false, // swapIssuerAndClient
            signatureData // Include signature data if available
          );

          // Extract base64 content and convert to buffer
          const base64Content = pdfDataUrl.split(',')[1];
          const pdfBuffer = Buffer.from(base64Content, 'base64');

          // Add to ZIP with sanitized filename (include "Signed" if signature is present)
          const baseFilename = invoice.invoiceNumber.replace(/[^a-z0-9_-]/gi, '_');
          const filename = signatureData ? `${baseFilename}_Signed.pdf` : `${baseFilename}.pdf`;
          zip.file(filename, pdfBuffer);

          return { success: true, filename, invoiceNumber: invoice.invoiceNumber };
        } catch (error) {
          console.error(`Error generating PDF for invoice ${invoice.invoiceNumber}:`, error);
          return {
            success: false,
            invoiceNumber: invoice.invoiceNumber,
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined
          };
        }
      })();

      pdfPromises.push(pdfPromise);
    }

    // Wait for all PDFs to be generated
    const results = await Promise.all(pdfPromises);

    // Filter out failed generations
    const successfulResults = results.filter(result => result && result.success);
    const failedResults = results.filter(result => result && !result.success);

    if (successfulResults.length === 0) {
      console.error('No PDFs were successfully generated:', failedResults);
      return NextResponse.json({
        error: 'Failed to generate any PDFs',
        details: failedResults,
        invoiceCount: invoices.length,
        failedCount: failedResults.length
      }, { status: 500 });
    }

    // If only one PDF, return it directly without ZIP
    if (successfulResults.length === 1) {
      const singleResult = successfulResults[0];
      const invoice = invoices.find(inv => inv.invoiceNumber === singleResult.invoiceNumber);

      // Get the PDF buffer from the ZIP (we still used ZIP to store it temporarily)
      const pdfFile = zip.file(singleResult.filename);
      if (pdfFile) {
        const pdfBuffer = await pdfFile.async('nodebuffer');

        const response = new NextResponse(pdfBuffer);
        response.headers.set('Content-Type', 'application/pdf');
        response.headers.set('Content-Disposition', `attachment; filename="${singleResult.filename}"`);

        return response;
      }
    }

    // Multiple PDFs: Generate ZIP file
    console.log(`Generating ZIP file with ${successfulResults.length} PDFs`);
    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });
    console.log(`ZIP file generated, size: ${zipBuffer.length} bytes`);

    // Return ZIP file as response
    const response = new NextResponse(zipBuffer);
    response.headers.set('Content-Type', 'application/zip');
    response.headers.set('Content-Disposition', `attachment; filename="invoices_${new Date().toISOString().split('T')[0]}.zip"`);

    return response;

  } catch (error) {
    console.error('Error in mass PDF download:', error);
    return NextResponse.json({
      error: 'Failed to generate PDF archive',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
