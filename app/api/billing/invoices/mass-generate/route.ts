import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import InvoiceItem from '@/models/InvoiceItem';
import Commission from '@/models/Commission';
import Bonus from '@/models/Bonus';
import User from '@/models/User';
import EmailTemplate from '@/models/EmailTemplate';
import InvoiceSigningToken from '@/models/InvoiceSigningToken';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { updateProgress, clearProgress } from './progress/route';
import { generateInvoicePDFDataURL } from '@/lib/utils/html-pdf-utils';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import * as BillingUtils from '@/lib/utils/billing-utils';
import { canUserManageBilling } from '@/lib/utils/permissions-utils';
import { randomBytes } from 'crypto';
import { generateInvoiceSigningUrl } from '@/lib/utils/invoice-signing-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import TaxType from '@/models/TaxType';
import InvoiceItemType from '@/models/InvoiceItemType';
import Reservation from '@/models/Reservation';
import CommissionType from '@/models/CommissionType';
import { determineTaxTypeForInvoice } from '../../../utils/tax-validation';
import { logAuditEvent } from '@/lib/utils/audit-utils';
import { getInvoiceEmailSubject } from '@/lib/utils/email-templates';

// Get logo URL for email embedding
function getLogoUrl(): string {
  try {
    // Use environment domain or fallback to localhost
    const domain = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const logoUrl = `${domain}/amq-logo.png`;
    console.log('Logo URL:', logoUrl);
    return logoUrl;
  } catch (error) {
    console.error('Error generating logo URL:', error);
    return '';
  }
}

interface UserCommissionData {
  userId: string;
  userName: string;
  userEmail: string;
  totalAmount: number;
  totalBonus: number;
  commissionCount: number;
  latestCommissionDate: Date;
  commissions: any[]; // Store actual commission objects
  bonuses: any[]; // Store actual bonus objects
}

// Helper function to wait for a specified time
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Transaction retry function with exponential backoff
const executeWithRetry = async <T>(
  operation: (session: mongoose.ClientSession) => Promise<T>,
  maxRetries: number = 5
): Promise<T> => {
  let lastError: any;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    const dbSession = await mongoose.startSession();

    try {
      dbSession.startTransaction();
      const result = await operation(dbSession);
      await dbSession.commitTransaction();
      return result;
    } catch (error: any) {
      await dbSession.abortTransaction();

      // Check if this is a WriteConflict (code 112) or transaction error that can be retried
      const isRetryableError = error.code === 112 ||
        error.errorLabels?.includes('TransientTransactionError');

      if (!isRetryableError || attempt === maxRetries - 1) {
        // Not retryable or last attempt
        lastError = error;
        throw error;
      }

      // Exponential backoff: 200ms, 400ms, 800ms, 1600ms...
      const backoffTime = Math.pow(2, attempt) * 200;
      console.log(`Transaction retry ${attempt + 1}/${maxRetries} after ${backoffTime}ms`);
      await sleep(backoffTime);
    } finally {
      dbSession.endSession();
    }
  }

  // Should never reach here (we either return or throw earlier)
  throw lastError || new Error('Unknown error in transaction retry');
};

// Type guard for items (move to top-level scope)
function isInvoiceItem(obj: any): obj is { quantity: number; unitPrice: number } {
  return obj && typeof obj === 'object' && typeof obj.quantity === 'number' && typeof obj.unitPrice === 'number';
}

// Utility to group items by unitPrice (amount) - same logic as import commissions
interface GroupedByUnitPrice<T extends { amount: number; _id: string; date?: string }> {
  unitPrice: number;
  quantity: number;
  itemIds: string[];
  dateRange?: { start: string; end: string };
  items: T[];
}

function groupByUnitPrice<T extends { amount: number; _id: string; date?: string }>(
  items: T[]
): GroupedByUnitPrice<T>[] {
  const groups: Record<number, GroupedByUnitPrice<T>> = {};
  for (const item of items) {
    if (!groups[item.amount]) {
      groups[item.amount] = {
        unitPrice: item.amount,
        quantity: 0,
        itemIds: [],
        items: [],
      };
    }
    groups[item.amount].quantity++;
    groups[item.amount].itemIds.push(item._id);
    groups[item.amount].items.push(item);
  }
  // Optionally, compute date range for each group
  Object.values(groups).forEach((group) => {
    const dates = group.items.map((i) => i.date).filter(Boolean) as string[];
    if (dates.length) {
      const sorted = dates.sort();
      group.dateRange = { start: sorted[0], end: sorted[sorted.length - 1] };
    }
  });
  return Object.values(groups);
}

// Helper function to generate invoice item title based on invoice item type code
async function generateInvoiceItemTitle(
  invoiceItemType: any,
  commission?: any,
  quantity: number = 1,
  groupItems?: any[]
): Promise<string> {
  const code = invoiceItemType.code;

  // Helper function to format date as DD/MM/YYYY
  const formatDate = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  switch (code) {
    case 'commissions':
      // For grouped coupons, include date range
      if (quantity > 1 && groupItems && groupItems.length > 0) {
        const commissionDates = groupItems.map((c: any) => c.createdAt || c.updatedAt).filter(Boolean);
        if (commissionDates.length > 0) {
          return 'COUPONS';
        }
      }
      return 'COUPONS';

    case 'bonuses':
      // For grouped bonuses, include date range
      if (quantity > 1 && groupItems && groupItems.length > 0) {
        const bonusDates = groupItems.map((b: any) => b.date).filter(Boolean);
        if (bonusDates.length > 0) {
          // Sort dates from oldest to newest
          const sortedDates = bonusDates.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
          const startDate = new Date(sortedDates[0]);
          const endDate = new Date(sortedDates[sortedDates.length - 1]);
          return `Bonus collecté du ${formatDate(startDate)} au ${formatDate(endDate)}`;
        }
      }
      // For single bonus, include the date if available
      if (quantity === 1 && groupItems && groupItems.length > 0 && groupItems[0].date) {
        const bonusDate = new Date(groupItems[0].date);
        return `Bonus collecté le ${formatDate(bonusDate)}`;
      }
      return invoiceItemType.name + (quantity > 1 ? ` (x${quantity})` : '');

    case 'commission-vente':
      // For commission-vente, get customer name from reservation and add date
      if (commission?.reservationId) {
        try {
          const reservation = await Reservation.findById(commission.reservationId).lean() as any;
          if (reservation?.customerInfo) {
            const client1Name = reservation.customerInfo.client1Name || '';
            const client2Name = reservation.customerInfo.client2Name || '';
            const customerName = client2Name ? `${client1Name} ${client2Name}` : client1Name;
            const commissionDate = commission.createdAt ? new Date(commission.createdAt) : new Date();
            return `Commission sur la vente de ${customerName} le ${formatDate(commissionDate)}`;
          }
        } catch (error) {
          console.error('Error fetching reservation for commission-vente title:', error);
        }
      }
      // Fallback to default name if reservation not found
      return invoiceItemType.name;

    default:
      // For all other types, use the invoice item type name
      return invoiceItemType.name + (quantity > 1 ? ` (x${quantity})` : '');
  }
}

export async function POST(request: NextRequest) {
  await dbConnect();

  // Check user authorization
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session?.user || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { startDate, endDate, sendEmails: requestSendEmails, branchId, selectedUserIds } = await request.json();

  // Email sending functionality temporarily disabled
  const sendEmails = false;

  if (!startDate || !endDate) {
    return NextResponse.json(
      { error: 'startDate and endDate parameters are required' },
      { status: 400 }
    );
  }

  // Get the user ID from the session
  const user = session?.user as any;
  const adminUserId = user?.id || user?._id?.toString();

  if (!adminUserId) {
    return NextResponse.json({ error: 'User ID not found in session' }, { status: 500 });
  }

  // Log the start of mass invoice generation
  await logAuditEvent(request, {
    action: 'MASS_INVOICE_GENERATED',
    entityType: 'System',
    description: `Mass invoice generation started for period ${startDate} to ${endDate}${branchId && branchId !== 'all' ? ` (Branch: ${branchId})` : ''}`,
    metadata: {
      startDate,
      endDate,
      branchId: branchId || 'all',
      sendEmails: false, // Email sending disabled
      emailsRequested: !!requestSendEmails, // Track what was requested
      initiatedBy: adminUserId
    }
  }, session);

  // Reset progress tracking
  clearProgress(adminUserId);
  updateProgress(adminUserId, 0, 'preparing', false);

  // Parse dates and set to beginning and end of day
  // Use the same date parsing logic as other commission APIs to ensure consistency
  const parsedStartDate = new Date(startDate.replace(/-/g, '/'));
  parsedStartDate.setHours(0, 0, 0, 0);

  const parsedEndDate = new Date(endDate.replace(/-/g, '/'));
  parsedEndDate.setHours(23, 59, 59, 999);

  try {
    // Update progress
    updateProgress(adminUserId, 10, 'fetchingCommissions', false);

    // Build the base query filter for commissions
    const commissionsFilter = {
      createdAt: {
        $gte: parsedStartDate,
        $lte: parsedEndDate
      },
      isApproved: true
    };

    // Fetch commissions for the date range, with optional user and branch filtering
    let commissions;
    if (selectedUserIds && selectedUserIds.length > 0) {
      // If specific users are selected, filter by those user IDs
      const selectedUserObjectIds = selectedUserIds.map((id: string) => new mongoose.Types.ObjectId(id));
      commissions = await Commission.aggregate([
        { $match: { ...commissionsFilter, userId: { $in: selectedUserObjectIds } } },
        {
          $lookup: {
            from: "reservations",
            localField: "reservationId",
            foreignField: "_id",
            as: "reservation"
          }
        },
        {
          $match: {
            "reservation": { $ne: [] }, // Reservation must exist
            "reservation.isDeleted": { $ne: true } // Reservation must not be deleted
          }
        },
        {
          $lookup: {
            from: "commissiontypes",
            localField: "commissionTypeId",
            foreignField: "_id",
            as: "commissionTypeDetails"
          }
        },
        {
          $unwind: {
            path: "$commissionTypeDetails",
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $project: {
            _id: 1,
            userId: 1,
            reservationId: 1,
            amount: 1,
            approvedBy: 1,
            isApproved: 1,
            approvedAt: 1,
            createdAt: 1,
            updatedAt: 1,
            commissionTypeId: {
              _id: "$commissionTypeDetails._id",
              name: "$commissionTypeDetails.name",
              code: "$commissionTypeDetails.code"
            }
          }
        }
      ]);
    } else if (branchId && branchId !== 'all') {
      // If branch filtering is needed but no specific users selected, use aggregation to filter by user's branchIds
      commissions = await Commission.aggregate([
        { $match: commissionsFilter },
        {
          $lookup: {
            from: "reservations",
            localField: "reservationId",
            foreignField: "_id",
            as: "reservation"
          }
        },
        {
          $match: {
            "reservation": { $ne: [] }, // Reservation must exist
            "reservation.isDeleted": { $ne: true } // Reservation must not be deleted
          }
        },
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "userDetails"
          }
        },
        {
          $unwind: {
            path: "$userDetails",
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $match: {
            "userDetails.branchIds": { $in: [new mongoose.Types.ObjectId(branchId)] }
          }
        },
        {
          $lookup: {
            from: "commissiontypes",
            localField: "commissionTypeId",
            foreignField: "_id",
            as: "commissionTypeDetails"
          }
        },
        {
          $unwind: {
            path: "$commissionTypeDetails",
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $project: {
            _id: 1,
            userId: 1,
            reservationId: 1,
            amount: 1,
            approvedBy: 1,
            isApproved: 1,
            approvedAt: 1,
            createdAt: 1,
            updatedAt: 1,
            commissionTypeId: {
              _id: "$commissionTypeDetails._id",
              name: "$commissionTypeDetails.name",
              code: "$commissionTypeDetails.code"
            }
          }
        }
      ]);
    } else {
      // No branch filtering - fetch all commissions with reservation validation
      commissions = await Commission.aggregate([
        { $match: commissionsFilter },
        {
          $lookup: {
            from: "reservations",
            localField: "reservationId",
            foreignField: "_id",
            as: "reservation"
          }
        },
        {
          $match: {
            "reservation": { $ne: [] }, // Reservation must exist
            "reservation.isDeleted": { $ne: true } // Reservation must not be deleted
          }
        },
        {
          $lookup: {
            from: "commissiontypes",
            localField: "commissionTypeId",
            foreignField: "_id",
            as: "commissionTypeDetails"
          }
        },
        {
          $unwind: {
            path: "$commissionTypeDetails",
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $project: {
            _id: 1,
            userId: 1,
            reservationId: 1,
            amount: 1,
            approvedBy: 1,
            isApproved: 1,
            approvedAt: 1,
            createdAt: 1,
            updatedAt: 1,
            commissionTypeId: {
              _id: "$commissionTypeDetails._id",
              name: "$commissionTypeDetails.name",
              code: "$commissionTypeDetails.code"
            }
          }
        }
      ]);
    }

    // Update progress
    updateProgress(adminUserId, 20, 'fetchingBonuses', false);

    // Fetch bonuses for the date range, with optional branch filtering
    let bonusQuery: any = {
      date: {
        $gte: parsedStartDate,
        $lte: parsedEndDate
      }
    };

    // Apply user filtering based on selectedUserIds and/or branchId
    if (selectedUserIds && selectedUserIds.length > 0) {
      // If specific users are selected, filter by those user IDs
      const selectedUserObjectIds = selectedUserIds.map((id: string) => new mongoose.Types.ObjectId(id));
      bonusQuery.userId = { $in: selectedUserObjectIds };
    } else if (branchId && branchId !== 'all') {
      // If branch filtering is needed but no specific users selected, get user IDs from the selected branch
      const User = mongoose.model('User');
      const branchUsers = await User.find({
        branchIds: { $in: [new mongoose.Types.ObjectId(branchId)] }
      }).select('_id').lean();

      const branchUserIds = branchUsers.map(user => user._id);
      bonusQuery.userId = { $in: branchUserIds };
    }

    const bonuses = await Bonus.find(bonusQuery).lean();

    // Update progress
    updateProgress(adminUserId, 30, 'processingData', false);

    // Group commissions and bonuses by user
    const userCommissionMap = new Map<string, UserCommissionData>();

    // Process commissions data - group by user
    for (const commission of commissions) {
      const userId = commission.userId.toString();

      if (userCommissionMap.has(userId)) {
        const userData = userCommissionMap.get(userId);
        if (userData) {
          userData.totalAmount += commission.amount;
          userData.commissionCount += 1;
          userData.commissions.push(commission);

          // Update latest date if this commission is newer
          if (commission.createdAt > userData.latestCommissionDate) {
            userData.latestCommissionDate = commission.createdAt;
          }
        }
      } else {
        userCommissionMap.set(userId, {
          userId,
          userName: "",
          userEmail: "",
          totalAmount: commission.amount,
          totalBonus: 0,
          commissionCount: 1,
          latestCommissionDate: commission.createdAt,
          commissions: [commission],
          bonuses: []
        });
      }
    }

    // Process bonuses data - group by user
    for (const bonus of bonuses) {
      const userId = bonus.userId.toString();

      if (userCommissionMap.has(userId)) {
        // User already has commissions, add bonuses
        const userData = userCommissionMap.get(userId);
        if (userData) {
          userData.totalBonus += bonus.amount;
          userData.bonuses.push(bonus);
        }
      } else {
        // User only has bonuses
        userCommissionMap.set(userId, {
          userId,
          userName: "",
          userEmail: "",
          totalAmount: 0,
          totalBonus: bonus.amount,
          commissionCount: 0,
          latestCommissionDate: new Date(),
          commissions: [],
          bonuses: [bonus]
        });
      }
    }

    // Update progress
    updateProgress(adminUserId, 40, 'fetchingUsers', false);

    // Get user details for all users
    const userIds = Array.from(userCommissionMap.keys());
    if (userIds.length === 0) {
      // Update progress to complete but unsuccessful
      updateProgress(adminUserId, 100, 'complete', true);

      // Log no data found
      await logAuditEvent(request, {
        action: 'MASS_INVOICE_GENERATED',
        entityType: 'System',
        description: `Mass invoice generation completed with no data found for period ${startDate} to ${endDate}${selectedUserIds && selectedUserIds.length > 0 ? ` (${selectedUserIds.length} selected users)` : branchId && branchId !== 'all' ? ` (Branch: ${branchId})` : ''}`,
        metadata: {
          success: false,
          reason: 'No commissions or bonuses found',
          startDate,
          endDate,
          branchId: branchId || 'all',
          selectedUserIds: selectedUserIds || [],
          completedBy: adminUserId
        }
      }, session);

      return NextResponse.json({
        success: false,
        message: 'No commissions or bonuses found for the selected date range'
      });
    }

    const users = await User.find({
      _id: { $in: userIds.map(id => new mongoose.Types.ObjectId(id)) }
    }).lean();

    // Add user names to the map
    for (const user of users) {
      const userId = user._id?.toString();
      if (userId && userCommissionMap.has(userId)) {
        const userData = userCommissionMap.get(userId);
        if (userData) {
          userData.userName = user.name || "Unknown";
          userData.userEmail = user.email || "";
        }
      }
    }

    // Update progress
    updateProgress(adminUserId, 50, 'generatingInvoices', false);

    // Process each user's data and create invoices with items
    const results = await executeWithRetry(async (dbSession) => {
      const createdInvoices = [];
      const today = new Date();
      const dateStr = today.getFullYear().toString() +
        (today.getMonth() + 1).toString().padStart(2, '0') +
        today.getDate().toString().padStart(2, '0');

      // Find the latest invoice number for today to determine sequence
      const latestInvoice = await Invoice.findOne({
        invoiceNumber: { $regex: `^INV-${dateStr}-` }
      }).sort({ invoiceNumber: -1 });

      let sequentialNumber = 1;
      if (latestInvoice) {
        const parts = latestInvoice.invoiceNumber.split('-');
        if (parts.length === 3) {
          sequentialNumber = parseInt(parts[2], 10) + 1 || 1;
        }
      }

      // Process each user's data and create invoices with items
      const userDataArray = Array.from(userCommissionMap.values());
      const totalUsers = userDataArray.length;

      // Update progress with total items to process
      updateProgress(
        adminUserId,
        50,
        'generatingInvoices',
        false,
        totalUsers,
        0
      );

      // Track all successfully generated invoices for email sending
      const invoicesForEmail = [];

      // Get complete user data with tax information
      const userDetailsMap = new Map();
      const usersWithTaxInfo = await User.find({
        _id: { $in: userDataArray.map(userData => new mongoose.Types.ObjectId(userData.userId)) }
      }).lean();

      // Create a map of user details for quick access
      usersWithTaxInfo.forEach(user => {
        const userId = user && typeof user === 'object' && '_id' in user ?
          String(user._id) : undefined;
        if (userId) {
          userDetailsMap.set(userId, user);
        }
      });

      // Fetch commission item type once (no branchId)
      const commissionType = await InvoiceItemType.findOne({ code: 'commissions' });
      if (!commissionType) throw new Error('InvoiceItemType for commissions not found');
      // Fetch bonus item type once
      const bonusType = await InvoiceItemType.findOne({ code: 'bonuses' });
      if (!bonusType) throw new Error('InvoiceItemType for bonuses not found');

      for (let i = 0; i < userDataArray.length; i++) {
        const userData = userDataArray[i];

        // Get complete user data including tax information
        const completeUserData = userDetailsMap.get(userData.userId);

        // Fetch the user's tax type and validate tax info
        let taxType: { code: string; names: string[]; percentages: number[] } = { code: 'NO_TAX', names: [], percentages: [] };
        let taxTypeId: string | undefined = undefined;
        let invalidTaxInfo = false;

        if (completeUserData?.taxInfo) {
          if (completeUserData.taxInfo.isTaxable === 'yes' && completeUserData.taxInfo.taxtypeid) {
            const originalTaxType = await TaxType.findById(completeUserData.taxInfo.taxtypeid).lean();
            if (originalTaxType && !Array.isArray(originalTaxType)) {
              // Use enhanced tax validation logic
              const taxDecision = await determineTaxTypeForInvoice(completeUserData, originalTaxType);
              taxType = taxDecision.taxType;
              taxTypeId = taxDecision.taxTypeId;
              invalidTaxInfo = taxDecision.invalidTaxInfo;
            }
          } else if (completeUserData.taxInfo.isTaxable === 'no') {
            const noTaxType = await TaxType.findOne({ code: 'no_tax' }).lean();
            if (noTaxType && !Array.isArray(noTaxType) && noTaxType._id) {
              taxTypeId = noTaxType._id.toString();
              taxType = {
                code: noTaxType.code,
                names: noTaxType.names,
                percentages: noTaxType.percentages
              };
            }
          }
        }

        // Fallback: If no taxTypeId is set, use 'no_tax' as default
        if (!taxTypeId) {
          console.log(`No tax type found for user ${userData.userName} (${userData.userId}), falling back to 'no_tax'`);
          const noTaxType = await TaxType.findOne({ code: 'no_tax' }).lean();
          if (noTaxType && !Array.isArray(noTaxType) && noTaxType._id) {
            taxTypeId = noTaxType._id.toString();
            taxType = {
              code: noTaxType.code,
              names: noTaxType.names,
              percentages: noTaxType.percentages
            };
            invalidTaxInfo = true; // Mark as invalid since we had to fall back
          } else {
            // Last resort: create a minimal tax type object and skip this user
            console.error(`Critical error: 'no_tax' tax type not found in database for user ${userData.userName} (${userData.userId})`);
            continue; // Skip this user to avoid validation error
          }
        }

        // Update progress for each user processed
        const progress = 50 + Math.floor((i / totalUsers) * 40); // Progress from 50% to 90%
        updateProgress(
          adminUserId,
          progress,
          'generatingInvoices',
          false,
          totalUsers,
          i
        );

        if (userData.totalAmount === 0 && userData.totalBonus === 0) {
          continue; // Skip users with no commissions or bonuses
        }

        // Generate invoice number
        const invoiceNumber = `INV-${dateStr}-${sequentialNumber.toString().padStart(4, '0')}`;
        sequentialNumber++;

        // Format the date range for invoice title in French
        const formatFrenchDateRange = (startDateStr: string, endDateStr: string): string => {
          // Parse dates directly from YYYY-MM-DD format to avoid timezone issues
          const [startYear, startMonthNum, startDayNum] = startDateStr.split('-').map(Number);
          const [endYear, endMonthNum, endDayNum] = endDateStr.split('-').map(Number);

          const frenchMonths = [
            'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
            'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
          ];

          const startDay = startDayNum;
          const endDay = endDayNum;
          const startMonth = frenchMonths[startMonthNum - 1]; // Month is 1-based in date string
          const endMonth = frenchMonths[endMonthNum - 1]; // Month is 1-based in date string

          // If start and end are in the same month, show: "Du 17 au 23 mai 2025"
          if (startMonthNum === endMonthNum && startYear === endYear) {
            return `Du ${startDay} au ${endDay} ${endMonth} ${endYear}`;
          } else {
            // If different months, show: "Du 30 avril au 6 mai 2025"
            return `Du ${startDay} ${startMonth} au ${endDay} ${endMonth} ${endYear}`;
          }
        };

        const weekStr = formatFrenchDateRange(startDate, endDate);

        // Prepare invoice data
        const invoiceData: any = {
          userId: userData.userId,
          title: weekStr,
          invoiceNumber: invoiceNumber,
          createdBy: adminUserId,
          status: 'nouveau',
          date: today,
          createdAt: today,
          manualItems: [], // Will be filled with invoice items
          startDate: new Date(startDate), // Use original startDate from request
          endDate: new Date(endDate), // Use original endDate from request
          taxTypeId: taxTypeId,
          invalidTaxInfo: invalidTaxInfo, // Flag indicating if tax info validation failed
          statusHistory: [{
            status: 'nouveau',
            changedAt: today,
            changedBy: adminUserId,
            isAutomatic: false,
            reason: 'Mass generated invoice'
          }]
        };

        // Add QST and TPS registration numbers for users with tpstvq tax type
        // Always include tax numbers if available, even if invalid
        if (taxType?.code === 'tpstvq') {
          console.log(`Processing TPSTVQ tax type for user ${userData.userName} (${userData.userId})`);
          console.log(`QST validation data:`, completeUserData?.taxInfo?.qstValidation);

          // Add QST registration number (full format already stored in database)
          if (completeUserData?.taxInfo?.qstRegistrationNumber) {
            invoiceData.qstRegistrationNumber = completeUserData.taxInfo.qstRegistrationNumber;
            console.log(`Added QST registration number to invoice ${invoiceNumber}: ${invoiceData.qstRegistrationNumber}`);
          }

          // Add TPS registration number (full format already stored in database)
          if (completeUserData?.taxInfo?.tpsRegistrationNumber) {
            invoiceData.tpsRegistrationNumber = completeUserData.taxInfo.tpsRegistrationNumber;
            console.log(`Added TPS registration number to invoice ${invoiceNumber}: ${invoiceData.tpsRegistrationNumber}`);
          }

          // Log validation status for reference but don't block inclusion
          const qstValidation = completeUserData?.taxInfo?.qstValidation;
          if (qstValidation) {
            console.log(`QST validation status for user ${userData.userName}: isValid=${qstValidation.isValid}, status=${qstValidation.status}`);
          } else {
            console.log(`No QST validation data for user ${userData.userName}`);
          }
        } else {
          console.log(`Tax type is not TPSTVQ (${taxType?.code}) for user ${userData.userName}, skipping registration numbers`);
        }

        // Create invoice
        const newInvoice = new Invoice(invoiceData);

        const createdInvoice = await newInvoice.save({ session: dbSession });
        createdInvoices.push({
          _id: createdInvoice._id,
          invoiceNumber: createdInvoice.invoiceNumber,
          userId: userData.userId,
          userName: userData.userName
        });

        // Log individual invoice creation (outside transaction to avoid blocking)
        // We'll log this after the transaction commits
        createdInvoice._auditData = {
          commissionCount: userData.commissions.length,
          bonusCount: userData.bonuses.length,
          totalAmount: userData.totalAmount + userData.totalBonus,
          dateRange: { startDate, endDate }
        };

        // Create invoice items using improved grouping and naming logic
        const invoiceItems = [];
        const formattedItemsForEmail = [];

        // Process commissions - group by unit price and determine invoice item type
        if (userData.commissions.length > 0) {
          // Group commissions by commission type to determine invoice item type
          const commissionsByType = new Map();
          for (const commission of userData.commissions) {
            const typeId = commission.commissionTypeId?._id?.toString() || commission.commissionTypeId?.toString();
            if (!commissionsByType.has(typeId)) {
              commissionsByType.set(typeId, []);
            }
            commissionsByType.get(typeId).push(commission);
          }

          // Process each commission type group
          for (const [, commissionsOfType] of Array.from(commissionsByType.entries())) {
            // Find the appropriate invoice item type for this commission type
            let invoiceItemType = await InvoiceItemType.findOne({ code: "Autres" });

            // Try to find a more specific invoice item type based on commission type code
            const commissionTypeCode = commissionsOfType[0]?.commissionTypeId?.code || '';
            if (commissionTypeCode === 'commission-vente') {
              const venteType = await InvoiceItemType.findOne({ code: 'commission-vente' });
              if (venteType) invoiceItemType = venteType;

            } else if (commissionTypeCode == 'commissions') {
              const papType = await InvoiceItemType.findOne({ code: 'commissions' });
              if (papType) invoiceItemType = papType;
            }

            // Group by unit price (amount)
            const groupedCommissions = groupByUnitPrice(commissionsOfType);

            for (const group of groupedCommissions) {
              // Generate title based on invoice item type code
              const title = await generateInvoiceItemTitle(
                invoiceItemType,
                group.items[0], // Pass first commission for reservation lookup
                group.quantity,
                group.items // Pass all items for date range calculation
              );

              // Use today's date for invoice items
              const latestDate = new Date();

              const invoiceItem = await InvoiceItem.create(
                [{
                  title,
                  quantity: group.quantity,
                  date: latestDate,
                  unitPrice: group.unitPrice,
                  taxable: true,
                  invoiceitemtypeId: invoiceItemType?._id || commissionsOfType[0]?.commissionTypeId?._id,
                }],
                { session: dbSession }
              );

              invoiceItems.push(invoiceItem[0]._id);

              // Store for email formatting
              formattedItemsForEmail.push({
                title,
                quantity: group.quantity,
                date: new Date(latestDate),
                unitPrice: group.unitPrice,
                taxable: true
              });
            }
          }
        }

        // Process bonuses - group by unit price
        if (userData.bonuses.length > 0) {
          if (!bonusType) {
            throw new Error('InvoiceItemType for bonuses not found');
          }

          // Add date field to bonuses for grouping
          const bonusesWithDate = userData.bonuses.map(bonus => ({
            ...bonus,
            date: bonus.date.toISOString()
          }));

          const groupedBonuses = groupByUnitPrice(bonusesWithDate);

          for (const group of groupedBonuses) {
            // Generate title based on invoice item type code
            const title = await generateInvoiceItemTitle(bonusType, null, group.quantity, group.items);

            // Use today's date for invoice items
            const latestDate = new Date();

            const invoiceItem = await InvoiceItem.create(
              [{
                title,
                quantity: group.quantity,
                date: new Date(latestDate),
                unitPrice: group.unitPrice,
                taxable: true,
                invoiceitemtypeId: bonusType._id,
              }],
              { session: dbSession }
            );

            invoiceItems.push(invoiceItem[0]._id);

            // Store for email formatting
            formattedItemsForEmail.push({
              title,
              quantity: group.quantity,
              date: new Date(latestDate),
              unitPrice: group.unitPrice,
              taxable: true
            });
          }
        }

        // Calculate subtotal
        const allItemsRaw = await Promise.all(
          invoiceItems.map(id => InvoiceItem.findById(id).session(dbSession).lean())
        );
        const allItems = allItemsRaw
          .flatMap((item: any) => Array.isArray(item) ? item : [item])
          .filter(isInvoiceItem);
        let grandTotal = 0;
        let taxes = 0;
        if (sendEmails) {
          const subtotal = BillingUtils.calculateSubtotal(allItems);
          taxes = taxType.percentages.reduce((acc: number, pct: number) => acc + subtotal * (pct / 100), 0);
          grandTotal = subtotal + taxes;
        }

        // Update invoice with items only, not totals
        await Invoice.findByIdAndUpdate(
          createdInvoice._id,
          {
            $push: { manualItems: { $each: invoiceItems } }
          },
          { session: dbSession }
        );

        // --- CONTRE-FACTURE GENERATION ---
        // Generate contre-facture if any items require it
        const itemTypeIds = allItems.map((item: any) => item.invoiceitemtypeId);
        const itemTypes = await InvoiceItemType.find({ _id: { $in: itemTypeIds } }).session(dbSession);
        const itemTypeMap = Object.fromEntries(itemTypes.map((type: any) => [String(type._id), type]));

        // Filter items that require contre-facture
        const contreFactureItems = allItems.filter((item: any) => {
          const type = itemTypeMap[item.invoiceitemtypeId];
          return type && type.requiresContreFacture;
        }).map((item: any) => {
          const type = itemTypeMap[item.invoiceitemtypeId];
          return {
            ...item,
            title: "LOCATION D\'ESPACE",
            unitPrice: (type as any).chargeAmount,
            chargeAmount: (type as any).chargeAmount,
            taxable: true, // Contre facture items are ALWAYS taxable
          };
        });

        if (contreFactureItems.length > 0) {
          // Fetch the 'amq' tax type
          const amqTaxType = await TaxType.findOne({ code: 'amq' }).session(dbSession).lean() as { _id: any, names: string[], percentages: number[] } | null;
          if (amqTaxType && amqTaxType._id) {
            // Calculate taxes and total
            const subtotal = contreFactureItems.reduce((sum, item) => sum + (item.unitPrice * item.quantity), 0);
            // For contre factures, taxes are ALWAYS calculated on ALL items regardless of taxable flag
            const totalTaxes = amqTaxType.percentages.map((rate, idx) =>
              contreFactureItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice * (rate / 100)), 0)
            );
            const total = subtotal + totalTaxes.reduce((sum, t) => sum + t, 0);

            const contreFacture = {
              userId: userData.userId,
              invoiceNumber: invoiceNumber + '-CF',
              status: 'nouveau',
              taxTypeId: amqTaxType._id,
              title: `Invoice for ${userData.userName} - ${weekStr} (Contre-Facture)`,
              date: today,
              items: contreFactureItems,
              subtotal,
              totalTaxes,
              total,
              startDate: new Date(startDate),
              endDate: new Date(endDate),
              // Add AMQ tax registration numbers
              tpsRegistrationNumber: '783000151RT001',
              qstRegistrationNumber: '1231059977TQ0001'
            };

            // Save contre-facture to invoice
            // Use findById and save to trigger pre-save hooks
            const invoiceForContreFacture = await Invoice.findById(createdInvoice._id).session(dbSession);
            if (invoiceForContreFacture) {
              invoiceForContreFacture.contreFacture = contreFacture;
              await invoiceForContreFacture.save({ session: dbSession });
            }
          }
        }

        // Store invoice info for email if needed
        if (sendEmails) {
          invoicesForEmail.push({
            invoiceId: createdInvoice._id,
            invoiceNumber: createdInvoice.invoiceNumber,
            userId: userData.userId,
            userName: userData.userName,
            userEmail: userData.userEmail,
            calculatedTotal: grandTotal,
            taxes,
            items: formattedItemsForEmail, // Use the grouped and properly titled items
            date: createdInvoice.createdAt,
            taxType
          });
        }
      }

      // Update progress
      updateProgress(adminUserId, 90, 'finalizingInvoices', false);

      // Store the invoices for email sending
      if (sendEmails && invoicesForEmail.length > 0) {
        // We'll handle emails after the transaction
        (global as any).__invoicesForEmail = invoicesForEmail;
      }

      return createdInvoices;
    });

    // Log individual invoice creations after successful transaction
    for (const invoiceInfo of results) {
      try {
        await logAuditEvent(request, {
          action: 'INVOICE_CREATED',
          entityType: 'Invoice',
          entityId: invoiceInfo._id.toString(),
          entityName: invoiceInfo.invoiceNumber,
          description: `Invoice ${invoiceInfo.invoiceNumber} created via mass generation for ${invoiceInfo.userName}`,
          metadata: {
            massGeneration: true,
            userId: invoiceInfo.userId,
            userName: invoiceInfo.userName,
            dateRange: { startDate, endDate },
            branchId: branchId || 'all'
          },
          relatedEntities: [{
            type: 'User',
            id: invoiceInfo.userId,
            name: invoiceInfo.userName
          }]
        }, session);
      } catch (auditError) {
        console.error(`Failed to log audit for invoice ${invoiceInfo.invoiceNumber}:`, auditError);
        // Don't fail the entire operation for audit logging errors
      }
    }

    // After transaction, handle email sending if needed
    if (sendEmails) {
      const invoicesForEmail = (global as any).__invoicesForEmail || [];
      delete (global as any).__invoicesForEmail;

      if (invoicesForEmail.length > 0) {
        updateProgress(adminUserId, 95, 'sendingEmails', false, invoicesForEmail.length, 0);

        // Get email template for invoices
        const emailTemplate = await EmailTemplate.findOne({
          type: 'invoice',
          disabled: { $ne: true }
        });

        // Get logo URL for email embedding
        const logoUrl = getLogoUrl();

        // Fallback content if no template exists - French with professional styling
        let emailContent = `
          <!DOCTYPE html>
          <html lang="fr">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Facture {{invoiceNumber}}</title>
            <style>
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9f9f9;
              }
              .email-container {
                background-color: white;
                border-radius: 8px;
                padding: 30px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
              .header {
                text-align: center;
                border-bottom: 3px solid #374151;
                padding-bottom: 20px;
                margin-bottom: 30px;
              }
              .company-logo {
                max-width: 200px;
                height: auto;
                margin: 0 auto 10px auto;
                display: block;
              }
              .invoice-info {
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 6px;
                margin: 20px 0;
              }
              .invoice-number {
                font-size: 18px;
                font-weight: bold;
                color: #374151;
              }
              .amount {
                font-size: 20px;
                font-weight: bold;
                color: #059669;
              }
              .button {
                display: inline-block;
                background-color: #dc2626 !important;
                color: white !important;
                padding: 12px 24px;
                text-decoration: none;
                border-radius: 6px;
                font-weight: bold;
                margin: 20px 0;
              }
              .button:hover {
                background-color: #b91c1c !important;
                color: white !important;
              }
              .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
                font-size: 14px;
                color: #6b7280;
              }
            </style>
          </head>
          <body>
            <div class="email-container">
              <div class="header">
                {{LOGO_PLACEHOLDER}}
                <p>200 rue Principale, local 8, St-Sauveur, QC J0R 1R0</p>
              </div>

              <p>Bonjour {{name}},</p>

              <p>Nous vous transmettons votre facture en pièce jointe.</p>

              <div class="invoice-info">
                <div class="invoice-number">Facture N° {{invoiceNumber}}</div>
                <p><strong>Date :</strong> {{invoiceDate}}</p>
                <p><strong>Montant total :</strong> <span class="amount">{{invoiceTotal}} $ CAD</span></p>
              </div>

              <p>Vous pouvez consulter et signer votre facture en ligne en cliquant sur le lien ci-dessous :</p>

              <p style="text-align: center;">
                <a href="{{signingLink}}" class="button">Consulter et signer la facture</a>
              </p>

              <p>Si vous avez des questions concernant cette facture, n'hésitez pas à nous contacter.</p>

              <div class="footer">
                <p><strong>Joanny Villeneuve et Sandra Legault</strong><br>
                <EMAIL></p>


                <p><em>Ceci est un message automatique, merci de ne pas y répondre directement.</em></p>
              </div>
            </div>
          </body>
          </html>
        `;
        let emailSubject = getInvoiceEmailSubject();

        if (emailTemplate) {
          emailContent = emailTemplate.content;
          if (emailTemplate.name) {
            emailSubject = emailTemplate.name;
          }
        }

        // Replace logo placeholder with actual logo or fallback text
        console.log('Logo URL exists:', !!logoUrl);
        console.log('Logo URL:', logoUrl);

        const logoHtml = logoUrl
          ? `<img src="${logoUrl}" alt="AMQ Partners" class="company-logo" />`
          : '<h1 style="font-size: 24px; font-weight: bold; color: #374151; margin: 0;">AMQ Partners</h1>';

        console.log('Logo HTML length:', logoHtml.length);
        console.log('Logo HTML preview:', logoHtml);

        // Check if placeholder exists before replacement
        const hasPlaceholder = emailContent.includes('{{LOGO_PLACEHOLDER}}');
        console.log('Email content contains logo placeholder before replacement:', hasPlaceholder);

        emailContent = emailContent.replace('{{LOGO_PLACEHOLDER}}', logoHtml);

        // Verify replacement worked
        const stillHasPlaceholder = emailContent.includes('{{LOGO_PLACEHOLDER}}');
        console.log('Email content still contains logo placeholder after replacement:', stillHasPlaceholder);

        // Add signing link to email content if it doesn't already have a variable for it
        if (!emailContent.includes('{{signingLink}}')) {
          emailContent += '<p>You can view and sign your invoice at: <a href="{{signingLink}}">{{signingLink}}</a></p>';
        }

        // Send emails for each invoice
        for (let i = 0; i < invoicesForEmail.length; i++) {
          const invoice = invoicesForEmail[i];

          try {
            // Update progress
            updateProgress(
              adminUserId,
              95 + Math.floor((i / invoicesForEmail.length) * 4), // 95% to 99%
              'sendingEmails',
              false,
              invoicesForEmail.length,
              i
            );

            // Get user details for the PDF
            const userData = await User.findById(invoice.userId).lean();
            const userPhone = userData && typeof userData === 'object' && 'phone' in userData ?
              userData.phone as string : '';
            const userCompanyName = userData && typeof userData === 'object' && 'companyName' in userData ?
              userData.companyName as string : '';

            // Find the actual invoice to get tax information
            const invoiceData: any = await Invoice.findById(invoice.invoiceId).lean();

            // Fetch taxType for the invoice
            let taxType: { code: string; names: string[]; percentages: number[] } = { code: 'NO_TAX', names: [], percentages: [] };
            if (invoiceData?.taxTypeId) {
              const foundTaxType = await TaxType.findById(invoiceData.taxTypeId).lean();
              if (foundTaxType && !Array.isArray(foundTaxType)) {
                taxType = {
                  code: foundTaxType.code,
                  names: foundTaxType.names,
                  percentages: foundTaxType.percentages
                };
              }
            }

            // Check if a token already exists for this invoice and user
            let signingToken = await InvoiceSigningToken.findOne({
              invoiceId: invoice.invoiceId,
              userId: invoice.userId
            });

            let tokenToUse: string;

            if (signingToken) {
              // Use existing token - DO NOT regenerate
              tokenToUse = signingToken.token;
              console.log(`[Mass Generate] Using existing token for invoice ${invoice.invoiceNumber}, user ${invoice.userId}`);
              
              // Ensure invoice.signingToken is consistent with InvoiceSigningToken.token
              const invoiceDoc = await Invoice.findById(invoice.invoiceId);
              if (invoiceDoc && invoiceDoc.signingToken !== tokenToUse) {
                console.log(`[Mass Generate] Syncing invoice.signingToken with existing token for invoice ${invoice.invoiceNumber}`);
                invoiceDoc.signingToken = tokenToUse;
                await invoiceDoc.save();
              }
            } else {
              // Generate a new token only if none exists
              tokenToUse = randomBytes(32).toString('hex');
              signingToken = await InvoiceSigningToken.create({
                token: tokenToUse,
                invoiceId: invoice.invoiceId,
                userId: invoice.userId,
                createdAt: new Date()
              });
              
              // Also update the invoice.signingToken field for consistency
              const invoiceDoc = await Invoice.findById(invoice.invoiceId);
              if (invoiceDoc) {
                invoiceDoc.signingToken = tokenToUse;
                await invoiceDoc.save();
              }
              
              console.log(`[Mass Generate] Created new token for invoice ${invoice.invoiceNumber}, user ${invoice.userId}`);
            }

            // Generate the signing URL using the existing or new token
            const signingUrl = generateInvoiceSigningUrl(tokenToUse);

            // Generate PDF using HTML template
            const invoiceDetails = {
              invoiceNumber: invoice.invoiceNumber,
              date: invoice.date,
              dueDate: new Date(invoice.date),
              status: 'nouveau',
              total: invoice.calculatedTotal,
              subtotal: BillingUtils.calculateSubtotal(invoice.items),
              issuerInfo: {
                name: userCompanyName || invoice.userName || 'Client',
                address: (userData && typeof userData === 'object' && 'address' in userData) ? userData.address as string : '',
                city: (userData && typeof userData === 'object' && 'city' in userData) ? userData.city as string : '',
                province: (userData && typeof userData === 'object' && 'province' in userData) ? userData.province as string : '',
                postalCode: (userData && typeof userData === 'object' && 'postalCode' in userData) ? userData.postalCode as string : '',
                phone: userPhone
              },
              clientInfo: {
                name: invoice.userName || 'Client',
                companyName: userCompanyName,
                phone: userPhone,
                email: invoice.userEmail || ''
              },
              // Include TPS and QST registration numbers from the invoice data if they exist
              tpsRegistrationNumber: invoiceData?.tpsRegistrationNumber,
              qstRegistrationNumber: invoiceData?.qstRegistrationNumber
            };

            // Set due date to 30 days after invoice date
            invoiceDetails.dueDate.setDate(invoiceDetails.dueDate.getDate() + 30);

            // Company info for PDF
             const companyInfo = {
                name: "Alimentation Mon Quartier",
                address: "200 rue Principale, local 8",
                city: "St-Sauveur",
                province: "Québec",
                postalCode: "J0R 1R0",
                phone: "**************",
                email: "<EMAIL>",
              };

            // Generate the PDF using HTML template
            const pdfDataUrl = await generateInvoicePDFDataURL(
              invoice.items,
              invoiceDetails,
              companyInfo,
              taxType,
              false // swapIssuerAndClient
            );

            // Extract base64 content
            const base64Content = pdfDataUrl.split(',')[1];

            // --- CONTRE-FACTURE PDF GENERATION ---
            let contreFacturePdfBase64: string | null = null;
            if (invoiceData?.contreFacture && invoiceData.contreFacture.items && invoiceData.contreFacture.items.length) {
              // Fetch contre-facture tax type
              const contreFactureTaxType = await TaxType.findById(invoiceData.contreFacture.taxTypeId).lean();
              if (contreFactureTaxType) {
                // Build invoiceDetails for contre-facture
                const contreFactureDetails = {
                  invoiceNumber: invoiceData.contreFacture.invoiceNumber,
                  date: invoiceData.contreFacture.date,
                  dueDate: invoiceData.contreFacture.endDate || undefined,
                  status: invoiceData.contreFacture.status,
                  total: invoiceData.contreFacture.total,
                  subtotal: invoiceData.contreFacture.subtotal,
                  totalTaxes: invoiceData.contreFacture.totalTaxes,
                  issuerInfo: {
                    name: "Alimentation Mon Quartier",
                    address: "200 rue Principale, local 8",
                    city: "St-Sauveur",
                    province: "Québec",
                    postalCode: "J0R 1R0",
                    phone: "**************"
                  },
                  clientInfo: {
                    name: invoice.userName || 'Client',
                    companyName: userCompanyName,
                    phone: userPhone,
                    email: invoice.userEmail || ''
                  },
                  // Include AMQ tax registration numbers for contre-facture
                  tpsRegistrationNumber: invoiceData.contreFacture.tpsRegistrationNumber,
                  qstRegistrationNumber: invoiceData.contreFacture.qstRegistrationNumber
                };

                // Tax type for contre-facture
                const contreFactureTaxTypeFormatted = {
                  code: (contreFactureTaxType as any).code,
                  names: (contreFactureTaxType as any).names,
                  percentages: (contreFactureTaxType as any).percentages,
                };

                // Generate contre-facture PDF using HTML template with swapIssuerAndClient: true
                const contreFacturePdfDataUrl = await generateInvoicePDFDataURL(
                  invoiceData.contreFacture.items,
                  contreFactureDetails,
                  companyInfo,
                  contreFactureTaxTypeFormatted,
                  true // swapIssuerAndClient
                );
                contreFacturePdfBase64 = contreFacturePdfDataUrl.split(',')[1];
              }
            }

            // Prepare email attachments
            const attachments = [
              {
                name: `${invoice.invoiceNumber}.pdf`,
                content: base64Content,
                type: 'application/pdf'
              }
            ];
            if (contreFacturePdfBase64) {
              attachments.push({
                name: `${invoice.invoiceNumber}-CF.pdf`,
                content: contreFacturePdfBase64,
                type: 'application/pdf'
              });
            }

            // Send email with attached PDF(s)
            await sendBrevoTemplatedEmail({
              to: [{ email: invoice.userEmail, name: invoice.userName }],
              subject: emailSubject,
              content: emailContent,
              variables: {
                name: invoice.userName,
                invoiceNumber: invoice.invoiceNumber,
                invoiceDate: new Date(invoice.date).toLocaleDateString('fr-CA', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                }),
                invoiceTotal: invoice.calculatedTotal.toFixed(2),
                signingLink: signingUrl
              },
              attachments
            });

            // Log email sending
            try {
              await logAuditEvent(request, {
                action: 'INVOICE_EMAIL_SENT',
                entityType: 'Invoice',
                entityId: invoice.invoiceId.toString(),
                entityName: invoice.invoiceNumber,
                description: `Invoice ${invoice.invoiceNumber} email sent to ${invoice.userEmail} via mass generation`,
                metadata: {
                  massGeneration: true,
                  recipientEmail: invoice.userEmail,
                  recipientName: invoice.userName,
                  attachmentCount: attachments.length,
                  hasContreFacture: attachments.length > 1,
                  signingUrl: signingUrl
                },
                relatedEntities: [{
                  type: 'User',
                  id: invoice.userId,
                  name: invoice.userName
                }]
              }, session);
            } catch (auditError) {
              console.error(`Failed to log email audit for invoice ${invoice.invoiceNumber}:`, auditError);
            }
          } catch (emailError) {
            console.error(`Error sending invoice email for ${invoice.invoiceNumber}:`, emailError);
            // Continue with other emails even if one fails
          }
        }
      }
    }

    // Final update - complete
    updateProgress(adminUserId, 100, 'complete', true);

    // Log successful completion
    await logAuditEvent(request, {
      action: 'MASS_INVOICE_GENERATED',
      entityType: 'System',
      description: `Mass invoice generation completed successfully. Generated ${results.length} invoices for period ${startDate} to ${endDate}${selectedUserIds && selectedUserIds.length > 0 ? ` (${selectedUserIds.length} selected users)` : branchId && branchId !== 'all' ? ` (Branch: ${branchId})` : ''} (email sending disabled)`,
      metadata: {
        success: true,
        invoiceCount: results.length,
        startDate,
        endDate,
        branchId: branchId || 'all',
        selectedUserIds: selectedUserIds || [],
        emailsSent: false, // Email sending disabled
        completedBy: adminUserId,
        invoiceNumbers: results.map(r => r.invoiceNumber)
      },
      relatedEntities: results.map(r => ({
        type: 'Invoice',
        id: r._id.toString(),
        name: r.invoiceNumber
      }))
    }, session);

    return NextResponse.json({
      success: true,
      message: `Successfully generated ${results.length} invoices (email sending disabled)`,
      invoices: results
    });

  } catch (error) {
    console.error('Error generating invoices:', error);

    // Error update
    updateProgress(adminUserId, 100, 'error', true);

    // Log error
    try {
      await logAuditEvent(request, {
        action: 'MASS_INVOICE_GENERATED',
        entityType: 'System',
        description: `Mass invoice generation failed for period ${startDate} to ${endDate}${selectedUserIds && selectedUserIds.length > 0 ? ` (${selectedUserIds.length} selected users)` : branchId && branchId !== 'all' ? ` (Branch: ${branchId})` : ''}`,
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          success: false,
          startDate,
          endDate,
          branchId: branchId || 'all',
          selectedUserIds: selectedUserIds || [],
          emailsSent: false, // Email sending disabled
          failedBy: adminUserId,
          errorDetails: error instanceof Error ? error.stack : String(error)
        }
      }, session);
    } catch (auditError) {
      console.error('Failed to log audit for mass generation error:', auditError);
    }

    return NextResponse.json(
      { error: 'Failed to generate invoices' },
      { status: 500 }
    );
  }
}