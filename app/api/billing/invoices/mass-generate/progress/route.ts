import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import * as RoleUtils from '@/lib/utils/role-utils';

// Global progress store to track invoice generation progress
// This is a simple in-memory store that works for a single instance
// In production with multiple instances, you'd use Redis or another shared storage
interface ProgressData {
  userId: string;
  progress: number;
  stage: string;
  timestamp: number;
  isComplete: boolean;
  totalItems?: number;
  processedItems?: number;
}

// Progress will expire after 10 minutes
const PROGRESS_EXPIRY_MS = 10 * 60 * 1000;

export const progressStore: Map<string, ProgressData> = new Map();

// Clean up expired progress data
setInterval(() => {
  const now = Date.now();
  // Use Array.from() to fix the TypeScript iteration issue
  Array.from(progressStore.entries()).forEach(([userId, data]) => {
    if (now - data.timestamp > PROGRESS_EXPIRY_MS) {
      progressStore.delete(userId);
    }
  });
}, 6 * 1000); // Check every 6 seconds

export async function GET(request: NextRequest) {
  // Check authorization
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Get current user's ID
  const user = session.user as any;
  const userId = user?.id || user?._id;
  
  if (!userId) {
    return NextResponse.json({ error: 'User ID not found' }, { status: 400 });
  }
  
  // Get progress data for this user
  const progressData = progressStore.get(userId.toString());
  
  if (!progressData) {
    return NextResponse.json({ 
      progress: 0,
      stage: 'idle',
      isComplete: false
    });
  }
  
  return NextResponse.json(progressData);
}

// Helper function to update progress
export function updateProgress(
  userId: string, 
  progress: number, 
  stage: string, 
  isComplete: boolean = false,
  totalItems?: number,
  processedItems?: number
) {
  progressStore.set(userId, {
    userId,
    progress,
    stage,
    timestamp: Date.now(),
    isComplete,
    totalItems,
    processedItems
  });
}

// Helper function to clear progress
export function clearProgress(userId: string) {
  progressStore.delete(userId);
} 