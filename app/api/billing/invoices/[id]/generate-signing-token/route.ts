import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import InvoiceSigningToken from '@/models/InvoiceSigningToken';
import { getToken } from 'next-auth/jwt';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserManageBilling } from '@/lib/utils/permissions-utils';
import { randomBytes } from 'crypto';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { ensureTokenConsistency } from '@/lib/utils/invoice-token-sync';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const { id } = await params;
    const body = await request.json();
    const { userId } = body;
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' }, 
        { status: 400 }
      );
    }
    
    // Check if invoice exists
    const invoice = await Invoice.findById(id);
    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' }, 
        { status: 404 }
      );
    }
    
    // Ensure token consistency between invoice.signingToken and InvoiceSigningToken.token
    const { token, isNewToken } = await ensureTokenConsistency(id, userId);
    
    // Generate the secure URL
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const signingUrl = `${baseUrl}/invoice-signing/${token}`;
    
    return NextResponse.json({
      success: true,
      signingUrl,
      token,
      message: isNewToken ? 'Generated new signing token' : 'Using existing signing token'
    });
    
  } catch (error: any) {
    console.error('Error generating signing token:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate signing token',
        details: error.message
      }, 
      { status: 500 }
    );
  }
} 