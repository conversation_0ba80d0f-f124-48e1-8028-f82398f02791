import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import TaxType from '@/models/TaxType';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserManageBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { determineTaxTypeForInvoice } from '@/app/api/utils/tax-validation';

// POST: Refresh tax info for an invoice
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const p = await params;
    
    // Find the invoice
    const invoice = await Invoice.findById(p.id);
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Check if invoice has both userId and taxTypeId
    if (!invoice.userId || !invoice.taxTypeId) {
      return NextResponse.json({ 
        error: 'Invoice must have both userId and taxTypeId to refresh tax info' 
      }, { status: 400 });
    }

    // Fetch the tax type to check if it's tpstvq
    const taxType = await TaxType.findById(invoice.taxTypeId).lean();
    
    if (!taxType || Array.isArray(taxType)) {
      return NextResponse.json({ error: 'Tax type not found' }, { status: 404 });
    }

    let refreshResult = {
      taxTypeCode: (taxType as any).code,
      invalidTaxInfo: false,
      tpsRegistrationNumber: null as string | null,
      qstRegistrationNumber: null as string | null,
      message: ''
    };

    if ((taxType as any).code === 'tpstvq') {
      console.log(`Refreshing TPSTVQ tax info for invoice ${invoice._id}`);
      
      // Fetch the user's complete tax info
      const user = await User.findById(invoice.userId).lean();
      
      if (user && !Array.isArray(user) && (user as any).taxInfo) {
        // Use the tax validation logic to determine the appropriate tax type
        const taxDecision = await determineTaxTypeForInvoice(user, taxType);
        invoice.invalidTaxInfo = taxDecision.invalidTaxInfo;
        
        // Add QST registration number (full format already stored in database)
        if ((user as any).taxInfo.qstRegistrationNumber) {
          invoice.qstRegistrationNumber = (user as any).taxInfo.qstRegistrationNumber;
          refreshResult.qstRegistrationNumber = invoice.qstRegistrationNumber;
          console.log(`Updated QST registration number: ${invoice.qstRegistrationNumber}`);
        } else {
          invoice.qstRegistrationNumber = undefined;
          refreshResult.qstRegistrationNumber = null;
        }

        // Add TPS registration number (full format already stored in database)
        if ((user as any).taxInfo.tpsRegistrationNumber) {
          invoice.tpsRegistrationNumber = (user as any).taxInfo.tpsRegistrationNumber;
          refreshResult.tpsRegistrationNumber = invoice.tpsRegistrationNumber;
          console.log(`Updated TPS registration number: ${invoice.tpsRegistrationNumber}`);
        } else {
          invoice.tpsRegistrationNumber = undefined;
          refreshResult.tpsRegistrationNumber = null;
        }

        refreshResult.invalidTaxInfo = invoice.invalidTaxInfo;

        // Log validation status for reference
        const qstValidation = (user as any).taxInfo.qstValidation;
        if (qstValidation) {
          console.log(`QST validation status: isValid=${qstValidation.isValid}, status=${qstValidation.status}`);
          refreshResult.message = `Tax info refreshed. QST validation: ${qstValidation.isValid ? 'Valid' : 'Invalid'} (${qstValidation.status})`;
        } else {
          console.log(`No QST validation data for user`);
          refreshResult.message = 'Tax info refreshed. No QST validation data available.';
        }
      } else {
        console.log(`No tax info found for user ${invoice.userId}`);
        invoice.invalidTaxInfo = true;
        invoice.tpsRegistrationNumber = undefined;
        invoice.qstRegistrationNumber = undefined;
        refreshResult.invalidTaxInfo = true;
        refreshResult.message = 'No tax info found for user. Registration numbers cleared.';
      }
    } else {
      console.log(`Tax type is not TPSTVQ (${(taxType as any).code}), clearing registration numbers`);
      // Clear tax registration numbers for non-TPSTVQ tax types
      invoice.invalidTaxInfo = false;
      invoice.tpsRegistrationNumber = undefined;
      invoice.qstRegistrationNumber = undefined;
      refreshResult.message = `Tax type is ${(taxType as any).code}. Registration numbers cleared.`;
    }
    
    // Save the updated invoice
    await invoice.save();
    
    return NextResponse.json({
      success: true,
      refreshResult,
      invoice: {
        _id: invoice._id,
        invalidTaxInfo: invoice.invalidTaxInfo,
        tpsRegistrationNumber: invoice.tpsRegistrationNumber,
        qstRegistrationNumber: invoice.qstRegistrationNumber
      }
    });
  } catch (error) {
    console.error('Refresh tax info error:', error);
    return NextResponse.json({ 
      error: 'Failed to refresh tax info', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}
