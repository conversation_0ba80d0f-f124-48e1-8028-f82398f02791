import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import Invoice from '@/models/Invoice';
import dbConnect from '@/lib/db';

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const invoice = await Invoice.findById(params.id)
      .populate('statusHistory.changedBy', 'name email')
      .select('statusHistory invoiceNumber status');
    
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }
    
    // Sort status history by date (newest first)
    const sortedHistory = invoice.statusHistory.sort((a, b) => 
      new Date(b.changedAt).getTime() - new Date(a.changedAt).getTime()
    );
    
    return NextResponse.json({
      invoiceNumber: invoice.invoiceNumber,
      currentStatus: invoice.status,
      statusHistory: sortedHistory.map(entry => ({
        status: entry.status,
        changedAt: entry.changedAt,
        changedBy: entry.changedBy ? {
          id: entry.changedBy._id,
          name: entry.changedBy.name,
          email: entry.changedBy.email
        } : null,
        isAutomatic: entry.isAutomatic,
        reason: entry.reason
      }))
    });
  } catch (error) {
    console.error('Status history fetch failed:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch status history' 
    }, { status: 500 });
  }
}
