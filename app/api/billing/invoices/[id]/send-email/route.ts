import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import InvoiceItem from '@/models/InvoiceItem';
import User from '@/models/User';
import EmailTemplate from '@/models/EmailTemplate';
import InvoiceSigningToken from '@/models/InvoiceSigningToken';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { generateInvoicePDFDataURL } from '@/lib/utils/html-pdf-utils';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';

import { canUserAccessBilling } from '@/lib/utils/permissions-utils';
import { isValidStatusTransition } from '@/lib/utils/invoice-status-utils';

import { randomBytes } from 'crypto';
import { generateInvoiceSigningUrl } from '@/lib/utils/invoice-signing-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import TaxType from '@/models/TaxType';
import { InvoiceAuditLogger } from '@/lib/utils/audit-utils';
import { getInvoiceEmailSubject } from '@/lib/utils/email-templates';
import { ensureTokenConsistency } from '@/lib/utils/invoice-token-sync';

// This must match exactly what the PDF generator expects
interface InvoiceItem {
  _id: string;
  title: string;
  quantity: number;
  date: string | Date;
  unitPrice: number;
  taxable: boolean;
}

// Interface for client info in invoice details
interface ClientInfo {
  name: string;
  companyName?: string;
  address?: string;
  city?: string;
  province?: string;
  postalCode?: string;
  phone?: string;
}

// Interface for invoice details as expected by the PDF generator
interface InvoiceDetails {
  invoiceNumber: string;
  date: string | Date;
  dueDate?: string | Date;
  status: string;
  signedAt?: Date | string; // Add signedAt field
  clientInfo?: ClientInfo;
  taxType: {
    code: string;
    names: string[];
    percentages: number[];
  };
  qstRegistrationNumber?: string;
  tpsRegistrationNumber?: string;
}

// Get logo URL for email embedding
function getLogoUrl(): string {
  try {
    // Use environment domain or fallback to localhost
    const domain = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const logoUrl = `${domain}/amq-logo.png`;
    console.log('Logo URL:', logoUrl);
    return logoUrl;
  } catch (error) {
    console.error('Error generating logo URL:', error);
    return '';
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    const p = await params;
    // Check authorization
    const session = await getServerSession(authOptions);
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    if (!session || !canUserAccessBilling(session.user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get invoice ID from params
    const invoiceId = p.id;

    // Fetch the invoice (NOT using .lean() so we have access to methods)
    const invoice = await Invoice.findById(invoiceId);
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Fetch the invoice items
    const items = await InvoiceItem.find({
      _id: { $in: invoice.manualItems || [] }
    }).lean();

    console.log('Invoice:', invoiceId, 'Items count:', items.length, 'ManualItems:', invoice.manualItems?.length || 0);

    if (!items.length) {
      return NextResponse.json({
        error: 'Cannot send email: No items in invoice'
      }, { status: 400 });
    }

    // Get the user/client details
    const userData = await User.findById(invoice.userId).lean();
    if (!userData) {
      return NextResponse.json({
        error: 'User/Client not found for this invoice'
      }, { status: 404 });
    }

    const user = userData as any; // Type assertion for lean document

    if (!user.email) {
      return NextResponse.json({
        error: 'User/Client has no email address'
      }, { status: 400 });
    }

    // Get email template for invoices
    const emailTemplate = await EmailTemplate.findOne({
      type: 'invoice',
      disabled: { $ne: true }
    });

    // Get logo URL for email embedding
    const logoUrl = getLogoUrl();
    // Fallback content if no template exists - French with professional styling
    let emailContent = `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Facture {{invoiceNumber}}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
          }
          .email-container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .header {
            text-align: center;
            border-bottom: 3px solid #374151;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .company-logo {
            max-width: 200px;
            height: auto;
            margin: 0 auto 10px auto;
            display: block;
          }
          .invoice-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
          }
          .invoice-number {
            font-size: 18px;
            font-weight: bold;
            color: #374151;
          }
          .amount {
            font-size: 20px;
            font-weight: bold;
            color: #059669;
          }
          .button {
            display: inline-block;
            background-color: #dc2626 !important;
            color: white !important;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 20px 0;
          }
          .button:hover {
            background-color: #b91c1c !important;
            color: white !important;
          }
          .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            {{LOGO_PLACEHOLDER}}
            <p>200 rue Principale, local 8, St-Sauveur, QC J0R 1R0</p>
          </div>

          <p>Bonjour {{name}},</p>

          <p>Nous vous transmettons votre facture en pièce jointe.</p>

          <div class="invoice-info">
            <div class="invoice-number">Facture N° {{invoiceNumber}}</div>
            <p><strong>Date :</strong> {{invoiceDate}}</p>
            <p><strong>Montant total :</strong> <span class="amount">{{invoiceTotal}} $ CAD</span></p>
          </div>

          <p>Vous pouvez consulter et signer votre facture en ligne en cliquant sur le lien ci-dessous :</p>

          <p style="text-align: center;">
            <a href="{{signingLink}}" class="button">Consulter et signer la facture</a>
          </p>

          <p>Si vous avez des questions concernant cette facture, n'hésitez pas à nous contacter.</p>

          <div class="footer">
            <p><strong>Joanny Villeneuve et Sandra Legault</strong><br>
            <EMAIL></p>

            <p><em>Ceci est un message automatique, merci de ne pas y répondre directement.</em></p>
          </div>
        </div>
      </body>
      </html>
    `;
    let emailSubject = getInvoiceEmailSubject();

    if (emailTemplate) {
      emailContent = emailTemplate.content;
      if (emailTemplate.name) {
        emailSubject = emailTemplate.name;
      }
    }

    // Replace logo placeholder with actual logo or fallback text
    console.log('Logo URL exists:', !!logoUrl);
    console.log('Logo URL:', logoUrl);

    const logoHtml = logoUrl
      ? `<img src="${logoUrl}" alt="AMQ Partners" class="company-logo" />`
      : '<h1 style="font-size: 24px; font-weight: bold; color: #374151; margin: 0;">AMQ Partners</h1>';

    console.log('Logo HTML length:', logoHtml.length);
    console.log('Logo HTML preview:', logoHtml);

    // Check if placeholder exists before replacement
    const hasPlaceholder = emailContent.includes('{{LOGO_PLACEHOLDER}}');
    console.log('Email content contains logo placeholder before replacement:', hasPlaceholder);

    emailContent = emailContent.replace('{{LOGO_PLACEHOLDER}}', logoHtml);

    // Verify replacement worked
    const stillHasPlaceholder = emailContent.includes('{{LOGO_PLACEHOLDER}}');
    console.log('Email content still contains logo placeholder after replacement:', stillHasPlaceholder);

    // Create client info object
    let clientInfo: ClientInfo | undefined = undefined;
    if (user) {
      clientInfo = {
        name: user.name || 'Client',
        companyName: (user as any).companyName || '',
        phone: typeof user.phone === 'string' ? user.phone : '',
        email: user.email || ''
      } as ClientInfo;
    }

    // Calculate invoice totals
    const subtotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);

    // Determine due date (30 days after invoice date)
    const invoiceDate = new Date(invoice.date);
    const dueDate = new Date(invoiceDate);
    dueDate.setDate(dueDate.getDate() + 30);

    // Fetch the tax type
    const taxType = await TaxType.findById(invoice.taxTypeId).lean() as { code: string; names: string[]; percentages: number[] } | null;
    if (!taxType) {
      return NextResponse.json({ error: 'Tax type not found for this invoice' }, { status: 404 });
    }

    // Calculate total tax using billing utilities
    const BillingUtils = await import('@/lib/utils/billing-utils');
    const totalTax = BillingUtils.calculateTotalTaxes(items as any, taxType).reduce((sum, tax) => sum + tax, 0);

    // Generate the PDF
    const invoiceDetails: InvoiceDetails = {
      invoiceNumber: invoice.invoiceNumber,
      date: invoice.date,
      dueDate,
      status: invoice.status,
      clientInfo,
      taxType: {
        code: taxType.code,
        names: taxType.names,
        percentages: taxType.percentages,
      },
      // Include TPS and QST registration numbers from the invoice data if they exist
      tpsRegistrationNumber: (invoice as any).tpsRegistrationNumber,
      qstRegistrationNumber: (invoice as any).qstRegistrationNumber
    };

    // Ensure token consistency between invoice.signingToken and InvoiceSigningToken.token
    const userId = user._id.toString();
    const { token: tokenToUse, isNewToken } = await ensureTokenConsistency(invoiceId, userId);
    
    console.log(`[Email Send] ${isNewToken ? 'Created new' : 'Using existing'} token for invoice ${invoiceId}, user ${userId}`);

    // Generate the signing URL using the consistent token
    const signingUrl = generateInvoiceSigningUrl(tokenToUse);

    // Add signing link to email content if it doesn't already have a variable for it
    if (!emailContent.includes('{{signingLink}}')) {
      emailContent += '<p>You can view and sign your invoice at: <a href="{{signingLink}}">{{signingLink}}</a></p>';
    }

    // Note: PDF generation removed for signing emails to avoid attachments

    // --- CONTRE-FACTURE GENERATION ---
    // Always regenerate contre-facture (overwrite any existing one)
    // Fetch all item types for these items
    const InvoiceItemType = (await import('@/models/InvoiceItemType')).default;
    const itemTypeIds = items.map((item: any) => item.invoiceitemtypeId);
    const itemTypes = await InvoiceItemType.find({ _id: { $in: itemTypeIds } });
    const itemTypeMap = Object.fromEntries(itemTypes.map((type: any) => [String(type._id), type]));
    // Filter items that require contre-facture
    const contreFactureItems = items.filter((item: any) => {
      const type = itemTypeMap[item.invoiceitemtypeId];
      return type && type.requiresContreFacture;
    }).map((item: any) => {
      const type = itemTypeMap[item.invoiceitemtypeId];
      return {
        ...item,
        title: "LOCATION D\'ESPACE",
        unitPrice: (type as any).chargeAmount,
        chargeAmount: (type as any).chargeAmount,
        taxable: true, // Contre facture items are ALWAYS taxable
      };
    });
    let contreFacture = null;
    if (contreFactureItems.length) {
      // Fetch the 'amq' tax type (instead of 'no_tax')
      const amqTaxType = await TaxType.findOne({ code: 'amq' }).lean() as { _id: any, names: string[], percentages: number[] } | null;
      if (!amqTaxType || !amqTaxType._id) {
        return NextResponse.json({ error: "'amq' tax type not found" }, { status: 500 });
      }
      // Calculate taxes and total
      const subtotal = contreFactureItems.reduce((sum, item) => sum + (item.unitPrice * item.quantity), 0);
      // For contre factures, taxes are ALWAYS calculated on ALL items regardless of taxable flag
      const totalTaxes = amqTaxType.percentages.map((rate) =>
        contreFactureItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice * (rate / 100)), 0)
      );
      const total = subtotal + totalTaxes.reduce((sum, t) => sum + t, 0);
      contreFacture = {
        userId: invoice.userId,
        invoiceNumber: invoice.invoiceNumber + '-CF',
        status: 'nouveau',
        taxTypeId: amqTaxType._id,
        title: invoice.title + ' (Contre-Facture)',
        date: new Date(),
        items: contreFactureItems,
        subtotal,
        totalTaxes,
        total,
        startDate: invoice.startDate,
        endDate: invoice.endDate,
        // Add AMQ tax registration numbers
        tpsRegistrationNumber: '783000151RT001',
        qstRegistrationNumber: '1231059977TQ0001'
      };
      // Always save (overwrite) contre-facture to invoice
      // Use findById and save to trigger pre-save hooks
      const invoiceForContreFacture = await Invoice.findById(invoiceId);
      if (invoiceForContreFacture) {
        invoiceForContreFacture.contreFacture = contreFacture;
        await invoiceForContreFacture.save();
      }
    }
    let contreFacturePdfBase64: string | null = null;
    if (contreFacture && contreFacture.items && contreFacture.items.length) {
      // Fetch contre-facture tax type
      const contreFactureTaxType = await TaxType.findById(contreFacture.taxTypeId).lean();
      if (contreFactureTaxType) {
        // Fetch client info (original beneficiary)
        let contreFactureClientInfo = clientInfo;
        if (!contreFactureClientInfo && user) {
          contreFactureClientInfo = {
            name: user.name || 'Client',
            companyName: (user as any).companyName || '',
            phone: typeof user.phone === 'string' ? user.phone : ''
          };
        }
        // Build invoiceDetails for contre-facture
        const contreFactureDetails = {
          invoiceNumber: contreFacture.invoiceNumber,
          date: contreFacture.date,
          dueDate: contreFacture.endDate || undefined,
          status: contreFacture.status,
          clientInfo: contreFactureClientInfo,
          taxType: {
            code: (contreFactureTaxType as any).code,
            names: (contreFactureTaxType as any).names,
            percentages: (contreFactureTaxType as any).percentages,
          },
          // Include AMQ tax registration numbers for contre-facture
          tpsRegistrationNumber: contreFacture.tpsRegistrationNumber,
          qstRegistrationNumber: contreFacture.qstRegistrationNumber
        };
        // Use same company info as UI
         const companyInfo = {
      name: "Alimentation Mon Quartier",
      address: "200 rue Principale, local 8",
      city: "St-Sauveur",
      province: "Québec",
      postalCode: "J0R 1R0",
      phone: "**************",
      email: "<EMAIL>",
    };
        // Prepare contre-facture details for HTML PDF generation
        const htmlContreFactureDetails = {
          ...contreFactureDetails,
          total: contreFacture.total,
          subtotal: contreFacture.subtotal,
          totalTaxes: contreFacture.totalTaxes,
          issuerInfo: {
            name: "Alimentation Mon Quartier",
            address: "200 rue Principale, local 8",
            city: "St-Sauveur",
            province: "Québec",
            postalCode: "J0R 1R0",
            phone: "**************"
          }
        };

        // Tax type for contre-facture
        const contreFactureTaxTypeFormatted = {
          code: (contreFactureTaxType as any).code,
          names: (contreFactureTaxType as any).names,
          percentages: (contreFactureTaxType as any).percentages,
        };

        // Generate contre-facture PDF using HTML template with swapIssuerAndClient: true
        const contreFacturePdfDataUrl = await generateInvoicePDFDataURL(
          contreFacture.items,
          htmlContreFactureDetails,
          companyInfo,
          contreFactureTaxTypeFormatted,
          true // swapIssuerAndClient
        );
        contreFacturePdfBase64 = contreFacturePdfDataUrl.split(',')[1];
      }
    }

    // Send email without PDF attachments for signing emails
    await sendBrevoTemplatedEmail({
      to: [{ email: user.email, name: user.name || 'Client' }],
      subject: emailSubject,
      content: emailContent,
      variables: {
        name: user.name || 'Client',
        invoiceNumber: invoice.invoiceNumber,
        invoiceDate: new Date(invoice.date).toLocaleDateString('fr-CA', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        invoiceTotal: (subtotal + totalTax).toFixed(2),
        signingLink: signingUrl
      }
      // No attachments for signing emails
    });

    // Fix any existing contre-facture with invalid status before saving
    if (invoice.contreFacture) {
      const validStatuses = ['nouveau', 'verifie', 'envoye', 'signe', 'en_traitement', 'paye', 'en_retard'];
      if (!validStatuses.includes(invoice.contreFacture.status)) {
        console.log(`[Email Send] Fixing invalid contre-facture status from '${invoice.contreFacture.status}' to 'nouveau' for invoice ${invoice._id}`);
        invoice.contreFacture.status = 'nouveau';
      }
    }

    // Update invoice status to "Envoyé" only if current status is before "envoye" in flow order
    const oldStatus = invoice.status;
    const shouldUpdateStatus = isValidStatusTransition(oldStatus, 'envoye');

    if (shouldUpdateStatus) {
      invoice.addStatusChange('envoye', session.user.id, false, 'Email sent');
      await invoice.save();

      // Log audit event for status change
      await InvoiceAuditLogger.logInvoiceStatusChanged(
        request,
        invoice._id.toString(),
        invoice.invoiceNumber,
        oldStatus,
        'envoye',
        session
      );
    } else {
      // Just save the invoice without status change
      await invoice.save();
      console.log(`[Email Send] Skipped status change for invoice ${invoice.invoiceNumber} - current status '${oldStatus}' cannot transition to 'envoye'`);
    }

    return NextResponse.json({
      success: true,
      message: `Email sent to ${user.email} (with contre-facture${contreFacturePdfBase64 ? '' : ' (not generated)'})`,
      signingUrl,
      contreFactureAttached: !!contreFacturePdfBase64
    });
  } catch (error) {
    console.error('Error sending invoice email:', error);
    return NextResponse.json({
      error: 'Failed to send invoice email',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}