import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserManageBilling } from '@/lib/utils/permissions-utils';
import Invoice from '@/models/Invoice';
import { InvoiceAuditLogger } from '@/lib/utils/audit-utils';
import { isValidStatusTransition, getValidNextStatuses, getStatusInfo } from '@/lib/utils/invoice-status-utils';
import dbConnect from '@/lib/db';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';

export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();

  const session = await getServerSession(authOptions);
  if(!session){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(!session.user.permissions){
    session.user.permissions=await getUserPermissions(session);
  }
  if (!canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { status, reason } = await request.json();

    if (!status) {
      return NextResponse.json({ error: 'Status is required' }, { status: 400 });
    }

    const invoice = await Invoice.findById(params.id);
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    const oldStatus = invoice.status;

    // Validate status transition
    if (!isValidStatusTransition(oldStatus, status)) {
      const currentStatusInfo = getStatusInfo(oldStatus);
      const targetStatusInfo = getStatusInfo(status);
      const validNextStatuses = getValidNextStatuses(oldStatus);

      let userFriendlyMessage = `Impossible de passer du statut "${currentStatusInfo.label}" au statut "${targetStatusInfo.label}".`;

      if (validNextStatuses.length > 0) {
        const validStatusLabels = validNextStatuses.map(s => getStatusInfo(s).label).join(', ');
        userFriendlyMessage += ` Les statuts autorisés depuis "${currentStatusInfo.label}" sont : ${validStatusLabels}.`;
      } else {
        userFriendlyMessage += ` Le statut "${currentStatusInfo.label}" ne peut plus être modifié.`;
      }

      return NextResponse.json({
        error: userFriendlyMessage,
        details: {
          currentStatus: oldStatus,
          targetStatus: status,
          validNextStatuses: validNextStatuses
        }
      }, { status: 400 });
    }

    // Fix any existing contre-facture with invalid status before saving
    if (invoice.contreFacture && invoice.contreFacture.status === 'new') {
      console.log(`[Status Update] Fixing contre-facture status from 'new' to 'nouveau' for invoice ${invoice._id}`);
      invoice.contreFacture.status = 'nouveau';
    }

    // Update invoice status
    invoice.addStatusChange(status, session.user.id, false, reason);
    await invoice.save();

    // Log audit event
    await InvoiceAuditLogger.logInvoiceStatusChanged(
      request,
      invoice._id.toString(),
      invoice.invoiceNumber,
      oldStatus,
      status,
      session
    );

    return NextResponse.json({
      success: true,
      invoice: {
        id: invoice._id,
        status: invoice.status,
        statusHistory: invoice.statusHistory
      }
    });
  } catch (error) {
    console.error('Status update failed:', error);
    return NextResponse.json({
      error: 'Status update failed'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();

  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const invoice = await Invoice.findById(params.id)
      .select('status statusHistory invoiceNumber')
      .populate('statusHistory.changedBy', 'name email');

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    return NextResponse.json({
      invoiceNumber: invoice.invoiceNumber,
      currentStatus: invoice.status,
      statusHistory: invoice.statusHistory
    });
  } catch (error) {
    console.error('Status fetch failed:', error);
    return NextResponse.json({
      error: 'Failed to fetch status'
    }, { status: 500 });
  }
}
