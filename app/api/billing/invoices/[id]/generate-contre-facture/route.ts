import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import InvoiceItem from '@/models/InvoiceItem';
import InvoiceItemType from '@/models/InvoiceItemType';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserManageBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import TaxType from '@/models/TaxType';

// POST: Generate contre-facture for an invoice
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const p = await params;
    const invoice = await Invoice.findById(p.id);
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }
    // Fetch all items for this invoice
    const items = await InvoiceItem.find({ _id: { $in: invoice.manualItems } });
    if (!items.length) {
      return NextResponse.json({ error: 'No items found for invoice' }, { status: 400 });
    }
    // Fetch all item types for these items
    const itemTypeIds = items.map((item: any) => item.invoiceitemtypeId);
    const itemTypes = await InvoiceItemType.find({ _id: { $in: itemTypeIds } });
    // Map itemTypeId to type
    const itemTypeMap = Object.fromEntries(itemTypes.map((type: any) => [String(type._id), type]));
    console.log('itemTypeMap', itemTypeMap);
    // Filter items that require contre-facture
    const contreFactureItems = items.filter((item: any) => {
      const type = itemTypeMap[item.invoiceitemtypeId];
      console.log('type', type);
      return type && type.requiresContreFacture;
    }).map((item: any) => {
      const type = itemTypeMap[item.invoiceitemtypeId];
      return {
        ...item.toObject(),
        title: 'LOCATION D\'ESPACE',
        unitPrice: type.chargeAmount,
        chargeAmount: type.chargeAmount,
        taxable: true, // Contre facture items are ALWAYS taxable
      };
    });
    if (!contreFactureItems.length) {
      return NextResponse.json({ error: 'No items require contre-facture' }, { status: 400 });
    }
    // Fetch the 'amq' tax type (instead of 'no_tax')
    const amqTaxType = await TaxType.findOne({ code: 'amq' }).lean() as { _id: any, names: string[], percentages: number[] } | null;
    if (!amqTaxType || !amqTaxType._id) {
      return NextResponse.json({ error: "'amq' tax type not found" }, { status: 500 });
    }
    // Calculate taxes and total
    const subtotal = contreFactureItems.reduce((sum: number, item: any) => sum + (item.unitPrice * item.quantity), 0);
    // For contre factures, taxes are ALWAYS calculated on ALL items regardless of taxable flag
    const totalTaxes = amqTaxType.percentages.map((rate, idx) =>
      contreFactureItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice * (rate / 100)), 0)
    );
    const total = subtotal + totalTaxes.reduce((sum, t) => sum + t, 0);
    // Build contre-facture object
    const contreFacture = {
      userId: invoice.userId, // original beneficiary (client)
      invoiceNumber: invoice.invoiceNumber + '-CF',
      status: 'nouveau',
      taxTypeId: amqTaxType._id,
      title: invoice.title + ' (Contre-Facture)',
      date: new Date(),
      items: contreFactureItems,
      subtotal,
      totalTaxes,
      total,
      startDate: invoice.startDate,
      endDate: invoice.endDate,
      // Add AMQ tax registration numbers
      tpsRegistrationNumber: '783000151RT001',
      qstRegistrationNumber: '1231059977TQ0001'
    };
    // Save contre-facture to invoice
    invoice.contreFacture = contreFacture;

    // Fix any existing contre-facture with invalid status before saving
    if (invoice.contreFacture && invoice.contreFacture.status === 'new') {
      console.log(`[Generate Contre-Facture] Fixing contre-facture status from 'new' to 'nouveau' for invoice ${invoice._id}`);
      invoice.contreFacture.status = 'nouveau';
    }

    await invoice.save();
    return NextResponse.json({ contreFacture });
  } catch (error) {
    console.error('Generate contre-facture error:', error);
    return NextResponse.json({ error: 'Failed to generate contre-facture' }, { status: 500 });
  }
}