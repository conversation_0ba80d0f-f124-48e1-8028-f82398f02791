import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import { getToken } from 'next-auth/jwt';
import * as RoleUtils from '@/lib/utils/role-utils';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';
import { canUserAccessBilling, canUserManageBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import * as BillingUtils from '@/lib/utils/billing-utils';
import TaxType from '@/models/TaxType';
import User from '@/models/User';
import { determineTaxTypeForInvoice } from '../../../utils/tax-validation';
import { InvoiceAuditLogger } from '@/lib/utils/audit-utils';
import { shouldItemsBeTaxable } from '../../../utils/invoice-item-utils';
import InvoiceItem from '@/models/InvoiceItem';

// GET: Fetch a single invoice by ID (admin only)
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session || !canUserAccessBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const p=await params;
    const invoice = await Invoice.findById(p.id);
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Return invoice with calculated totals instead of stored values
    const invoiceWithTotals = await BillingUtils.getInvoiceWithCalculatedTotals(invoice);
    return NextResponse.json(invoiceWithTotals);
  } catch (error) {
    console.error('Error fetching invoice:', error);
    return NextResponse.json({ error: 'Failed to fetch invoice' }, { status: 500 });
  }
}

// PATCH: Update an invoice by ID (admin only)
export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const body = await request.json();
    const p = await params;
    // Find the invoice
    const invoice = await Invoice.findById(p.id);
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Store original data for audit logging
    const originalData = invoice.toObject();

    // Update allowed fields
    if (body.title !== undefined) invoice.title = body.title;
    if (body.date !== undefined) invoice.date = new Date(body.date);
    if (body.status !== undefined) invoice.status = body.status;
    if (body.userId !== undefined) invoice.userId = body.userId;
    if (body.taxTypeId !== undefined) invoice.taxTypeId = body.taxTypeId;
    if (body.startDate !== undefined) invoice.startDate = body.startDate ? new Date(body.startDate) : undefined;
    if (body.endDate !== undefined) invoice.endDate = body.endDate ? new Date(body.endDate) : undefined;

    // Handle tax number population and validation if taxTypeId or userId is being updated
    const taxTypeChanged = body.taxTypeId !== undefined;
    const userChanged = body.userId !== undefined;

    if (taxTypeChanged || userChanged) {
      try {
        // Use the current or updated values
        const currentTaxTypeId = body.taxTypeId !== undefined ? body.taxTypeId : invoice.taxTypeId;
        const currentUserId = body.userId !== undefined ? body.userId : invoice.userId;

        if (currentTaxTypeId && currentUserId) {
          // Fetch the tax type to check if it's tpstvq
          const taxType = await TaxType.findById(currentTaxTypeId).lean();

          if (taxType && !Array.isArray(taxType) && (taxType as any).code === 'tpstvq') {
            console.log(`Processing TPSTVQ tax type for invoice update`);

            // Fetch the user's complete tax info
            const user = await User.findById(currentUserId).lean();

            if (user && !Array.isArray(user) && (user as any).taxInfo) {
              // Use the tax validation logic to determine the appropriate tax type
              const taxDecision = await determineTaxTypeForInvoice(user, taxType);
              invoice.invalidTaxInfo = taxDecision.invalidTaxInfo;

              // Add QST registration number (full format already stored in database)
              if ((user as any).taxInfo.qstRegistrationNumber) {
                invoice.qstRegistrationNumber = (user as any).taxInfo.qstRegistrationNumber;
                console.log(`Added QST registration number to invoice: ${invoice.qstRegistrationNumber}`);
              } else {
                invoice.qstRegistrationNumber = undefined;
              }

              // Add TPS registration number (full format already stored in database)
              if ((user as any).taxInfo.tpsRegistrationNumber) {
                invoice.tpsRegistrationNumber = (user as any).taxInfo.tpsRegistrationNumber;
                console.log(`Added TPS registration number to invoice: ${invoice.tpsRegistrationNumber}`);
              } else {
                invoice.tpsRegistrationNumber = undefined;
              }

              // Log validation status for reference
              const qstValidation = (user as any).taxInfo.qstValidation;
              if (qstValidation) {
                console.log(`QST validation status for user: isValid=${qstValidation.isValid}, status=${qstValidation.status}`);
              } else {
                console.log(`No QST validation data for user`);
              }
            } else {
              console.log(`No tax info found for user ${currentUserId}`);
              invoice.invalidTaxInfo = true;
              invoice.tpsRegistrationNumber = undefined;
              invoice.qstRegistrationNumber = undefined;
            }
          } else {
            console.log(`Tax type is not TPSTVQ (${(taxType as any)?.code}), clearing registration numbers`);
            // Clear tax registration numbers for non-TPSTVQ tax types
            invoice.invalidTaxInfo = false;
            invoice.tpsRegistrationNumber = undefined;
            invoice.qstRegistrationNumber = undefined;
          }
        }
      } catch (error) {
        console.error('Error processing tax information during update:', error);
        // Don't fail the invoice update, just log the error
      }
    }

    // Fix any existing contre-facture with invalid status before saving
    if (invoice.contreFacture && invoice.contreFacture.status === 'new') {
      console.log(`[Invoice Update] Fixing contre-facture status from 'new' to 'nouveau' for invoice ${invoice._id}`);
      invoice.contreFacture.status = 'nouveau';
    }

    // Save the updated invoice
    await invoice.save();

    // If tax type changed, update all related invoice items' taxable status
    if (taxTypeChanged) {
      try {
        const newTaxableStatus = await shouldItemsBeTaxable(invoice.taxTypeId?.toString());

        // Update all manual items for this invoice
        if (invoice.manualItems && invoice.manualItems.length > 0) {
          await InvoiceItem.updateMany(
            { _id: { $in: invoice.manualItems } },
            { $set: { taxable: newTaxableStatus } }
          );

          console.log(`Updated ${invoice.manualItems.length} invoice items' taxable status to ${newTaxableStatus} for invoice ${invoice.invoiceNumber}`);
        }
      } catch (error) {
        console.error('Error updating invoice items taxable status:', error);
        // Don't fail the invoice update, just log the error
      }
    }

    // Log audit event
    await InvoiceAuditLogger.logInvoiceUpdated(
      request,
      invoice._id.toString(),
      invoice.invoiceNumber,
      originalData,
      invoice.toObject(),
      session
    );

    // Log status change separately if status was updated
    if (body.status !== undefined && originalData.status !== body.status) {
      await InvoiceAuditLogger.logInvoiceStatusChanged(
        request,
        invoice._id.toString(),
        invoice.invoiceNumber,
        originalData.status,
        body.status,
        session
      );
    }

    return NextResponse.json(invoice);
  } catch (error) {
    console.error('Update invoice error:', error);
    return NextResponse.json({ error: 'Failed to update invoice' }, { status: 500 });
  }
}

// DELETE: Soft-delete an invoice by ID (admin only)
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const p = await params;
    // Soft delete by setting deletedAt and deletedBy
    const invoice = await Invoice.findById(p.id);
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Get the user ID from the session
    const user = session?.user as any;
    const userId = user?.id || user?._id;

    invoice.deletedAt = new Date();
    invoice.deletedBy = userId;
    await invoice.save();

    // Log audit event
    const { logAuditEvent } = await import('@/lib/utils/audit-utils');
    await logAuditEvent(request, {
      action: 'INVOICE_DELETED',
      entityType: 'Invoice',
      entityId: invoice._id.toString(),
      entityName: invoice.invoiceNumber,
      description: `Invoice ${invoice.invoiceNumber} deleted (soft delete)`,
      metadata: {
        deletedAt: invoice.deletedAt
      }
    }, session);

    return NextResponse.json({ message: 'Invoice deleted successfully' });
  } catch (error) {
    console.error('Delete invoice error:', error);
    return NextResponse.json({ error: 'Failed to delete invoice' }, { status: 500 });
  }
}