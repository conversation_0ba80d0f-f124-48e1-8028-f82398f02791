import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import { getToken } from 'next-auth/jwt';
import * as RoleUtils from '@/lib/utils/role-utils';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';
import { canUserAccessBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import * as BillingUtils from '@/lib/utils/billing-utils';

// GET: Fetch invoice by invoice number (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: { invoiceNumber: string } }
) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session || !canUserAccessBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const p=await params;
  try {
    const invoice = await Invoice.findOne({ 
      invoiceNumber: p.invoiceNumber,
      deletedAt: { $exists: false }
    });
    
    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }
    
    // Get invoice with calculated totals
    const invoiceWithTotals = await BillingUtils.getInvoiceWithCalculatedTotals(invoice);
    if (invoiceWithTotals.commissions) delete invoiceWithTotals.commissions;
    
    return NextResponse.json(invoiceWithTotals);
  } catch (error) {
    console.error('Get invoice by number error:', error);
    return NextResponse.json({ error: 'Failed to fetch invoice' }, { status: 500 });
  }
} 