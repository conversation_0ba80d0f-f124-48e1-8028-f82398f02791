import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserManageBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { logAuditEvent } from '@/lib/utils/audit-utils';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    // Check authorization
    const session = await getServerSession(authOptions);
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    if (!session || !canUserManageBilling(session.user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { invoiceIds } = await request.json();

    if (!Array.isArray(invoiceIds) || invoiceIds.length === 0) {
      return NextResponse.json({ error: 'No invoice IDs provided' }, { status: 400 });
    }

    // Get the user ID from the session
    const user = session?.user as any;
    const userId = user?.id || user?._id;

    if (!userId) {
      return NextResponse.json({ error: 'User ID not found in session' }, { status: 500 });
    }

    // Fetch invoices to verify they exist and aren't already deleted
    const invoices = await Invoice.find({ 
      _id: { $in: invoiceIds },
      deletedAt: { $exists: false } // Only get non-deleted invoices
    });

    if (invoices.length === 0) {
      return NextResponse.json({ error: 'No valid invoices found to delete' }, { status: 404 });
    }

    // Perform mass soft delete
    const result = await Invoice.updateMany(
      { 
        _id: { $in: invoices.map(inv => inv._id) },
        deletedAt: { $exists: false } // Ensure we don't update already deleted invoices
      },
      {
        deletedAt: new Date(),
        deletedBy: userId
      }
    );

    // Log audit events for each deleted invoice
    for (const invoice of invoices) {
      await logAuditEvent(request, {
        action: 'INVOICE_DELETED',
        entityType: 'Invoice',
        entityId: invoice._id.toString(),
        entityName: invoice.invoiceNumber,
        description: `Invoice ${invoice.invoiceNumber} deleted (mass delete)`,
        metadata: {
          deletedAt: new Date(),
          deletedBy: userId,
          massOperation: true
        }
      }, session);
    }

    // Log the mass operation itself
    await logAuditEvent(request, {
      action: 'MASS_INVOICE_DELETE',
      entityType: 'System',
      description: `Mass delete operation completed for ${result.modifiedCount} invoices`,
      metadata: {
        invoiceIds: invoices.map(inv => inv._id.toString()),
        requestedCount: invoiceIds.length,
        deletedCount: result.modifiedCount,
        deletedBy: userId
      }
    }, session);

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${result.modifiedCount} invoice(s)`,
      deletedCount: result.modifiedCount,
      requestedCount: invoiceIds.length
    });

  } catch (error) {
    console.error('Error in mass delete invoices:', error);
    return NextResponse.json(
      { error: 'Failed to delete invoices' },
      { status: 500 }
    );
  }
}
