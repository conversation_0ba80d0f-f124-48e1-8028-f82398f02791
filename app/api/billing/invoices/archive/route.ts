import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../../lib/db';
import Invoice from '@/models/Invoice';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserManageBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '../../../utils/server-permission-utils';

export async function POST(request: NextRequest) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
    const { invoiceIds, archive } = await request.json();
    
    if (!Array.isArray(invoiceIds) || invoiceIds.length === 0) {
      return NextResponse.json({ error: 'No invoice IDs provided' }, { status: 400 });
    }
    
    // Update the archived status of the invoices
    const updateData = archive 
      ? { archivedAt: new Date() } 
      : { $unset: { archivedAt: "" } };
    
    const result = await Invoice.updateMany(
      { _id: { $in: invoiceIds } },
      updateData
    );
    
    return NextResponse.json({
      success: true,
      message: archive ? 'Invoices archived successfully' : 'Invoices unarchived successfully',
      count: result.modifiedCount,
    });
  } catch (error) {
    console.error('Error archiving/unarchiving invoices:', error);
    return NextResponse.json({ error: 'Failed to process archive operation' }, { status: 500 });
  }
} 