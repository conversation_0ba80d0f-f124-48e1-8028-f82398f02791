import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserManageBilling } from '@/lib/utils/permissions-utils';
import Invoice from '@/models/Invoice';
import { InvoiceAuditLogger } from '@/lib/utils/audit-utils';
import { isValidStatusTransition, getStatusInfo } from '@/lib/utils/invoice-status-utils';
import dbConnect from '@/lib/db';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { getUserRoles } from '@/app/api/utils/server-permission-utils';
export async function PATCH(request: NextRequest) {
  await dbConnect();

  const session = await getServerSession(authOptions);
  if(!session){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(!session.user.permissions){
    session.user.permissions=await getUserPermissions(session);
  }
  if(!session.user.roles){
    session.user.roles=await getUserRoles(session);
  }
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { invoiceIds, status, reason } = await request.json();

    if (!Array.isArray(invoiceIds) || invoiceIds.length === 0) {
      return NextResponse.json({ error: 'Invoice IDs are required' }, { status: 400 });
    }

    if (!status) {
      return NextResponse.json({ error: 'Status is required' }, { status: 400 });
    }

    const invoices = await Invoice.find({ _id: { $in: invoiceIds } });
    const results = [];

    for (const invoice of invoices) {
      try {
        const oldStatus = invoice.status;

        // Fix any existing contre-facture with invalid status before saving
        if (invoice.contreFacture && invoice.contreFacture.status === 'new') {
          console.log(`[Bulk Status Update] Fixing contre-facture status from 'new' to 'nouveau' for invoice ${invoice._id}`);
          invoice.contreFacture.status = 'nouveau';
        }

        if (isValidStatusTransition(oldStatus, status)) {
          invoice.addStatusChange(status, session.user.id, false, reason);
          await invoice.save();

          await InvoiceAuditLogger.logInvoiceStatusChanged(
            request,
            invoice._id.toString(),
            invoice.invoiceNumber,
            oldStatus,
            status,
            session
          );

          results.push({
            id: invoice._id,
            invoiceNumber: invoice.invoiceNumber,
            success: true,
            oldStatus,
            newStatus: status
          });
        } else {
          const currentStatusInfo = getStatusInfo(oldStatus);
          const targetStatusInfo = getStatusInfo(status);
          const userFriendlyMessage = `Impossible de passer du statut "${currentStatusInfo.label}" au statut "${targetStatusInfo.label}"`;

          results.push({
            id: invoice._id,
            invoiceNumber: invoice.invoiceNumber,
            success: false,
            error: userFriendlyMessage,
            oldStatus
          });
        }
      } catch (error) {
        results.push({
          id: invoice._id,
          invoiceNumber: invoice.invoiceNumber,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          oldStatus: invoice.status
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return NextResponse.json({
      success: true,
      message: `Updated ${successCount} invoices successfully${failureCount > 0 ? `, ${failureCount} failed` : ''}`,
      results
    });
  } catch (error) {
    console.error('Bulk status update failed:', error);
    return NextResponse.json({
      error: 'Bulk status update failed'
    }, { status: 500 });
  }
}
