import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/db';
import Invoice from '@/models/Invoice';
import { getToken } from 'next-auth/jwt';
import * as RoleUtils from '@/lib/utils/role-utils';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth';
import { canUserAccessBilling, canUserManageBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions, getUserRoles } from '../../utils/server-permission-utils';
import * as BillingUtils from '@/lib/utils/billing-utils';
import TaxType from '@/models/TaxType';
import User from '@/models/User';
import { determineTaxTypeForInvoice } from '../../utils/tax-validation';
import { InvoiceAuditLogger } from '@/lib/utils/audit-utils';

export async function GET(request: NextRequest) {
  await dbConnect();
  const session=await getServerSession(authOptions)
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session || !canUserAccessBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    // Pagination support
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const skip = (page - 1) * limit;

    // Get filter parameters
    const status = searchParams.get('status');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const search = searchParams.get('search');

    // Get sorting parameters
    const sortField = searchParams.get('sortField') || 'createdAt';
    const sortDirection = searchParams.get('sortDirection') || 'desc';

    // Build sort object for MongoDB
    const sort: Record<string, 1 | -1> = {
      [sortField]: sortDirection === 'asc' ? 1 : -1
    };

    // Build the filter object
    const filter: any = {};
    const conditions: any[] = [];

    // Handle deleted filter
    const showDeleted = searchParams.get('showDeleted') === 'true';
    if (showDeleted) {
      // When showDeleted is true, only show deleted invoices
      conditions.push({
        deletedAt: { $exists: true, $ne: null }
      });
    } else {
      // By default, only show non-deleted invoices
      conditions.push({
        $or: [
          { deletedAt: { $exists: false } },
          { deletedAt: null }
        ]
      });
    }

    // Handle archived filter
    const showArchived = searchParams.get('showArchived') === 'true';
    if (showArchived) {
      // When showArchived is true, only show archived invoices
      conditions.push({
        archivedAt: { $exists: true, $ne: null }
      });
    } else {
      // By default, only show non-archived invoices
      conditions.push({
        $or: [
          { archivedAt: { $exists: false } },
          { archivedAt: null }
        ]
      });
    }

    // Add status filter if provided
    if (status) {
      conditions.push({ status });
    }

    // Add date range filters if provided
    if (startDate || endDate) {
      const dateFilter: any = {};

      // Filter by invoice creation date (createdAt)
      if (startDate) {
        dateFilter.$gte = new Date(startDate);
      }
      if (endDate) {
        const endDateObj = new Date(endDate);
        endDateObj.setHours(23, 59, 59, 999); // End of the day
        dateFilter.$lte = endDateObj;
      }

      if (Object.keys(dateFilter).length > 0) {
        conditions.push({ createdAt: dateFilter });
      }
    }

    // Add search filter if provided
    if (search && search.trim() !== '') {
      const searchTerm = search.trim();
      const searchRegex = new RegExp(searchTerm, 'i');

      // First, find users whose names match the search term
      const User = (await import('@/models/User')).default;
      const matchingUsers = await User.find({
        name: searchRegex,
        deletedAt: null
      }).select('_id').lean();
      const matchingUserIds = matchingUsers.map(user => user._id);

      conditions.push({
        $or: [
          { title: searchRegex },
          { invoiceNumber: searchRegex },
          { userId: { $in: matchingUserIds } } // Search by beneficiary (user name)
        ]
      });
    }

    // Handle signed filter - add to conditions array for proper intersection
    const signed = request.nextUrl?.searchParams?.get('signed');
    if (signed === 'signed') {
      conditions.push({ $or: [ { signedAt: { $ne: null } }, { signatureData: { $ne: null } } ] });
    } else if (signed === 'not_signed') {
      conditions.push({ $and: [ { $or: [ { signedAt: null }, { signedAt: { $exists: false } } ] }, { $or: [ { signatureData: null }, { signatureData: { $exists: false } } ] } ] });
    }

    // Combine all conditions with $and for proper intersection of all filters
    if (conditions.length > 0) {
      filter.$and = conditions;
    }

    // Use the filter directly (no separate merging needed)
    const mergedFilter = filter;

    // Execute query with filters and sorting
    let invoices: any[] = [];
    let total = await Invoice.countDocuments(mergedFilter);
    if (sortField === 'isSigned') {
      // For signed sorting, fetch all filtered invoices, then sort and paginate in-memory
      invoices = await Invoice.find(mergedFilter);
    } else {
      // For all other sorts, use DB-side sort/pagination
      invoices = await Invoice.find(mergedFilter)
        .sort(sort)
        .skip(skip)
        .limit(limit);
    }

    // Calculate totals for each invoice
    const invoicesWithTotals = await Promise.all(
      invoices.map(invoice => BillingUtils.getInvoiceWithCalculatedTotals(invoice))
    );

    // Collect all unique userIds from invoices
    const userIds = Array.from(new Set<string>(invoicesWithTotals.map(inv => inv.userId).filter(Boolean)));

    // Fetch all users in one query (only needed fields)
    let usersMap: Record<string, { _id: string; name: string; email: string }> = {};
    if (userIds.length > 0) {
      const User = (await import('@/models/User')).default;
      const users = await User.find({ _id: { $in: userIds } })
        .select('_id name email')
        .lean();
      usersMap = users.reduce((acc: any, user: any) => {
        acc[user._id.toString()] = {
          _id: user._id.toString(),
          name: user.name,
          email: user.email,
        };
        return acc;
      }, {});
    }

    // Attach user object to each invoice
    const invoicesWithUser = invoicesWithTotals.map(inv => ({
      ...inv,
      user: inv.userId ? usersMap[inv.userId.toString()] || null : null,
      taxType: inv.taxType,
      totalTaxes: inv.totalTaxes,
    }));

    // --- Calculate startDate/endDate from items if missing ---
    // Find invoices missing startDate or endDate
    const invoicesNeedingDates = invoicesWithUser.filter(inv => !inv.startDate || !inv.endDate);
    let manualItemsMap: Record<string, any[]> = {};
    if (invoicesNeedingDates.length > 0) {
      // Collect all manualItems ObjectIds
      const allItemIds = invoicesNeedingDates.flatMap(inv => inv.manualItems || []);
      if (allItemIds.length > 0) {
        const InvoiceItem = (await import('@/models/InvoiceItem')).default;
        // Fetch all items in one query
        const allItems = await InvoiceItem.find({ _id: { $in: allItemIds } }).select('date _id');
        // Group items by invoice (by which invoice's manualItems contains the item)
        for (const inv of invoicesNeedingDates) {
          const itemObjs = allItems.filter(item => (inv.manualItems || []).some((id: any) => id.toString() === item._id.toString()));
          manualItemsMap[inv._id.toString()] = itemObjs;
        }
      }
    }
    // Set calculated startDate/endDate if missing
    for (const inv of invoicesWithUser) {
      if ((!inv.startDate || !inv.endDate) && manualItemsMap[inv._id.toString()] && manualItemsMap[inv._id.toString()].length > 0) {
        const dates = manualItemsMap[inv._id.toString()].map(item => item.date).filter(Boolean).map(d => new Date(d));
        if (dates.length > 0) {
          const minDate = new Date(Math.min(...dates.map(d => d.getTime())));
          const maxDate = new Date(Math.max(...dates.map(d => d.getTime())));
          if (!inv.startDate) inv.startDate = minDate;
          if (!inv.endDate) inv.endDate = maxDate;
        }
      }
    }
    let finalInvoices = invoicesWithUser;
    if (sortField === 'isSigned') {
      finalInvoices = [...invoicesWithUser].sort((a, b) => {
        const aSigned = !!(a.signedAt || a.signatureData);
        const bSigned = !!(b.signedAt || b.signatureData);
        if (aSigned === bSigned) return 0;
        if (sortDirection === 'asc') {
          // Non-signed first
          return aSigned ? 1 : -1;
        } else {
          // Signed first
          return aSigned ? -1 : 1;
        }
      });
      // Apply pagination in-memory
      finalInvoices = finalInvoices.slice(skip, skip + limit);
    }

    return NextResponse.json({ invoices: finalInvoices, total });
  } catch (error) {
    console.error('Error fetching invoices:', error);
    return NextResponse.json({ error: 'Failed to fetch invoices' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if(session &&!session?.user.roles){
    session.user.roles = await getUserRoles(session);
  }
  if (!session || !canUserManageBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const body = await request.json();

    // Get the createdBy ID from the session
    const user = session?.user as any;
    const userId = user?.id || user?._id;

    if (!userId) {
      return NextResponse.json({ error: 'User ID not found in session' }, { status: 500 });
    }

    // Generate invoice number if not provided
    if (!body.invoiceNumber) {
      const today = new Date();
      const dateStr = today.getFullYear().toString() +
                     (today.getMonth() + 1).toString().padStart(2, '0') +
                     today.getDate().toString().padStart(2, '0');

      // Find the latest invoice with a similar prefix to determine the next sequential number
      const latestInvoice = await Invoice.findOne({
        invoiceNumber: { $regex: `^INV-${dateStr}-` }
      }).sort({ invoiceNumber: -1 });

      let sequentialNumber = 1;
      if (latestInvoice) {
        const parts = latestInvoice.invoiceNumber.split('-');
        if (parts.length === 3) {
          sequentialNumber = parseInt(parts[2], 10) + 1 || 1;
        }
      }

      body.invoiceNumber = `INV-${dateStr}-${sequentialNumber.toString().padStart(4, '0')}`;
    }

    // Handle tax number population and validation if taxTypeId and userId are provided
    let invalidTaxInfo = false;
    let tpsRegistrationNumber = undefined;
    let qstRegistrationNumber = undefined;

    if (body.taxTypeId && body.userId) {
      try {
        // Fetch the tax type to check if it's tpstvq
        const taxType = await TaxType.findById(body.taxTypeId).lean();

        if (taxType && !Array.isArray(taxType) && (taxType as any).code === 'tpstvq') {
          console.log(`Processing TPSTVQ tax type for invoice creation`);

          // Fetch the user's complete tax info
          const user = await User.findById(body.userId).lean();

          if (user && !Array.isArray(user) && (user as any).taxInfo) {
            // Use the tax validation logic to determine the appropriate tax type
            const taxDecision = await determineTaxTypeForInvoice(user, taxType);
            invalidTaxInfo = taxDecision.invalidTaxInfo;

            // Add QST registration number (full format already stored in database)
            if ((user as any).taxInfo.qstRegistrationNumber) {
              qstRegistrationNumber = (user as any).taxInfo.qstRegistrationNumber;
              console.log(`Added QST registration number to invoice: ${qstRegistrationNumber}`);
            }

            // Add TPS registration number (full format already stored in database)
            if ((user as any).taxInfo.tpsRegistrationNumber) {
              tpsRegistrationNumber = (user as any).taxInfo.tpsRegistrationNumber;
              console.log(`Added TPS registration number to invoice: ${tpsRegistrationNumber}`);
            }

            // Log validation status for reference
            const qstValidation = (user as any).taxInfo.qstValidation;
            if (qstValidation) {
              console.log(`QST validation status for user: isValid=${qstValidation.isValid}, status=${qstValidation.status}`);
            } else {
              console.log(`No QST validation data for user`);
            }
          } else {
            console.log(`No tax info found for user ${body.userId}`);
            invalidTaxInfo = true;
          }
        } else {
          console.log(`Tax type is not TPSTVQ (${(taxType as any)?.code}), skipping registration numbers`);
        }
      } catch (error) {
        console.error('Error processing tax information:', error);
        // Don't fail the invoice creation, just log the error
      }
    }

    // Create the invoice without setting subtotal, tax, total as these will be calculated on demand
    const invoiceData: any = {
      ...body,
      status: body.status || "nouveau",
      createdBy: userId,
      createdAt: new Date(),
      startDate: body.startDate ? new Date(body.startDate) : undefined,
      endDate: body.endDate ? new Date(body.endDate) : undefined,
      taxTypeId: body.taxTypeId,
      invalidTaxInfo,
      statusHistory: [{
        status: body.status || 'nouveau',
        changedAt: new Date(),
        changedBy: userId,
        isAutomatic: false,
        reason: 'Invoice created'
      }]
    };

    // Add tax registration numbers if they were populated
    if (tpsRegistrationNumber) {
      invoiceData.tpsRegistrationNumber = tpsRegistrationNumber;
    }
    if (qstRegistrationNumber) {
      invoiceData.qstRegistrationNumber = qstRegistrationNumber;
    }

    const invoice = await Invoice.create(invoiceData);

    // Log audit event
    await InvoiceAuditLogger.logInvoiceCreated(
      request,
      invoice._id.toString(),
      invoice.invoiceNumber,
      invoiceData,
      session
    );

    return NextResponse.json(invoice, { status: 201 });
  } catch (error: any) {
    console.error('Invoice creation error:', error);
    return NextResponse.json({ error: 'Failed to create invoice', errorDetails: error.toString() }, { status: 500 });
  }
}