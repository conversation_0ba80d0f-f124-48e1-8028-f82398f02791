import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import InvoiceItem from '@/models/InvoiceItem';
import User from '@/models/User';
import TaxType from '@/models/TaxType';
import InvoiceSigningToken from '@/models/InvoiceSigningToken';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { generateInvoicePDFDataURL } from '@/lib/utils/html-pdf-utils';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import { canUserAccessBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { randomBytes } from 'crypto';
import { InvoiceAuditLogger } from '@/lib/utils/audit-utils';
import { generateInvoiceSigningUrl } from '@/lib/utils/invoice-signing-utils';
import * as BillingUtils from '@/lib/utils/billing-utils';
import { isValidStatusTransition } from '@/lib/utils/invoice-status-utils';
import { getInvoiceEmailSubject } from '@/lib/utils/email-templates';

// Get logo URL for email embedding
function getLogoUrl(): string {
  try {
    // Use environment domain or fallback to localhost
    const domain = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const logoUrl = `${domain}/amq-logo.png`;
    console.log('Logo URL:', logoUrl);
    return logoUrl;
  } catch (error) {
    console.error('Error generating logo URL:', error);
    return '';
  }
}

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    // Check authorization
    const session = await getServerSession(authOptions);
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    if (!session || !canUserAccessBilling(session.user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { invoiceIds } = await request.json();

    if (!Array.isArray(invoiceIds) || invoiceIds.length === 0) {
      return NextResponse.json({ error: 'No invoice IDs provided' }, { status: 400 });
    }

    // Fetch all invoices with their related data (not lean, as we need to update them)
    const invoices = await Invoice.find({ _id: { $in: invoiceIds } })
      .populate('userId')
      .populate('taxTypeId');

    if (!invoices || invoices.length === 0) {
      console.log('No invoices found for IDs:', invoiceIds);
      return NextResponse.json({ error: 'No invoices found' }, { status: 404 });
    }

    console.log(`Processing ${invoices.length} invoices for email sending:`, invoices.map(inv => inv.invoiceNumber));

    // Company info
     const companyInfo = {
      name: "Alimentation Mon Quartier",
      address: "200 rue Principale, local 8",
      city: "St-Sauveur",
      province: "Québec",
      postalCode: "J0R 1R0",
      phone: "**************",
      email: "<EMAIL>",
    };

    const results = [];
    const errors = [];

    for (const invoice of invoices) {
      try {
        const user = invoice.userId;

        if (!user || !user.email) {
          errors.push({
            invoiceNumber: invoice.invoiceNumber,
            error: 'User email not found'
          });
          continue;
        }

        // Fetch invoice items using manualItems array
        let items: any[] = [];

        if (invoice.manualItems && invoice.manualItems.length > 0) {
          items = await InvoiceItem.find({ _id: { $in: invoice.manualItems } }).lean();
        }

        if (!items || items.length === 0) {
          errors.push({
            invoiceNumber: invoice.invoiceNumber,
            error: 'No invoice items found'
          });
          continue;
        }

        // Calculate totals
        const subtotal = BillingUtils.calculateSubtotal(items as any);
        const taxType = invoice.taxTypeId;
        const total = BillingUtils.calculateGrandTotalWithTaxType(items as any, taxType);

        // Find existing signing token or create a new one
        let signingToken = await InvoiceSigningToken.findOne({ 
          invoiceId: invoice._id,
          userId: user._id 
        });

        let tokenToUse: string;

        if (signingToken) {
          // Use existing token - DO NOT regenerate
          tokenToUse = signingToken.token;
          console.log(`[Mass Email Send] Using existing token for invoice ${invoice.invoiceNumber}, user ${user._id}`);
          
          // Ensure invoice.signingToken is consistent with InvoiceSigningToken.token
          if (invoice.signingToken !== tokenToUse) {
            console.log(`[Mass Email Send] Syncing invoice.signingToken with existing token for invoice ${invoice.invoiceNumber}`);
            await Invoice.findByIdAndUpdate(invoice._id, { signingToken: tokenToUse });
          }
        } else {
          // Generate a new token only if none exists
          tokenToUse = randomBytes(32).toString('hex');
          signingToken = new InvoiceSigningToken({
            invoiceId: invoice._id,
            userId: user._id,
            token: tokenToUse,
            createdAt: new Date()
          });
          await signingToken.save();
          
          // Also update the invoice.signingToken field for consistency
          await Invoice.findByIdAndUpdate(invoice._id, { signingToken: tokenToUse });
          
          console.log(`[Mass Email Send] Created new token for invoice ${invoice.invoiceNumber}, user ${user._id}`);
        }

        // Generate the signing URL using the existing or new token
        const signingUrl = generateInvoiceSigningUrl(tokenToUse);

        // Prepare invoice details for HTML PDF generation
        const invoiceDetails = {
          invoiceNumber: invoice.invoiceNumber,
          title: invoice.title,
          date: invoice.date,
          dueDate: invoice.dueDate,
          status: invoice.status,
          total: total,
          subtotal: subtotal,
          issuerInfo: {
            name: (user as any).companyName || user.name || 'Client',
            address: (user as any).address,
            city: (user as any).city,
            province: (user as any).province,
            postalCode: (user as any).postalCode,
            phone: user.phone
          },
          // Include tax registration numbers from invoice
          tpsRegistrationNumber: invoice.tpsRegistrationNumber,
          qstRegistrationNumber: invoice.qstRegistrationNumber,
        };

        // Format tax type for PDF generation
        const taxTypeFormatted = {
          code: (taxType as any).code,
          names: (taxType as any).names,
          percentages: (taxType as any).percentages,
        };

        // Check if invoice is signed and get signature data
        let signatureData = undefined;
        if (invoice.signedAt && invoice.signatureData) {
          signatureData = invoice.signatureData;
        } else {
          // Check signing token for signature data
          const InvoiceSigningToken = (await import('@/models/InvoiceSigningToken')).default;
          const signingToken = await InvoiceSigningToken.findOne({
            invoiceId: invoice._id,
            signedAt: { $exists: true }
          });
          if (signingToken && signingToken.signatureData) {
            signatureData = signingToken.signatureData;
          }
        }

        // Note: PDF generation removed for signing emails to avoid attachments

        // Get logo URL for email embedding
        const logoUrl = getLogoUrl();

        // Email content (French) - Using same template structure as individual send-email route
        let emailSubject = getInvoiceEmailSubject();
        let emailContent = `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Facture {{invoiceNumber}}</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
          }
          .email-container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          }
          .header {
            text-align: center;
            border-bottom: 3px solid #374151;
            padding-bottom: 20px;
            margin-bottom: 30px;
          }
          .company-logo {
            max-width: 200px;
            height: auto;
            margin: 0 auto 10px auto;
            display: block;
          }
          .invoice-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
          }
          .invoice-number {
            font-size: 18px;
            font-weight: bold;
            color: #374151;
          }
          .amount {
            font-size: 20px;
            font-weight: bold;
            color: #059669;
          }
          .button {
            display: inline-block;
            background-color: #dc2626 !important;
            color: white !important;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 20px 0;
          }
          .button:hover {
            background-color: #b91c1c !important;
            color: white !important;
          }
          .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="header">
            {{LOGO_PLACEHOLDER}}
            <p>200 rue Principale, local 8, St-Sauveur, QC J0R 1R0</p>
          </div>

          <p>Bonjour {{name}},</p>

          <p>Nous vous transmettons votre facture en pièce jointe.</p>

          <div class="invoice-info">
            <div class="invoice-number">Facture N° {{invoiceNumber}}</div>
            <p><strong>Date :</strong> {{invoiceDate}}</p>
            <p><strong>Montant total :</strong> <span class="amount">{{invoiceTotal}} $ CAD</span></p>
          </div>

          <p>Vous pouvez consulter et signer votre facture en ligne en cliquant sur le lien ci-dessous :</p>

          <p style="text-align: center;">
            <a href="{{signingLink}}" class="button">Consulter et signer la facture</a>
          </p>

          <p>Si vous avez des questions concernant cette facture, n'hésitez pas à nous contacter.</p>

          <div class="footer">
            <p><strong>Joanny Villeneuve et Sandra Legault</strong><br>
            <EMAIL></p>


            <p><em>Ceci est un message automatique, merci de ne pas y répondre directement.</em></p>
          </div>
        </div>
      </body>
      </html>
    `;

        // Replace logo placeholder with actual logo or fallback text
        console.log('Logo URL exists:', !!logoUrl);
        console.log('Logo URL:', logoUrl);

        const logoHtml = logoUrl
          ? `<img src="${logoUrl}" alt="AMQ Partners" class="company-logo" />`
          : '<h1 style="color: #374151; margin: 0; font-size: 24px; font-weight: bold;">AMQ Partners</h1>';

        console.log('Logo HTML length:', logoHtml.length);
        console.log('Logo HTML preview:', logoHtml);

        // Check if placeholder exists before replacement
        const hasPlaceholder = emailContent.includes('{{LOGO_PLACEHOLDER}}');
        console.log('Email content contains logo placeholder before replacement:', hasPlaceholder);

        emailContent = emailContent.replace('{{LOGO_PLACEHOLDER}}', logoHtml);

        // Verify replacement worked
        const stillHasPlaceholder = emailContent.includes('{{LOGO_PLACEHOLDER}}');
        console.log('Email content still contains logo placeholder after replacement:', stillHasPlaceholder);

        // Send email without PDF attachment for signing emails
        await sendBrevoTemplatedEmail({
          to: [{ email: user.email, name: user.name || 'Client' }],
          subject: emailSubject,
          content: emailContent,
          variables: {
            name: user.name || 'Client',
            invoiceNumber: invoice.invoiceNumber,
            invoiceDate: new Date(invoice.date).toLocaleDateString('fr-CA', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }),
            invoiceTotal: total.toFixed(2), // This now includes taxes in the total amount
            signingLink: signingUrl
          }
          // No attachments for signing emails
        });

        // Fix any existing contre-facture with invalid status before saving
        if (invoice.contreFacture && invoice.contreFacture.status === 'new') {
          console.log(`[Mass Email Send] Fixing contre-facture status from 'new' to 'nouveau' for invoice ${invoice._id}`);
          invoice.contreFacture.status = 'nouveau';
        }

        // Update invoice status to "Envoyé" only if current status is before "envoye" in flow order
        const oldStatus = invoice.status;
        const shouldUpdateStatus = isValidStatusTransition(oldStatus, 'envoye');

        if (shouldUpdateStatus) {
          invoice.addStatusChange('envoye', session.user.id, false, 'Email sent');
          await invoice.save();

          // Log audit event for status change
          await InvoiceAuditLogger.logInvoiceStatusChanged(
            request,
            invoice._id.toString(),
            invoice.invoiceNumber,
            oldStatus,
            'envoye',
            session
          );
        } else {
          // Just save the invoice without status change
          await invoice.save();
          console.log(`[Mass Email Send] Skipped status change for invoice ${invoice.invoiceNumber} - current status '${oldStatus}' cannot transition to 'envoye'`);
        }

        results.push({
          invoiceNumber: invoice.invoiceNumber,
          email: user.email,
          success: true
        });

      } catch (error) {
        console.error(`Error sending email for invoice ${invoice.invoiceNumber}:`, error);
        errors.push({
          invoiceNumber: invoice.invoiceNumber,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully sent ${results.length} emails${errors.length > 0 ? ` (${errors.length} failed)` : ''}`,
      results,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    console.error('Error in mass email sending:', error);
    return NextResponse.json({
      error: 'Failed to send emails',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
