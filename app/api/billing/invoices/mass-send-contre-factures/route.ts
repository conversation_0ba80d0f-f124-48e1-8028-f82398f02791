import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import { canUserAccessBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { generateInvoicePDFDataURL } from '@/lib/utils/html-pdf-utils';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    // Check authorization
    const session = await getServerSession(authOptions);
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    if (!session || !canUserAccessBilling(session.user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { invoiceIds } = await request.json();
    if (!Array.isArray(invoiceIds) || invoiceIds.length === 0) {
      return NextResponse.json({ error: 'No invoice IDs provided' }, { status: 400 });
    }

    // Fetch all invoices with their related data (not lean, as we may update)
    const invoices = await Invoice.find({ _id: { $in: invoiceIds } })
      .populate('userId');

    if (!invoices || invoices.length === 0) {
      return NextResponse.json({ error: 'No invoices found' }, { status: 404 });
    }

    const results = [];
    const errors = [];

    for (const invoice of invoices) {
      try {
        const user = invoice.userId;
        if (!user || !user.email) {
          errors.push({ invoiceNumber: invoice.invoiceNumber, error: 'User email not found' });
          continue;
        }

        // Re-generate contre facture using same logic as single endpoint
        // This logic is similar to /api/billing/invoices/[id]/generate-contre-facture

        // Fetch all items for this invoice
        const InvoiceItem = (await import('@/models/InvoiceItem')).default;
        const InvoiceItemType = (await import('@/models/InvoiceItemType')).default;
        const TaxType = (await import('@/models/TaxType')).default;

        const items = await InvoiceItem.find({ _id: { $in: invoice.manualItems } });
        if (!items.length) {
          errors.push({ invoiceNumber: invoice.invoiceNumber, error: 'No items found for invoice' });
          continue;
        }

        // Fetch all item types for these items
        const itemTypeIds = items.map((item: any) => item.invoiceitemtypeId);
        const itemTypes = await InvoiceItemType.find({ _id: { $in: itemTypeIds } });
        const itemTypeMap = Object.fromEntries(itemTypes.map((type: any) => [String(type._id), type]));

        // Filter items that require contre-facture
        const contreFactureItems = items.filter((item: any) => {
          const type = itemTypeMap[item.invoiceitemtypeId];
          return type && type.requiresContreFacture;
        }).map((item: any) => {
          const type = itemTypeMap[item.invoiceitemtypeId];
          return {
            ...item.toObject(),
            title: 'LOCATION D\'ESPACE',
            unitPrice: type.chargeAmount,
            chargeAmount: type.chargeAmount,
            taxable: true, // Contre facture items are ALWAYS taxable
          };
        });

        if (!contreFactureItems.length) {
          errors.push({ invoiceNumber: invoice.invoiceNumber, error: 'No items require contre-facture' });
          continue;
        }

        // Fetch the 'amq' tax type
        const amqTaxType = await TaxType.findOne({ code: 'amq' }).lean() as { _id: any, names: string[], percentages: number[] } | null;
        if (!amqTaxType || !amqTaxType._id) {
          errors.push({ invoiceNumber: invoice.invoiceNumber, error: "'amq' tax type not found" });
          continue;
        }

        // Calculate taxes and total
        const subtotal = contreFactureItems.reduce((sum: number, item: any) => sum + (item.unitPrice * item.quantity), 0);
        // For contre factures, taxes are ALWAYS calculated on ALL items regardless of taxable flag
        const totalTaxes = amqTaxType.percentages.map((rate) =>
          contreFactureItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice * (rate / 100)), 0)
        );
        const total = subtotal + totalTaxes.reduce((sum, t) => sum + t, 0);

        // Build contre-facture object
        const contreFacture = {
          userId: invoice.userId, // original beneficiary (client)
          invoiceNumber: invoice.invoiceNumber + '-CF',
          status: 'nouveau',
          taxTypeId: amqTaxType._id,
          title: invoice.title + ' (Contre-Facture)',
          date: new Date(),
          items: contreFactureItems,
          subtotal,
          totalTaxes,
          total,
          startDate: invoice.startDate,
          endDate: invoice.endDate,
          // Add AMQ tax registration numbers
          tpsRegistrationNumber: '783000151RT001',
          qstRegistrationNumber: '1231059977TQ0001'
        };

        // Save contre-facture to invoice
        invoice.contreFacture = contreFacture;
        await invoice.save();

        // Use the generated contre facture data directly
        const contreFactureData = contreFacture;
        const taxType = {
          code: 'amq',
          names: amqTaxType.names,
          percentages: amqTaxType.percentages
        };

        // 2. Fetch user info for client (beneficiary)
        let clientInfo: any = null;
        if (invoice.userId) {
          const userDoc = await User.findById(invoice.userId).lean();
          if (userDoc) clientInfo = userDoc;
        }
        const invoiceDetails = {
          invoiceNumber: contreFactureData.invoiceNumber,
          date: contreFactureData.date,
          dueDate: contreFactureData.endDate || undefined,
          status: contreFactureData.status,
          total: contreFactureData.total,
          subtotal: contreFactureData.subtotal,
          totalTaxes: contreFactureData.totalTaxes,
          issuerInfo: clientInfo ? {
            name: clientInfo.companyName || clientInfo.name,
            address: clientInfo.address,
            city: clientInfo.city,
            province: clientInfo.province,
            postalCode: clientInfo.postalCode,
            phone: clientInfo.phone,
          } : undefined,
          tpsRegistrationNumber: contreFactureData.tpsRegistrationNumber,
          qstRegistrationNumber: contreFactureData.qstRegistrationNumber,
        };
        const companyInfo = {
          name: "Alimentation Mon Quartier",
          address: "200 rue Principale, local 8",
          city: "St-Sauveur",
          province: "Québec",
          postalCode: "J0R 1R0",
          phone: "**************",
          email: "<EMAIL>",
        };
        const pdfDataUrl = await generateInvoicePDFDataURL(
          contreFactureData.items,
          invoiceDetails,
          companyInfo,
          taxType,
          true // contreFacture flag
        );
        if (!pdfDataUrl) {
          errors.push({ invoiceNumber: invoice.invoiceNumber, error: 'Failed to generate contre facture PDF' });
          continue;
        }
        // Prepare email
        const subject = `Facture`;
        const content = `Bonjour,\n\nVeuillez trouver en pièce jointe la facture.\n\nCordialement,\nL'équipe AMQ Partners`;
        // Send email with PDF attachment
        await sendBrevoTemplatedEmail({
          to: [{ email: user.email, name: user.name || 'Client' }],
          subject,
          content,
          attachments: [
            {
              name: `facture-${invoice.invoiceNumber}.pdf`,
              content: pdfDataUrl.split(',')[1], // Remove the data:application/pdf;base64, prefix
              type: 'application/pdf',
            },
          ],
        });
        results.push({ invoiceNumber: invoice.invoiceNumber, email: user.email, success: true });
      } catch (error) {
        errors.push({ invoiceNumber: invoice.invoiceNumber, error: error instanceof Error ? error.message : 'Unknown error' });
      }
    }
    return NextResponse.json({
      success: true,
      message: `Successfully sent ${results.length} contre facture emails${errors.length > 0 ? ` (${errors.length} failed)` : ''}`,
      results,
      errors: errors.length > 0 ? errors : undefined,
    });
  } catch (error) {
    return NextResponse.json({
      error: 'Failed to send contre facture emails',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
