import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { canUserAccessBilling } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import Invoice from '@/models/Invoice';
import dbConnect from '@/lib/db';

export async function GET(request: NextRequest) {
  await dbConnect();
  
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session || !canUserAccessBilling(session.user)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get filter parameters (same as main invoices endpoint)
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const search = searchParams.get('search');
    const signed = searchParams.get('signed');
    const showArchived = searchParams.get('showArchived') === 'true';
    const showDeleted = searchParams.get('showDeleted') === 'true';

    // Build base filter (same logic as main invoices endpoint)
    const filter: any = {};

    // Date range filter
    if (startDate || endDate) {
      filter.date = {};
      if (startDate) filter.date.$gte = new Date(startDate);
      if (endDate) filter.date.$lte = new Date(endDate);
    }

    // Search filter
    if (search) {
      filter.$or = [
        { invoiceNumber: { $regex: search, $options: 'i' } },
        { title: { $regex: search, $options: 'i' } }
      ];
    }

    // Signed filter
    if (signed === 'true') {
      filter.$or = [
        { signedAt: { $exists: true, $ne: null } },
        { signatureData: { $exists: true, $ne: null } }
      ];
    } else if (signed === 'false') {
      filter.$and = [
        { $or: [{ signedAt: { $exists: false } }, { signedAt: null }] },
        { $or: [{ signatureData: { $exists: false } }, { signatureData: null }] }
      ];
    }

    // Archive filter
    if (!showArchived) {
      filter.archivedAt = { $exists: false };
    }

    // Deleted filter
    if (!showDeleted) {
      filter.deletedAt = { $exists: false };
    }

    // Get counts for each status using aggregation
    const statusCounts = await Invoice.aggregate([
      { $match: filter },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Convert to object with all statuses (including 0 counts)
    const allStatuses = ['nouveau', 'verifie', 'envoye', 'signe', 'en_traitement', 'paye', 'en_retard'];
    const counts: { [key: string]: number } = {};
    
    allStatuses.forEach(status => {
      counts[status] = 0;
    });

    statusCounts.forEach(item => {
      if (allStatuses.includes(item._id)) {
        counts[item._id] = item.count;
      }
    });

    return NextResponse.json({ statusCounts: counts });
  } catch (error) {
    console.error('Error fetching status counts:', error);
    return NextResponse.json({ error: 'Failed to fetch status counts' }, { status: 500 });
  }
}
