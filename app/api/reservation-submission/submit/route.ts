import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import PendingReservation from '@/models/PendingReservation';
import { validatePendingReservationInput, checkApiKey } from '../utils/external-utils';
import { connectToDatabase } from '@/lib/mongodb';
import { phone } from 'phone';
import { ObjectId } from 'mongodb';

export async function POST(req: NextRequest) {
  // API key check
  const apiKey = req.headers.get('x-api-key');
  if (!checkApiKey(apiKey)) {
    return NextResponse.json({ error: 'Invalid or missing API key.' }, { status: 401 });
  }

  let data;
  try {
    data = await req.json();
  } catch {
    return NextResponse.json({ error: 'Invalid JSON body.' }, { status: 400 });
  }

  const { valid, error } = validatePendingReservationInput(data);
  if (!valid) {
    return NextResponse.json({ error }, { status: 400 });
  }

  // Use 'phone' package to normalize and extract last 10 digits
  const phoneResult = phone(data.phone, { country: 'CA' });

  const rawPhone = phoneResult.isValid && phoneResult.phoneNumber ? phoneResult.phoneNumber.replace(/\D/g, '').slice(-10) : '';

  const db = await connectToDatabase();
  try {
    const insertData: any = {
      name: data.name,
      email: data.email,
      phone: rawPhone,
      postal: data.postal,
      message: data.message ?? '',
      createdAt: new Date(),
      updatedAt: new Date(),
      statusId: new ObjectId('682e045506573bed428c2950'),
    };

    // Add source if provided
    if (data.source) {
      insertData.source = data.source;
    }

    await db.collection('pendingReservations').insertOne(insertData);
    return NextResponse.json({ message: 'Pending reservation created successfully.' }, { status: 200 });
  } catch (err) {
    console.error('Database write failed:', err);
    return NextResponse.json({ error: 'Internal server error.' }, { status: 500 });
  }
} 