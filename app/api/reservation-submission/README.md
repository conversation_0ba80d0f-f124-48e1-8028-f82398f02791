# Reservation Submission API

## English

### Endpoint
`POST /api/reservation-submission/submit`

### Authentication
- Requires an API key in the `x-api-key` header.

### Request Headers
- `Content-Type: application/json`
- `x-api-key: YOUR_API_KEY`

### Request Body
```
{
  "name": "string (required)",
  "email": "string (required)",
  "phone": "string (required, valid phone number, will be stored as 10 digits)",
  "postal": "string (required, valid postal code)",
  "message": "string (optional, additional message)",
  "source": "string (optional, exactly 6 alphanumeric characters)"
}
```

### Validation
- **Phone:** Must be a valid phone number (format accepted: ************). Will be stored as a raw 10-digit string (e.g., 5141234567).
- **Postal:** Must be a valid postal code (e.g., A1A 1A1 or A1A1A1).
- **Source:** Must be exactly 6 alphanumeric characters (A-Z, a-z, 0-9). Examples: "ABC123", "PROMO1", "WEB001".

### Responses
- `200 OK` – Reservation created successfully
  - `{ "message": "Pending reservation created successfully." }`
- `400 Bad Request` – Missing or invalid parameters
  - `{ "error": "Missing or invalid ..." }`
- `401 Unauthorized` – Invalid or missing API key
  - `{ "error": "Invalid or missing API key." }`
- `500 Internal Server Error` – Database write failed
  - `{ "error": "Database write failed." }`

---

## Français

### Endpoint
`POST /api/reservation-submission/submit`

### Authentification
- Nécessite une clé API dans l'en-tête `x-api-key`.

### En-têtes de requête
- `Content-Type: application/json`
- `x-api-key: VOTRE_CLÉ_API`

### Corps de la requête
```
{
  "name": "chaîne (obligatoire)",
  "email": "chaîne (obligatoire)",
  "phone": "chaîne (obligatoire, numéro canadien valide, sera stocké sur 10 chiffres)",
  "postal": "chaîne (obligatoire, code postal canadien valide)",
  "message": "chaîne (optionnel, message additionnel)",
  "source": "chaîne (optionnel, exactement 6 caractères alphanumériques)"
}
```

### Validation
- **Téléphone :** Doit être un numéro canadien valide (format accepté :  ************). Sera stocké sous forme de chaîne de 10 chiffres (ex : 5141234567).
- **Code postal :** Doit être un code postal canadien valide (ex : A1A 1A1 ou A1A1A1).
- **Source :** Doit être exactement 6 caractères alphanumériques (A-Z, a-z, 0-9). Exemples : "ABC123", "PROMO1", "WEB001".

### Réponses
- `200 OK` – Réservation en attente créée avec succès
  - `{ "message": "Pending reservation created successfully." }`
- `400 Bad Request` – Paramètres manquants ou invalides
  - `{ "error": "Missing or invalid ..." }`
- `401 Unauthorized` – Clé API manquante ou invalide
  - `{ "error": "Invalid or missing API key." }`
- `500 Internal Server Error` – Échec de l'écriture en base de données
  - `{ "error": "Database write failed." }`

---