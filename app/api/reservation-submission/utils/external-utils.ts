import { phone } from 'phone';

const VALID_API_KEY = process.env.THIRD_PARTY_API_KEY || 'your-secure-api-key';

export function checkApiKey(apiKey: string | null): boolean {
  return apiKey === VALID_API_KEY;
}

interface PendingReservationInput {
  name?: unknown;
  email?: unknown;
  phone?: unknown;
  postal?: unknown;
  message?: unknown;
  source?: unknown;
}

function isValidCanadianPostalCode(postal: string): boolean {
  // Canadian postal code: A1A 1A1 or A1A1A1 (case-insensitive)
  return /^[ABCEGHJ-NPRSTVXY]\d[ABCEGHJ-NPRSTV-Z][ -]?\d[ABCEGHJ-NPRSTV-Z]\d$/i.test(postal);
}

function isValidCanadianPhone(phoneNumber: string): boolean {
  // Use the 'phone' npm package for validation, default to Canada
  const result = phone(phoneNumber, { country: 'CA' });
  console.log('result', result);
  return result.isValid;
}

export function validatePendingReservationInput(data: PendingReservationInput): { valid: boolean; error?: string } {
  if (!data || typeof data !== 'object') {
    return { valid: false, error: 'Request body must be an object.' };
  }
  if (!data.name || typeof data.name !== 'string') {
    return { valid: false, error: 'Missing or invalid name.' };
  }
  if (!data.email || typeof data.email !== 'string') {
    return { valid: false, error: 'Missing or invalid email.' };
  }
  if (!data.phone || typeof data.phone !== 'string' || !isValidCanadianPhone(data.phone)) {
    console.log(data.phone)
    return { valid: false, error: 'Missing or invalid Canadian phone number.' };
  }
  if (!data.postal || typeof data.postal !== 'string' || !isValidCanadianPostalCode(data.postal)) {
    return { valid: false, error: 'Missing or invalid Canadian postal code.' };
  }
  if (data.message !== undefined && typeof data.message !== 'string') {
    return { valid: false, error: 'Invalid message.' };
  }
  if (data.source !== undefined) {
    if (typeof data.source !== 'string') {
      return { valid: false, error: 'Invalid source.' };
    }
    if (!/^[A-Za-z0-9]{6}$/.test(data.source)) {
      return { valid: false, error: 'Source must be exactly 6 alphanumeric characters.' };
    }
  }
  return { valid: true };
} 