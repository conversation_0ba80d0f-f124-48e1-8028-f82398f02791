import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';
import ContactRequestStatus from '@/models/ContactRequestStatus';
import { normalizePhoneNumber } from '@/lib/twilio';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const db = await connectToDatabase();

    // Get query parameters for filtering and pagination
    const url = new URL(req.url);
    const statusId = url.searchParams.get('statusId');
    const search = url.searchParams.get('search');
    const archived = url.searchParams.get('archived');
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '25', 10);

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    // Build query based on filters
    const query: any = {};
    if (statusId) {
      query.statusId = new mongoose.Types.ObjectId(statusId);
    }

    // Handle archived filter
    if (archived === 'true') {
      query.isArchived = true;
    } else {
      // Default to showing non-archived items
      query.isArchived = { $ne: true };
    }
    
    // Add search functionality
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      query.$or = [
        { name: searchRegex },
        { email: searchRegex },
        { phone: searchRegex },
        { postal: searchRegex },
        { message: searchRegex },
        { source: searchRegex }
      ];
    }
    
    // Get total count for pagination
    const total = await db.collection('pendingReservations').countDocuments(query);

    // Fetch requests with populated status information and pagination
    const pendingReservations = await db.collection('pendingReservations')
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .toArray();

    // Get all statuses to use for mapping
    const statuses = await db.collection('contactrequeststatuses').find().toArray();
    const statusesMap = new Map();
    statuses.forEach(status => {
      statusesMap.set(status._id.toString(), status);
    });

    // Get all contact request sources for frontend reference
    const sources = await db.collection('contactrequestsources')
      .find({})
      .sort({ createdAt: -1 })
      .toArray();

    // Create a map of sources by tag for quick lookup
    const sourcesMap = new Map();
    sources.forEach(source => {
      sourcesMap.set(source.tag, {
        _id: source._id.toString(),
        tag: source.tag,
        source: source.source,
        influencer: source.influencer,
        bitlyUrl: source.bitlyUrl,
        hits: source.hits,
        createdAt: source.createdAt,
        updatedAt: source.updatedAt
      });
    });

    // Process each request to add reservation information and duplicate detection
    const formattedRequests = await Promise.all(
      pendingReservations.map(async (req) => {
        let status = undefined;

        // Check if the request has a statusId and find corresponding status
        if (req.statusId) {
          const statusId = req.statusId.toString();
          const statusData = statusesMap.get(statusId);

          if (statusData) {
            status = {
              _id: statusData._id,
              name: statusData.name,
              name_en: statusData.name_en,
              code: statusData.code,
              color: statusData.color,
              order: statusData.order || 0
            };
          }
        }

        // Note: reservationId is included in the response for frontend use

        // Check for duplicate phone numbers in existing reservations
        let duplicatePhoneReservations: any[] = [];
        if (req.phone) {
          const normalizedPhone = normalizePhoneNumber(req.phone);
          if (normalizedPhone) {
            try {
              // Find reservations with the same normalized phone number
              const duplicates = await db.collection('reservations')
                .find({
                  'customerInfo.phone': normalizedPhone,
                  _id: { $ne: req.reservationId }, // Exclude the linked reservation if it exists
                  isDeleted: { $ne: true } // Exclude soft-deleted reservations
                })
                .project({ 
                  _id: 1, 
                  'customerInfo.client1Name': 1, 
                  'customerInfo.phone': 1, 
                  status: 1 
                })
                .toArray();
                duplicatePhoneReservations = duplicates;
              
            } catch (error) {
              console.error('Error checking for duplicate phone numbers:', error);
              // Continue processing even if duplicate detection fails
            }
          }
        }

        return {
          ...req,
          _id: req._id.toString(), // Ensure _id is a string
          status,
          reservationId: req.reservationId ? req.reservationId.toString() : null,
          hasReservation: !!req.reservationId,
          duplicatePhoneReservations
        };
      })
    );

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: formattedRequests,
      pagination: {
        page,
        limit,
        total,
        totalPages
      },
      sources: Array.from(sourcesMap.values())
    });
  } catch (error) {
    console.error('Error fetching contact requests:', error);
    return NextResponse.json({ error: 'Failed to fetch contact requests.' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
  await dbConnect();
    const db = mongoose.connection;
    const data = await req.json();

    // Validate required fields
    if (!data.name || !data.email || !data.phone || !data.postal) {
      return NextResponse.json(
        { error: 'Name, email, phone, and postal are required' },
        { status: 400 }
      );
    }

    // Find default "new" status if not provided
    if (!data.statusId) {
      const newStatus = await ContactRequestStatus.findOne({ code: 'new' });
      if (newStatus) {
        data.statusId = newStatus._id;
        data.statusUpdatedAt = new Date();
      }
    }

    // Add timestamps
    data.createdAt = new Date();
    data.updatedAt = new Date();

    // Create the contact request
    const result = await db.collection('pendingReservations').insertOne(data);
    const newRequest = { ...data, _id: result.insertedId };
    return NextResponse.json(newRequest, { status: 201 });
  } catch (error) {
    console.error('Error creating contact request:', error);
    return NextResponse.json(
      { error: 'Failed to create contact request' },
      { status: 500 }
    );
  }
}
