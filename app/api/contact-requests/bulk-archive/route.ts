import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = await connectToDatabase();
    
    const { contactRequestIds, archived = true } = await req.json();

    // Validate input
    if (!contactRequestIds || !Array.isArray(contactRequestIds) || contactRequestIds.length === 0) {
      return NextResponse.json(
        { error: 'contactRequestIds array is required' },
        { status: 400 }
      );
    }

    // Validate that all IDs are valid ObjectIds
    const validIds = contactRequestIds.filter(id => mongoose.Types.ObjectId.isValid(id));
    if (validIds.length !== contactRequestIds.length) {
      return NextResponse.json(
        { error: 'All contact request IDs must be valid' },
        { status: 400 }
      );
    }

    // Convert string IDs to ObjectIds
    const objectIds = validIds.map(id => new mongoose.Types.ObjectId(id));

    // Use the same collection name as the GET route
    const collection = db.collection('pendingReservations');

    // Update the contact requests using native driver to match the GET route
    const result = await collection.updateMany(
      { _id: { $in: objectIds } },
      {
        $set: {
          isArchived: archived,
          updatedAt: new Date()
        }
      }
    );

    return NextResponse.json({
      success: true,
      modifiedCount: result.modifiedCount,
      matchedCount: result.matchedCount,
      message: archived
        ? `${result.modifiedCount} contact requests archived successfully`
        : `${result.modifiedCount} contact requests unarchived successfully`
    });

  } catch (error) {
    console.error('Error in bulk archive operation:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk archive operation' },
      { status: 500 }
    );
  }
}
