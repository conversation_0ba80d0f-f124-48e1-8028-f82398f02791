import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import User from '@/models/User';
import ContractAuditLog from '@/models/ContractAuditLog';
import { getUserFromToken } from '@/app/api/affectations/utils/mobile_auth_utils';
import { userRequiresContract } from '@/lib/utils/contract-data-mapper';

export async function GET(request: NextRequest) {
  await dbConnect();

  try {
    const requestHeaders = request.headers;

    // Check if this is a mobile request (has Authorization header)
    const authHeader = requestHeaders.get('Authorization');
    let session = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      // Mobile authentication
      session = await getUserFromToken({ isMobile: true }, requestHeaders);
    }

    // Fallback to web authentication if not mobile or mobile auth failed
    if (!session) {
      session = await getServerSession(authOptions);
    }

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id || session.user._id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID not found in session' },
        { status: 400 }
      );
    }

    // Get user data with populated roles
    const user = await User.findById(userId).populate('roles').lean();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user requires a contract
    const requiresContract = userRequiresContract(user as any);

    // Get contract signing token if it exists
    const contractToken = await ContractSigningToken.findOne({ userId })
      .populate('createdBy', 'name email')
      .lean();

    // Return 404 if no contract is found
    if (!contractToken) {
      return NextResponse.json(
        { error: 'Contract not found' },
        { status: 404 }
      );
    }

    // Get recent audit logs
    const auditLogs = await ContractAuditLog.find({
      userId,
      token: (contractToken as any).token
    })
    .sort({ timestamp: -1 })
    .limit(10)
    .lean();

    // Determine contract status
    let contractStatus: string;
    if ((contractToken as any).isCompleted) {
      contractStatus = 'completed';
    } else if ((contractToken as any).adminSignedAt) {
      contractStatus = 'awaiting_user_signature';
    } else {
      contractStatus = 'awaiting_admin_signature';
    }

    // Prepare contract details
    const token = contractToken as any;
    const contractDetails = {
      token: token.token,
      contractStartDate: token.contractStartDate,
      adminSignedAt: token.adminSignedAt,
      userSignedAt: token.userSignedAt,
      isCompleted: token.isCompleted,
      firstVisitedAt: token.firstVisitedAt,
      createdAt: token.createdAt,
      createdBy: {
        name: token.createdBy?.name,
        email: token.createdBy?.email
      },
      // Generate signing URL if contract is ready for user signature
      signingUrl: token.adminSignedAt && !token.userSignedAt
        ? `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/contract-signing/${token.token}`
        : null
    };

    // Prepare user roles information
    const userData = user as any;
    const userRoles = userData.roles?.map((role: any) => ({
      _id: role._id,
      name: role.name,
      description: role.description
    })) || [];

    const response = {
      success: true,
      user: {
        id: userData._id,
        name: userData.name,
        email: userData.email,
        roles: userRoles
      },
      contract: {
        requiresContract,
        status: contractStatus,
        details: contractDetails,
        statusDescription: getStatusDescription(contractStatus)
      },
      auditLogs: auditLogs.map(log => ({
        action: log.action,
        timestamp: log.timestamp,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        metadata: log.metadata
      }))
    };

    return NextResponse.json(response);

  } catch (error: any) {
    console.error('Error fetching user contract info:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch contract information',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}



function getStatusDescription(status: string): string {
  switch (status) {
    case 'awaiting_admin_signature':
      return 'Waiting for admin to sign contract';
    case 'awaiting_user_signature':
      return 'Ready for your signature';
    case 'completed':
      return 'Contract fully signed by both parties';
    default:
      return 'Unknown status';
  }
}
