import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/db';
import Reservation from '@/models/Reservation';
// Removed ReservationStatus import as status is updated directly
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  // TODO: Add specific permission check for updating attendance status if needed

  const { id } = params; // Standard way to access params in Next.js route handlers
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return NextResponse.json({ message: 'Invalid Reservation ID' }, { status: 400 });
  }

  let requestBody;
  try {
    requestBody = await request.json();
  } catch (error) {
    return NextResponse.json({ message: 'Invalid request body' }, { status: 400 });
  }

  const { status } = requestBody;

  // Validate the status - expecting 'present'
  if (status !== 'present') {
    return NextResponse.json({ message: 'Invalid status provided. Only "present" is allowed.' }, { status: 400 });
  }

  try {
  await dbConnect();

    // Find the reservation and update its status directly to "present"
    const updatedReservation = await Reservation.findByIdAndUpdate(
      id,
      { $set: { status: 'present', presentAt: new Date() } }, // Update status and set presentAt timestamp
      { new: true } // Return the updated document
    )
    // Select fields needed for response, ensuring they exist on the Reservation model
    .select('_id customerInfo.client1Name status appointmentId presentAt'); // Include presentAt in selection

    if (!updatedReservation) {
      return NextResponse.json({ message: 'Reservation not found' }, { status: 404 });
    }

    // Format the response payload
    const clientName = updatedReservation.customerInfo?.client1Name || 'Client Inconnu';

    const responsePayload = {
        _id: updatedReservation._id.toString(),
        clientName: clientName,
        status: updatedReservation.status, // Should be "present" now
        appointmentId: updatedReservation.appointmentId?.toString(),
        presentAt: updatedReservation.presentAt?.toISOString(), // Include presentAt in response
    };

    console.log(`API: Marked reservation ${id} as present.`);
    return NextResponse.json(responsePayload);

  } catch (error) {
    // Log the specific error for debugging
    console.error(`Error updating reservation ${id} status:`, error);

    // Check for specific Mongoose errors if needed
    // if (error instanceof mongoose.Error...) {}

    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}