import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/db';
import Reservation from '@/models/Reservation';
import Appointment from '@/models/Appointment'; // Import Appointment model
import { parseISO } from 'date-fns';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// Define interface for the response payload structure
interface FormattedReservation {
    _id: string;
    clientName: string;
    visitDate: string;
    visitTime: string;
    status: string;
    appointmentId?: string;
    adultCount: number;
    childCount: number;
    phone?: string; // Added phone
}

const isValidDate = (dateString: string | null): boolean => {
  if (!dateString) return false;
  const date = parseISO(dateString);
  return !isNaN(date.getTime());
};

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  // TODO: Add permission check here if needed

  const { searchParams } = new URL(request.url);
  const branchId = searchParams.get('branchId');
  const dateString = searchParams.get('date'); // Expecting 'yyyy-MM-dd' format

  if (!branchId) {
    return NextResponse.json({ message: 'Branch ID is required' }, { status: 400 });
  }

  if (!isValidDate(dateString)) {
    return NextResponse.json({ message: 'Valid date is required (yyyy-MM-dd)' }, { status: 400 });
  }

  try {
  await dbConnect();

    console.log(`API: Fetching appointments for branch ${branchId} on date ${dateString}`);

    // Step 1: Find appointments for the given branch and date
    const appointments = await Appointment.find({
        branchId: new mongoose.Types.ObjectId(branchId),
        date: dateString // Query the date string directly
    })
    .select('_id startHour endHour date') // Select necessary fields from Appointment
    .sort({ startHour: 1 }); // Sort by start hour

    if (!appointments || appointments.length === 0) {
        console.log(`API: No appointments found for branch ${branchId} on date ${dateString}.`);
        return NextResponse.json([]);
    }

    // Step 2: Create a map of appointment details for quick lookup
    const appointmentDetailsMap = new Map(appointments.map(app => [
        app._id.toString(),
        { startHour: app.startHour, endHour: app.endHour, date: app.date }
    ]));
    const appointmentIds = appointments.map(app => app._id);

    // Step 3: Find reservations linked to these appointments
    const reservations = await Reservation.find({
        appointmentId: { $in: appointmentIds },
        isDeleted: { $ne: true }
    })
    // Select fields needed for calculation and display
    .select('_id customerInfo.client1Name customerInfo.hasCompanion customerInfo.client2Name customerInfo.phone preferences.childrenAges status appointmentId'); // Added customerInfo.phone

    if (!reservations || reservations.length === 0) {
        console.log(`API: No active reservations found for the appointments on branch ${branchId}, date ${dateString}.`);
        return NextResponse.json([]);
    }

    // Step 4: Combine reservation data with appointment data
    const combinedData = reservations.map(res => {
        const appointmentDetails = appointmentDetailsMap.get(res.appointmentId?.toString() || '');
        if (!appointmentDetails) {
            console.warn(`Reservation ${res._id} links to missing/filtered appointment ${res.appointmentId}`);
            return null; // Return null to filter out later
        }

        const clientName = res.customerInfo?.client1Name || 'Client Inconnu';
        const visitTime = `${appointmentDetails.startHour || '??:??'} - ${appointmentDetails.endHour || '??:??'}`;
        const visitDate = appointmentDetails.date || dateString!;

        let statusCode = 'pending';
        if (typeof res.status === 'string') {
            statusCode = res.status;
        }

        // Calculate adult and child counts
        let adultCount = 1; // Start with client1
        if (res.customerInfo?.hasCompanion || (res.customerInfo?.client2Name && res.customerInfo.client2Name.trim() !== '')) {
            adultCount++;
        }
        adultCount += res.preferences?.childrenAges?.age13to17 || 0;

        const childCount = (res.preferences?.childrenAges?.age0to5 || 0) + (res.preferences?.childrenAges?.age6to12 || 0);

        const formatted: FormattedReservation = {
            _id: res._id.toString(),
            clientName: clientName,
            visitDate: visitDate,
            visitTime: visitTime,
            status: statusCode,
            appointmentId: res.appointmentId?.toString(),
            adultCount: adultCount,
            childCount: childCount,
            phone: res.customerInfo?.phone, // Add phone number
        };
        return formatted;
    });

    // Filter out null entries where appointment details were missing
    const validCombinedData = combinedData.filter((item): item is FormattedReservation => item !== null);

    // Step 5: Sort the valid combined data by visit time
    validCombinedData.sort((a, b) => a.visitTime.localeCompare(b.visitTime));

    // Step 6: Return the final sorted data
    console.log(`API: Found ${validCombinedData.length} valid reservations matching criteria.`);
    return NextResponse.json(validCombinedData);

  } catch (error) { // Correctly placed catch block
    console.error('Error fetching attendance data:', error);
    return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
  }
}
