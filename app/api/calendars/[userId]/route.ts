import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Calendar from '@/models/Calendar';
import { DateAvailability, TimeRange } from '@/models/Calendar';
import { format, parse, getHours } from 'date-fns';

// Helper function to convert TimeRange[] to string[] (hours)
function convertRangesToHours(ranges: TimeRange[]): string[] {
  const hoursSet = new Set<string>();
  
  for (const range of ranges) {
    if (!range || !range.start) { // Check if range and range.start are defined
      console.warn("Invalid range object:", range); // Log invalid range for debugging
      continue; // Skip to the next range
    }
    const startHour = parseInt(range.start.split(':')[0], 10);
    const endHour = parseInt(range.end.split(':')[0], 10);
    
    for (let hour = startHour; hour <= endHour; hour++) {
      hoursSet.add(`${hour.toString().padStart(2, '0')}:00`);
    }
  }
  
  return Array.from(hoursSet).sort();
}

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await dbConnect();
    const { userId } = await params;

    // Get search params from the URL
    const searchParams = request.nextUrl.searchParams;
    const start = searchParams.get('start');
    const end = searchParams.get('end');

    const calendar = await Calendar.findOne({ userId });
    
    // Return empty availability if calendar doesn't exist
    if (!calendar) {
      // Create an empty calendar object with empty availability
      return NextResponse.json({ 
        userId,
        availability: {} 
      });
    }

    // If start and end dates are provided, filter availability
    if (start && end) {
      const filteredAvailability: { [key: string]: string[] } = {};
      const availability = calendar.availability || {};
      
      // Filter dates within range
      Object.entries(availability).forEach(([date, hours]) => {
        if (date >= start && date <= end) {
          filteredAvailability[date] = Array.isArray(hours) ? hours : [];
        }
      });

      return NextResponse.json({
        ...calendar.toObject(),
        availability: filteredAvailability
      });
    }

    return NextResponse.json(calendar);
  } catch (error) {
    console.error('Error fetching calendar:', error);
    return NextResponse.json(
      { error: 'Failed to fetch calendar' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await dbConnect();
    const { userId } = await params;
    const body = await request.json();
    
    // Get the date range from the request with fallback values
    const { availability } = body;
    const startDate = body.startDate || format(new Date(), 'yyyy-MM-01'); // Default to 1st of current month
    const endDate = body.endDate || format(new Date(), 'yyyy-MM-31'); // Default to 31st of current month
    
    let calendar = await Calendar.findOne({ userId });
    
    // If calendar doesn't exist, create it instead of returning 404
    if (!calendar) {
      calendar = new Calendar({
        userId,
        availability: {}
      });
    }

    // Initialize availability if it doesn't exist
    if (!calendar.availability) {
      calendar.availability = {};
    }

    // Update only the dates provided in the availability object
    // instead of deleting dates in a range that might be unintended
    if (availability) {
      calendar.availability = {
        ...calendar.availability,
        ...availability
      };
    }

    await calendar.save();
    return NextResponse.json(calendar);
  } catch (error) {
    console.error('Error updating calendar:', error);
    return NextResponse.json(
      { error: 'Failed to update calendar' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await dbConnect();
    const { userId } = params;
    const body = await request.json();

    // Check if calendar already exists
    const existingCalendar = await Calendar.findOne({ userId });
    if (existingCalendar) {
      return NextResponse.json(
        { error: 'Calendar already exists' },
        { status: 409 }
      );
    }
    
    // Create calendar with hours format directly
    const calendar = await Calendar.create({
      userId,
      availability: body.availability || {}
    });

    return NextResponse.json(calendar, { status: 201 });
  } catch (error) {
    console.error('Error creating calendar:', error);
    return NextResponse.json(
      { error: 'Failed to create calendar' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    await dbConnect();
    const { userId } = params;
    
    const calendar = await Calendar.findOneAndDelete({ userId });
    if (!calendar) {
      return NextResponse.json(
        { error: 'Calendar not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ deleted: true });
  } catch (error) {
    console.error('Error deleting calendar:', error);
    return NextResponse.json(
      { error: 'Failed to delete calendar' },
      { status: 500 }
    );
  }
}