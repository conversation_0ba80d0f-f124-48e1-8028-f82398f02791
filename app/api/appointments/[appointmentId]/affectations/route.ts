import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Reservation from '@/models/Reservation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { getExcludedStatusCodes } from '@/lib/utils/reservation-status-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { appointmentId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    const appointmentId = params.appointmentId;

    // Convert string ID to MongoDB ObjectId
    const objectId = new mongoose.Types.ObjectId(appointmentId);

    // Get excluded status codes dynamically
    const excludedStatusCodes = await getExcludedStatusCodes();

    const reservations = await Reservation.find({
      appointmentId: objectId,
      status: { $nin: excludedStatusCodes } // Exclude reservations with excluded statuses
    })
    .sort({ createdAt: -1 }) // Most recent first
    .lean(); // Convert to plain JavaScript objects

    return NextResponse.json({ reservations });
  } catch (error: any) {
    console.error('Error fetching appointment affectations:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch affectations' },
      { status: 500 }
    );
  }
} 