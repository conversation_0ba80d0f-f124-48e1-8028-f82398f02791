import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Appointment from '@/models/Appointment';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { date, appointments } = body;

    if (!date || !appointments || !Array.isArray(appointments)) {
      return NextResponse.json({ error: 'Invalid request data' }, { status: 400 });
    }

    // Validate date format
    if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return NextResponse.json({ error: 'Invalid date format. Use YYYY-MM-DD' }, { status: 400 });
    }

    await dbConnect();

    // Validate branchId before creating appointments
    const firstAppointment = appointments[0];
    if (!firstAppointment.branchId) {
      return NextResponse.json({ error: 'Branch ID is required' }, { status: 400 });
    }

    try {
      // Validate that branchId is a valid ObjectId
      new mongoose.Types.ObjectId(firstAppointment.branchId);
    } catch (error) {
      return NextResponse.json({ error: 'Invalid Branch ID format' }, { status: 400 });
    }

    // Log the appointments being created
    console.log('Creating appointments with date:', date);
    console.log('First appointment data:', JSON.stringify(appointments[0]));

    // Create all appointments individually to ensure proper validation
    const createdAppointments = [];
    
    for (const apt of appointments) {
      try {
        // Create a new object with all required fields explicitly set
        const appointmentData = {
          branchId: new mongoose.Types.ObjectId(apt.branchId),
          date: date, // Always use the date from the request body
          startHour: apt.startHour,
          endHour: apt.endHour,
          overbooking: apt.overbooking,
          max_capacity_family: apt.max_capacity_family
        };
        
        console.log('Creating appointment with data:', JSON.stringify(appointmentData));
        
        // Use insertOne directly to bypass any potential middleware issues
        const result = await mongoose.connection.db.collection('appointments').insertOne(appointmentData);
        
        // Get the created document
        const newAppointment = await mongoose.connection.db.collection('appointments').findOne({ _id: result.insertedId });
        
        createdAppointments.push(newAppointment);
      } catch (error: any) {
        console.error('Error creating appointment:', error);
        return NextResponse.json({ 
          error: `Failed to create appointment: ${error.message}`,
          details: error
        }, { status: 500 });
      }
    }

    return NextResponse.json(createdAppointments);

  } catch (error: any) {
    console.error('Error creating bulk appointments:', error);
    return NextResponse.json({ 
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
} 