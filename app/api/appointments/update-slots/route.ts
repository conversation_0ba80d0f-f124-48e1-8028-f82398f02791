import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { getExcludedStatusCodes } from '@/lib/utils/reservation-status-utils';
import { AppointmentSlot, DatabaseAppointmentSlot } from '@/app/types/appointment';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import Reservation from '@/models/Reservation';
import Appointment from '@/models/Appointment';

/**
 * Compares an existing slot with an updated slot to determine if there are changes
 * @param existingSlot The slot from the database
 * @param newSlot The incoming slot from the client
 * @returns true if there are changes, false if slots are effectively the same
 */
function hasChanges(existingSlot: any, newSlot: any): boolean {
  return (
    existingSlot.startHour !== newSlot.startHour ||
    existingSlot.endHour !== newSlot.endHour ||
    existingSlot.enabled !== newSlot.enabled ||
    existingSlot.capacity !== newSlot.capacity ||
    existingSlot.online !== newSlot.online ||
    existingSlot.home !== newSlot.home ||
    existingSlot.max_capacity_family !== newSlot.max_capacity_family
  );
}

export async function POST(req: Request) {
  // Block action if not authenticated
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const { branchId, date, slots } = await req.json();

    if (!branchId || !date || !slots || !Array.isArray(slots)) {
      return NextResponse.json(
        { error: 'branchId, date, and slots array are required' },
        { status: 400 }
      );
    }

    console.log(`📌 [update-slots] Processing update for branchId=${branchId}, date=${date}, slots count=${slots.length}`);

    // Connect to database
    const db = await connectToDatabase();

    // Get existing slots
    const existingSlots = await db
      .collection('appointments')
      .find({
        branchId: new ObjectId(branchId),
        date: date, // Match by string date (no conversion to Date object)
      })
      .toArray();

    // Get excluded status codes and calculate excluded reservations for capacity adjustment
    const excludedStatusCodes = await getExcludedStatusCodes();
    const appointmentIds = existingSlots.map((slot: any) => slot._id);

    // Get excluded reservations count for capacity adjustment
    const excludedReservations = await db.collection("reservations")
      .aggregate([
        {
          $match: {
            appointmentId: { $in: appointmentIds },
            status: { $in: excludedStatusCodes },
            isDeleted: { $ne: true }
          }
        },
        { $group: { _id: '$appointmentId', count: { $sum: 1 } } }
      ])
      .toArray();

    const excludedMap = Object.fromEntries(excludedReservations.map((r: any) => [r._id.toString(), r.count]));

    console.log(`📌 [update-slots] Found ${existingSlots.length} existing slots in database`);
    
    // Log a sample of existing slots for debugging
    if (existingSlots.length > 0) {
      const sampleSlot = existingSlots[0];
      console.log(`📌 [update-slots] Sample existing slot: ${JSON.stringify({
        _id: sampleSlot._id.toString(),
        date: sampleSlot.date,
        dateType: typeof sampleSlot.date,
        startHour: sampleSlot.startHour,
        endHour: sampleSlot.endHour
      })}`);
    }

    // Debug incoming slots
    console.log(`📌 [update-slots] First incoming slot: ${JSON.stringify(slots[0])}`);

    // Process slots for updates, creations, and deletions
    const updates: any[] = [];
    const creations: any[] = [];
    const deletions: ObjectId[] = [];
    const unchanged: any[] = [];
    
    // Process existing vs incoming slots
    const processedExistingIds = new Set<string>();

    // First, handle incoming slots (updates or creations)
    for (const slot of slots) {
      // Handle slots with _id (potential updates)
      if (slot._id) {
        // Try to find the existing slot with this ID
        const existingSlot = existingSlots.find(
          (es: any) => es._id.toString() === slot._id.toString()
        );

        if (existingSlot) {
          // We found an existing slot with this ID - check if it changed
          console.log(`📌 [update-slots] Found existing slot with ID ${slot._id}`);

          // Adjust capacity back to original value by adding excluded reservations
          const excludedCount = excludedMap[slot._id.toString()] || 0;
          const originalCapacity = (slot.capacity || 0) + excludedCount;

          console.log(`📌 [update-slots] Capacity adjustment for slot ${slot._id}:`);
          console.log(`  - Incoming capacity: ${slot.capacity}`);
          console.log(`  - Excluded reservations: ${excludedCount}`);
          console.log(`  - Calculated original capacity: ${originalCapacity}`);
          console.log(`  - Existing capacity in DB: ${existingSlot.capacity}`);

          // Create a slot with the calculated original capacity for comparison
          const slotWithOriginalCapacity = {
            ...slot,
            capacity: originalCapacity
          };

          const isChanged = hasChanges(existingSlot, slotWithOriginalCapacity);
          if (isChanged) {
            console.log(`📌 [update-slots] Slot with ID ${slot._id} has changes - will update`);

            updates.push({
              updateOne: {
                filter: { _id: new ObjectId(slot._id) },
                update: {
                  $set: {
                    ...slot,
                    capacity: originalCapacity, // Store original capacity in database
                    _id: new ObjectId(slot._id),
                    branchId: new ObjectId(branchId),
                    date: date // Store as string, not Date object
                  },
                },
              },
            });
          } else {
            console.log(`📌 [update-slots] Slot with ID ${slot._id} has no changes - will skip`);
            unchanged.push(slot);
          }
          processedExistingIds.add(existingSlot._id.toString());
        } else {
          // No existing slot found with this ID - create a new one
          console.log(`📌 [update-slots] Slot with ID ${slot._id} not found in database - will create new`);
          // For new slots, capacity is stored as-is since there are no excluded reservations yet
          creations.push({
            ...slot,
            _id: new ObjectId(), // Generate a new ID
            branchId: new ObjectId(branchId),
            date: date, // Store as string, not Date object
          });
        }
      } else {
        // New slot without ID
        console.log(`📌 [update-slots] New slot without ID - will create new`);
        // For new slots, capacity is stored as-is since there are no excluded reservations yet
        creations.push({
          ...slot,
          branchId: new ObjectId(branchId),
          date: date, // Store as string, not Date object
        });
      }
    }

    // Find slots to delete (existing slots not in the incoming list)
    for (const existingSlot of existingSlots) {
      if (!processedExistingIds.has(existingSlot._id.toString())) {
        deletions.push(existingSlot._id);
      }
    }

    // BLOCK: Check if any slot to be deleted has reservations
    if (deletions.length > 0) {
      // Query reservations for any of the slots to be deleted
      const slotsWithReservations = await Reservation.find({
        appointmentId: { $in: deletions },
        isDeleted: { $ne: true },
      }).limit(1); // Only need to know if any exist
      if (slotsWithReservations.length > 0) {
        return NextResponse.json({
          error: 'Cannot delete slots with existing reservations. Please manage or move the affected reservations first.'
        }, { status: 400 });
      }
    }

    console.log(`📌 [update-slots] Operations summary: ${updates.length} updates, ${creations.length} creations, ${deletions.length} deletions, ${unchanged.length} unchanged`);

    // Execute database operations
    let results = { created: 0, updated: 0, deleted: 0, unchanged: unchanged.length };

    // Perform updates if any
    if (updates.length > 0) {
      console.log(`📌 [update-slots] Executing bulk update operations`);
      const updateResult = await db.collection('appointments').bulkWrite(updates);
      results.updated = updateResult.modifiedCount;
      console.log(`📌 [update-slots] Update result: ${updateResult.modifiedCount} documents modified out of ${updates.length} operations`);
    }

    // Perform creations if any
    if (creations.length > 0) {
      console.log(`📌 [update-slots] Executing insertMany for new slots`);
      const insertResult = await db.collection('appointments').insertMany(creations);
      results.created = Object.keys(insertResult.insertedIds).length;
    }

    // Perform deletions if any
    if (deletions.length > 0) {
      console.log(`📌 [update-slots] Executing deleteMany for removed slots`);
      const deleteResult = await db.collection('appointments').deleteMany({
        _id: { $in: deletions },
      });
      results.deleted = deleteResult.deletedCount;
    }

    console.log(`📌 [update-slots] Operation complete: ${JSON.stringify(results)}`);

    return NextResponse.json({
      message: 'Appointment slots updated successfully',
      results,
    });
  } catch (error: any) {
    console.error('Error updating appointment slots:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update appointment slots' },
      { status: 500 }
    );
  }
} 