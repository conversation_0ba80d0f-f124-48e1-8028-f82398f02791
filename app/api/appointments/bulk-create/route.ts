import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { getExcludedStatusCodes } from '@/lib/utils/reservation-status-utils';

interface AppointmentData {
  branchId: string;
  date: string;
  startHour: string;
  endHour: string;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
}

export async function POST(request: Request) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { appointments } = body;

    if (!appointments || !Array.isArray(appointments) || appointments.length === 0) {
      return NextResponse.json(
        { error: 'Appointments array is required and must not be empty' },
        { status: 400 }
      );
    }

    // Validate each appointment
    for (const appointment of appointments) {
      const { branchId, date, startHour, endHour, capacity, online, home, max_capacity_family } = appointment;

      if (!branchId || !date || !startHour || !endHour || 
          capacity === undefined || online === undefined || 
          home === undefined || max_capacity_family === undefined) {
        return NextResponse.json(
          { error: 'All appointment fields are required' },
          { status: 400 }
        );
      }

      // Validate date format (YYYY-MM-DD)
      if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return NextResponse.json(
          { error: `Invalid date format: ${date}. Expected YYYY-MM-DD` },
          { status: 400 }
        );
      }

      // Validate time format (HH:MM)
      if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(startHour) || 
          !/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(endHour)) {
        return NextResponse.json(
          { error: `Invalid time format for appointment on ${date}. Expected HH:MM` },
          { status: 400 }
        );
      }

      // Validate capacity values
      if (capacity < 0 || online < 0 || home < 0 || max_capacity_family < 0) {
        return NextResponse.json(
          { error: 'Capacity values must be non-negative' },
          { status: 400 }
        );
      }

      // Validate ObjectId format for branchId
      if (!ObjectId.isValid(branchId)) {
        return NextResponse.json(
          { error: `Invalid branchId format: ${branchId}` },
          { status: 400 }
        );
      }
    }

    // Connect to database
    const db = await connectToDatabase();
    const appointmentsCollection = db.collection('appointments');

    // Prepare appointments for insertion
    const appointmentsToInsert = appointments.map((appointment: AppointmentData) => ({
      ...appointment,
      branchId: new ObjectId(appointment.branchId),
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    // Check for existing appointments to update or create new ones
    const existingAppointments = await appointmentsCollection.find({
      $or: appointmentsToInsert.map(apt => ({
        branchId: apt.branchId,
        date: apt.date,
        startHour: apt.startHour,
        endHour: apt.endHour,
      }))
    }).toArray();

    // Create a map of existing appointments for quick lookup
    const existingAppointmentsMap = new Map(
      existingAppointments.map(apt => [
        `${apt.branchId.toString()}-${apt.date}-${apt.startHour}-${apt.endHour}`,
        apt
      ])
    );

    // Get excluded status codes for reservation counting
    const excludedStatusCodes = await getExcludedStatusCodes();

    let created = 0;
    let updated = 0;
    const appointmentsToCreate = [];
    const appointmentsToUpdate = [];

    // Process each appointment
    for (const appointmentData of appointmentsToInsert) {
      const key = `${appointmentData.branchId.toString()}-${appointmentData.date}-${appointmentData.startHour}-${appointmentData.endHour}`;
      const existingAppointment = existingAppointmentsMap.get(key);

      if (existingAppointment) {
        // Count reservations for this appointment (excluding those with excludeFromAffectations)
        const reservationCount = await db.collection('reservations').countDocuments({
          appointmentId: existingAppointment._id,
          status: { $nin: excludedStatusCodes },
          isDeleted: { $ne: true }
        });

        // Count excluded reservations for capacity adjustment
        const excludedReservationCount = await db.collection('reservations').countDocuments({
          appointmentId: existingAppointment._id,
          status: { $in: excludedStatusCodes },
          isDeleted: { $ne: true }
        });

        // Determine the new capacity based on business rules
        let newCapacity;
        if (appointmentData.capacity >= reservationCount) {
          // User picked capacity is sufficient for existing reservations
          newCapacity = appointmentData.capacity;
        } else {
          // User picked capacity is less than reservations, use reservation count
          newCapacity = reservationCount;
        }

        // Adjust capacity for excluded reservations
        const finalCapacity = newCapacity + excludedReservationCount;

        appointmentsToUpdate.push({
          _id: existingAppointment._id,
          capacity: finalCapacity,
          online: appointmentData.online,
          home: appointmentData.home,
          max_capacity_family: appointmentData.max_capacity_family,
          updatedAt: new Date()
        });
      } else {
        // New appointment to create
        appointmentsToCreate.push(appointmentData);
      }
    }

    // Update existing appointments
    if (appointmentsToUpdate.length > 0) {
      for (const updateData of appointmentsToUpdate) {
        const { _id, ...updateFields } = updateData;
        await appointmentsCollection.updateOne(
          { _id },
          { $set: updateFields }
        );
        updated++;
      }
    }

    // Insert new appointments
    if (appointmentsToCreate.length > 0) {
      const result = await appointmentsCollection.insertMany(appointmentsToCreate);
      created = Object.keys(result.insertedIds).length;
    }

    return NextResponse.json({
      message: 'Bulk appointment operation completed',
      created,
      updated,
      total: appointmentsToInsert.length,
    });

  } catch (error: any) {
    console.error('Error in bulk appointment creation:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create appointments' },
      { status: 500 }
    );
  }
}