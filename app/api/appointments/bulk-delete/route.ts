import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Appointment from '@/models/Appointment';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { branchId, date } = await req.json();

    if (!branchId || !date) {
      return NextResponse.json(
        { error: 'Invalid request. branchId and date are required' },
        { status: 400 }
      );
    }

    // Connect to database using mongoose
    await dbConnect();
    
    // Delete all appointments for this branch and date using Mongoose
    const result = await Appointment.deleteMany({
      branchId: new mongoose.Types.ObjectId(branchId),
      date: date
    });

    return NextResponse.json({
      success: true,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    console.error('Error deleting appointments:', error);
    return NextResponse.json(
      { error: 'Failed to delete appointments: ' + (error instanceof Error ? error.message : String(error)) },
      { status: 500 }
    );
  }
} 