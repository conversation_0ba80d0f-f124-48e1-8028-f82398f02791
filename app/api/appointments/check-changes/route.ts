import { NextResponse } from 'next/server';
import { ObjectId } from 'mongodb';
import { connectToDatabase } from '@/lib/mongodb';
import Reservation from '@/models/Reservation';
import { getExcludedStatusCodes } from '@/lib/utils/reservation-status-utils';

interface TimeSlot {
  _id?: string | ObjectId;
  startHour: string;
  endHour: string;
  enabled?: boolean;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
}

interface ExistingSlot {
  _id: ObjectId;
  branchId: ObjectId;
  date: string; // Date is stored as string in YYYY-MM-DD format
  startHour: string;
  endHour: string;
  enabled: boolean;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
}

// Add formatting helpers for slots
const formatSlot = (slot: any) => {
  return {
    id: slot._id ? slot._id.toString() : 'new',
    timeRange: `${slot.startHour} - ${slot.endHour}`,
    enabled: slot.enabled,
    capacity: slot.capacity,
    online: slot.online,
    home: slot.home,
    max_capacity_family: slot.max_capacity_family
  };
};

export async function POST(request: Request) {
  try {
    console.log("Starting check-changes API request");
    
    // Log the raw request data for debugging
    const requestBody = await request.text();
    console.log("Raw request body:", requestBody);
    
    // Parse request body
    let body;
    try {
      body = JSON.parse(requestBody);
      console.log("Request body parsed successfully");
    } catch (err) {
      console.error("Failed to parse JSON body:", err);
      return NextResponse.json({
        error: "Invalid JSON in request body",
        rawBody: requestBody.substring(0, 200) + (requestBody.length > 200 ? '...' : '')
      }, { status: 400 });
    }
    
    const { branchId, date, slots } = body;
    
    console.log("Request params:", { 
      branchId, 
      date, 
      slotsCount: slots?.length,
      firstSlot: slots && slots.length > 0 ? slots[0] : null,
      lastSlot: slots && slots.length > 0 ? slots[slots.length - 1] : null
    });

    // Validate request
    if (!branchId) {
      console.error("Missing branchId in request");
      return NextResponse.json({ error: 'Branch ID is required' }, { status: 400 });
    }
    if (!date) {
      console.error("Missing date in request");
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }
    if (!slots || !Array.isArray(slots)) {
      console.error("Missing or invalid slots array in request");
      return NextResponse.json({ 
        error: 'Slots must be an array',
        receivedType: typeof slots,
        receivedValue: slots
      }, { status: 400 });
    }

    console.log("Connecting to database...");
    const db = await connectToDatabase();
    console.log("Database connection successful");
    
    // Get existing slots for the given date and branch
    console.log(`Fetching existing slots for branch ${branchId} on date ${date}`);
    try {
      const existingSlots = await db
        .collection('appointments')
        .find({
          branchId: new ObjectId(branchId),
          date: date  // Search by exact string match
        })
        .toArray() as ExistingSlot[];
      
      console.log(`Found ${existingSlots.length} existing slots`);
      
      if (existingSlots.length > 0) {
        console.log("Sample existing slot:", existingSlots[0]);
      }

      // Create maps for efficient lookups
      const existingSlotById = new Map<string, ExistingSlot>();
      const existingSlotByTimeRange = new Map<string, ExistingSlot>();
      
      // Track which slots will be kept
      const existingIdsToKeep = new Set<string>();
      
      // Initialize maps for existing slots
      existingSlots.forEach((slot) => {
        const id = slot._id.toString();
        const timeRange = `${slot.startHour}:${slot.endHour}`;
        
        existingSlotById.set(id, slot);
        existingSlotByTimeRange.set(timeRange, slot);
      });

      // Create a set of new slots' time ranges and a map of new slots by time range
      const newTimeRanges = new Set(
        slots.map((slot: TimeSlot) => `${slot.startHour}:${slot.endHour}`)
      );
      const newSlotsByTimeRange = new Map(
        slots.map((slot: TimeSlot) => [`${slot.startHour}:${slot.endHour}`, slot])
      );
      
      // Find slots that will be deleted and slots with capacity decrements
      let toDelete = 0;
      let slotsToBeDeleted: string[] = [];
      let slotsToBeDeletedDetails: Array<{
        id: string;
        timeRange: string;
        startHour: string;
        endHour: string;
        capacity: number;
        online: number;
        home: number;
        max_capacity_family: number;
      }> = [];
      const slotsToBeDeletedWithReservations: Array<{
        id: string;
        timeRange: string;
        startHour: string;
        endHour: string;
        capacity: number;
        online: number;
        home: number;
        max_capacity_family: number;
        hasReservations: boolean;
      }> = [];
      // New: Track slots with capacity decrements below reservations
      const slotsWithCapacityBelowReservations: Array<{
        id: string;
        timeRange: string;
        startHour: string;
        endHour: string;
        oldCapacity: number;
        newCapacity: number;
        reservationCount: number;
      }> = [];
      // Get excluded status codes dynamically
      const excludedStatusCodes = await getExcludedStatusCodes();

      for (const [id, slot] of Array.from(existingSlotById.entries())) {
        const timeRange = `${slot.startHour}:${slot.endHour}`;
        const newSlot = newSlotsByTimeRange.get(timeRange);
        // Slot is being deleted if not present in new slots
        if (!newSlot) {
          toDelete++;
          slotsToBeDeleted.push(timeRange);
          slotsToBeDeletedDetails.push({
            id: id,
            timeRange,
            startHour: slot.startHour,
            endHour: slot.endHour,
            capacity: slot.capacity,
            online: slot.online,
            home: slot.home,
            max_capacity_family: slot.max_capacity_family
          });
          // Check if this slot has reservations (excluding those with excludeFromAffectations statuses)
          const reservationCount = await Reservation.countDocuments({
            appointmentId: slot._id,
            isDeleted: { $ne: true },
            status: { $nin: excludedStatusCodes }
          });
          slotsToBeDeletedWithReservations.push({
            id: id,
            timeRange,
            startHour: slot.startHour,
            endHour: slot.endHour,
            capacity: slot.capacity,
            online: slot.online,
            home: slot.home,
            max_capacity_family: slot.max_capacity_family,
            hasReservations: reservationCount > 0
          });
        } else {
          // Slot exists, check for capacity decrement below reservations (excluding those with excludeFromAffectations statuses)
          const reservationCount = await Reservation.countDocuments({
            appointmentId: slot._id,
            isDeleted: { $ne: true },
            status: { $nin: excludedStatusCodes }
          });
          if (
            typeof newSlot.capacity === 'number' &&
            newSlot.capacity < reservationCount
          ) {
            slotsWithCapacityBelowReservations.push({
              id: id,
              timeRange,
              startHour: slot.startHour,
              endHour: slot.endHour,
              oldCapacity: slot.capacity,
              newCapacity: newSlot.capacity,
              reservationCount
            });
          }
        }
      }

      console.log(`Analysis complete: ${toDelete} slots will be deleted`);
      if (toDelete > 0) {
        console.log("Slots to be deleted:", slotsToBeDeleted);
        console.log("Slots to be deleted with details:", slotsToBeDeletedDetails);
      }
      
      // Format slots for easier comparison in logs
      const formattedExistingSlots = existingSlots.map(formatSlot);
      const formattedNewSlots = slots.map(formatSlot);

      console.log("Formatted existing slots:", formattedExistingSlots);
      console.log("Formatted new slots:", formattedNewSlots);

      return NextResponse.json({
        toDelete,
        existingSlotCount: existingSlots.length,
        newSlotCount: slots.length,
        slotsToBeDeleted: slotsToBeDeleted,
        slotsToBeDeletedDetails: slotsToBeDeletedDetails,
        slotsToBeDeletedDetailsWithReservations: slotsToBeDeletedWithReservations,
        slotsWithCapacityBelowReservations,
        debug: {
          existingIdCount: existingSlotById.size,
          existingTimeRanges: Array.from(existingSlotByTimeRange.keys()),
          newTimeRanges: Array.from(newTimeRanges),
          slotsWithIds: slots.filter(s => s._id).length,
          idsToKeep: Array.from(existingIdsToKeep),
          formattedExistingSlots,
          formattedNewSlots
        }
      });
    } catch (dbError: any) {
      console.error("Database operation error:", dbError);
      return NextResponse.json({
        error: `Database error: ${dbError.message}`,
        details: dbError.stack
      }, { status: 500 });
    }
  } catch (error: any) {
    console.error('Error checking changes:', error);
    
    return NextResponse.json(
      { 
        error: error.message || 'An error occurred while checking changes',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
} 