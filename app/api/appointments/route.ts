import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Appointment from '@/models/Appointment';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import Reservation from '@/models/Reservation';
import { getExcludedStatusCodes } from '@/lib/utils/reservation-status-utils';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!branchId) {
      return NextResponse.json({ error: 'Branch ID is required' }, { status: 400 });
    }

    await dbConnect();

    let query: any = { branchId };

    // If date range is provided
    if (startDate && endDate) {
      query.date = { $gte: startDate, $lte: endDate };
    }

    const appointments = await Appointment.find(query).sort({ date: 1, startHour: 1 }).lean();

    // Get excluded status codes dynamically
    const excludedStatusCodes = await getExcludedStatusCodes();

    // Get all appointment IDs
    const appointmentIds = appointments.map((app: any) => app._id);

    // Get excluded reservations count for capacity adjustment
    const excludedReservations = await Reservation.aggregate([
      {
        $match: {
          appointmentId: { $in: appointmentIds },
          status: { $in: excludedStatusCodes },
          isDeleted: { $ne: true }
        }
      },
      { $group: { _id: '$appointmentId', count: { $sum: 1 } } }
    ]);

    const excludedMap = Object.fromEntries(excludedReservations.map((r: any) => [r._id.toString(), r.count]));

    // Adjust capacity by subtracting excluded reservations (consistent with other endpoints)
    const appointmentsWithAdjustedCapacity = appointments.map((appointment: any) => {
      const excludedCount = excludedMap[appointment._id.toString()] || 0;
      const adjustedCapacity = Math.max(0, (appointment.capacity || 0) - excludedCount);

      return {
        ...appointment,
        capacity: adjustedCapacity,
        // Store original capacity for reference if needed
        originalCapacity: appointment.capacity
      };
    });

    return NextResponse.json({ appointments: appointmentsWithAdjustedCapacity });

  } catch (error: any) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { branchId, date, startHour, endHour, capacity, online, home, max_capacity_family } = body;

    if (!branchId || !date || !startHour || !endHour || !capacity || !online || !home || !max_capacity_family) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    await dbConnect();

    const appointment = await Appointment.create({
      branchId,
      date,
      startHour,
      endHour,
      capacity,
      online,
      home,
      max_capacity_family
    });

    return NextResponse.json(appointment);

  } catch (error: any) {
    console.error('Error creating appointment:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 