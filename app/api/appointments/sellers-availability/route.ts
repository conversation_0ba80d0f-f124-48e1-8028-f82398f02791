import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Branch from '@/models/Branch';
import Calendar from '@/models/Calendar';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

interface SellerAvailability {
  _id: string;
  name: string;
  email: string;
  status: 'present' | 'partially_available' | 'absent';
}

interface AppointmentSellersData {
  appointmentId: string;
  date: string;
  startHour: string;
  endHour: string;
  sellers: {
    present: SellerAvailability[];
    partially_available: SellerAvailability[];
    absent: SellerAvailability[];
    counts: {
      present: number;
      partially_available: number;
      absent: number;
    };
  };
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const body = await request.json();
    const { branchId, appointments } = body;

    if (!branchId || !appointments || !Array.isArray(appointments)) {
      return NextResponse.json({ 
        error: 'Missing required parameters: branchId and appointments array' 
      }, { status: 400 });
    }

    // Find the branch
    const branch = await Branch.findById(branchId);
    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }

    // Get all users from the branch (responsible, agents, and sellers)
    const allUserIds = [
      ...(branch.responsible || []),
      ...(branch.agents || []),
      ...(branch.sellers || [])
    ];

    // Remove duplicates
    const uniqueUserIds = Array.from(new Set(allUserIds.map(id => id.toString())));

    // Get all users data with their roles
    const allUsers = await User.find({
      _id: { $in: uniqueUserIds }
    }).select('_id name email roles').populate('roles', '_id name').lean();

    // Get all unique dates from appointments
    const uniqueDates = [...new Set(appointments.map((apt: any) => apt.date))];

    // Get all calendars for these users and dates
    const calendars = await Calendar.find({
      userId: { $in: uniqueUserIds }
    }).lean();

    // Create a map of user calendars for quick lookup
    const calendarMap = new Map();
    calendars.forEach(calendar => {
      calendarMap.set(calendar.userId.toString(), calendar.availability || {});
    });

    // Process each appointment
    const appointmentsData: AppointmentSellersData[] = [];

    for (const appointment of appointments) {
      const { _id: appointmentId, date, startHour, endHour } = appointment;

      // Generate all hours needed for this appointment
      const requiredHours = [];
      const startHourNum = parseInt(startHour.split(':')[0], 10);
      const endHourNum = parseInt(endHour.split(':')[0], 10);
      
      for (let hour = startHourNum; hour < endHourNum; hour++) {
        requiredHours.push(`${hour.toString().padStart(2, '0')}:00`);
      }

      const present: SellerAvailability[] = [];
      const partially_available: SellerAvailability[] = [];
      const absent: SellerAvailability[] = [];

      // Check availability for each user
      for (const user of allUsers) {
        const userId = user._id.toString();
        const userCalendar = calendarMap.get(userId) || {};
        const userAvailableHours = userCalendar[date] || [];

        // Count how many required hours the user has available
        const availableRequiredHours = requiredHours.filter(hour =>
          userAvailableHours.includes(hour)
        );

        const sellerData: SellerAvailability = {
          _id: user._id.toString(),
          name: user.name,
          email: user.email || '',
          status: 'absent'
        };

        if (availableRequiredHours.length === requiredHours.length) {
          // User has all required hours available
          sellerData.status = 'present';
          present.push(sellerData);
        } else if (availableRequiredHours.length > 0) {
          // User has some but not all required hours available
          sellerData.status = 'partially_available';
          partially_available.push(sellerData);
        } else {
          // User has no required hours available
          // Only include in absent list if user has seller role
          const hasSellersRole = user.roles && user.roles.some((role: any) =>
            role._id && role._id.toString() === '67e0aad60f0a3bdeba18542c'
          );

          if (hasSellersRole) {
            sellerData.status = 'absent';
            absent.push(sellerData);
          }
          // Non-sellers who are absent are simply not included in any list
        }
      }

      appointmentsData.push({
        appointmentId: appointmentId.toString(),
        date,
        startHour,
        endHour,
        sellers: {
          present,
          partially_available,
          absent,
          counts: {
            present: present.length,
            partially_available: partially_available.length,
            absent: absent.length
          }
        }
      });
    }

    return NextResponse.json({ 
      branchId,
      appointments: appointmentsData 
    });

  } catch (error) {
    console.error('Error fetching sellers availability:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch sellers availability' 
    }, { status: 500 });
  }
}
