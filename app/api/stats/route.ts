import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { validateDashboardToken, extractTokenFromRequest } from '@/lib/utils/dashboard-token-utils';
import { connectToDatabase } from '@/lib/mongodb';
import Branch from '@/models/Branch';
import User from '@/models/User';
import { getUserRoles } from '@/app/api/utils/server-permission-utils';
import { isSuperAdmin } from '@/lib/utils/role-utils';
import { ReservationStatsService, ReservationStatsFilters } from '@/lib/services/reservation-stats-service';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  let responseData: any = {};
  let statusCode = 200;

  try {
  await dbConnect();

    // Extract query parameters for filtering
    const { searchParams } = new URL(req.url);
    const filterBranches = searchParams.get('filterBranches')?.split(',').filter(Boolean);
    const filterDateFrom = searchParams.get('filterDateFrom');
    const filterDateTo = searchParams.get('filterDateTo');
    const filterAgentOrSeller = searchParams.get('filterAgentOrSeller');
    const filterUserId = searchParams.get('filterUserId');
    const timezone = searchParams.get('timezone'); // Client timezone

    const compareBranches = searchParams.get('compareBranches')?.split(',').filter(Boolean);
    const compareDateFrom = searchParams.get('compareDateFrom');
    const compareDateTo = searchParams.get('compareDateTo');
    const compareAgentOrSeller = searchParams.get('compareAgentOrSeller');
    const compareUserId = searchParams.get('compareUserId');

    // Get users with PAP and Seller roles
    const papRoleId = '67fbd1707839bdba5be4b02b';
    const sellerRoleId = '67e0aad60f0a3bdeba18542c';

    const papUsers = await User.find(
      {
        roles: papRoleId,
        deletedAt: null,
        isActive: true
      },
      '_id name email'
    ).sort({ name: 1 });

    const sellerUsers = await User.find(
      {
        roles: sellerRoleId,
        deletedAt: null,
        isActive: true
      },
      '_id name email'
    ).sort({ name: 1 });

    // Determine authentication type and available branches
    let availableBranches;
    let dashboardType;
    let isAuthenticated = false;

    // Check for dashboard token first
    const dashboardToken = extractTokenFromRequest(req);

    if (dashboardToken) {
      const tokenValidation = await validateDashboardToken(dashboardToken);
      if (tokenValidation.isValid) {
        // For dashboard-view: all branches are available
        availableBranches = await Branch.find({}, 'name _id phone').sort({ name: 1 });
        dashboardType = 'dashboard-view';
        isAuthenticated = true;
      } else {
        responseData = {
          message: 'Invalid dashboard token'
        };
        statusCode = 401;
      }
    } else {
      // Fall back to session authentication
      const session = await getServerSession(authOptions);
      if (session?.user) {
        // Get user roles to check if SuperAdmin
        const userRoles = await getUserRoles(session);
        const isUserSuperAdmin = isSuperAdmin(userRoles);

        if (isUserSuperAdmin) {
          // SuperAdmin: all branches are available
          availableBranches = await Branch.find({}, 'name _id phone').sort({ name: 1 });
        } else {
          // Regular admin: only branches where userId exists in responsible array
          const userId = session.user.id;
          availableBranches = await Branch.find(
            { responsible: userId },
            'name _id phone'
          ).sort({ name: 1 });
        }

        dashboardType = 'admin-dashboard';
        isAuthenticated = true;
      } else {
        responseData = {
          message: 'No authentication found'
        };
        statusCode = 401;
      }
    }

    // If authenticated, proceed with common processing
    if (isAuthenticated && availableBranches) {
      // Calculate actual stats if filters are provided
      let statsData: any = {
        message: 'No filters applied',
        timestamp: new Date().toISOString(),
        hasFilters: !!(filterBranches || filterDateFrom || filterAgentOrSeller || filterUserId),
        hasCompareFilters: !!(compareBranches || compareDateFrom || compareAgentOrSeller || compareUserId)
      };

      const hasFilters = !!(filterBranches || filterDateFrom || filterAgentOrSeller || filterUserId);
      const hasCompareFilters = !!(compareBranches || compareDateFrom || compareAgentOrSeller || compareUserId);

      if (hasFilters) {
        try {
          const statsService = ReservationStatsService.getInstance();

          // Build filters
          const filters: ReservationStatsFilters = {
            branches: filterBranches || undefined,
            dateFrom: filterDateFrom || undefined,
            dateTo: filterDateTo || undefined,
            agentOrSeller: filterAgentOrSeller as 'agent' | 'seller' || undefined,
            userId: filterUserId || undefined,
            timezone: timezone || undefined
          };

          // Build compare filters if provided
          const compareFiltersObj: ReservationStatsFilters = {
            branches: compareBranches || undefined,
            dateFrom: compareDateFrom || undefined,
            dateTo: compareDateTo || undefined,
            agentOrSeller: compareAgentOrSeller as 'agent' | 'seller' || undefined,
            userId: compareUserId || undefined,
            timezone: timezone || undefined
          };

          // Validate compare settings
          if (hasCompareFilters && !statsService.validateCompareSettings(filters, compareFiltersObj)) {
            responseData = {
              message: 'Invalid compare settings: agent type must match between filters and compare settings'
            };
            statusCode = 400;
          } else {
            // Calculate stats
            if (hasCompareFilters) {
              const result = await statsService.calculateStatsWithComparison(filters, compareFiltersObj);
              statsData = {
                stats: result.stats,
                compareStats: result.compareStats,
                comparisons: result.comparisons,
                comparisonContext: result.comparisonContext,
                timestamp: new Date().toISOString(),
                hasFilters: true,
                hasCompareFilters: true
              };
            } else {
              const result = await statsService.calculateStats(filters);
              statsData = {
                stats: result,
                timestamp: new Date().toISOString(),
                hasFilters: true,
                hasCompareFilters: false
              };
            }
          }
        } catch (error: any) {
          console.error('Error calculating stats:', error);
          statsData = {
            error: 'Failed to calculate stats',
            message: error.message,
            timestamp: new Date().toISOString(),
            hasFilters: true,
            hasCompareFilters
          };
        }
      }

      responseData = {
        message: dashboardType,
        availableBranches: availableBranches,
        papUsers: papUsers,
        sellerUsers: sellerUsers,
        // Include applied filters in response
        appliedFilters: {
          filterBranches,
          filterDateFrom,
          filterDateTo,
          filterAgentOrSeller,
          filterUserId
        },
        compareFilters: {
          compareBranches,
          compareDateFrom,
          compareDateTo,
          compareAgentOrSeller,
          compareUserId
        },
        statsData
      };
    }

  } catch (error: any) {
    console.error('Error in stats endpoint:', error);
    responseData = {
      message: 'Failed to get stats'
    };
    statusCode = 500;
  }

  return NextResponse.json(responseData, { status: statusCode });
}
