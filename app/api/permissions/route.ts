import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Permission from '@/models/Permission';

export async function GET() {
  try {
    await dbConnect();
    const permissions = await Permission.find().lean();
    return NextResponse.json(permissions);
  } catch (error) {
    console.error('GET permissions error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch permissions' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    await dbConnect();
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.code) {
      return NextResponse.json(
        { error: 'Name and code are required' },
        { status: 400 }
      );
    }

    // Check if permission code already exists
    const existingPermission = await Permission.findOne({
      $or: [
        { code: body.code },
        { name: body.name }
      ]
    });

    if (existingPermission) {
      return NextResponse.json(
        { error: 'Permission with this name or code already exists' },
        { status: 400 }
      );
    }

    // Create new permission
    const permission = await Permission.create({
      name: body.name,
      code: body.code.toUpperCase(),
      description: body.description || body.name
    });

    return NextResponse.json(permission);
  } catch (error: any) {
    console.error('Create permission error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create permission' },
      { status: 500 }
    );
  }
} 