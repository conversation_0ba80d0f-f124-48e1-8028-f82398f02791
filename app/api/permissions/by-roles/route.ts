import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Role from '@/models/Role';
import Permission from '@/models/Permission';

export async function POST(request: Request) {
  try {
    await dbConnect();
    const { roleIds } = await request.json();

    // Fetch roles with populated permissions
    const roles = await Role.find({
      _id: { $in: roleIds }
    }).populate('permissions');

    // Extract all permission codes
    const permissions = roles.flatMap(role => role.permissions);

    // Get full permission details
    const fullPermissions = await Permission.find({
      _id: { $in: permissions }
    });

    return NextResponse.json(fullPermissions);
  } catch (error) {
    console.error('Error fetching role permissions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch permissions' },
      { status: 500 }
    );
  }
} 