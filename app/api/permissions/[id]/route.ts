import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Permission from '@/models/Permission';

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    const body = await request.json();
    const permission = await Permission.findByIdAndUpdate(params.id, body, { new: true });
    return NextResponse.json(permission);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to update permission' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    await Permission.findByIdAndDelete(params.id);
    return NextResponse.json({ message: 'Permission deleted successfully' });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to delete permission' }, { status: 500 });
  }
} 