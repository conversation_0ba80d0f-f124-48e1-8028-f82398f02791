import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

const ALLOWED_ROOTS = ['/var/log/amq_partners', '/var/log/amq_partners/dev'];

function isAllowed(p: string) {
  return ALLOWED_ROOTS.some(root => p === root || p.startsWith(root + '/'));
}

export async function GET(req: NextRequest) {
  const url = new URL(req.url);
  const filePath = url.searchParams.get('path');
  if (!filePath || !isAllowed(filePath) || !filePath.endsWith('.txt')) {
    return NextResponse.json({ error: 'Invalid or forbidden path' }, { status: 400 });
  }
  try {
    const text = await fs.readFile(filePath, 'utf8');
    const lines = text.split(/\r?\n/).filter(Boolean);
    const content = lines.map(line => {
      try {
        return JSON.parse(line);
      } catch {
        return { error: 'Invalid JSON', line };
      }
    });
    return NextResponse.json({ content });
  } catch (err: any) {
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
} 