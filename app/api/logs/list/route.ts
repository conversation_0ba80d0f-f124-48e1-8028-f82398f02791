import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

const ALLOWED_ROOTS = ['/var/log/amq_partners', '/var/log/amq_partners/dev'];

function isAllowed(p: string) {
  return ALLOWED_ROOTS.some(root => p === root || p.startsWith(root + '/'));
}

export async function GET(req: NextRequest) {
  const url = new URL(req.url);
  const dirPath = url.searchParams.get('path');
  if (!dirPath || !isAllowed(dirPath)) {
    return NextResponse.json({ error: 'Invalid or forbidden path' }, { status: 400 });
  }
  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });
    const result = entries.map(entry => ({
      path: path.join(dirPath, entry.name),
      name: entry.name,
      isDir: entry.isDirectory(),
    }));
    return NextResponse.json({ entries: result });
  } catch (err: any) {
    return NextResponse.json({ error: err.message }, { status: 500 });
  }
} 