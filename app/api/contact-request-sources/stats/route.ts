import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import ContactRequestSource from '@/models/ContactRequestSource';
import { getUserPermissions } from '../../utils/server-permission-utils';
import { CONTACT_REQUEST_PERMISSIONS, PermissionCode } from '@/types/permission-codes';
import { hasPermission } from '@/lib/permissions';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const userPermissions = await getUserPermissions(session);
    if (!hasPermission(userPermissions as PermissionCode[], CONTACT_REQUEST_PERMISSIONS.MANAGE_CONTACT_REQUESTS_SOURCES)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

  await dbConnect();

    // Get basic statistics
    const totalSources = await ContactRequestSource.countDocuments();
    
    // Get total hits across all sources
    const hitsSummary = await ContactRequestSource.aggregate([
      {
        $group: {
          _id: null,
          totalHits: { $sum: '$hits' },
          averageHits: { $avg: '$hits' }
        }
      }
    ]);

    const totalHits = hitsSummary[0]?.totalHits || 0;
    const averageHits = hitsSummary[0]?.averageHits || 0;

    // Get top performing sources
    const topSources = await ContactRequestSource.find()
      .sort({ hits: -1 })
      .limit(5)
      .select('tag source influencer hits bitlyUrl')
      .lean();

    // Get performance by source type
    const sourceTypeStats = await ContactRequestSource.aggregate([
      {
        $group: {
          _id: '$source',
          count: { $sum: 1 },
          totalHits: { $sum: '$hits' },
          averageHits: { $avg: '$hits' }
        }
      },
      {
        $sort: { totalHits: -1 }
      }
    ]);

    // Get performance by influencer
    const influencerStats = await ContactRequestSource.aggregate([
      {
        $group: {
          _id: '$influencer',
          count: { $sum: 1 },
          totalHits: { $sum: '$hits' },
          averageHits: { $avg: '$hits' }
        }
      },
      {
        $sort: { totalHits: -1 }
      }
    ]);

    // Get recent activity (sources created in last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentSources = await ContactRequestSource.find({
      createdAt: { $gte: sevenDaysAgo }
    })
    .sort({ createdAt: -1 })
    .limit(10)
    .select('tag source influencer hits createdAt')
    .lean();

    // Get sources with recent activity (hits in last 24 hours based on webhook data)
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    const recentActivity = await ContactRequestSource.find({
      'webhookData.timestamp': { $gte: oneDayAgo }
    })
    .sort({ 'webhookData.timestamp': -1 })
    .limit(10)
    .select('tag source influencer hits webhookData')
    .lean();

    return NextResponse.json({
      summary: {
        totalSources,
        totalHits,
        averageHits: Math.round(averageHits * 100) / 100
      },
      topSources,
      sourceTypeStats,
      influencerStats,
      recentSources,
      recentActivity
    });
  } catch (error) {
    console.error('Error fetching contact request sources stats:', error);
    return NextResponse.json({ error: 'Failed to fetch statistics.' }, { status: 500 });
  }
}