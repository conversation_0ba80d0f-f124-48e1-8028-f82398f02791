import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import ContactRequestSource from '@/models/ContactRequestSource';
import { getUserPermissions } from '../../utils/server-permission-utils';
import { CONTACT_REQUEST_PERMISSIONS, PermissionCode } from '@/types/permission-codes';
import { hasPermission } from '@/lib/permissions';
import BitlyService, { BitlyApiError } from '@/lib/services/bitly';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const userPermissions = await getUserPermissions(session);
    if (!hasPermission(userPermissions as PermissionCode[], CONTACT_REQUEST_PERMISSIONS.MANAGE_CONTACT_REQUESTS_SOURCES)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

  await dbConnect();

    // Get query parameters for time range
    const url = new URL(req.url);
    const timeUnit = url.searchParams.get('unit') || 'day';
    const timeUnits = parseInt(url.searchParams.get('units') || '30', 10);

    // Validate time parameters
    const validUnits = ['minute', 'hour', 'day', 'week', 'month'];
    if (!validUnits.includes(timeUnit)) {
      return NextResponse.json({ error: 'Invalid time unit' }, { status: 400 });
    }

    // Get all sources from database
    const sources = await ContactRequestSource.find()
      .select('tag source influencer bitlyUrl hits')
      .lean();

    if (sources.length === 0) {
      return NextResponse.json({
        summary: {
          totalSources: 0,
          totalClicks: 0,
          averageClicks: 0
        },
        clickTrends: [],
        geographicData: {
          countries: [],
          cities: []
        },
        deviceData: [],
        referrerData: [],
        sourcePerformance: [],
        influencerPerformance: []
      });
    }

    const bitlyService = new BitlyService();
    const analyticsData: any = {
      summary: {
        totalSources: sources.length,
        totalClicks: 0,
        averageClicks: 0
      },
      clickTrends: [],
      geographicData: {
        countries: [],
        cities: []
      },
      deviceData: [],
      referrerData: [],
      sourcePerformance: [],
      influencerPerformance: []
    };

    // Collect analytics for each source
    const sourceAnalytics = [];
    const clicksParams = {
      unit: timeUnit as any,
      units: timeUnits
    };

    const metricsParams = {
      ...clicksParams,
      size: 50
    };

    for (const source of sources) {
      try {
        // Extract bitlink ID from URL
        const bitlinkId = source.bitlyUrl.replace(/^https?:\/\//, '');
        
        // Get click summary
        const clicksSummary = await bitlyService.getBitlinkClicksSummary(bitlinkId, clicksParams);
        
        // Get detailed click data
        const clicksData = await bitlyService.getBitlinkClicks(bitlinkId, clicksParams);
        
        // Get geographic data
        const countriesData = await bitlyService.getBitlinkCountries(bitlinkId, metricsParams);
        const citiesData = await bitlyService.getBitlinkCities(bitlinkId, metricsParams);
        
        // Get device data
        const devicesData = await bitlyService.getBitlinkDevices(bitlinkId, metricsParams);
        
        // Get referrer data
        const referrersData = await bitlyService.getBitlinkReferrers(bitlinkId, metricsParams);

        sourceAnalytics.push({
          source,
          clicksSummary,
          clicksData,
          countriesData,
          citiesData,
          devicesData,
          referrersData
        });

        analyticsData.summary.totalClicks += clicksSummary.total_clicks;
      } catch (error) {
        console.warn(`Failed to get analytics for source ${source.tag}:`, error);
        // Continue with other sources even if one fails
        sourceAnalytics.push({
          source,
          error: error instanceof BitlyApiError ? error.message : 'Unknown error'
        });
      }
    }

    // Calculate average clicks
    analyticsData.summary.averageClicks = Math.round(
      (analyticsData.summary.totalClicks / sources.length) * 100
    ) / 100;

    // Aggregate click trends across all sources
    const clickTrendsMap = new Map();
    sourceAnalytics.forEach(({ clicksData }) => {
      if (clicksData?.link_clicks) {
        clicksData.link_clicks.forEach((dataPoint: any) => {
          const existing = clickTrendsMap.get(dataPoint.date) || 0;
          clickTrendsMap.set(dataPoint.date, existing + dataPoint.clicks);
        });
      }
    });

    analyticsData.clickTrends = Array.from(clickTrendsMap.entries())
      .map(([date, clicks]) => ({ date, clicks }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Aggregate geographic data
    const countriesMap = new Map();
    const citiesMap = new Map();
    
    sourceAnalytics.forEach(({ countriesData, citiesData }) => {
      if (countriesData?.metrics) {
        countriesData.metrics.forEach((metric: any) => {
          const existing = countriesMap.get(metric.value) || 0;
          countriesMap.set(metric.value, existing + metric.clicks);
        });
      }
      
      if (citiesData?.metrics) {
        citiesData.metrics.forEach((metric: any) => {
          const key = `${metric.city}, ${metric.region}, ${metric.country}`;
          const existing = citiesMap.get(key) || { ...metric, clicks: 0 };
          citiesMap.set(key, { ...existing, clicks: existing.clicks + metric.clicks });
        });
      }
    });

    analyticsData.geographicData.countries = Array.from(countriesMap.entries())
      .map(([country, clicks]) => ({ country, clicks }))
      .sort((a, b) => b.clicks - a.clicks)
      .slice(0, 20);

    analyticsData.geographicData.cities = Array.from(citiesMap.values())
      .sort((a, b) => b.clicks - a.clicks)
      .slice(0, 20);

    // Aggregate device data
    const devicesMap = new Map();
    sourceAnalytics.forEach(({ devicesData }) => {
      if (devicesData?.metrics) {
        devicesData.metrics.forEach((metric: any) => {
          const existing = devicesMap.get(metric.device_type) || 0;
          devicesMap.set(metric.device_type, existing + metric.clicks);
        });
      }
    });

    analyticsData.deviceData = Array.from(devicesMap.entries())
      .map(([device_type, clicks]) => ({ device_type, clicks }))
      .sort((a, b) => b.clicks - a.clicks);

    // Aggregate referrer data
    const referrersMap = new Map();
    sourceAnalytics.forEach(({ referrersData }) => {
      if (referrersData?.metrics) {
        referrersData.metrics.forEach((metric: any) => {
          const existing = referrersMap.get(metric.value) || 0;
          referrersMap.set(metric.value, existing + metric.clicks);
        });
      }
    });

    analyticsData.referrerData = Array.from(referrersMap.entries())
      .map(([referrer, clicks]) => ({ referrer, clicks }))
      .sort((a, b) => b.clicks - a.clicks)
      .slice(0, 20);

    // Calculate performance by source type
    const sourceTypeMap = new Map();
    sourceAnalytics.forEach(({ source, clicksSummary }) => {
      if (clicksSummary) {
        const existing = sourceTypeMap.get(source.source) || { count: 0, totalClicks: 0 };
        sourceTypeMap.set(source.source, {
          count: existing.count + 1,
          totalClicks: existing.totalClicks + clicksSummary.total_clicks
        });
      }
    });

    analyticsData.sourcePerformance = Array.from(sourceTypeMap.entries())
      .map(([sourceType, data]) => ({
        sourceType,
        count: data.count,
        totalClicks: data.totalClicks,
        averageClicks: Math.round((data.totalClicks / data.count) * 100) / 100
      }))
      .sort((a, b) => b.totalClicks - a.totalClicks);

    // Calculate performance by influencer
    const influencerMap = new Map();
    sourceAnalytics.forEach(({ source, clicksSummary }) => {
      if (clicksSummary) {
        const existing = influencerMap.get(source.influencer) || { count: 0, totalClicks: 0 };
        influencerMap.set(source.influencer, {
          count: existing.count + 1,
          totalClicks: existing.totalClicks + clicksSummary.total_clicks
        });
      }
    });

    analyticsData.influencerPerformance = Array.from(influencerMap.entries())
      .map(([influencer, data]) => ({
        influencer,
        count: data.count,
        totalClicks: data.totalClicks,
        averageClicks: Math.round((data.totalClicks / data.count) * 100) / 100
      }))
      .sort((a, b) => b.totalClicks - a.totalClicks);

    return NextResponse.json(analyticsData);
  } catch (error) {
    console.error('Error fetching contact request sources analytics:', error);
    return NextResponse.json({ error: 'Failed to fetch analytics.' }, { status: 500 });
  }
}