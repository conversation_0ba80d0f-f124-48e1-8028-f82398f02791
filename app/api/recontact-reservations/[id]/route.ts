import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import RecontactReservation from '@/models/RecontactReservation';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// GET - Get single recontact reservation
export const GET = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid ID' }, { status: 400 });
    }

    const recontactReservation = await RecontactReservation.aggregate([
      { $match: { _id: new mongoose.Types.ObjectId(id) } },
      {
        $lookup: {
          from: 'recontactreservationstatuses',
          localField: 'statusId',
          foreignField: '_id',
          as: 'status'
        }
      },
      { $unwind: '$status' }
    ]);

    if (!recontactReservation.length) {
      return NextResponse.json(
        { message: 'Recontact reservation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(recontactReservation[0]);

  } catch (error) {
    console.error('Error fetching recontact reservation:', error);
    return NextResponse.json(
      { message: 'Failed to fetch recontact reservation' },
      { status: 500 }
    );
  }
};

// PUT - Update recontact reservation
export const PUT = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;
    const body = await request.json();

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid ID' }, { status: 400 });
    }

    const updateData: any = {
      updatedBy: session.user.id
    };

    if (body.recontactDate) {
      updateData.recontactDate = new Date(body.recontactDate);
    }

    if (body.statusId) {
      updateData.statusId = body.statusId;
    }

    const updatedRecontact = await RecontactReservation.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!updatedRecontact) {
      return NextResponse.json(
        { message: 'Recontact reservation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedRecontact);

  } catch (error) {
    console.error('Error updating recontact reservation:', error);
    return NextResponse.json(
      { message: 'Failed to update recontact reservation' },
      { status: 500 }
    );
  }
};

// DELETE - Delete recontact reservation
export const DELETE = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid ID' }, { status: 400 });
    }

    const deletedRecontact = await RecontactReservation.findByIdAndDelete(id);

    if (!deletedRecontact) {
      return NextResponse.json(
        { message: 'Recontact reservation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Recontact reservation deleted successfully' });

  } catch (error) {
    console.error('Error deleting recontact reservation:', error);
    return NextResponse.json(
      { message: 'Failed to delete recontact reservation' },
      { status: 500 }
    );
  }
};
