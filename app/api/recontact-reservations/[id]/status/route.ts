import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import RecontactReservation from '@/models/RecontactReservation';
import RecontactReservationStatus from '@/models/RecontactReservationStatus';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// PATCH - Update recontact reservation status
export const PATCH = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await context.params;
    const body = await request.json();
    const { statusId } = body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ message: 'Invalid ID' }, { status: 400 });
    }

    if (!statusId || !mongoose.Types.ObjectId.isValid(statusId)) {
      return NextResponse.json({ message: 'Invalid status ID' }, { status: 400 });
    }

    // Validate status exists
    const status = await RecontactReservationStatus.findById(statusId);
    if (!status) {
      return NextResponse.json({ message: 'Status not found' }, { status: 404 });
    }

    const updatedRecontact = await RecontactReservation.findByIdAndUpdate(
      id,
      {
        statusId: statusId,
        updatedBy: session.user.id
      },
      { new: true, runValidators: true }
    );

    if (!updatedRecontact) {
      return NextResponse.json(
        { message: 'Recontact reservation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedRecontact);

  } catch (error) {
    console.error('Error updating recontact status:', error);
    return NextResponse.json(
      { message: 'Failed to update status' },
      { status: 500 }
    );
  }
};
