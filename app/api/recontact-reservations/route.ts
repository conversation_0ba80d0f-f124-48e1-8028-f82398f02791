import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import RecontactReservation from '@/models/RecontactReservation';
import RecontactReservationStatus from '@/models/RecontactReservationStatus';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// GET - List recontact reservations with filtering and pagination
export const GET = async (request: NextRequest) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const statusId = searchParams.get('statusId');
    const search = searchParams.get('search');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Build filter query
    const filter: any = {};

    if (statusId) {
      filter.statusId = new mongoose.Types.ObjectId(statusId);
    }

    if (search) {
      filter.$or = [
        { 'reservation.customerInfo.client1Name': { $regex: search, $options: 'i' } },
        { 'reservation.customerInfo.client2Name': { $regex: search, $options: 'i' } },
        { 'reservation.customerInfo.phone': { $regex: search, $options: 'i' } },
        { 'reservation.customerInfo.email': { $regex: search, $options: 'i' } }
      ];
    }

    if (dateFrom || dateTo) {
      filter.recontactDate = {};
      if (dateFrom) {
        // Start from beginning of the selected date
        const startOfDay = new Date(dateFrom + 'T00:00:00.000Z');
        startOfDay.setHours(startOfDay.getHours() + 4);
        filter.recontactDate.$gte = startOfDay;
        console.log('Date filter $gte:', startOfDay.toISOString());

        // If no dateTo is provided (single date selection), automatically set $lte to same date + 4 hours
        if (!dateTo) {
          const endOfSingleDate = new Date(dateFrom + 'T23:59:59.999Z');
          endOfSingleDate.setHours(endOfSingleDate.getHours() + 4);
          filter.recontactDate.$lte = endOfSingleDate;
          console.log('Single date filter $lte (auto-set):', endOfSingleDate.toISOString());
        }
      }
      if (dateTo) {
        // Account for timezone differences by ending 4 hours after the date
        const endOfDay = new Date(dateTo + 'T23:59:59.999Z');
        endOfDay.setHours(endOfDay.getHours() + 4);
        filter.recontactDate.$lte = endOfDay;
        console.log('Date range filter $lte:', endOfDay.toISOString());
      }
    }
    
    // Execute query with pagination
    const skip = (page - 1) * limit;



    const [data, total] = await Promise.all([
      RecontactReservation.aggregate([
        { $match: filter },
        {
          $lookup: {
            from: 'recontactreservationstatuses',
            localField: 'statusId',
            foreignField: '_id',
            as: 'status'
          }
        },
        { $unwind: '$status' },
        { $sort: { recontactDate: 1, createdAt: -1 } },
        { $skip: skip },
        { $limit: limit }
      ]),
      RecontactReservation.countDocuments(filter)
    ]);

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching recontact reservations:', error);
    return NextResponse.json(
      { message: 'Failed to fetch recontact reservations' },
      { status: 500 }
    );
  }
};

// POST - Create new recontact reservation (used by transfer service)
export const POST = async (request: NextRequest) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { recontactDate, statusId, reservation } = body;

    // Validate required fields
    if (!recontactDate || !statusId || !reservation) {
      return NextResponse.json(
        { message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate status exists
    const status = await RecontactReservationStatus.findById(statusId);
    if (!status) {
      return NextResponse.json(
        { message: 'Invalid status ID' },
        { status: 400 }
      );
    }

    const recontactReservation = new RecontactReservation({
      recontactDate: new Date(recontactDate),
      statusId: statusId,
      reservation,
      createdBy: session.user.id,
      updatedBy: session.user.id
    });

    await recontactReservation.save();
    
    return NextResponse.json(recontactReservation, { status: 201 });

  } catch (error) {
    console.error('Error creating recontact reservation:', error);
    return NextResponse.json(
      { message: 'Failed to create recontact reservation' },
      { status: 500 }
    );
  }
};
