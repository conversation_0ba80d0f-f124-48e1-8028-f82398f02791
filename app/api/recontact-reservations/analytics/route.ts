import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import RecontactReservation from '@/models/RecontactReservation';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// GET - Get analytics data for recontact reservations
export const GET = async (request: NextRequest) => {
  try {
  await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const statusId = searchParams.get('statusId');

    // Build filter query
    const filter: any = {};

    if (dateFrom || dateTo) {
      filter.recontactDate = {};
      if (dateFrom) filter.recontactDate.$gte = new Date(dateFrom);
      if (dateTo) filter.recontactDate.$lte = new Date(dateTo);
    }

    if (statusId) {
      filter.statusId = new mongoose.Types.ObjectId(statusId);
    }

    // Get total counts
    const totalCount = await RecontactReservation.countDocuments(filter);

    // Get status breakdown
    const statusBreakdown = await RecontactReservation.aggregate([
      { $match: filter },
      {
        $lookup: {
          from: 'recontactreservationstatuses',
          localField: 'statusId',
          foreignField: '_id',
          as: 'status'
        }
      },
      { $unwind: '$status' },
      {
        $group: {
          _id: '$statusId',
          count: { $sum: 1 },
          statusName: { $first: '$status.name' },
          statusColor: { $first: '$status.color' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get daily trends (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyTrends = await RecontactReservation.aggregate([
      {
        $match: {
          ...filter,
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Get upcoming recontacts (next 7 days)
    const today = new Date();
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const upcomingRecontacts = await RecontactReservation.aggregate([
      {
        $match: {
          recontactDate: {
            $gte: today,
            $lte: nextWeek
          }
        }
      },
      {
        $lookup: {
          from: 'recontactreservationstatuses',
          localField: 'statusId',
          foreignField: '_id',
          as: 'status'
        }
      },
      { $unwind: '$status' },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$recontactDate' }
          },
          count: { $sum: 1 },
          reservations: {
            $push: {
              _id: '$_id',
              customerName: '$reservation.customerInfo.client1Name',
              phone: '$reservation.customerInfo.phone',
              status: '$status.name',
              statusColor: '$status.color'
            }
          }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Get overdue recontacts
    const overdueRecontacts = await RecontactReservation.aggregate([
      {
        $match: {
          recontactDate: { $lt: today }
        }
      },
      {
        $lookup: {
          from: 'recontactreservationstatuses',
          localField: 'statusId',
          foreignField: '_id',
          as: 'status'
        }
      },
      { $unwind: '$status' },
      {
        $group: {
          _id: '$statusId',
          count: { $sum: 1 },
          statusName: { $first: '$status.name' },
          statusColor: { $first: '$status.color' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    const overdueCount = overdueRecontacts.reduce((sum, item) => sum + item.count, 0);

    return NextResponse.json({
      summary: {
        totalCount,
        overdueCount,
        upcomingCount: upcomingRecontacts.reduce((sum, day) => sum + day.count, 0)
      },
      statusBreakdown,
      dailyTrends,
      upcomingRecontacts,
      overdueRecontacts
    });

  } catch (error) {
    console.error('Error fetching recontact analytics:', error);
    return NextResponse.json(
      { message: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
};
