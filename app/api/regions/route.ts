import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import Region from '@/app/models/Region';
import { isValidObjectId } from 'mongoose';
import { REGION_PERMISSIONS } from '@/types/permission-codes';
import { hasPermission } from '@/lib/permissions';

export async function GET() {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const regions = await Region.find().sort({ name: 1 }).lean();
    return NextResponse.json(regions);
  } catch (error) {
    console.error('GET regions error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch regions' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.code) {
      return NextResponse.json(
        { error: 'Name and code are required' },
        { status: 400 }
      );
    }

    // Validate code format
    if (!/^[A-Z]{2}$/.test(body.code)) {
      return NextResponse.json(
        { error: 'Code must be exactly 2 uppercase letters' },
        { status: 400 }
      );
    }

    const region = await Region.create({
      name: body.name,
      code: body.code.toUpperCase()
    });

    return NextResponse.json(region);
  } catch (error: any) {
    console.error('Create region error:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A region with this name or code already exists' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to create region' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    if (!body._id || !isValidObjectId(body._id)) {
      return NextResponse.json(
        { error: 'Invalid region ID' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (!body.name || !body.code) {
      return NextResponse.json(
        { error: 'Name and code are required' },
        { status: 400 }
      );
    }

    // Validate code format
    if (!/^[A-Z]{2}$/.test(body.code)) {
      return NextResponse.json(
        { error: 'Code must be exactly 2 uppercase letters' },
        { status: 400 }
      );
    }

    const region = await Region.findByIdAndUpdate(
      body._id,
      {
        name: body.name,
        code: body.code.toUpperCase()
      },
      { new: true }
    ).lean();

    if (!region) {
      return NextResponse.json(
        { error: 'Region not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(region);
  } catch (error: any) {
    console.error('Update region error:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A region with this name or code already exists' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to update region' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id || !isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid region ID' },
        { status: 400 }
      );
    }

    const region = await Region.findByIdAndDelete(id).lean();

    if (!region) {
      return NextResponse.json(
        { error: 'Region not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Delete region error:', error);
    return NextResponse.json(
      { error: 'Failed to delete region' },
      { status: 500 }
    );
  }
} 