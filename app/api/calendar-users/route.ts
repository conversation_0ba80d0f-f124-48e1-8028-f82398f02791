import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { getServerSession } from 'next-auth';

export async function GET() {
  try {
    await dbConnect();
    const session = await getServerSession();

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find users and populate their roles
    const users = await User.find().populate('roles').select('_id name email roles');
    
    // Transform the response to include role names
    const transformedUsers = users.map(user => {
      const userData = user.toObject ? user.toObject() : user;
      return {
        _id: userData._id,
        name: userData.name,
        email: userData.email,
        // Extract role name from the first role if available
        roleName: userData.roles && userData.roles.length > 0 && userData.roles[0].name 
          ? userData.roles[0].name 
          : 'No role'
      };
    });

    return NextResponse.json({ users: transformedUsers });
  } catch (error) {
    console.error('Error fetching users for calendar:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}