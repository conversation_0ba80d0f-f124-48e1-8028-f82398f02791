import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { ObjectId } from 'mongodb';
import Appointment from '@/models/Appointment';
import { getExcludedStatusCodes } from '@/lib/utils/reservation-status-utils';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const legacyDate = searchParams.get('date'); // Get legacy date parameter
    const allDates = searchParams.get('allDates') === 'true';
    const branchId = searchParams.get('branchId');

    if (!branchId) {
      return NextResponse.json(
        { error: 'Branch ID parameter is required' },
        { status: 400 }
      );
    }

    // If legacy date is provided but not startDate/endDate, use it for both
    const effectiveStartDate = startDate || legacyDate;
    const effectiveEndDate = endDate || legacyDate;

    // If not showing all dates, either startDate/legacyDate must be provided
    if (!allDates && !effectiveStartDate) {
      return NextResponse.json(
        { error: 'When not showing all dates, at least startDate or date parameter is required' },
        { status: 400 }
      );
    }

    // Build the match condition based on parameters
    let matchCondition: any = {
      branchId: new ObjectId(branchId)
    };

    // Add date filtering if not showing all dates
    if (!allDates) {
      if (effectiveStartDate && effectiveEndDate) {
        // Date range filter
        matchCondition.date = { $gte: effectiveStartDate, $lte: effectiveEndDate };
      } else if (effectiveStartDate) {
        // Single date filter
        matchCondition.date = effectiveStartDate;
      }
    }

    // Get excluded status codes dynamically
    const excludedStatusCodes = await getExcludedStatusCodes();

    const appointments = await Appointment
      .aggregate([
        {
          $match: matchCondition
        },
        {
          $lookup: {
            from: 'reservations',
            let: { appointmentId: '$_id' },
            pipeline: [
              { $match: { $expr: { $and: [
                { $eq: ['$appointmentId', '$$appointmentId'] },
                { $not: { $in: ['$status', excludedStatusCodes] } },
                { $ne: ['$isDeleted', true] }
              ] },
             } }
            ],
            as: 'reservations'
          }
        },
        {
          $addFields: {
            reservationCounts: {
              branch: {
                $size: {
                  $filter: {
                    input: '$reservations',
                    as: 'reservation',
                    cond: { $eq: ['$$reservation.type', 'branch'] }
                  }
                }
              },
              online: {
                $size: {
                  $filter: {
                    input: '$reservations',
                    as: 'reservation',
                    cond: { $eq: ['$$reservation.type', 'online'] }
                  }
                }
              },
              family: {
                $size: {
                  $filter: {
                    input: '$reservations',
                    as: 'reservation',
                    cond: { $eq: ['$$reservation.type', 'family'] }
                  }
                }
              },
              home: {
                $size: {
                  $filter: {
                    input: '$reservations',
                    as: 'reservation',
                    cond: { $eq: ['$$reservation.type', 'home'] }
                  }
                }
              }
            }
          }
        },
        {
          $project: {
            reservations: 0
          }
        },
        {
          $sort: {
            date: 1,
            startHour: 1
          }
        }
      ]);

    let appointmentsWithCapacity = appointments;
    if (appointments.length > 0) {
      // Get all appointment IDs
      const appointmentIds = appointments.map((a: any) => a._id);
      // Fetch excluded reservations for all appointments
      const db = await dbConnect();
      const Reservation = db.model('Reservation');

      // Fetch excluded reservations using dynamic status codes
      const excludedReservations = await Reservation.aggregate([
        { $match: { appointmentId: { $in: appointmentIds }, status: { $in: excludedStatusCodes }, isDeleted: { $ne: true } } },
        { $group: { _id: '$appointmentId', count: { $sum: 1 } } }
      ]);

      const excludedMap = Object.fromEntries(excludedReservations.map((r: any) => [r._id.toString(), r.count]));

      // Adjust capacity in-memory
      appointmentsWithCapacity = appointments.map((a: any) => {
        const excludedCount = excludedMap[a._id.toString()] || 0;
        return {
          ...a,
          capacity: Math.max(0, (a.capacity || 0) - excludedCount)
        };
      });
    }
    return NextResponse.json({ appointments: appointmentsWithCapacity });
  } catch (error) {
    console.error('Error in appointments route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 