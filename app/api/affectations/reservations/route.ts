import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { ObjectId } from 'mongodb';
import Reservation from '@/models/Reservation';
import { getExcludedStatusCodes } from '@/lib/utils/reservation-status-utils';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const appointmentId = searchParams.get('appointmentId');

    if (!appointmentId) {
      return NextResponse.json(
        { error: 'Appointment ID is required' },
        { status: 400 }
      );
    }

    // Check if appointmentId is a comma-separated list
    const appointmentIds = appointmentId.includes(',')
      ? appointmentId.split(',').map(id => new ObjectId(id.trim()))
      : [new ObjectId(appointmentId)];

    // Get excluded status codes dynamically
    const excludedStatusCodes = await getExcludedStatusCodes();

    const reservations = await Reservation
      .aggregate([
        {
          $match: {
            appointmentId: { $in: appointmentIds },
            status: { $nin: excludedStatusCodes },
            isDeleted: { $ne: true }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'partnerId',
            foreignField: '_id',
            as: 'partner'
          }
        },
        {
          $addFields: {
            partner: { $arrayElemAt: ['$partner', 0] }
          }
        }
      ]);

    return NextResponse.json({ reservations });
  } catch (error) {
    console.error('Error in reservations route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 