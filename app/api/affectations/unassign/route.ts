import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectToMongooseDatabase from '@/lib/db';
import ReservationStatus from '@/models/ReservationStatus';
import mongoose from 'mongoose';
import Reservation from '@/models/Reservation';
import ReservationNote from '@/models/ReservationNote';
import { generateCommissions, deleteCommissionsBasedOnRecipient } from '@/app/api/utils/commission-utils';
import Branch from '@/models/Branch';
import { getUserFromToken } from '../utils/mobile_auth_utils';
import { getUserRoles, getUserPermissions } from '../../utils/server-permission-utils';
import { emitSocketEvent } from '../../utils/socket-utils';
export async function POST(request: Request) {
  const requestBody = await request.json();
  const requestHeaders = request.headers;
  let session=await getUserFromToken(requestBody,requestHeaders);
  try {
    
    // Connect to both MongoDB types for consistency
    const db = await connectToDatabase();
    await connectToMongooseDatabase();
    
    // Get user session for adding notes
    if(!session){
      session = await getServerSession(authOptions);
    }
    if(!session){
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    if(!session.user.roles){
      session.user.roles=await getUserRoles(session);
    }
    if(!session.user.permissions){
      session.user.permissions=await getUserPermissions(session);
    }
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { reservationId } = requestBody;

    if (!reservationId) {
      return NextResponse.json(
        { error: 'Reservation ID is required' },
        { status: 400 }
      );
    }

    // Get reservation details to include the previously assigned seller in note
    const reservation = await db.collection('reservations').findOne(
      { _id: new ObjectId(reservationId) }
    );

    if (!reservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }
    
    // Get seller information if there's an assigned user
    let sellerName = "Unknown";
    if (reservation.assigned_user_id) {
      const seller = await db.collection('users').findOne(
        { _id: reservation.assigned_user_id }
      );
      if (seller) {
        sellerName = seller.name;
      }
    }
    
    // --- Revert to previous status and push to previousStatuses ---
    let previousStatuses = Array.isArray(reservation.previousStatuses) ? [...reservation.previousStatuses] : [];
    console.log("previousStatuses",previousStatuses);
    let revertStatus = 'present';
    let revertStatusId = undefined;
    if (previousStatuses.length > 1) {
      // Look at the previous status (last entry)
      const prev = previousStatuses[previousStatuses.length - 2];
      console.log("prev",prev);
      revertStatus = prev.status;
      revertStatusId = prev.statusId;
    } else if (previousStatuses.length === 1) {
      revertStatus = "new";
      const revertstatus=await ReservationStatus.findOne({ code: revertStatus });
      revertStatusId = revertstatus._id;
    }
    // Push a new entry for the revert
    previousStatuses.push({
      status: revertStatus,
      statusId: revertStatusId,
      changedAt: new Date(),
      changedBy: session.user.id ? new ObjectId(session.user.id) : undefined,
    });

    // Update the reservation by removing assigned_user_id, presentAt, and reverting status
    const result = await db.collection('reservations').updateOne(
      { _id: new ObjectId(reservationId) },
      { 
        $set: { 
          status: revertStatus,
          updatedAt: new Date(),
          previousStatuses
        },
        $unset: {
          assigned_user_id: "",
          presentAt: ""
        }
      }
    );
      console.log("revertStatus",revertStatus);
    if (!result.matchedCount) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }
     // Get the updated reservation from Mongoose for consistency with the commission system
     const updatedReservation = await Reservation.findById(reservationId);
    const updateData:any={}
    updateData.status=updatedReservation.status;
    if (updatedReservation && updatedReservation.status !== reservation.status){
      console.log("updateData", updateData);
      if(!updateData.assigned_user_id){
          updateData.assigned_user_id=updatedReservation.assigned_user_id
      }
      if(!updateData.sellingAmount){
          updateData.sellingAmount=updatedReservation.sellingAmount
      }
      if(!updateData.confirmationType){
          updateData.confirmationType=updatedReservation.confirmationType
      }
      await emitSocketEvent('status_change', {reservationId: reservationId, status: updateData,userId: session.user.id});
  }
    // Create a note about the unassignment
    const noteContent = `Seller ${sellerName} unassigned from reservation. Status changed to 'Confirmé'.`;
    
    await db.collection('reservationnotes').insertOne({
      reservationId: new ObjectId(reservationId),
      userId: new ObjectId(session.user.id),
      content: noteContent,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Get the status ID for 'present' to use in commission generation
    const presentStatus = await ReservationStatus.findOne({ 
      code: 'PRESENT' 
    });
    console.log("presentStatus",presentStatus);
    if (presentStatus) {
      // Get the updated reservation from Mongoose for consistency with the commission system
      const updatedReservation = await Reservation.findById(reservationId);
      if (updatedReservation) {
        // When unassigning a user from a reservation, we need to:
        // 1. Remove any existing commissions for the previously assigned user that are related to commission types
        //    with statusTrigger ID 67ebad013e89231218d22719
        // 2. Generate new commissions based on the updated 'present' status
        
        // Delete commissions based on recipient field settings
        const deletedCommissionUsers = await deleteCommissionsBasedOnRecipient(
          db,
          reservationId,
          "67ebad013e89231218d22719",
          reservation
        );

        // Create a single note about the commission deletions if any were deleted
        if (deletedCommissionUsers.length > 0) {
          const userNames = deletedCommissionUsers.join(', ');
          const commissionNoteContent = `Deleted commissions for user(s) ${userNames} that were associated with the reservation.`;
          await db.collection('reservationnotes').insertOne({
            reservationId: new ObjectId(reservationId),
            userId: new ObjectId(session.user.id),
            content: commissionNoteContent,
            createdAt: new Date(),
            updatedAt: new Date()
          });
        }
        
        // Generate commissions based on the new status
        await generateCommissions(updatedReservation, presentStatus._id.toString(), session.user._id);
      }
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in unassign route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}