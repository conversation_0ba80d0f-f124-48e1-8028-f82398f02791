import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { getExcludedStatusCodes } from '@/lib/utils/reservation-status-utils';

interface User {
  _id: ObjectId;
  name: string;
  email: string;
  phone?: string;
  type?: string;
  isActive?: boolean;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const branchId = searchParams.get('branchId');

    if (!date || !branchId) {
      return NextResponse.json(
        { error: 'Date and Branch ID parameters are required' },
        { status: 400 }
      );
    }

    const db = await connectToDatabase();
    
    // First, fetch all appointments for the date and branch
    const appointments = await db.collection('appointments')
      .find({
        date: date,
        branchId: new ObjectId(branchId)
      })
      .sort({ startHour: 1 })
      .toArray();
    
    if (!appointments.length) {
      return NextResponse.json({ appointments: [], reservations: [], users: [] });
    }
    
    // Extract the appointment IDs
    const appointmentIds = appointments.map(app => app._id);

    // Get excluded status codes dynamically
    const excludedStatusCodes = await getExcludedStatusCodes();

    // Fetch reservations for these appointments with partner info
    const reservations = await db.collection('reservations')
      .aggregate([
        {
          $match: {
            appointmentId: { $in: appointmentIds },
            status: { $nin: excludedStatusCodes },
            isDeleted: { $ne: true }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'partnerId',
            foreignField: '_id',
            as: 'partnerObject'
          }
        },
        {
          $addFields: {
            // Add populated partner data for invitation sources
            partnerData: {
              $cond: {
                if: { $eq: ['$source', 'invitation'] },
                then: { $arrayElemAt: ['$partnerObject', 0] },
                else: null
              }
            }
          }
        }
      ])
      .toArray();
    
    // Get all assigned user IDs from reservations
    const assignedUserIds = reservations
      .filter(res => res.assigned_user_id)
      .map(res => new ObjectId(res.assigned_user_id));
    
    // If there are assigned users, fetch their details
    let users: User[] = [];
    if (assignedUserIds.length > 0) {
      users = await db.collection('users')
        .find({ _id: { $in: assignedUserIds } })
        .project({ 
          _id: 1, 
          name: 1, 
          email: 1, 
          phone: 1,
          type: 1,
          isActive: 1
        })
        .toArray() as unknown as User[];
    }
    
    // Get excluded reservations count for capacity adjustment
    const excludedReservations = await db.collection('reservations')
      .aggregate([
        {
          $match: {
            appointmentId: { $in: appointmentIds },
            status: { $in: excludedStatusCodes },
            isDeleted: { $ne: true }
          }
        },
        { $group: { _id: '$appointmentId', count: { $sum: 1 } } }
      ])
      .toArray();

    const excludedMap = Object.fromEntries(excludedReservations.map((r: any) => [r._id.toString(), r.count]));

    // Transform appointments to include formatted fields and adjusted capacity
    const formattedAppointments = appointments.map(appointment => {
      // Calculate counts for each type of reservation (only non-excluded reservations)
      const branchReservations = reservations.filter(
        r => r.appointmentId.toString() === appointment._id.toString() && r.type === 'branch'
      );
      const onlineReservations = reservations.filter(
        r => r.appointmentId.toString() === appointment._id.toString() && r.type === 'online'
      );
      const familyReservations = reservations.filter(
        r => r.appointmentId.toString() === appointment._id.toString() && r.type === 'family'
      );
      const homeReservations = reservations.filter(
        r => r.appointmentId.toString() === appointment._id.toString() && r.type === 'home'
      );

      // Adjust capacity by subtracting excluded reservations
      const excludedCount = excludedMap[appointment._id.toString()] || 0;
      const adjustedCapacity = Math.max(0, (appointment.capacity || 0) - excludedCount);

      return {
        ...appointment,
        capacity: adjustedCapacity,
        reservationCounts: {
          branch: branchReservations.length,
          online: onlineReservations.length,
          family: familyReservations.length,
          home: homeReservations.length
        }
      };
    });
    
    // Transform reservations to include appointment time and date
    const formattedReservations = reservations.map(reservation => {
      const appointment = appointments.find(
        app => app._id.toString() === reservation.appointmentId.toString()
      );
      
      // Find assigned user if any
      const assignedUser = reservation.assigned_user_id
        ? users.find(user => user._id.toString() === reservation.assigned_user_id.toString())
        : null;
        
      return {
        ...reservation,
        assignedUser,
        appointmentDate: appointment?.date,
        appointmentTime: appointment ? `${appointment.startHour}-${appointment.endHour}` : ''
      };
    });

    return NextResponse.json({
      appointments: formattedAppointments,
      reservations: formattedReservations,
      users
    });
  } catch (error) {
    console.error('Error in all-reservations route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 