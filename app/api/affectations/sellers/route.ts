import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Branch from '@/models/Branch';
import Calendar from '@/models/Calendar';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const appointmentDate = searchParams.get('appointmentDate');
    const appointmentStartTime = searchParams.get('appointmentStartTime');
    const appointmentEndTime = searchParams.get('appointmentEndTime');

    if (!branchId || !appointmentDate || !appointmentStartTime || !appointmentEndTime) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    // Find the branch
    const branch = await Branch.findById(branchId);
    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }

    // Get all users from the branch (responsible, agents, and sellers)
    const allUserIds = [
      ...(branch.responsible || []),
      ...(branch.agents || []),
      ...(branch.sellers || [])
    ];

    // Remove duplicates
    const uniqueUserIds = Array.from(new Set(allUserIds.map(id => id.toString())));

    // Generate all hours needed for the appointment
    const requiredHours = [];
    const startHour = parseInt(appointmentStartTime.split(':')[0], 10);
    const endHour = parseInt(appointmentEndTime.split(':')[0], 10);
    
    for (let hour = startHour; hour < endHour; hour++) {
      requiredHours.push(`${hour.toString().padStart(2, '0')}:00`);
    }

    // Find calendars with all required hours available
    const availabilityKey = `availability.${appointmentDate}`;
    const availableCalendars = await Calendar.find({
      userId: { $in: uniqueUserIds },
      [availabilityKey]: { $all: requiredHours }
    }).select('userId');

    // Get the user IDs with available calendars
    const availableUserIds = availableCalendars.map(calendar => calendar.userId);

    // Find the corresponding users
    const availableUsers = await User.find({
      _id: { $in: availableUserIds }
    }).select('_id name email');

    return NextResponse.json({ users: availableUsers });

  } catch (error) {
    console.error('Error fetching available branch users:', error);
    return NextResponse.json({ error: 'Failed to fetch available branch users' }, { status: 500 });
  }
}