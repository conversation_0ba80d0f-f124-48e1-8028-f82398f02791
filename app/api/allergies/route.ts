import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Allergy from '@/models/Allergy';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

interface AllergyData {
  name: string;
  name_en: string;
  createdAt: Date;
  updatedAt: Date;
}

export async function GET(request: Request) {
  try {
    // Connect to database
    try {
      await dbConnect();
    } catch (dbError) {
      console.error('Database connection error:', dbError);
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'fr';
    
    // Use Mongoose model instead of direct collection access
    const allergies = await Allergy.find({})
      .sort({ name: 1 })
      .lean();

    // Format response based on language
    const formattedAllergies = allergies.map((allergy) => ({
      _id: allergy._id,
      name: lang === 'en' ? allergy.name_en : allergy.name,
      name_en: allergy.name_en,
      name_fr: allergy.name
    }));

    return NextResponse.json(formattedAllergies);
  } catch (error) {
    console.error('Error fetching allergies:', error);
    
    // Provide more specific error messages based on the error type
    let errorMessage = 'Failed to fetch allergies';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.name_en) {
      return NextResponse.json(
        { error: 'Name and English name are required' },
        { status: 400 }
      );
    }

    // Connect to database using Mongoose
    try {
      await dbConnect();
    } catch (dbError) {
      console.error('Database connection error:', dbError);
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    // Use Mongoose model instead of direct collection access
    const newAllergy = new Allergy({
      name: body.name,
      name_en: body.name_en
    });
    
    const savedAllergy = await newAllergy.save();

    return NextResponse.json(savedAllergy);
  } catch (error: any) {
    console.error('Create allergy error:', error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'An allergy with this name already exists' },
        { status: 400 }
      );
    }
    
    // Provide more specific error messages based on the error type
    let errorMessage = 'Failed to create allergy';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
} 