import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Allergy from '@/models/Allergy';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { isValidObjectId } from 'mongoose';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!isValidObjectId(params.id)) {
      return NextResponse.json(
        { error: 'Invalid allergy ID' },
        { status: 400 }
      );
    }

    const allergy = await Allergy.findById(params.id).lean();
    
    if (!allergy) {
      return NextResponse.json(
        { error: 'Allergy not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(allergy);
  } catch (error) {
    console.error('GET allergy error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch allergy' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!isValidObjectId(params.id)) {
      return NextResponse.json(
        { error: 'Invalid allergy ID' },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.name_en) {
      return NextResponse.json(
        { error: 'Name and English name are required' },
        { status: 400 }
      );
    }

    const updatedAllergy = await Allergy.findByIdAndUpdate(
      params.id,
      {
        name: body.name,
        name_en: body.name_en,
      },
      { new: true, runValidators: true }
    ).lean();

    if (!updatedAllergy) {
      return NextResponse.json(
        { error: 'Allergy not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedAllergy);
  } catch (error: any) {
    console.error('Update allergy error:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'An allergy with this name already exists' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to update allergy' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!isValidObjectId(params.id)) {
      return NextResponse.json(
        { error: 'Invalid allergy ID' },
        { status: 400 }
      );
    }

    const deletedAllergy = await Allergy.findByIdAndDelete(params.id);

    if (!deletedAllergy) {
      return NextResponse.json(
        { error: 'Allergy not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Allergy deleted successfully' });
  } catch (error) {
    console.error('Delete allergy error:', error);
    return NextResponse.json(
      { error: 'Failed to delete allergy' },
      { status: 500 }
    );
  }
} 