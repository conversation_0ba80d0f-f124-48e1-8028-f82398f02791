import { NextRequest, NextResponse } from 'next/server';
import { StatsExportService } from '@/lib/services/stats-export-service';
import fs from 'fs';
import path from 'path';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const resolvedParams = await params;
    console.log('Download request received for token:', resolvedParams?.token);
    const { token } = resolvedParams;

    if (!token) {
      console.log('No token provided');
      return NextResponse.json(
        { error: 'Download token is required' },
        { status: 400 }
      );
    }

    const exportService = StatsExportService.getInstance();
    const tokenData = await exportService.getDownloadTokenData(token);

    console.log('Token data:', tokenData);

    if (!tokenData) {
      console.log('Invalid or expired token');
      return NextResponse.json(
        { error: 'Invalid or expired download link' },
        { status: 404 }
      );
    }

    const filePath = path.join(process.cwd(), 'temp', 'exports', tokenData.fileName);
    console.log('Looking for file at:', filePath);

    if (!fs.existsSync(filePath)) {
      console.log('File not found at path:', filePath);
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Read file
    const fileBuffer = fs.readFileSync(filePath);
    const fileExtension = path.extname(tokenData.fileName).toLowerCase();

    // Determine content type
    let contentType = 'application/octet-stream';
    if (fileExtension === '.csv') {
      contentType = 'text/csv';
    } else if (fileExtension === '.pdf') {
      contentType = 'application/pdf';
    }

    // Create response with file
    const response = new NextResponse(fileBuffer);
    response.headers.set('Content-Type', contentType);
    response.headers.set('Content-Disposition', `attachment; filename="${tokenData.fileName}"`);
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;
  } catch (error) {
    console.error('Error serving download:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
