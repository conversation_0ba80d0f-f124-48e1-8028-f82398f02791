import { NextRequest, NextResponse } from 'next/server';
import { StatsExportService } from '@/lib/services/stats-export-service';
import { DailyStatsAggregationService } from '@/lib/services/daily-stats-aggregation';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import StatsEmailSettings from '@/models/StatsEmailSettings';

export async function POST(req: NextRequest) {
  try {
    console.log('Export generation request received');

    // Authenticate request
    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated) {
      console.log('Authentication failed');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { emailSettingId, date, format } = body;
    console.log('Export request params:', { emailSettingId, date, format });

    if (!emailSettingId || !date || !format) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    if (!['csv', 'pdf'].includes(format)) {
      return NextResponse.json(
        { error: 'Invalid format. Must be csv or pdf' },
        { status: 400 }
      );
    }

    // Get email setting to determine selected stats
    const emailSetting = await StatsEmailSettings.findById(emailSettingId);
    if (!emailSetting) {
      return NextResponse.json(
        { error: 'Email setting not found' },
        { status: 404 }
      );
    }

    // Get selected stats
    const selectedStats = emailSetting.getSelectedStatsArray();

    // Aggregate statistics
    const statsService = DailyStatsAggregationService.getInstance();
    const statsData = await statsService.aggregateDailyStats(date);

    // Generate export
    const exportService = StatsExportService.getInstance();
    const result = await exportService.generateExport(statsData, {
      format,
      date,
      selectedStats,
      emailSettingId
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Export generation failed' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      downloadUrl: result.downloadUrl,
      fileName: result.fileName,
      expiresAt: result.expiresAt
    });
  } catch (error) {
    console.error('Error generating export:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
