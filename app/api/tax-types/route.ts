import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import TaxType from '@/models/TaxType';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { BILLING_PERMISSIONS, SETTINGS_PERMISSIONS } from '@/types/permission-codes';
import { getUserPermissions } from '../utils/server-permission-utils';

// GET: List all tax types
export async function GET() {
  await dbConnect();
  
  try {
    const taxTypes = await TaxType.find().lean();
    return NextResponse.json({ taxTypes });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch tax types.' }, { status: 500 });
  }
}

// POST: Create a new tax type
export async function POST(req: NextRequest) {
  await dbConnect();
  const session = await getServerSession(authOptions);
  if(!session){
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(session && !session.user.permissions){
    session.user.permissions=await getUserPermissions(session);
  }
  
  if (!session?.user || !session.user.permissions?.includes(BILLING_PERMISSIONS.MANAGE_TAX_TYPES)) {
    return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
  }
  try {
    const { code, names, percentages } = await req.json();
    if (!code || !names || !percentages) {
      return NextResponse.json({ error: 'Missing required fields.' }, { status: 400 });
    }
    const created = await TaxType.create({ code, names, percentages });
    return NextResponse.json({ taxType: created });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create tax type.' }, { status: 500 });
  }
} 