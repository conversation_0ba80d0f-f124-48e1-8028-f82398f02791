import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import TaxType from '@/models/TaxType';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { BILLING_PERMISSIONS, SETTINGS_PERMISSIONS } from '@/types/permission-codes';
import mongoose from 'mongoose';
import { getUserPermissions } from '../../utils/server-permission-utils';

interface Params {
  params: { id: string };
}

// GET: Get a single tax type by id
export async function GET(request: NextRequest, { params }: Params) {
  await dbConnect();
  const { id } =await params;
  if (!mongoose.Types.ObjectId.isValid(id)) {
    return NextResponse.json({ error: 'Invalid tax type ID' }, { status: 400 });
  }
  try {
    const taxType = await TaxType.findById(id).lean();
    if (!taxType) {
      return NextResponse.json({ error: 'Tax type not found' }, { status: 404 });
    }
    return NextResponse.json({ taxType });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch tax type.' }, { status: 500 });
  }
}

// PUT: Update a tax type by id
export async function PUT() {
  return NextResponse.json({ error: 'Editing tax types is not allowed.' }, { status: 403 });
}

// DELETE: Delete a tax type by id
export async function DELETE() {
  return NextResponse.json({ error: 'Delete not allowed.' }, { status: 405 });
} 