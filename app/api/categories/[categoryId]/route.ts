import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';

export async function PUT(
  request: Request,
  { params }: { params: { categoryId: string } }
) {
  try {
    const connection = await dbConnect();
    if (!connection.connection.db) {
      throw new Error('Database not connected');
    }
    const db = connection.connection.db;
    
    const data = await request.json();
    
    const result = await db.collection('Category').findOneAndUpdate(
      { _id: new mongoose.Types.ObjectId(params.categoryId) },
      { 
        $set: {
          ...data,
          updatedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    if (!result) {
      return NextResponse.json({ error: 'Category not found' }, { status: 404 });
    }

    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('PUT Error:', error);
    return NextResponse.json({ error: 'Failed to update category' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { categoryId: string } }
) {
  try {
    const connection = await dbConnect();
    if (!connection.connection.db) {
      throw new Error('Database not connected');
    }
    
    const result = await connection.connection.db.collection('Category').deleteOne({
      _id: new mongoose.Types.ObjectId(params.categoryId)
    });

    if (result.deletedCount === 0) {
      return NextResponse.json({ error: 'Category not found' }, { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('DELETE Error:', error);
    return NextResponse.json({ error: 'Failed to delete category' }, { status: 500 });
  }
} 