import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';

export async function GET() {
  try {
    const connection = await dbConnect();
    if (!connection.connection.db) {
      throw new Error('Database not connected');
    }
    
    const categories = await connection.connection.db
      .collection('Category')
      .find({})
      .sort({ order: 1 })
      .toArray();
      
    return NextResponse.json(categories);
  } catch (error) {
    console.error('GET Error:', error);
    return NextResponse.json({ error: 'Failed to fetch categories' }, { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const connection = await dbConnect();
    if (!connection.connection.db) {
      throw new Error('Database not connected');
    }
    const db = connection.connection.db;
    
    const data = await req.clone().json();
    
    const lastCategory = await db
      .collection('Category')
      .findOne({}, { sort: { order: -1 } });
    
    const nextOrder = (lastCategory?.order ?? -1) + 1;
    
    const result = await db.collection('Category').insertOne({
      ...data,
      order: nextOrder,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    const newCategory = await db
      .collection('Category')
      .findOne({ _id: result.insertedId });

    return NextResponse.json(newCategory);
  } catch (error) {
    console.error('POST Error:', error);
    return NextResponse.json(
      { error: 'Failed to create category' }, 
      { status: 500 }
    );
  }
} 