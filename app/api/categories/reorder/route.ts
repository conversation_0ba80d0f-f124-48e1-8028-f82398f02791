import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';

export async function PUT(request: Request) {
  try {
    const connection = await dbConnect();
    if (!connection.connection.db) {
      throw new Error('Database not connected');
    }
    const db = connection.connection.db;
    
    const { categories } = await request.json();
    
    // Update each category's order in sequence
    for (const { id, order } of categories) {
      await db.collection('Category').updateOne(
        { _id: new mongoose.Types.ObjectId(id) },
        { $set: { order, updatedAt: new Date() } }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Reorder Error:', error);
    return NextResponse.json(
      { error: 'Failed to reorder categories' },
      { status: 500 }
    );
  }
} 