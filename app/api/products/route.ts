import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';

export async function GET() {
  try {
    const connection = await dbConnect();
    if (!connection.connection.db) {
      throw new Error('Database not connected');
    }
    
    const products = await connection.connection.db
      .collection('Product')
      .find({})
      .sort({ generalOrder: 1 })
      .toArray();
      
    return NextResponse.json(products);
  } catch (error) {
    console.error('GET Error:', error);
    return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const connection = await dbConnect();
    if (!connection.connection.db) {
      throw new Error('Database not connected');
    }
    const db = connection.connection.db;
    
    const data = await req.clone().json();
    
    const lastProduct = await db
      .collection('Product')
      .findOne({}, { sort: { generalOrder: -1 } });
    
    const nextOrder = (lastProduct?.generalOrder ?? -1) + 1;
    
    const result = await db.collection('Product').insertOne({
      ...data,
      generalOrder: nextOrder,
      featuredOrder: data.isFeatured ? nextOrder : 0,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    const newProduct = await db
      .collection('Product')
      .findOne({ _id: result.insertedId });

    return NextResponse.json(newProduct);
  } catch (error) {
    console.error('POST Error:', error);
    return NextResponse.json(
      { error: 'Failed to create product' }, 
      { status: 500 }
    );
  }
} 