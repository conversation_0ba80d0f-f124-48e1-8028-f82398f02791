import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Product from '@/models/Product';

export async function PUT(request: Request) {
  try {
    await dbConnect();
    const { categoryId, products } = await request.json();

    // Update each product's category order
    await Promise.all(
      products.map((product: any) =>
        Product.findByIdAndUpdate(product._id, {
          categoryOrder: product.categoryOrder,
        })
      )
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Reorder error:', error);
    return NextResponse.json(
      { error: 'Failed to update product order' },
      { status: 500 }
    );
  }
} 