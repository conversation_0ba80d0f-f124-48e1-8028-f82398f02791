import { NextResponse, NextRequest } from 'next/server';
import dbConnect from '@/lib/db';
import ScheduledSMS from '@/models/ScheduledSMS';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

// Helper to check if user is a SuperAdmin
const isSuperAdmin = (session: any) => {
  if (!session?.user?.roles) return false;
  
  return session.user.roles.some((role: any) => 
    typeof role === 'object' 
      ? role._id.toString() === '67add3214badd3283e873329' 
      : role.toString() === '67add3214badd3283e873329'
  );
};

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    
    // Check if user is authorized
    if (!session || !isSuperAdmin(session)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    const { id } = await params;
    
    // Validate ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
    }
    
    // Get request body
    const body = await request.json();
    const { action, scheduledAt } = body;
    
    // Fetch the SMS
    const sms = await ScheduledSMS.findById(id);
    if (!sms) {
      return NextResponse.json({ error: 'Scheduled SMS not found' }, { status: 404 });
    }
    
    // Only allow rescheduling if status is 'pending' or 'failed'
    if (action === 'reschedule' && (sms.status === 'pending' || sms.status === 'failed' || sms.status === 'canceled')) {
      if (!scheduledAt) {
        return NextResponse.json({ error: 'scheduledAt is required for rescheduling' }, { status: 400 });
      }
      
      const newScheduledAt = new Date(scheduledAt);
      
      // Validate date
      if (isNaN(newScheduledAt.getTime())) {
        return NextResponse.json({ error: 'Invalid date format' }, { status: 400 });
      }
      
      // Set status to pending and update scheduledAt
      sms.status = 'pending';
      sms.scheduledAt = newScheduledAt;
      await sms.save();
      
      return NextResponse.json({ message: 'SMS rescheduled successfully', sms });
    } 
    // Handle cancellation
    else if (action === 'cancel') {
      sms.status = 'canceled';
      await sms.save();
      
      return NextResponse.json({ message: 'SMS canceled successfully', sms });
    } 
    else {
      return NextResponse.json({ error: 'Invalid action or SMS status' }, { status: 400 });
    }
    
  } catch (error: any) {
    console.error('Error updating scheduled SMS:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 