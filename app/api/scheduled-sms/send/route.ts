import { NextRequest, NextResponse } from 'next/server';
import ScheduledSMS from '@/models/ScheduledSMS';
import Reservation from '@/models/Reservation';
import Appointment from '@/models/Appointment';
import Branch from '@/models/Branch';
import { sendSMS, normalizePhoneNumber } from '@/lib/twilio';
import mongoose from 'mongoose';

export async function POST(request: NextRequest) {
  const logs: string[] = [];
  const smsResults: any[] = [];
  const startTime = Date.now();
  try {
    logs.push('Starting scheduled-sms/send API endpoint execution');
    // Find all scheduled SMS that are pending and due
    const now = new Date();
    const scheduledSMSList = await ScheduledSMS.find({
      status: 'pending',
      scheduledAt: { $lte: now }
    });
    logs.push(`Found ${scheduledSMSList.length} scheduled SMS to process`);
    let sent = 0;
    let failed = 0;
    let errors: any[] = [];
    for (const sms of scheduledSMSList) {
      const smsLog: string[] = [];
      try {
        smsLog.push(`Processing SMS ${sms._id}`);
        // Get reservation
        const reservation = await Reservation.findById(sms.reservationId);
        if (!reservation) throw new Error('Reservation not found');
        smsLog.push('Found reservation');
        // Get appointment
        const appointment = await Appointment.findById(reservation.appointmentId);
        if (!appointment) throw new Error('Appointment not found');
        smsLog.push('Found appointment');
        // Get branch
        const branch = await Branch.findById(appointment.branchId);
        if (!branch) throw new Error('Branch not found');
        smsLog.push('Found branch');
        // Get phone numbers
        const from = normalizePhoneNumber(branch.automatedPhone);
        const to = normalizePhoneNumber(reservation.customerInfo.phone);
        if (!from || !to) throw new Error('Missing phone number');
        smsLog.push(`Sending SMS from ${from} to ${to}`);
        // Send SMS
        await sendSMS(from, to, sms.body, true);
        // Update status
        sms.status = 'sent';
        await sms.save();
        sent++;
        smsLog.push('SMS sent and status updated to sent');
        smsResults.push({ smsId: sms._id, status: 'sent', logs: smsLog });
      } catch (err: any) {
        sms.status = 'failed';
        await sms.save();
        failed++;
        errors.push({ smsId: sms._id, error: err.message });
        smsLog.push('Failed to send scheduled SMS: ' + err.message);
        smsResults.push({ smsId: sms._id, status: 'failed', logs: smsLog });
        console.error('Failed to send scheduled SMS:', err);
      }
    }
    const endTime = Date.now();
    logs.push(`Processed ${scheduledSMSList.length} scheduled SMS. Sent: ${sent}, Failed: ${failed}`);
    logs.push(`Total execution time: ${endTime - startTime}ms`);
    return NextResponse.json({ sent, failed, errors, smsResults, logs });
  } catch (error: any) {
    logs.push('Error in scheduled SMS sender route: ' + error.message);
    console.error('Error in scheduled SMS sender route:', error);
    return NextResponse.json({ error: error.message, logs }, { status: 500 });
  }
} 