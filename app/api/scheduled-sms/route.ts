import { NextResponse, NextRequest } from 'next/server';
import dbConnect from '@/lib/db';
import ScheduledSMS from '@/models/ScheduledSMS';
import Reservation from '@/models/Reservation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { getUserPermissions, getUserRoles } from '../utils/server-permission-utils';

// Helper to check if user is a SuperAdmin
const isSuperAdmin = (session: any) => {
  if (!session?.user?.roles) return false;
  
  return session.user.roles.some((role: any) => 
    typeof role === 'object' 
      ? role._id.toString() === '67add3214badd3283e873329' 
      : role.toString() === '67add3214badd3283e873329'
  );
};

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    if(!session){
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    if(!session.user.roles){
      session.user.roles=await getUserRoles(session);
    }
    if(!session.user.permissions){
      session.user.permissions=await getUserPermissions(session);
    }
    // Check if user is authorized
    if (!isSuperAdmin(session)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    const search = url.searchParams.get('search') || '';
    
    // Build query
    let query: any = {};
    if (search) {
      query.body = { $regex: search, $options: 'i' };
    }
    
    // Get total count for pagination
    const total = await ScheduledSMS.countDocuments(query);
    
    // Get basic SMS data without trying to populate
    const messages = await ScheduledSMS.find(query)
      .sort({ scheduledAt: -1 })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .lean();
    
    // Fetch all reservations for these messages in a single query
    const reservationIds = messages.map(msg => msg.reservationId);
    const reservations = await Reservation.find({
      _id: { $in: reservationIds }
    }).lean();
    
    // Create a map for easy lookup
    const reservationMap: Record<string, any> = {};
    for (const res of reservations) {
      // TypeScript-safe approach to access _id
      const id = res._id ? res._id.toString() : '';
      if (id) {
        reservationMap[id] = res;
      }
    }
    
    // Now enhance the data with custom fields
    const enhancedMessages = messages.map(msg => {
      const reservationId = msg.reservationId.toString();
      const reservation = reservationMap[reservationId];
      
      // Safe access to nested properties
      const customerName = reservation?.customerInfo?.client1Name || "Customer";
      const phone = reservation?.customerInfo?.phone || "Unknown";
      
      return {
        ...msg,
        // Replace object ID references with objects that match our expected structure
        templateId: {
          _id: msg.templateId,
          name: "SMS Template" // Default name since we can't populate
        },
        reservationId: {
          _id: msg.reservationId,
          customerInfo: {
            name: customerName,
            phone: phone
          }
        }
      };
    });
    
    return NextResponse.json({
      messages: enhancedMessages,
      pagination: {
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    });
    
  } catch (error: any) {
    console.error('Error fetching scheduled SMS:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
} 