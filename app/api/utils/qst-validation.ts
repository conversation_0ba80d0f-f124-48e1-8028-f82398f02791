// QST Registration Number Validation Utility
// Shared function for validating QST numbers with Revenu Quebec API

export interface QSTValidationResponse {
  isValid: boolean;
  status?: string; // Store the actual status value for audit purposes
  statusDescription?: string; // Human-readable status description
  apiResponse?: {
    Resultat?: {
      StatutSousDossierUsager?: string;
      DescriptionStatut?: string;
      DateStatut?: string;
      NomEntreprise?: string;
      RaisonSociale?: string;
    } | null;
    OperationReussie?: boolean;
    MessagesFonctionnels?: Array<{
      DescriptionMessage?: string;
      CodeMessage?: string;
    }>;
    MessagesInformatifs?: Array<any>;
  };
  errorMessage?: string;
}

export async function validateQSTRegistrationNumber(qstNumber: string): Promise<QSTValidationResponse> {
  // Validate that the QST number is in the correct format (10 digits + TQ + 4 digits)
  if (!qstNumber || !/^\d{10}TQ\d{4}$/.test(qstNumber.replace(/\s/g, ''))) {
    return {
      isValid: false,
      errorMessage: 'QST registration number must be in format: 10 digits + TQ + 4 digits (e.g., 1234567890TQ0001)',
      apiResponse: {
        OperationReussie: false,
        Resultat: null
      }
    };
  }

  // Use the full QST number as provided (no auto-appending)
  const formattedQstNumber = qstNumber.replace(/\s/g, '');

  // Real Revenu Quebec API endpoint for QST validation
  // See: https://svcnab2b.revenuquebec.ca/2019/02/ValidationTVQ/{qstNumber}
  const endpoint = `https://svcnab2b.revenuquebec.ca/2019/02/ValidationTVQ/${formattedQstNumber}`;
  console.log("QST validation endpoint:", endpoint);

  try {
    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const res = await fetch(endpoint, {
      method: 'GET',
      headers: {
        // Use the exact headers from the working browser request
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Priority': 'u=0, i',
        'Cache-Control': 'max-age=0'
      },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!res.ok) {
      const errorText = await res.text();
      console.error('QST API error:', res.status, errorText);

      // Handle specific error cases
      if (res.status === 403) {
        return {
          isValid: false,
          errorMessage: 'QST validation service temporarily unavailable (access restricted)'
        };
      }

      return {
        isValid: false,
        errorMessage: `API error: ${res.status} - Service unavailable`
      };
    }

    const responseText = await res.text();

    // Try to parse as JSON first (for detailed response)
    try {
      const jsonResponse = JSON.parse(responseText);
      console.log('QST API Response:', JSON.stringify(jsonResponse, null, 2));

      // Extract status information from the API response
      const status = jsonResponse.Resultat?.StatutSousDossierUsager;
      const statusDescription = jsonResponse.Resultat?.DescriptionStatut;

      // Determine validity based on status
      // "R" = Registered and valid (holder is registered for QST and meets all requirements)
      // "A" = Revoked/cancelled (registration has been revoked or cancelled)
      // Any other value = Invalid
      let isValid = false;
      let errorMessage: string | undefined;

      if (jsonResponse.OperationReussie === true && jsonResponse.Resultat) {
        if (status === 'R') {
          isValid = true;
          // No error message for valid registrations
        } else if (status === 'A') {
          isValid = false;
          errorMessage = 'QST registration has been revoked or cancelled by Revenu Québec';
        } else {
          isValid = false;
          // Provide more specific error messages based on status
          if (status) {
            errorMessage = `QST registration status "${status}" is not valid. Only status "R" (registered) is considered valid.`;
          } else {
            errorMessage = 'QST registration status could not be determined';
          }
        }
      } else {
        isValid = false;
        // Handle error messages from the API
        if (jsonResponse.MessagesFonctionnels && jsonResponse.MessagesFonctionnels.length > 0) {
          const errorCode = jsonResponse.MessagesFonctionnels[0].CodeMessage;
          if (errorCode === 'GX.IdentifiantEntreeInvalide') {
            errorMessage = 'Invalid QST registration number format';
          } else {
            errorMessage = `QST validation failed: ${errorCode}`;
          }
        } else {
          errorMessage = 'QST validation operation failed - unable to verify registration status';
        }
      }

      return {
        isValid,
        status,
        statusDescription,
        apiResponse: jsonResponse,
        errorMessage
      };
    } catch (parseError) {
      console.error('Failed to parse QST API response as JSON:', parseError);
      console.log('Raw response text:', responseText);

      // Handle plain text responses
      const responseTextLower = responseText.trim().toLowerCase();

      if (responseTextLower === 'demande non conforme') {
        return {
          isValid: false,
          errorMessage: 'QST registration number format is non-compliant (demande non conforme)',
          apiResponse: {
            OperationReussie: false,
            Resultat: null
          }
        };
      }

      // Fallback for other plain text responses
      const isValid = responseTextLower === 'true';
      return {
        isValid,
        apiResponse: {
          OperationReussie: isValid,
          Resultat: null
        },
        errorMessage: isValid ? undefined : `QST registration is invalid: ${responseText}`
      };
    }
  } catch (err) {
    console.error('QST API call failed:', err);

    // Handle specific error types
    if (err instanceof Error) {
      if (err.name === 'AbortError') {
        return {
          isValid: false,
          errorMessage: 'QST validation request timed out'
        };
      }
      return {
        isValid: false,
        errorMessage: `QST validation failed: ${err.message}`
      };
    }

    return {
      isValid: false,
      errorMessage: 'QST validation service unavailable'
    };
  }
}
