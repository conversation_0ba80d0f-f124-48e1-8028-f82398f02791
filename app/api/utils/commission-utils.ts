import mongoose from 'mongoose';
import CommissionType from '@/models/CommissionType';
import Commission from '@/models/Commission';
import ReservationNote from '@/models/ReservationNote';
import Appointment from '@/models/Appointment';
import { ObjectId } from 'mongodb';

/**
 * Checks if a commission type is active based on date and time restrictions
 */
export const isCommissionTypeActive = (commissionType: any, currentDate: Date): boolean => {
  // If it's always active, return true
  if (commissionType.isAlwaysActive) {
    return true;
  }

  // Check date range
  if (commissionType.startDate && commissionType.endDate) {
    const startDate = new Date(commissionType.startDate);
    const endDate = new Date(commissionType.endDate);
    
    if (currentDate < startDate || currentDate > endDate) {
      return false;
    }
  }

  // Check time restrictions
  if (commissionType.timeRestricted && commissionType.startTime && commissionType.endTime) {
    const hours = currentDate.getHours();
    const minutes = currentDate.getMinutes();
    const currentTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    
    if (currentTime < commissionType.startTime || currentTime > commissionType.endTime) {
      return false;
    }
  }

  // Check day of week restrictions
  if (commissionType.dayRestricted && commissionType.daysOfWeek && commissionType.daysOfWeek.length > 0) {
    const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
    if (!commissionType.daysOfWeek.includes(dayOfWeek)) {
      return false;
    }
  }

  return true;
};

/**
 * Generates commissions for a reservation when status changes
 */
export const generateCommissions = async (reservation: any, statusId: string, sessionUserId: string) => {
  try {
    // Get all commission types that are triggered by this status
    const commissionTypes = await CommissionType.find({
      statusTrigger: new mongoose.Types.ObjectId(statusId)
    }).lean();

    if (!commissionTypes || commissionTypes.length === 0) {
      return [];
    }

    const currentDate = new Date();
    const createdCommissions = [];

    // Fetch branch information if the reservation has an appointmentId
    let branchId = null;
    
    // Try to get branchId from preferences first
    if (reservation.preferences && reservation.preferences.branchId) {
      branchId = reservation.preferences.branchId;
    }
    
    // If not available, try to get from the appointment
    if (!branchId && reservation.appointmentId) {
      try {
        const appointment = await Appointment.findById(reservation.appointmentId).lean();
        if (appointment && appointment.branchId) {
          branchId = appointment.branchId.toString();
        }
      } catch (error) {
        console.error('Error fetching appointment:', error);
        // Continue without branch information
      }
    }

    // Check each commission type
    for (const commissionType of commissionTypes) {
      // Skip if the commission type is not active based on date/time restrictions
      if (!isCommissionTypeActive(commissionType, currentDate)) {
        continue;
      }

      // Check branch eligibility if restricted
      if (commissionType.branchRestricted && 
          commissionType.eligibleBranches && 
          commissionType.eligibleBranches.length > 0) {
        
        // If reservation doesn't have a branchId or the branch is not in the eligible list, skip
        if (!branchId || 
            !commissionType.eligibleBranches.some((branch: mongoose.Types.ObjectId) => 
              branch.toString() === branchId)) {
          // Branch not eligible, skip this commission type
          continue;
        }
      }

      // Determine recipients of the commission
      const recipients: mongoose.Types.ObjectId[] = [];
      /*
        THIS IS FOR FUTURE USE NEVER DELETE THIS
        // If partner is a recipient
        if (commissionType.recipient.branchPAP && reservation.partnerId) {
          recipients.push(new mongoose.Types.ObjectId(reservation.partnerId));
        }
        // If assigned user is a recipient
        if (commissionType.recipient.assignedUser && reservation.assigned_user_id) {
          recipients.push(new mongoose.Types.ObjectId(reservation.assigned_user_id));
        }
      */
      console.log(reservation.partnerId, 'reservation.partnerId');
      //--------this is for now ---------
      recipients.push(reservation.partnerId);
      //--------------------------------

      // Filter recipients by user eligibility if restricted
      let eligibleRecipients = [...recipients];
      if (commissionType.userRestricted && 
          commissionType.eligibleUsers && 
          commissionType.eligibleUsers.length > 0) {
        
        // Keep only recipients that are in the eligible users list
        eligibleRecipients = recipients.filter(userId => 
          commissionType.eligibleUsers?.some((eligibleUser: mongoose.Types.ObjectId) => 
            eligibleUser.toString() === userId.toString()
          )
        );
        
        // If no eligible recipients after filtering, skip this commission type
        if (eligibleRecipients.length === 0) {
          continue;
        }
      }

      // Create a commission for each eligible recipient
      for (const userId of eligibleRecipients) {
        try {
          // Check if this exact commission already exists for this user
          const existingCommission = await Commission.findOne({
            reservationId: reservation._id,
            commissionTypeId: commissionType._id,
            userId
          });

          // Skip if identical commission already exists for this specific user
          if (existingCommission) {
            continue;
          }

          // Create the commission
          const commission = await Commission.create({
            userId,
            reservationId: reservation._id,
            commissionTypeId: commissionType._id,
            amount: commissionType.amount,
            isApproved: commissionType.isAutoApproved === true,
            approvedAt: commissionType.isAutoApproved === true ? new Date() : null,
            approvedBy: commissionType.isAutoApproved === true ? new mongoose.Types.ObjectId(sessionUserId) : null,
          });

          createdCommissions.push(commission);

          // Add a note about the commission
          // Fetch user details if possible to include in the note
          let recipientInfo = userId.toString();
          try {
            const User = mongoose.model('User');
            const user = await User.findById(userId).select('name email').lean();
            if (user) {
              // Type assertion to handle the user object
              const typedUser = user as unknown as { 
                name?: string;
                email?: string;
              };
              if (typedUser.name && typedUser.email) {
                recipientInfo = `${typedUser.name} (${typedUser.email})`;
              }
            }
          } catch (error) {
            // If we can't get user details, just use the ID
            console.log('Unable to fetch user details for note:', error);
          }

          // Create a note about the commission
          const noteContent = `Commission of $${commissionType.amount.toFixed(2)} CAD has been given to ${recipientInfo} based on commission type: ${commissionType.name}`;
          
          // Use the ReservationNote model
          await ReservationNote.create({
            reservationId: reservation._id,
            userId: sessionUserId,
            content: noteContent,
            createdAt: new Date(),
            updatedAt: new Date()
          });
        } catch (err) {
          console.error('Error creating individual commission:', err);
          // Continue to next recipient even if one fails
        }
      }
    }

    return createdCommissions;
  } catch (error) {
    console.error('Error generating commissions:', error);
    return [];
  }
};

/**
 * Deletes commissions based on recipient field settings for a specific status trigger
 * @param db - MongoDB database connection
 * @param reservationId - ID of the reservation
 * @param statusTriggerId - ID of the status trigger to filter commission types
 * @param reservation - The reservation object containing assigned_user_id and partnerId
 * @param sessionUserId - ID of the user performing the action (for notes)
 * @returns Array of user names who had commissions deleted
 */
export const deleteCommissionsBasedOnRecipient = async (
  db: any,
  reservationId: string,
  statusTriggerId: string,
  reservation: any
): Promise<string[]> => {
  try {
    // Find commission types with the specific status trigger
    const commissionTypesWithStatusTrigger = await db.collection('commissiontypes').find({
      statusTrigger: ObjectId.createFromHexString(statusTriggerId)
    }).toArray();
    
    console.log("commissionTypesWithStatusTrigger",commissionTypesWithStatusTrigger);
    // Track users who had commissions deleted for note creation
    const deletedCommissionUsers = new Set<string>();

    // Process each commission type individually based on recipient settings
    for (const commissionType of commissionTypesWithStatusTrigger) {
      const usersToDeleteCommissionsFor = [];

      // Check recipient settings and determine which users to delete commissions for
      if (commissionType.recipient?.assignedUser && reservation.assigned_user_id) {
        usersToDeleteCommissionsFor.push(ObjectId.createFromHexString(reservation.assigned_user_id));

        // Get assigned user name for note
        const assignedUser = await db.collection('users').findOne(
          { _id: reservation.assigned_user_id }
        );
        if (assignedUser) {
          deletedCommissionUsers.add(assignedUser.name);
        }
      }

      if (commissionType.recipient?.branchPAP && reservation.partnerId) {
        usersToDeleteCommissionsFor.push(reservation.partnerId);

        // Get partner name for note
        const partner = await db.collection('users').findOne(
          { _id: reservation.partnerId }
        );
        if (partner) {
          deletedCommissionUsers.add(partner.name);
        }
      }

      // Delete commissions for the determined users
      if (usersToDeleteCommissionsFor.length > 0) {
        await db.collection('commissions').deleteMany({
          reservationId: ObjectId.createFromHexString(reservationId),
          userId: { $in: usersToDeleteCommissionsFor },
          commissionTypeId: commissionType._id
        });
      }
    }

    return Array.from(deletedCommissionUsers);
  } catch (error) {
    console.error('Error deleting commissions based on recipient:', error);
    return [];
  }
};

/**
 * Transfers commissions from old partner to new partner when partnerId changes
 * @param db - MongoDB database connection
 * @param reservationId - ID of the reservation
 * @param oldPartnerId - ID of the old partner
 * @param newPartnerId - ID of the new partner
 * @param sessionUserId - ID of the user performing the action (for notes)
 * @returns Object with old and new partner names for note creation
 */
export const transferPartnerCommissions = async (
  db: any,
  reservationId: string,
  oldPartnerId: string,
  newPartnerId: string
): Promise<{ oldPartnerName: string; newPartnerName: string; transferredCount: number }> => {
  try {
    // Get partner names for note creation
    const [oldPartner, newPartner] = await Promise.all([
      db.collection('users').findOne({ _id: ObjectId.createFromHexString(oldPartnerId) }),
      db.collection('users').findOne({ _id: ObjectId.createFromHexString(newPartnerId) })
    ]);

    const oldPartnerName = oldPartner?.name || 'Unknown Partner';
    const newPartnerName = newPartner?.name || 'Unknown Partner';

    // Find commission types with branchPAP recipient
    const commissionTypesWithBranchPAP = await db.collection('commissiontypes').find({
      'recipient.branchPAP': true
    }).toArray();

    if (commissionTypesWithBranchPAP.length === 0) {
      return { oldPartnerName, newPartnerName, transferredCount: 0 };
    }

    const commissionTypeIds = commissionTypesWithBranchPAP.map((ct: any) => ct._id);

    // Find existing commissions for the old partner with branchPAP commission types
    const existingCommissions = await db.collection('commissions').find({
      reservationId: ObjectId.createFromHexString(reservationId),
      userId: ObjectId.createFromHexString(oldPartnerId),
      commissionTypeId: { $in: commissionTypeIds }
    }).toArray();

    if (existingCommissions.length === 0) {
      return { oldPartnerName, newPartnerName, transferredCount: 0 };
    }

    // Create new commissions for the new partner with the same data
    const newCommissions = existingCommissions.map((commission: any) => ({
      userId: ObjectId.createFromHexString(newPartnerId),
      reservationId: commission.reservationId,
      commissionTypeId: commission.commissionTypeId,
      amount: commission.amount,
      isApproved: commission.isApproved,
      approvedBy: commission.approvedBy,
      approvedAt: commission.approvedAt,
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    // Delete old commissions and insert new ones in a transaction-like manner
    await db.collection('commissions').deleteMany({
      reservationId: ObjectId.createFromHexString(reservationId),
      userId: ObjectId.createFromHexString(oldPartnerId),
      commissionTypeId: { $in: commissionTypeIds }
    });

    if (newCommissions.length > 0) {
      await db.collection('commissions').insertMany(newCommissions);
    }

    return {
      oldPartnerName,
      newPartnerName,
      transferredCount: existingCommissions.length
    };
  } catch (error) {
    console.error('Error transferring partner commissions:', error);
    return {
      oldPartnerName: 'Unknown Partner',
      newPartnerName: 'Unknown Partner',
      transferredCount: 0
    };
  }
};