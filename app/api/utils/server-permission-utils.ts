import User from "@/models/User";
import Role from "@/models/Role";
import Permission from "@/models/Permission";


export async function getUserRoles(session: any) {
    const user = await User.findById(session.user.id);
    return user?.roles;
}
export async function getUserPermissions(session: any) {
    let permissions: Set<string> = new Set();
    const user = await User.findById(session.user.id);
    if (!user) return [];
    // Collect all permission ObjectIds from directPermissions and roles
    const directPermissionIds = user.directPermissions || [];
    const roles = await Role.find({ _id: { $in: user.roles } });
    const rolePermissionIds = roles.reduce((acc: any[], role: any) => {
      if (role.permissions) {
        acc.push(...role.permissions);
      }
      return acc;
    }, []);
    // Combine and dedupe all permission ids
    const allPermissionIds = Array.from(new Set([
      ...directPermissionIds.map((id: any) => id.toString()),
      ...rolePermissionIds.map((id: any) => id.toString()),
    ]));
    // Fetch Permission documents and extract codes
    const permissionDocs = await Permission.find({ _id: { $in: allPermissionIds } });
    permissionDocs.forEach((perm: any) => {
      if (perm.code) permissions.add(perm.code);
    });
    return Array.from(permissions);
  }