import User from '@/models/User';
import ReservationStatus from '@/models/ReservationStatus';
import Branch from '@/models/Branch';

interface ReservationFilters {
  statusCodes?: string[];
  branchAccess?: 'all' | 'assigned' | 'none';
  assignmentFilter?: 'all' | 'self' | 'none';
  partnerFilter?: 'all' | 'self' | 'none';
  availableColumns?: string[];
}

// Filter merging logic as per design document
function mergeFilters(roleFilters: ReservationFilters[], userFilters?: ReservationFilters): ReservationFilters {
  const effectiveFilters: ReservationFilters = {};

  // 1. Start with the union of all status codes from the user's roles
  const allStatusCodes = new Set<string>();
  roleFilters.forEach(filter => {
    if (filter.statusCodes) {
      filter.statusCodes.forEach(code => allStatusCodes.add(code));
    }
  });
  effectiveFilters.statusCodes = Array.from(allStatusCodes);

  // 2. Apply the most permissive branch access setting from roles
  const branchAccessPriority = { 'all': 3, 'assigned': 2, 'none': 1 };
  let maxBranchAccess: 'all' | 'assigned' | 'none' = 'none';
  roleFilters.forEach(filter => {
    if (filter.branchAccess && branchAccessPriority[filter.branchAccess] > branchAccessPriority[maxBranchAccess]) {
      maxBranchAccess = filter.branchAccess;
    }
  });
  effectiveFilters.branchAccess = maxBranchAccess;

  // 3. Apply the most restrictive assignment filter from roles
  const assignmentPriority = { 'none': 3, 'self': 2, 'all': 1 };
  let maxAssignmentFilter: 'all' | 'self' | 'none' = 'all';
  roleFilters.forEach(filter => {
    if (filter.assignmentFilter && assignmentPriority[filter.assignmentFilter] > assignmentPriority[maxAssignmentFilter]) {
      maxAssignmentFilter = filter.assignmentFilter;
    }
  });
  effectiveFilters.assignmentFilter = maxAssignmentFilter;

  // 4. Apply the most restrictive partner filter from roles
  const partnerPriority = { 'none': 3, 'self': 2, 'all': 1 };
  let maxPartnerFilter: 'all' | 'self' | 'none' = 'all';
  roleFilters.forEach(filter => {
    if (filter.partnerFilter && partnerPriority[filter.partnerFilter] > partnerPriority[maxPartnerFilter]) {
      maxPartnerFilter = filter.partnerFilter;
    }
  });
  effectiveFilters.partnerFilter = maxPartnerFilter;

  // Handle availableColumns - union of all columns from roles
  const allAvailableColumns = new Set<string>();
  roleFilters.forEach(filter => {
    if (filter.availableColumns) {
      filter.availableColumns.forEach(column => allAvailableColumns.add(column));
    }
  });
  effectiveFilters.availableColumns = Array.from(allAvailableColumns);

  // 5. Override with any user-specific filters
  if (userFilters) {
    if (userFilters.statusCodes && userFilters.statusCodes.length > 0) {
      effectiveFilters.statusCodes = userFilters.statusCodes;
    }
    if (userFilters.branchAccess) {
      effectiveFilters.branchAccess = userFilters.branchAccess;
    }
    if (userFilters.assignmentFilter) {
      effectiveFilters.assignmentFilter = userFilters.assignmentFilter;
    }
    if (userFilters.partnerFilter) {
      effectiveFilters.partnerFilter = userFilters.partnerFilter;
    }
    // For availableColumns, user-specific columns override role columns completely
    if (userFilters.availableColumns && userFilters.availableColumns.length > 0) {
      effectiveFilters.availableColumns = userFilters.availableColumns;
    }
  }

  return effectiveFilters;
}

export async function getUserEffectiveFilters(userId: string): Promise<ReservationFilters> {
  try {
    const user = await User.findById(userId)
      .select('roles reservationFilters')
      .populate('roles', 'reservationFilters')
      .lean();

    if (!user) {
      return {};
    }

    // Extract role filters
    const roleFilters: ReservationFilters[] = ((user as any).roles as any[])
      .map(role => role.reservationFilters)
      .filter(Boolean);

    // Merge filters according to the design document logic
    return mergeFilters(roleFilters, (user as any).reservationFilters);
  } catch (error) {
    console.error('Error getting user effective filters:', error);
    return {};
  }
}

export async function getAvailableStatuses(): Promise<any[]> {
  try {
    const statuses = await ReservationStatus.find()
      .select('_id name name_en code color order')
      .sort({ order: 1 })
      .lean();
    return statuses;
  } catch (error) {
    console.error('Error fetching reservation statuses:', error);
    return [];
  }
}

export async function getAvailableBranches(branchAccess?: 'all' | 'assigned' | 'none', userBranchIds?: string[]): Promise<any[]> {
  try {
    // If branchAccess is 'all', return all branches
    if (branchAccess === 'all') {
      const branches = await Branch.find()
        .select('_id name city region')
        .sort({ name: 1 })
        .lean();
      return branches;
    }

    // If branchAccess is 'assigned', only return user's assigned branches
    if (branchAccess === 'assigned' && userBranchIds && userBranchIds.length > 0) {
      const branches = await Branch.find({ _id: { $in: userBranchIds } })
        .select('_id name city region')
        .sort({ name: 1 })
        .lean();
      return branches;
    }

    // If branchAccess is 'none' or user has no assigned branches, return empty array
    return [];
  } catch (error) {
    console.error('Error fetching branches:', error);
    return [];
  }
}

export async function getAvailableUsers(): Promise<Array<{ id: string, name: string }>> {
  try {
    const users = await User.find()
      .select('_id name')
      .sort({ firstName: 1, lastName: 1 })
      .lean();

    return users.map(user => ({
      id: (user._id as any).toString(),
      name: (user as any).name
    }));
  } catch (error) {
    console.error('Error fetching users:', error);
    return [];
  }
}

export async function applyAccessControlToQuery(
  query: any,
  effectiveFilters: ReservationFilters,
  userId: string
): Promise<any> {
  // Apply status codes filter
  if (effectiveFilters.statusCodes && effectiveFilters.statusCodes.length > 0) {
    query.status = { $in: effectiveFilters.statusCodes };
  }

  // Apply assignment filter
  if (effectiveFilters.assignmentFilter === 'self') {
    query.assigned_user_id = userId;
  } else if (effectiveFilters.assignmentFilter === 'none') {
    query.assigned_user_id = null;
  }
  // 'all' means no additional filter

  // Apply partner filter
  if (effectiveFilters.partnerFilter === 'self') {
    query.partnerId = userId;
  } else if (effectiveFilters.partnerFilter === 'none') {
    query.partnerId = null;
  }
  // 'all' means no additional filter

  // Note: Branch access control is now handled separately in applyBranchAccessControl()
  return query;
}

/**
 * Apply branch access control to query - this should be called AFTER all other filters
 * to ensure it overrides any unauthorized branch filtering attempts
 */
export function applyBranchAccessControl(
  query: any,
  effectiveFilters: ReservationFilters,
  userBranchIds?: string[]
): any {
  // Note: Branch access is ignored when assignmentFilter='self' or partnerFilter='self'
  // because users should see their own assignments/partnerships regardless of branch
  const shouldIgnoreBranchAccess =
    effectiveFilters.assignmentFilter === 'self' ||
    effectiveFilters.partnerFilter === 'self';

  if (effectiveFilters.branchAccess === 'assigned' && !shouldIgnoreBranchAccess) {
    if (userBranchIds && userBranchIds.length > 0) {
      // Check if there's already a specific branch filter applied
      const existingBranchFilter = query['preferences.branchId'];

      if (existingBranchFilter && typeof existingBranchFilter === 'string') {
        // User is filtering by a specific branch - validate they have access to it
        if (userBranchIds.includes(existingBranchFilter)) {
          // User has access to this specific branch, keep the filter as is
          // No need to change the query
        } else {
          // User doesn't have access to this branch, return no results
          query['preferences.branchId'] = 'non-existent-branch-id';
        }
      } else {
        // No specific branch filter, apply user's allowed branches
        query['preferences.branchId'] = { $in: userBranchIds };
      }
    } else {
      // User has no assigned branches, return no results
      query['preferences.branchId'] = 'non-existent-branch-id';
    }
  }
  // If branchAccess is 'all' or 'none', no branch filter is applied
  // If assignmentFilter='self' or partnerFilter='self', branch filter is ignored

  return query;
}

export function getSelectFields(effectiveFilters: ReservationFilters): string | undefined {
  if (effectiveFilters.availableColumns && effectiveFilters.availableColumns.length > 0) {
    // Always include _id for MongoDB operations
    const fields = ['_id', ...effectiveFilters.availableColumns];
    return fields.join(' ');
  }
  return undefined; // Return all fields
}

// Field transformation mapping - maps database fields to frontend-friendly names
const FIELD_TRANSFORMATIONS: Record<string, {
  frontendKey: string;
  transform: (reservation: any, branchMap?: Map<string, string>, userMap?: Map<string, string>) => any;
  label: string;
  category: string;
  order?: number; // For controlling display order
  isMainColumn?: boolean; // Whether to show in main table or details
}> = {
  '_id': {
    frontendKey: '_id',
    transform: (reservation) => reservation._id,
    label: 'ID',
    category: 'basic'
  },
  'type': {
    frontendKey: 'type',
    transform: (reservation) => reservation.type || '',
    label: 'Type',
    category: 'basic',
    order: 2,
    isMainColumn: true
  },
  'status': {
    frontendKey: 'status',
    transform: (reservation) => reservation.status || '',
    label: 'Status',
    category: 'basic',
    order: 4,
    isMainColumn: true
  },
  'confirmationType': {
    frontendKey: 'confirmationType',
    transform: (reservation) => reservation.confirmationType || '',
    label: 'Confirmation Type',
    category: 'basic'
  },
  'createdAt': {
    frontendKey: 'createdAt',
    transform: (reservation) => reservation.createdAt,
    label: 'Created At',
    category: 'basic',
    order: 12,
    isMainColumn: true
  },
  'updatedAt': {
    frontendKey: 'updatedAt',
    transform: (reservation) => reservation.updatedAt,
    label: 'Updated At',
    category: 'basic'
  },
  'customerInfo.client1Name': {
    frontendKey: 'clientNames',
    transform: (reservation) => {
      const client1 = reservation.customerInfo?.client1Name || '';
      const client2 = reservation.customerInfo?.client2Name || '';
      return { client1, client2 };
    },
    label: 'Client Names',
    category: 'customer',
    order: 1,
    isMainColumn: true
  },
  'customerInfo.client2Name': {
    frontendKey: 'client2Name',
    transform: (reservation) => reservation.customerInfo?.client2Name || null,
    label: 'Client 2 Name',
    category: 'customer',
    isMainColumn: false
  },
  'customerInfo.hasCompanion': {
    frontendKey: 'hasCompanion',
    transform: (reservation) => reservation.customerInfo?.hasCompanion || false,
    label: 'Has Companion',
    category: 'customer'
  },
  'customerInfo.city': {
    frontendKey: 'city',
    transform: (reservation) => reservation.customerInfo?.city || '',
    label: 'City',
    category: 'customer',
    order: 5,
    isMainColumn: true
  },
  'customerInfo.postalCode': {
    frontendKey: 'postalCode',
    transform: (reservation) => reservation.customerInfo?.postalCode || '',
    label: 'Postal Code',
    category: 'customer'
  },
  'customerInfo.phone': {
    frontendKey: 'phone',
    transform: (reservation) => reservation.customerInfo?.phone || '',
    label: 'Phone',
    category: 'customer',
    order: 6,
    isMainColumn: true
  },
  'customerInfo.phone2': {
    frontendKey: 'phone2',
    transform: (reservation) => reservation.customerInfo?.phone2 || null,
    label: 'Phone 2',
    category: 'customer'
  },
  'customerInfo.email': {
    frontendKey: 'email',
    transform: (reservation) => reservation.customerInfo?.email || '',
    label: 'Email',
    category: 'customer',
    order: 7,
    isMainColumn: true
  },
  'customerInfo.isPostalCodeValid': {
    frontendKey: 'isPostalCodeValid',
    transform: (reservation) => reservation.customerInfo?.isPostalCodeValid || false,
    label: 'Valid Postal Code',
    category: 'customer'
  },
  'preferences.preferredLanguage': {
    frontendKey: 'preferredLanguage',
    transform: (reservation) => reservation.preferences?.preferredLanguage || '',
    label: 'Preferred Language',
    category: 'preferences'
  },
  'preferences.allergies': {
    frontendKey: 'allergies',
    transform: (reservation) => reservation.preferences?.allergies || '',
    label: 'Allergies',
    category: 'preferences'
  },
  'preferences.hasChildren': {
    frontendKey: 'hasChildren',
    transform: (reservation) => reservation.preferences?.hasChildren || false,
    label: 'Has Children',
    category: 'preferences'
  },
  'preferences.childrenAges.age0to5': {
    frontendKey: 'childrenAge0to5',
    transform: (reservation) => Number(reservation.preferences?.childrenAges?.age0to5) || 0,
    label: 'Children 0-5',
    category: 'preferences'
  },
  'preferences.childrenAges.age6to12': {
    frontendKey: 'childrenAge6to12',
    transform: (reservation) => Number(reservation.preferences?.childrenAges?.age6to12) || 0,
    label: 'Children 6-12',
    category: 'preferences'
  },
  'preferences.childrenAges.age13to17': {
    frontendKey: 'childrenAge13to17',
    transform: (reservation) => Number(reservation.preferences?.childrenAges?.age13to17) || 0,
    label: 'Children 13-17',
    category: 'preferences'
  },
  'preferences.branchId': {
    frontendKey: 'branchName',
    transform: (reservation, branchMap) => branchMap?.get(reservation.preferences?.branchId) || 'Unknown Branch',
    label: 'Branch',
    category: 'preferences',
    order: 3,
    isMainColumn: true
  },
  'preferences.visitDate': {
    frontendKey: 'visitDate',
    transform: (reservation) => reservation.preferences?.visitDate || '',
    label: 'Visit Date',
    category: 'preferences',
    order: 8,
    isMainColumn: true
  },
  'preferences.visitTime': {
    frontendKey: 'visitTime',
    transform: (reservation) => reservation.preferences?.visitTime || '',
    label: 'Visit Time',
    category: 'preferences',
    order: 9,
    isMainColumn: true
  },
  'appointmentId': {
    frontendKey: 'appointmentId',
    transform: (reservation) => reservation.appointmentId || null,
    label: 'Appointment ID',
    category: 'relationships'
  },
  'appointment.id': {
    frontendKey: 'appointmentDetails',
    transform: (reservation) => reservation.appointment || null,
    label: 'Appointment Details',
    category: 'appointment',
    order: 13,
    isMainColumn: true
  },
  'appointment.date': {
    frontendKey: 'appointmentDate',
    transform: (reservation) => reservation.appointment?.date || null,
    label: 'Appointment Date',
    category: 'appointment'
  },
  'appointment.startHour': {
    frontendKey: 'appointmentStartHour',
    transform: (reservation) => reservation.appointment?.startHour || null,
    label: 'Appointment Start Hour',
    category: 'appointment'
  },
  'appointment.capacity': {
    frontendKey: 'appointmentCapacity',
    transform: (reservation) => reservation.appointment?.capacity || null,
    label: 'Appointment Capacity',
    category: 'appointment'
  },
  'appointment.branchId': {
    frontendKey: 'appointmentBranchId',
    transform: (reservation) => reservation.appointment?.branchId || null,
    label: 'Appointment Branch ID',
    category: 'appointment'
  },
  'source': {
    frontendKey: 'source',
    transform: (reservation) => reservation.source || 'direct',
    label: 'Source',
    category: 'basic'
  },
  'partnerId': {
    frontendKey: 'partnerName',
    transform: (reservation, _branchMap, userMap) => {
      if (!reservation.partnerId) return null;
      // If partnerId is populated (object), use the populated data
      if (typeof reservation.partnerId === 'object' && reservation.partnerId.name) {
        return reservation.partnerId.name;
      }
      // Otherwise use the userMap lookup
      return userMap?.get(reservation.partnerId.toString()) || 'Unknown Partner';
    },
    label: 'Partner',
    category: 'relationships',
    order: 11,
    isMainColumn: true
  },
  'partner': {
    frontendKey: 'partner',
    transform: (reservation) => {
      // Only include partner info if source is invitation and partnerData is available
      if (reservation.source === 'invitation' && reservation.partnerData) {
        return {
          id: reservation.partnerData._id?.toString() || reservation.partnerData.toString(),
          name: reservation.partnerData.name || 'Unknown Partner',
          email: reservation.partnerData.email || ''
        };
      }
      return null;
    },
    label: 'Partner Info',
    category: 'relationships'
  },
  'assigned_user_id': {
    frontendKey: 'assignedUserName',
    transform: (reservation, _branchMap, userMap) => {
      if (!reservation.assigned_user_id) return null;
      return userMap?.get(reservation.assigned_user_id.toString()) || 'Unknown User';
    },
    label: 'Assigned User',
    category: 'relationships',
    order: 10,
    isMainColumn: true
  },
  'isDeleted': {
    frontendKey: 'isDeleted',
    transform: (reservation) => reservation.isDeleted || false,
    label: 'Is Deleted',
    category: 'status'
  },
  'deletedAt': {
    frontendKey: 'deletedAt',
    transform: (reservation) => reservation.deletedAt || null,
    label: 'Deleted At',
    category: 'status'
  },
  'presentAt': {
    frontendKey: 'presentAt',
    transform: (reservation) => reservation.presentAt || null,
    label: 'Present At',
    category: 'status'
  },
  'soldAt': {
    frontendKey: 'soldAt',
    transform: (reservation) => reservation.soldAt || null,
    label: 'Sold At',
    category: 'status'
  },
  'sellingAmount': {
    frontendKey: 'sellingAmount',
    transform: (reservation) => reservation.sellingAmount || null,
    label: 'Selling Amount',
    category: 'status'
  },
  'thankYouMessageSent': {
    frontendKey: 'thankYouMessageSent',
    transform: (reservation) => reservation.thankYouMessageSent || false,
    label: 'Thank You Message Sent',
    category: 'communication'
  },
  'grandJourSmsSent': {
    frontendKey: 'grandJourSmsSent',
    transform: (reservation) => reservation.grandJourSmsSent || false,
    label: 'Grand Jour SMS Sent',
    category: 'communication'
  },
  'stopMessage': {
    frontendKey: 'stopMessage',
    transform: (reservation) => reservation.stopMessage || '',
    label: 'Stop Message',
    category: 'communication'
  },
  'previousStatuses': {
    frontendKey: 'previousStatuses',
    transform: (reservation) => reservation.previousStatuses || [],
    label: 'Previous Statuses',
    category: 'history'
  }
};

// Helper function to ensure safe values for React rendering
function ensureSafeValue(value: any): any {
  if (value === null || value === undefined) {
    return null;
  }

  // Handle arrays - convert to safe representation
  if (Array.isArray(value)) {
    return value; // Let frontend handle array display
  }

  // Handle objects - convert to safe representation
  if (typeof value === 'object' && value !== null) {
    // For dates, convert to ISO string
    if (value instanceof Date) {
      return value.toISOString();
    }
    // For other objects, return a safe representation
    return value;
  }

  // For primitives, ensure they're safe
  if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
    return value;
  }

  // Fallback to string representation
  return String(value);
}

export function transformReservationData(
  reservations: any[],
  availableColumns: string[],
  branchMap: Map<string, string>,
  userMap: Map<string, string>
): any[] {
  return reservations.map(reservation => {
    const transformedReservation: any = {};

    // Always include _id
    transformedReservation._id = reservation._id;

    // Transform only available columns
    const columnsToTransform = availableColumns.length > 0 ? availableColumns : Object.keys(FIELD_TRANSFORMATIONS);

    columnsToTransform.forEach(column => {
      const transformation = FIELD_TRANSFORMATIONS[column];
      if (transformation) {
        const rawValue = transformation.transform(reservation, branchMap, userMap);
        transformedReservation[transformation.frontendKey] = ensureSafeValue(rawValue);
      }
    });

    return transformedReservation;
  });
}

// Create a reverse mapping from frontend keys to database fields for filtering/sorting
const FRONTEND_TO_DB_MAPPING: Record<string, string> = {};
Object.entries(FIELD_TRANSFORMATIONS).forEach(([dbField, config]) => {
  FRONTEND_TO_DB_MAPPING[config.frontendKey] = dbField;
});

export function getFrontendToDbMapping(): Record<string, string> {
  return FRONTEND_TO_DB_MAPPING;
}

export function mapFrontendFieldToDb(frontendField: string): string | null {
  return FRONTEND_TO_DB_MAPPING[frontendField] || null;
}

export function getSearchableFields(availableColumns: string[]): string[] {
  const columnsToCheck = availableColumns.length > 0 ? availableColumns : Object.keys(FIELD_TRANSFORMATIONS);

  // Return database field paths that are searchable
  return columnsToCheck.filter(column => {
    const transformation = FIELD_TRANSFORMATIONS[column];
    if (!transformation) return false;

    // Only include fields that make sense for text search
    const searchableFields = [
      'customerInfo.client1Name',
      'customerInfo.client2Name',
      'customerInfo.phone',
      'customerInfo.phone2',
      'customerInfo.email',
      'customerInfo.city',
      'status',
      'type',
      'preferences.visitDate',
      'preferences.visitTime',
      'preferences.preferredLanguage'
    ];

    return searchableFields.includes(column);
  });
}

export function getSortableFields(availableColumns: string[]): Record<string, string> {
  const columnsToCheck = availableColumns.length > 0 ? availableColumns : Object.keys(FIELD_TRANSFORMATIONS);
  const sortableMapping: Record<string, string> = {};

  columnsToCheck.forEach(column => {
    const transformation = FIELD_TRANSFORMATIONS[column];
    if (transformation) {
      // Map frontend key to database field for sorting
      sortableMapping[transformation.frontendKey] = column;
    }
  });

  return sortableMapping;
}

export function getColumnDefinitions(availableColumns: string[]): {
  mainColumns: Array<{
    accessorKey: string;
    label: string;
    category: string;
    order: number;
  }>;
  detailColumns: Array<{
    accessorKey: string;
    label: string;
    category: string;
  }>;
} {
  const columnsToShow = availableColumns.length > 0 ? availableColumns : Object.keys(FIELD_TRANSFORMATIONS);

  const mainColumns: Array<{
    accessorKey: string;
    label: string;
    category: string;
    order: number;
  }> = [];

  const detailColumns: Array<{
    accessorKey: string;
    label: string;
    category: string;
  }> = [];

  columnsToShow.forEach(column => {
    const transformation = FIELD_TRANSFORMATIONS[column];
    if (transformation) {
      if (transformation.isMainColumn) {
        mainColumns.push({
          accessorKey: transformation.frontendKey,
          label: transformation.label,
          category: transformation.category,
          order: transformation.order || 999
        });
      } else {
        detailColumns.push({
          accessorKey: transformation.frontendKey,
          label: transformation.label,
          category: transformation.category
        });
      }
    }
  });

  // Sort main columns by order
  mainColumns.sort((a, b) => a.order - b.order);

  return { mainColumns, detailColumns };
}
