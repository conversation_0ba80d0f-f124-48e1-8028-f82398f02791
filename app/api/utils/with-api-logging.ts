import { logApiRequest } from './api-request-logger';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import type { NextRequest } from 'next/server';

/**
 * Wraps an API route handler to automatically log every request and response.
 * Usage: export const GET = withApiLogging(async (req, ctx) => { ... })
 */
export function withApiLogging<T extends (...args: any[]) => Promise<any>>(
  handler: T
): T {
  return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    const req = args[0] as Request | NextRequest;
    let userId: string | null = null;
    try {
      const session = await getServerSession(authOptions);
      userId = session?.user?.id || null;
    } catch {}
    // Call the handler and capture the response
    const response = await handler(...args);
    // --- Log the request and response together ---
    try {
      let status: number | undefined;
      let body: any = undefined;
      if (response && typeof response === 'object' && 'status' in response) {
        status = response.status;
        // Try to get JSON body if possible
        if (typeof response.clone === 'function' && typeof response.json === 'function') {
          try {
            body = await response.clone().json();
          } catch {
            // Not JSON or already consumed
            body = undefined;
          }
        }
        // Fallback: try to log string or object
        if (body === undefined && typeof response.clone === 'function' && typeof response.text === 'function') {
          try {
            const text = await response.clone().text();
            // Try to parse as JSON if possible
            try {
              body = JSON.parse(text);
            } catch {
              body = text;
            }
          } catch {}
        }
      }
      await logApiRequest({
        req,
        userId,
        response: {
          status,
          body,
        },
      });
      if (process.env.NODE_ENV === 'development') {
        // eslint-disable-next-line no-console
        console.debug('[API RESPONSE LOG]', { status, body });
      }
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('Failed to log API response:', err);
    }
    return response;
  }) as T;
} 