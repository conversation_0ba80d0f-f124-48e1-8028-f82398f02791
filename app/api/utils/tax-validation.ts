// Tax Registration Number Validation Utilities
// Functions for validating TPS and QST registration number formats

/**
 * Checks if a user's roles require tax information
 * Only PAP and Seller roles require tax info
 * @param userRoles - Array of role objects or role names
 * @returns boolean indicating if any of the user's roles require tax info
 */
export function userRolesRequireTaxInfo(userRoles: any[]): boolean {
  if (!userRoles || userRoles.length === 0) return false;

  const rolesThatRequireTaxInfo = ['PAP', 'Seller'];
  const papRoleId = '67fbd1707839bdba5be4b02b'; // PAP role ID
  const sellerRoleId = '67e0aad60f0a3bdeba18542c'; // Seller role ID

  console.log('userRolesRequireTaxInfo - checking roles:', userRoles);
  console.log('userRolesRequireTaxInfo - PAP role ID:', papRoleId);
  console.log('userRolesRequireTaxInfo - Seller role ID:', sellerRoleId);

  const result = userRoles.some((role: any) => {
    if (typeof role === 'string') {
      // Check if it's a role name
      if (rolesThatRequireTaxInfo.includes(role)) {
        return true;
      }
      // Check if it's a role ID
      return role === papRoleId || role === sellerRoleId;
    } else if (typeof role === 'object' && role !== null) {
      // Check by role name
      if (role.name && rolesThatRequireTaxInfo.includes(role.name)) {
        return true;
      }
      // Check by role ID
      const roleId = role._id?.toString() || role.toString();
      return roleId === papRoleId || roleId === sellerRoleId;
    }
    return false;
  });

  console.log('userRolesRequireTaxInfo - result:', result);
  return result;
}

/**
 * Validates QST (Quebec Sales Tax) registration number format
 * Database stores: Full format with suffix (e.g., 1234567890TQ0001)
 * Format: 10 digits + TQ + 4 digits (e.g., 1234567890TQ0001)
 * @param qstNumber - The QST registration number to validate
 * @returns boolean indicating if the format is valid
 */
export function isValidQSTFormat(qstNumber: string): boolean {
  if (!qstNumber) return false;

  // Remove any spaces or special characters
  const cleaned = qstNumber.replace(/\s/g, '');

  // Check if it matches the full QST format: 10 digits + TQ + 4 digits
  const qstRegex = /^\d{10}TQ\d{4}$/;
  return qstRegex.test(cleaned);
}

/**
 * Validates TPS (Federal Sales Tax/GST) registration number format
 * Database stores: Full format with suffix (e.g., 123456789RT0001)
 * Format: 9 digits + RT + 4 digits (e.g., 123456789RT0001)
 * @param tpsNumber - The TPS registration number to validate
 * @returns boolean indicating if the format is valid
 */
export function isValidTPSFormat(tpsNumber: string): boolean {
  if (!tpsNumber) return false;

  // Remove any spaces or special characters
  const cleaned = tpsNumber.replace(/\s/g, '');

  // Check if it matches the full TPS format: 9 digits + RT + 4 digits
  const tpsRegex = /^\d{9}RT\d{4}$/;
  return tpsRegex.test(cleaned);
}

/**
 * Validates both QST and TPS registration numbers for a user
 * @param user - User object with taxInfo
 * @returns object with validation results
 */
export function validateUserTaxRegistrationNumbers(user: any): {
  qstValid: boolean;
  tpsValid: boolean;
  hasValidTaxInfo: boolean;
} {
  const qstNumber = user?.taxInfo?.qstRegistrationNumber;
  const tpsNumber = user?.taxInfo?.tpsRegistrationNumber;

  // Check QST format if provided
  const qstValid = !qstNumber || isValidQSTFormat(qstNumber);

  // Check TPS format if provided
  const tpsValid = !tpsNumber || isValidTPSFormat(tpsNumber);

  // Both must be valid (or empty) for overall validity
  const hasValidTaxInfo = qstValid && tpsValid;

  return {
    qstValid,
    tpsValid,
    hasValidTaxInfo
  };
}

/**
 * Checks if a user has valid tax information for TPSTVQ tax type
 * This includes:
 * - QST validation API result is valid
 * - QST registration number format is valid
 * - TPS registration number format is valid
 * @param user - User object with taxInfo
 * @returns boolean indicating if tax info is valid for TPSTVQ
 */
export function hasValidTPSTVQTaxInfo(user: any): boolean {
  const taxInfo = user?.taxInfo;

  if (!taxInfo) return false;

  // Check QST validation API result
  const qstValidationValid = taxInfo.qstValidation?.isValid === true;

  // Check registration number formats
  const formatValidation = validateUserTaxRegistrationNumbers(user);

  // For TPSTVQ, we need:
  // 1. QST validation API says it's valid
  // 2. QST registration number format is valid
  // 3. TPS registration number format is valid
  return qstValidationValid && formatValidation.hasValidTaxInfo;
}

/**
 * Validates tax registration numbers for TPSTVQ tax type during user creation/update
 * This function enforces strict validation and blocks operations if validation fails
 * @param taxInfo - Tax information object from user data
 * @param taxTypeCode - The tax type code (should be 'tpstvq' for strict validation)
 * @returns object with validation results and error messages
 */
export async function validateTPSTVQTaxInfo(taxInfo: any, taxTypeCode: string): Promise<{
  isValid: boolean;
  errors: string[];
  qstValidation?: any;
}> {
  const errors: string[] = [];
  let qstValidation: any = null;

  // Only enforce strict validation for tpstvq tax type
  if (taxTypeCode !== 'tpstvq') {
    return { isValid: true, errors: [] };
  }

  // For tpstvq, both QST and TPS registration numbers are required
  if (!taxInfo.qstRegistrationNumber) {
    errors.push('QST registration number is required for TPSTVQ tax type');
  }

  if (!taxInfo.tpsRegistrationNumber) {
    errors.push('TPS registration number is required for TPSTVQ tax type');
  }

  // Validate QST format
  if (taxInfo.qstRegistrationNumber && !isValidQSTFormat(taxInfo.qstRegistrationNumber)) {
    errors.push('QST registration number must be in format: 10 digits + TQ + 4 digits (e.g., 1234567890TQ0001)');
  }

  // Validate TPS format
  if (taxInfo.tpsRegistrationNumber && !isValidTPSFormat(taxInfo.tpsRegistrationNumber)) {
    errors.push('TPS registration number must be in format: 9 digits + RT + 4 digits (e.g., 123456789RT0001)');
  }

  // If format validation fails, return early
  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  // Validate QST number with Quebec API
  if (taxInfo.qstRegistrationNumber) {
    try {
      const { validateQSTRegistrationNumber } = await import('./qst-validation');
      qstValidation = await validateQSTRegistrationNumber(taxInfo.qstRegistrationNumber);

      if (!qstValidation.isValid) {
        errors.push(`QST registration number validation failed: ${qstValidation.errorMessage || 'Invalid registration number'}`);
      } else if (qstValidation.status !== 'R') {
        errors.push(`QST registration number is not in registered status (status: ${qstValidation.status || 'unknown'})`);
      }
    } catch (validationError) {
      errors.push(`QST validation service error: ${validationError instanceof Error ? validationError.message : 'Service unavailable'}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    qstValidation
  };
}

/**
 * Ensures tax registration numbers are in full format for display
 * Handles backward compatibility for old invoices with base numbers only
 * @param tpsNumber - TPS registration number (may be base format or full format)
 * @param qstNumber - QST registration number (may be base format or full format)
 * @returns object with full format numbers
 */
export function ensureFullTaxNumberFormat(tpsNumber?: string, qstNumber?: string): {
  tpsRegistrationNumber?: string;
  qstRegistrationNumber?: string;
} {
  const result: { tpsRegistrationNumber?: string; qstRegistrationNumber?: string } = {};

  // Handle TPS number
  if (tpsNumber) {
    const cleanedTps = tpsNumber.replace(/\s/g, '');
    if (/^\d{9}$/.test(cleanedTps)) {
      // Old format: base 9 digits only, append default suffix
      result.tpsRegistrationNumber = cleanedTps + 'RT0001';
    } else if (/^\d{9}RT\d{4}$/.test(cleanedTps)) {
      // New format: already has suffix
      result.tpsRegistrationNumber = cleanedTps;
    } else {
      // Invalid format, keep as-is for display
      result.tpsRegistrationNumber = tpsNumber;
    }
  }

  // Handle QST number
  if (qstNumber) {
    const cleanedQst = qstNumber.replace(/\s/g, '');
    if (/^\d{10}$/.test(cleanedQst)) {
      // Old format: base 10 digits only, append default suffix
      result.qstRegistrationNumber = cleanedQst + 'TQ0001';
    } else if (/^\d{10}TQ\d{4}$/.test(cleanedQst)) {
      // New format: already has suffix
      result.qstRegistrationNumber = cleanedQst;
    } else {
      // Invalid format, keep as-is for display
      result.qstRegistrationNumber = qstNumber;
    }
  }

  return result;
}

/**
 * Determines the appropriate tax type for a user during invoice generation
 * @param user - User object with taxInfo
 * @param originalTaxType - The originally selected tax type
 * @returns object with tax type decision and invalid flag
 */
export async function determineTaxTypeForInvoice(user: any, originalTaxType: any): Promise<{
  taxType: any;
  taxTypeId: string;
  invalidTaxInfo: boolean;
}> {
  // If original tax type is not TPSTVQ, use it as-is
  if (originalTaxType?.code !== 'tpstvq') {
    return {
      taxType: originalTaxType,
      taxTypeId: originalTaxType._id?.toString() || '',
      invalidTaxInfo: false
    };
  }

  // For TPSTVQ tax type, validate tax info
  const hasValidTaxInfo = hasValidTPSTVQTaxInfo(user);

  if (hasValidTaxInfo) {
    // Tax info is valid, use TPSTVQ
    return {
      taxType: originalTaxType,
      taxTypeId: originalTaxType._id?.toString() || '',
      invalidTaxInfo: false
    };
  } else {
    // Tax info is invalid, fall back to no_tax
    const TaxType = (await import('@/models/TaxType')).default;
    const noTaxType = await TaxType.findOne({ code: 'no_tax' }).lean();

    if (!noTaxType || Array.isArray(noTaxType)) {
      // Critical error: no_tax type doesn't exist in database
      console.error('Critical error: no_tax tax type not found in database during tax validation');
      throw new Error('Tax system configuration error: no_tax tax type not found in database');
    }

    return {
      taxType: {
        code: noTaxType.code,
        names: noTaxType.names,
        percentages: noTaxType.percentages
      },
      taxTypeId: (noTaxType._id as any).toString(),
      invalidTaxInfo: true
    };
  }
}
