/**
 * Utility functions for event report data transformation and processing
 */

import Reservation from '@/models/Reservation';

/**
 * Time Range Validation Utilities
 * Shared functions for validating event and personnel time ranges
 */

export interface TimeRangeValidationError {
  field: string;
  message: string;
}

/**
 * Validates that a start time is before an end time
 */
export function validateTimeRange(startTime: Date | string, endTime: Date | string): boolean {
  const start = new Date(startTime);
  const end = new Date(endTime);
  return start < end;
}

/**
 * Validates that personnel time ranges are within the event's time range
 */
export function validatePersonnelTimeRanges(
  eventStartTime: Date | string,
  eventEndTime: Date | string,
  personnel: Array<{
    userId: string;
    timeRange: {
      startTime: Date | string;
      endTime: Date | string;
    };
    name?: string;
  }>
): TimeRangeValidationError[] {
  const errors: TimeRangeValidationError[] = [];
  const eventStart = new Date(eventStartTime);
  const eventEnd = new Date(eventEndTime);

  personnel.forEach((person, index) => {
    const personStart = new Date(person.timeRange.startTime);
    const personEnd = new Date(person.timeRange.endTime);
    const personName = person.name || `Person ${index + 1}`;

    // Check if person's start time is before end time
    if (personStart >= personEnd) {
      errors.push({
        field: `personnel[${index}].timeRange`,
        message: `${personName}'s start time must be before end time`
      });
    }

    // Check if person's time range is within event time range
    if (personStart < eventStart) {
      errors.push({
        field: `personnel[${index}].timeRange.startTime`,
        message: `${personName}'s start time cannot be before event start time`
      });
    }

    if (personEnd > eventEnd) {
      errors.push({
        field: `personnel[${index}].timeRange.endTime`,
        message: `${personName}'s end time cannot be after event end time`
      });
    }
  });

  return errors;
}

/**
 * Validates event report data for submission
 */
export function validateEventReportForSubmission(report: any): string[] {
  const errors: string[] = [];

  // Check if event times are set
  if (!report.eventStartTime || !report.eventEndTime) {
    errors.push('Event start and end times are required');
  }

  // Check if start time is before end time
  if (report.eventStartTime && report.eventEndTime &&
      !validateTimeRange(report.eventStartTime, report.eventEndTime)) {
    errors.push('Event start time must be before end time');
  }

  // Check if all personnel have valid time ranges
  const allPersonnel = [...(report.paps || []), ...(report.cooks || [])];
  for (const person of allPersonnel) {
    if (!person.timeRange?.startTime || !person.timeRange?.endTime) {
      errors.push('All personnel must have valid time ranges');
      break;
    }

    if (!validateTimeRange(person.timeRange.startTime, person.timeRange.endTime)) {
      errors.push('Personnel start time must be before end time');
      break;
    }
  }

  // Validate personnel time ranges are within event time range
  if (report.eventStartTime && report.eventEndTime && allPersonnel.length > 0) {
    const personnelErrors = validatePersonnelTimeRanges(
      report.eventStartTime,
      report.eventEndTime,
      allPersonnel
    );

    if (personnelErrors.length > 0) {
      errors.push(...personnelErrors.map(error => error.message));
    }
  }

  // Check if cook percentages sum to 100%
  if (report.cooks && report.cooks.length > 0) {
    const totalPercentage = report.cooks.reduce((sum: number, cook: any) =>
      sum + (cook.percentage || 0), 0
    );

    if (Math.abs(totalPercentage - 100) > 0.01) {
      errors.push('Cook percentages must sum to 100%');
    }
  }

  return errors;
}

/**
 * Transform report data for frontend consumption
 * Calculates hours worked, formats personnel data, and adds commission information
 */
export async function transformReportForFrontend(report: any) {
  // Calculate hours worked for each person
  const calculateHoursWorked = (timeRange: any) => {
    const start = new Date(timeRange.startTime);
    const end = new Date(timeRange.endTime);
    return Math.round(((end.getTime() - start.getTime()) / (1000 * 60 * 60)) * 100) / 100;
  };

  // Mock commission rates (these should come from settings/config)
  const commissionRates = {
    supervisorRate: 25.00,
    papBaseRate: 15.00,
    papEarlyBookingRate: 20.00,
    cookBaseRate: 100.00
  };

  // Transform personnel data
  const personnelData = {
    supervisors: report.supervisors.map((supervisor: any) => ({
      userId: supervisor._id.toString(),
      name: supervisor.name,
      email: supervisor.email,
      commission: 0, // Will be calculated based on reservations
      hoursWorked: 0 // Supervisors don't have time ranges in current model
    })),
    paps: report.paps.map((pap: any) => ({
      userId: pap.userId._id.toString(),
      name: pap.userId.name,
      email: pap.userId.email,
      commission: 0, // Will be calculated based on reservations
      hoursWorked: calculateHoursWorked(pap.timeRange),
      timeRange: {
        startTime: pap.timeRange.startTime,
        endTime: pap.timeRange.endTime
      }
    })),
    cooks: report.cooks.map((cook: any) => ({
      userId: cook.userId._id.toString(),
      name: cook.userId.name,
      email: cook.userId.email,
      commission: 0, // Will be calculated based on reservations and percentage
      hoursWorked: calculateHoursWorked(cook.timeRange),
      percentage: cook.percentage || 100,
      timeRange: {
        startTime: cook.timeRange.startTime,
        endTime: cook.timeRange.endTime
      }
    }))
  };

  // Fetch actual linked reservations
  let reservationData = {
    totalReservations: 0,
    linkedReservations: []
  };

  try {
    // Get linked reservations from the main linkedReservationIds field
    if (report.linkedReservationIds && report.linkedReservationIds.length > 0) {
      const linkedReservations = await Reservation.find({
        _id: { $in: report.linkedReservationIds },
        isDeleted: { $ne: true }
      })
      .select('customerInfo preferences status type linkedAt linkingMethod')
      .lean();

      reservationData = {
        totalReservations: linkedReservations.length,
        linkedReservations: linkedReservations.map((reservation: any) => {
          // Calculate party size from customer info and children
          const hasCompanion = reservation.customerInfo?.hasCompanion || false;
          const childrenCount = (reservation.preferences?.childrenAges?.age0to5 || 0) +
                               (reservation.preferences?.childrenAges?.age6to12 || 0) +
                               (reservation.preferences?.childrenAges?.age13to17 || 0);
          const partySize = 1 + (hasCompanion ? 1 : 0) + childrenCount;

          // Build customer name
          const client1Name = reservation.customerInfo?.client1Name || 'Unknown Customer';
          const client2Name = reservation.customerInfo?.client2Name;
          const customerName = client2Name ? `${client1Name} & ${client2Name}` : client1Name;

          // Build reservation date/time
          const visitDate = reservation.preferences?.visitDate || '';
          const visitTime = reservation.preferences?.visitTime || '';

          // Handle visitTime format like "17:00-19:00" - use the start time
          let reservationTime;
          if (visitDate && visitTime) {
            try {
              // Extract start time from range (e.g., "17:00" from "17:00-19:00")
              const startTime = visitTime.split('-')[0] || '12:00';

              // Parse the visitDate (already in ISO format) and combine with start time
              const dateObj = new Date(visitDate);
              const [hours, minutes] = startTime.split(':').map(Number);

              dateObj.setHours(hours, minutes, 0, 0);
              reservationTime = dateObj.toISOString();
            } catch (error) {
              console.warn('Error parsing reservation date/time:', error);
              reservationTime = new Date().toISOString();
            }
          } else {
            reservationTime = new Date().toISOString();
          }

          return {
            _id: reservation._id.toString(),
            customerName,
            partySize,
            reservationTime,
            papCommission: 7.00, // Base PAP commission per reservation
            status: reservation.status || 'pending',
            type: reservation.type || 'branch',
            linkedAt: reservation.linkedAt,
            linkingMethod: reservation.linkingMethod || 'none'
          };
        })
      };
    }
  } catch (error) {
    console.error('Error fetching linked reservations:', error);
    // Keep default empty data if fetch fails
  }

  // Calculate total commissions
  const totalCommissions = personnelData.supervisors.reduce((sum: number, s: any) => sum + s.commission, 0) +
                          personnelData.paps.reduce((sum: number, p: any) => sum + p.commission, 0) +
                          personnelData.cooks.reduce((sum: number, c: any) => sum + c.commission, 0);

  return {
    ...report,
    totalCommissions,
    personnelData,
    reservationData,
    commissionRates
  };
}
