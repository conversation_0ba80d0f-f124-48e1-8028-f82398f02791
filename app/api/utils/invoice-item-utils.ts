import TaxType from '@/models/TaxType';

/**
 * Determines if invoice items should be taxable based on the tax type
 * @param taxTypeId - The tax type ID from the invoice
 * @returns boolean - true if items should be taxable, false otherwise
 */
export async function shouldItemsBeTaxable(taxTypeId: string): Promise<boolean> {
  try {
    if (!taxTypeId) {
      return false;
    }

    const taxType = await TaxType.findById(taxTypeId).lean();
    
    if (!taxType || Array.isArray(taxType)) {
      return false;
    }

    // Items are NOT taxable only if tax type is 'no_tax'
    // All other tax types make items taxable
    return (taxType as any).code !== 'no_tax';
  } catch (error) {
    console.error('Error determining taxable status:', error);
    // Default to false (not taxable) on error
    return false;
  }
}

/**
 * Gets the taxable status for items based on invoice ID
 * @param invoiceId - The invoice ID to check
 * @returns boolean - true if items should be taxable, false otherwise
 */
export async function getItemTaxableStatusForInvoice(invoiceId: string): Promise<boolean> {
  try {
    const Invoice = (await import('@/models/Invoice')).default;
    const invoice = await Invoice.findById(invoiceId).lean();
    
    if (!invoice || Array.isArray(invoice)) {
      return false;
    }

    return await shouldItemsBeTaxable((invoice as any).taxTypeId?.toString());
  } catch (error) {
    console.error('Error getting taxable status for invoice:', error);
    return false;
  }
}
