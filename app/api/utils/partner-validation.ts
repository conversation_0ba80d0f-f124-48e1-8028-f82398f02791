// Validation middleware for partner data
export const validatePartnerData = (data: any) => {
  const errors = [];
  
  // Required string fields
  ['name', 'email', 'phone', 'address', 'city', 'province', 'country', 'postalCode', 'divisionId'].forEach(field => {
    if (!data[field] || typeof data[field] !== 'string') {
      errors.push(`${field} is required and must be a string`);
    }
  });
  
  // Language validation
  if (!['fr', 'en'].includes(data.language)) {
    errors.push('language must be either "fr" or "en"');
  }
  
  // Numeric fields
  ['federalTax', 'provincialTax'].forEach(field => {
    if (data[field] !== undefined && typeof data[field] !== 'number') {
      errors.push(`${field} must be a number`);
    }
  });
  
  // Array fields
  ['owners', 'agents'].forEach(field => {
    if (data[field] !== undefined && !Array.isArray(data[field])) {
      errors.push(`${field} must be an array`);
    }
  });

  // ObjectId validation for branchId
  if (!data.branchId || !/^[0-9a-fA-F]{24}$/.test(data.branchId)) {
    errors.push('branchId must be a valid ObjectId');
  }
  
  return errors;
};

// Logging utility for partner operations
export const logPartnerOperation = (stage: string, data: any) => {
  console.log(`[Partner Operation] ${stage}`);
  console.log(JSON.stringify(data, null, 2));
};