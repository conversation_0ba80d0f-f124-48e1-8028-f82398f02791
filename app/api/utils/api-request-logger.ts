import { promises as fs } from 'fs';
import { NextRequest } from 'next/server';
import path from 'path';

interface ApiRequestLog {
  userId: string | null;
  method: string;
  path: string;
  timestamp: string;
  body?: any;
  response?: {
    status?: number;
    body?: any;
  };
}

// Safely stringify objects/arrays for logging, avoiding [Object] and circular refs
function safeStringify(obj: any, space = 2): string {
  const seen = new WeakSet();
  return JSON.stringify(obj, function (key, value) {
    if (typeof value === 'object' && value !== null) {
      if (seen.has(value)) return '[Circular]';
      seen.add(value);
      // Convert Mongoose docs or objects with toObject to plain objects
      if (typeof value.toObject === 'function') {
        try {
          return value.toObject();
        } catch {
          return value;
        }
      }
    }
    return value;
  }, space);
}

async function cleanupOldLogs(logDir: string, keep: number = 14) {
  try {
    const files = await fs.readdir(logDir);
    const logFiles = files.filter(f => /^log-\d{4}-\d{2}-\d{2}\.txt$/.test(f));
    if (logFiles.length > keep) {
      // Sort by date in filename ascending (oldest first)
      logFiles.sort();
      const toDelete = logFiles.slice(0, logFiles.length - keep);
      await Promise.all(toDelete.map(f => fs.unlink(path.join(logDir, f))));
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Failed to cleanup old log files:', err);
  }
}

/**
 * Logs API requests to /var/log/amq_partners/[route]/log-YYYY-MM-DD.txt
 * @param req NextRequest or Request
 * @param userId User ID string (or null if not available)
 * @param response Optional response info (status, body)
 * @param logDirRoot Optional override for log root (for testing)
 */
export async function logApiRequest({
  req,
  userId,
  response,
  logDirRoot,
}: {
  req: Request | NextRequest;
  userId: string | null;
  response?: { status?: number; body?: any };
  logDirRoot?: string;
}): Promise<void> {
  try {
    // Determine logDirRoot based on NEXT_PUBLIC_DEV_SERVER if not explicitly set
    let resolvedLogDirRoot = logDirRoot;
    if (!resolvedLogDirRoot) {
      if (process.env.NEXT_PUBLIC_DEV_SERVER === 'true') {
        resolvedLogDirRoot = '/var/log/amq_partners/dev';
      } else {
        resolvedLogDirRoot = '/var/log/amq_partners';
      }
    }
    const url = new URL(req.url);
    // Remove query params for log path
    const cleanPath = url.pathname.replace(/^\/api\//, '').replace(/\/$/, '');
    const logDir = path.join(resolvedLogDirRoot, cleanPath);
    // Use UTC date for filename
    const dateStr = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
    const logFile = path.join(logDir, `log-${dateStr}.txt`);

    // Try to get request body (if possible)
    let body: any = undefined;
    if (req.method !== 'GET' && req.method !== 'HEAD') {
      try {
        // Try to parse as JSON, fallback to text
        const text = await req.text();
        try {
          body = JSON.parse(text);
        } catch {
          body = text;
        }
      } catch {
        body = undefined;
      }
    }

    const logEntry: ApiRequestLog = {
      userId,
      method: req.method,
      path: url.pathname,
      timestamp: new Date().toISOString(),
      ...(body !== undefined ? { body } : {}),
      ...(response ? { response } : {}),
    };

    // Ensure log directory exists
    //await fs.mkdir(logDir, { recursive: true });
    // Append log entry using safeStringify
    //await fs.appendFile(logFile, safeStringify(logEntry) + '\n', 'utf8');

    // Cleanup old log files if more than 14 exist
    //await cleanupOldLogs(logDir, 14);

    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.debug('[API LOG]', logEntry);
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('Failed to log API request:', err);
  }
} 