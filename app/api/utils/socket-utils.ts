async function emitSocketEvent(type: string, data: any) {
    const notificationPayload = {
        type: type,
        data: data
      };
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL?.endsWith('/')?process.env.NEXT_PUBLIC_APP_URL:process.env.NEXT_PUBLIC_APP_URL+'/'}api/socket/emit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notificationPayload),
      });
}

export {emitSocketEvent};