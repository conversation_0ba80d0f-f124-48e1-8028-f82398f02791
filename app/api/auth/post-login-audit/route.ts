import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { UserAuditLogger } from '@/lib/utils/audit-utils';
import dbConnect from '@/lib/db';

/**
 * Endpoint to log successful login audit after NextAuth authentication
 * This should be called from the login page after successful authentication
 */
export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    // Get the current session to verify authentication
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Extract IP address and user agent from request
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Log successful login with proper context
    const auditLog = await UserAuditLogger.logUserLogin(
      session.user.email,
      session.user.id,
      session.user.name,
      ipAddress,
      userAgent,
      { 
        sessionId: session.user.id,
        loginMethod: 'credentials',
        postAuthAudit: true
      }
    );

    return NextResponse.json({
      success: true,
      message: 'Login audit logged successfully',
      auditLogId: auditLog?._id
    });

  } catch (error: any) {
    console.error('Post-login audit error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to log login audit' 
      },
      { status: 500 }
    );
  }
}
