import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { UserAuditLogger } from '@/lib/utils/audit-utils';
import dbConnect from '@/lib/db';

/**
 * Comprehensive endpoint to log both successful and failed login attempts
 * This endpoint can be called with or without authentication
 */
export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    const body = await request.json();
    const { email, password, success, errorMessage, userId, userName } = body;

    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }

    // Extract IP address and user agent from request
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    let auditLog;

    if (success) {
      // For successful login, we should have user info
      if (!userId || !userName) {
        // Try to get from session if not provided
        const session = await getServerSession(authOptions);
        if (!session?.user) {
          return NextResponse.json(
            { success: false, error: 'User information required for successful login audit' },
            { status: 400 }
          );
        }
        
        auditLog = await UserAuditLogger.logUserLogin(
          session.user.email,
          session.user.id,
          session.user.name,
          ipAddress,
          userAgent,
          { loginMethod: 'credentials', auditSource: 'api' }
        );
      } else {
        auditLog = await UserAuditLogger.logUserLogin(
          email,
          userId,
          userName,
          ipAddress,
          userAgent,
          { loginMethod: 'credentials', auditSource: 'api' }
        );
      }
    } else {
      // For failed login, log with available information
      auditLog = await UserAuditLogger.logUserLoginFailure(
        email,
        password || '', // Include attempted password for failed logins
        errorMessage || 'Login failed',
        ipAddress,
        userAgent,
        { auditSource: 'api' }
      );
    }

    return NextResponse.json({
      success: true,
      message: `${success ? 'Successful' : 'Failed'} login audit logged`,
      auditLogId: auditLog?._id,
      context: {
        ipAddress,
        userAgent,
        email
      }
    });

  } catch (error: any) {
    console.error('Login audit error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to log login audit' 
      },
      { status: 500 }
    );
  }
}
