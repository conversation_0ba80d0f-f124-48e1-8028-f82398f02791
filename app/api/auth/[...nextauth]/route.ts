import NextAuth, { AuthOptions, User } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
import dbConnect from '@/lib/db';
import UserModel from '@/models/User';
import PermissionModel from '@/models/Permission';
import RoleModel from '@/models/Role';
import { IPermission, IRole } from '@/types/next-auth';
import { Types } from 'mongoose';
import { UserAuditLogger } from '@/lib/utils/audit-utils';

interface MongoRole {
  _id: Types.ObjectId;
  name: string;
  description?: string;
  permissions: Types.ObjectId[];
}

interface MongoPermission {
  _id: Types.ObjectId;
  code: string;
  name: string;
  description: string;
}

interface MongoUser {
  _id: Types.ObjectId;
  name: string;
  email: string;
  password?: string;
  roles: Types.ObjectId[];
  directPermissions: Types.ObjectId[];
  isActive: boolean;
  image?: string;
}

// Helper function to get user's roles and permissions dynamically
async function getUserPermissions(userId: string) {
  await dbConnect();
  
  // Get user without password
  const userDoc = await UserModel.findById(userId);
  
  if (!userDoc) {
    return null;
  }
  
  const user = userDoc.toObject();
  
  // Fetch roles
  const rolesDocs = await RoleModel.find({
    _id: { $in: user.roles }
  })
  .select('name description permissions')
  .lean();

  // Convert rolesDocs to proper type
  const roles = (rolesDocs as unknown[]).map(role => ({
    _id: (role as any)._id as Types.ObjectId,
    name: (role as any).name as string,
    description: (role as any).description as string,
    permissions: ((role as any).permissions || []) as Types.ObjectId[]
  }));

  // Get all permission IDs from roles
  const rolePermissionIds = roles.reduce<Types.ObjectId[]>((acc, role) => {
    return acc.concat(role.permissions || []);
  }, []);

  // Fetch permissions
  const permissionsDocs = await PermissionModel.find({
    _id: { 
      $in: [...rolePermissionIds, ...(user.directPermissions || [])]
    }
  })
  .select('code name description')
  .lean();

  // Convert permissionsDocs to proper type
  const permissions = (permissionsDocs as unknown[]).map(perm => ({
    _id: (perm as any)._id as Types.ObjectId,
    code: (perm as any).code as string,
    name: (perm as any).name as string,
    description: (perm as any).description as string
  }));

  // Map roles to include only necessary data
  const processedRoles: IRole[] = roles.map(role => ({
    _id: role._id.toString(),
    name: role.name,
    description: role.description || '',
    permissions: (role.permissions || []).map(p => p.toString())
  }));

  // Get all permission codes from roles
  const rolePermissionCodes = permissions
    .filter(p => rolePermissionIds.map(id => id.toString()).includes(p._id.toString()))
    .map(p => p.code);

  // Map direct permissions, excluding those already present in role permissions
  const directPermissionIds = (user.directPermissions || []).map((id: Types.ObjectId) =>
    id.toString()
  );
  const processedDirectPermissions: IPermission[] = permissions
    .filter(p =>
      directPermissionIds.includes(p._id.toString()) &&
      !rolePermissionCodes.includes(p.code)
    )
    .map(p => ({
      _id: p._id.toString(),
      code: p.code,
      name: p.name,
      description: p.description || ''
    }));

  // Get all permission codes
  const permissionCodes = Array.from(new Set(permissions.map(p => p.code)));

  return {
    roles: processedRoles,
    directPermissions: processedDirectPermissions,
    permissions: permissionCodes
  };
}

export const authOptions: AuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials): Promise<User | null> {
        const email = credentials?.email?.toLowerCase();
        let user: any = null;

        try {
          if (!credentials?.email || !credentials?.password) {
            const error = 'Email and password are required';
            // Log failed login attempt
            if (email) {
              await UserAuditLogger.logUserLoginFailure(
                email,
                credentials?.password || '', // Include attempted password
                error,
                undefined, // IP address not available in authorize function
                undefined  // User agent not available in authorize function
              );
            }
            throw new Error(error);
          }

          await dbConnect();

          // Get user without population
          const userDoc = await UserModel.findOne({
            email: email,
            deletedAt: null
          }).select('+password');

          if (!userDoc) {
            const error = 'Invalid credentials';
            // Log failed login attempt for non-existent user
            await UserAuditLogger.logUserLoginFailure(
              email,
              credentials.password, // Include attempted password
              error,
              undefined, // IP address not available in authorize function
              undefined  // User agent not available in authorize function
            );
            throw new Error(error);
          }

          // Convert to our expected type
          user = userDoc.toObject();

          // Validate password
          if (!user.password) {
            const error = 'Invalid credentials';
            await UserAuditLogger.logUserLoginFailure(
              email,
              credentials.password, // Include attempted password
              error,
              undefined,
              undefined,
              { userId: user._id.toString(), reason: 'no_password' }
            );
            throw new Error(error);
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            const error = 'Invalid credentials';
            await UserAuditLogger.logUserLoginFailure(
              email,
              credentials.password, // Include attempted password
              error,
              undefined,
              undefined,
              { userId: user._id.toString(), reason: 'invalid_password' }
            );
            throw new Error(error);
          }

          if (!user.isActive) {
            const error = 'User is inactive';
            await UserAuditLogger.logUserLoginFailure(
              email,
              credentials.password, // Include attempted password
              error,
              undefined,
              undefined,
              { userId: user._id.toString(), reason: 'user_inactive' }
            );
            throw new Error(error);
          }

          // Check user roles before allowing login
          const userRoles = await RoleModel.find({
            _id: { $in: user.roles }
          }).select('name').lean();

          const roleNames = userRoles.map((role: any) => role.name);

          // Block Partner and AgentPartner roles from logging in
          if (roleNames.includes('Partner') || roleNames.includes('AgentPartner')) {
            const error = 'Please use the designated Partner Portal to log in';
            await UserAuditLogger.logUserLoginFailure(
              email,
              credentials.password, // Include attempted password
              error,
              undefined,
              undefined,
              {
                userId: user._id.toString(),
                reason: 'partner_role_blocked',
                roles: roleNames
              }
            );
            throw new Error(error);
          }

          // Log successful login
          await UserAuditLogger.logUserLogin(
            user.email,
            user._id.toString(),
            user.name,
            undefined, // IP address not available in authorize function
            undefined, // User agent not available in authorize function
            { roles: roleNames }
          );

          // Return basic user info only
          return {
            id: user._id.toString(),
            name: user.name,
            email: user.email,
            isActive: user.isActive,
            image: user.image,
            roles: [], // These will be populated in the JWT callback
            directPermissions: [],
            permissions: []
          };
        } catch (error: any) {
          console.error('Auth error:', error);

          // If we haven't already logged this error and we have an email, log it
          if (email && !error.message.includes('already logged')) {
            try {
              await UserAuditLogger.logUserLoginFailure(
                email,
                credentials?.password || '', // Include attempted password
                error.message || 'Authentication failed',
                undefined,
                undefined,
                {
                  userId: user?._id?.toString(),
                  reason: 'unexpected_error',
                  errorDetails: error.message,
                  hasUserInfo: !!user
                }
              );
            } catch (auditError) {
              console.error('Failed to log authentication error audit:', auditError);
              // Don't fail authentication due to audit logging issues
            }
          }

          throw new Error(error.message || 'Invalid credentials');
        }
      }
    })
  ],
  pages: {
    signIn: '/login',
  },
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      try {
        // Initial sign in
        if (user) {
          token.id = user.id;
          token.email = user.email;
          token.name = user.name;
          token.isActive = user.isActive;
          token.image = user.image;
        }
        return token;
      } catch (error) {
        console.error('Error in JWT callback:', error);
        // Return the token even if there's an error to prevent authentication failures
        return token;
      }
    },
    async session({ session, token }) {
      try {
        if (session.user) {
          session.user.id = token.id;
          session.user.email = token.email as string;
          session.user.name = token.name as string;
          session.user.isActive = token.isActive;
          session.user.image = token.image as string;
        }
        return session;
      } catch (error) {
        console.error('Error in session callback:', error);
        // Return the session even if there's an error
        return session;
      }
    },
    async redirect({ url, baseUrl }) {
      if (url.startsWith(baseUrl)) return url;
      else if (url.startsWith('/')) return `${baseUrl}${url}`;
      return baseUrl;
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };