
/*import NextAuth, { AuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';
import dbConnect from '@/lib/db';
import User from '@/models/User';

export const authOptions: AuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'Credentials',
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        try {
          if (!credentials?.email || !credentials?.password) {
            throw new Error('Email and password are required');
          }

          await dbConnect();

          const user = await User.findOne({ 
            email: credentials.email.toLowerCase() 
          })
          .select('+password')
          .populate({
            path: 'roles',
            populate: {
              path: 'permissions',
              select: 'code name description'
            }
          })
          .populate('directPermissions', 'code name description');

          if (!user?.password) {
            throw new Error('Invalid credentials');
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user.password
          );

          if (!isPasswordValid) {
            throw new Error('Invalid credentials');
          }

          if (!user.isActive) {
            throw new Error('User is inactive');
          }

          const userObject = user.toObject();
          
          const rolePermissions = userObject.roles.flatMap(role => 
            role.permissions.map(p => p.code)
          );
          const directPermissionCodes = userObject.directPermissions.map(p => p.code);
          
          userObject.allPermissions = [...new Set([...rolePermissions, ...directPermissionCodes])];
          
          delete userObject.password;

          return userObject;
        } catch (error: any) {
          throw new Error(error.message || 'Invalid credentials');
        }
      }
    })
  ],
  pages: {
    signIn: '/login',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user._id;
        token.roles = user.roles;
        token.permissions = user.allPermissions?.slice(0, 50) || [];
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.id;
        session.user.roles = token.roles;
        session.user.permissions = token.permissions;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      if (url.startsWith(baseUrl)) return url;
      else if (url.startsWith('/')) return `${baseUrl}${url}`;
      return baseUrl;
    },
  },
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  cookies: {
    sessionToken: {
      name: '__Secure-next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: true,
        domain: '.knock-pro.com'
      },
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST }; 
*/