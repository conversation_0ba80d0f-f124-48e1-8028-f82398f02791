import { NextRequest, NextResponse } from 'next/server';

/**
 * Endpoint to capture login context (IP address, user agent) before authentication
 * This can be called from the login page to store context for audit logging
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    // Extract IP address and user agent from request
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Store in a temporary cache or session storage
    // For now, we'll just return the context to be used by the client
    const loginContext = {
      email: email?.toLowerCase(),
      ipAddress,
      userAgent,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      context: loginContext
    });

  } catch (error: any) {
    console.error('Login context capture error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to capture login context' 
      },
      { status: 500 }
    );
  }
}
