import { NextRequest, NextResponse } from 'next/server';
import { UserAuditLogger } from '@/lib/utils/audit-utils';
import dbConnect from '@/lib/db';

/**
 * Test endpoint to verify audit logging functionality
 * This endpoint can be used to test login audit logging
 */
export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    const body = await request.json();
    const { email, password, success, errorMessage, userId, userName } = body;

    // Extract IP address and user agent from request
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    let auditLog;

    if (success) {
      // Test successful login audit
      auditLog = await UserAuditLogger.logUserLogin(
        email,
        userId,
        userName,
        ipAddress,
        userAgent,
        { testMode: true }
      );
    } else {
      // Test failed login audit
      auditLog = await UserAuditLogger.logUserLoginFailure(
        email,
        password || 'test-password-123', // Include test password for failed attempts
        errorMessage || 'Test login failure',
        ipAddress,
        userAgent,
        { testMode: true }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Audit log created successfully',
      auditLogId: auditLog?._id,
      ipAddress,
      userAgent
    });

  } catch (error: any) {
    console.error('Test audit logging error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to create audit log' 
      },
      { status: 500 }
    );
  }
}
