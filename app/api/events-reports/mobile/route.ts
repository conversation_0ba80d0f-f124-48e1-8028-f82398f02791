import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { EventReport } from '@/models/EventReport';
import { getUserFromToken } from '@/app/api/affectations/utils/mobile_auth_utils';
import dbConnect from '@/lib/db';
import { isSuperAdmin, isBranchesAdmin, isEventSupervisor, isCook, isPAPUser } from '@/lib/utils/role-utils';

export async function GET(request: Request) {
  try {
    // Handle mobile authentication
    const requestHeaders = request.headers;
    const authHeader = requestHeaders.get('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    // Get user from mobile token
    const session = await getUserFromToken({ isMobile: true }, requestHeaders);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

  await dbConnect();

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const name = searchParams.get('name'); // search parameter for event name
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build filter object for event reports based on user roles
    const filter: any = {};
    const userId = session.user._id || session.user.id;
    const userRoles = session.user.roles || [];
    // Role-based access control
    if (isSuperAdmin(userRoles)) {
      // Superadmins can see everything - no additional filters
    } else if (isBranchesAdmin(userRoles)) {
      // Branch admins see event reports where eventId->event->branchId is in user->branchIds
      const userBranchIds = session.user.branchIds || [];
      if (userBranchIds.length > 0) {
        // We'll need to populate eventId and filter after population since we need to check event.branchId
        filter._branchFilter = userBranchIds;
      } else {
        // If no branch IDs, return empty result
        filter._id = null;
      }
    } else {
      // For other roles (supervisors, cooks, paps), build OR condition
      const roleFilters = [];

      if (isEventSupervisor(userRoles)) {
        roleFilters.push({ supervisors: userId });
      }

      if (isCook(userRoles)) {
        roleFilters.push({ 'cooks.userId': userId });
      }

      if (isPAPUser(userRoles)) {
        roleFilters.push({ 'paps.userId': userId });
      }

      if (roleFilters.length > 0) {
        filter.$or = roleFilters;
      } else {
        // If user has no relevant roles, return empty result
        filter._id = null;
      }
    }
    // Status filter is applied on event-report
    if (status) {
      filter.status = status;
    }

    // Date filters on event report times
    if (startDate) {
      filter.eventStartTime = { $gte: new Date(startDate) };
    }

    if (endDate) {
      const end = new Date(endDate);
      // If only date is provided (YYYY-MM-DD), set to end of day
      if (/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
        end.setUTCHours(23, 59, 59, 999);
      }
      filter.eventEndTime = { $lte: end };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Extract branch filter for later use
    const branchFilter = filter._branchFilter;
    delete filter._branchFilter; // Remove from filter as it's not a valid MongoDB field

    // Query event reports with populated event data
    let reportsQuery = EventReport.find(filter)
      .populate({
        path: 'eventId',
        select: 'name location branchId partnerId eventTypeId clientGoal contactInfo resId notes',
        populate: [
          { path: 'branchId', select: 'name' },
          { path: 'partnerId', select: 'name' },
          { path: 'eventTypeId', select: 'name code' }
        ]
      })
      .populate('supervisors', 'name email avatar')
      .populate('paps.userId', 'name email avatar')
      .populate('cooks.userId', 'name email avatar')
      .lean();

    // Apply name filter if provided (search in event name)
    if (name) {
      // We need to filter by event name, so we'll do this after population
      const allReports = await reportsQuery;

      // Filter out reports where eventId doesn't point to a valid event
      let filteredReports = allReports.filter((report: any) => report.eventId != null);

      // Apply branch filter for branch admins
      if (branchFilter && branchFilter.length > 0) {
        filteredReports = filteredReports.filter((report: any) =>
          report.eventId?.branchId?._id &&
          branchFilter.some((branchId: any) =>
            branchId.toString() === report.eventId.branchId._id.toString()
          )
        );
      }

      // Filter by event name
      filteredReports = filteredReports.filter((report: any) =>
        report.eventId?.name?.toLowerCase().includes(name.toLowerCase())
      );

      // Apply sorting and pagination to filtered results
      const total = filteredReports.length;
      const reports = filteredReports
        .sort((a: any, b: any) => new Date(b.eventStartTime).getTime() - new Date(a.eventStartTime).getTime())
        .slice(skip, skip + limit);

      const transformedEvents = transformReportsToEvents(reports);

      return NextResponse.json({
        data: transformedEvents,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      });
    }

    // If no name filter, use regular query with pagination
    const allReports = await reportsQuery
      .sort({ eventStartTime: -1 });

    // Filter out reports where eventId doesn't point to a valid event
    let validReports = allReports.filter((report: any) => report.eventId != null);

    // Apply branch filter for branch admins
    if (branchFilter && branchFilter.length > 0) {
      validReports = validReports.filter((report: any) =>
        report.eventId?.branchId?._id &&
        branchFilter.some((branchId: any) =>
          branchId.toString() === report.eventId.branchId._id.toString()
        )
      );
    }

    // Apply pagination to valid reports
    const reports = validReports.slice(skip, skip + limit);
    const total = validReports.length;
    const transformedEvents = transformReportsToEvents(reports);

    // Return data in the format expected by Flutter
    return NextResponse.json({
      data: transformedEvents,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    console.error('Error fetching event reports for mobile:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event reports' },
      { status: 500 }
    );
  }
}

function transformReportsToEvents(reports: any[]) {
  return reports
    .filter((report: any) => report.eventId != null) // Additional safety check
    .map((report: any) => ({
      id: report.eventId._id?.toString() || '',
      agents: {
        requiredCount: report.paps?.length || 0,
        assignedAgents: report.paps?.map((pap: any) => ({
          _id: pap.userId?._id?.toString() || '',
          name: pap.userId?.name || '',
          email: pap.userId?.email || '',
          avatar: pap.userId?.avatar || '',
          timeRange: {
            startTime: pap.timeRange?.startTime?.toISOString(),
            endTime: pap.timeRange?.endTime?.toISOString()
          }
        })) || []
      },
      branchId: report.eventId.branchId?._id?.toString() || '',
      clientGoal: report.eventId.clientGoal || 0,
      contactInfo: report.eventId.contactInfo || {
        name: '',
        role: '',
        phone: ''
      },
      cooks: report.cooks?.map((cook: any) => ({
        _id: cook.userId?._id?.toString() || '',
        name: cook.userId?.name || '',
        email: cook.userId?.email || '',
        avatar: cook.userId?.avatar || '',
        timeRange: {
          startTime: cook.timeRange?.startTime?.toISOString(),
          endTime: cook.timeRange?.endTime?.toISOString()
        }
      })) || [],
      createdAt: report.createdAt?.toISOString(),
      endDate: report.eventEndTime?.toISOString(),
      eventTypeId: report.eventId.eventTypeId?._id?.toString() || '',
      eventTypeName: report.eventId.eventTypeId?.name || '',
      location: report.eventId.location || '',
      name: report.eventId.name || '',
      notes: report.eventId.notes || '',
      partnerId: report.eventId.partnerId?._id?.toString() || '',
      reportId: report._id?.toString() || '',
      resId: report.eventId.resId?.toString() || '',
      startDate: report.eventStartTime?.toISOString(),
      status: report.status || 'pending',
      supervisors: report.supervisors?.map((supervisor: any) => ({
        _id: supervisor._id?.toString() || '',
        name: supervisor.name || '',
        email: supervisor.email || '',
        avatar: supervisor.avatar || ''
      })) || [],
      updatedAt: report.updatedAt?.toISOString()
    }));
}
