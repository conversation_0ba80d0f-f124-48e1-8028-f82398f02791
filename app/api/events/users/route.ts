import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import User from '@/models/User';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get role parameter from query string
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');

    if (!role) {
      return NextResponse.json({ error: 'Role parameter is required' }, { status: 400 });
    }

    // Define role mappings based on environment
    const isDev = process.env.NEXT_PUBLIC_DEV_SERVER === 'true';
    
    const roleMap: Record<string, string> = {
      'pap': '67fbd1707839bdba5be4b02b',
      'supervisor': isDev ? '68542308d93e57259f691917' : '68683abe9428b7eee5312a8c',
      'cook': isDev ? '68542308d93e57259f691918' : '68683abe9428b7eee5312a8d'
    };

    const roleId = roleMap[role.toLowerCase()];
    
    if (!roleId) {
      return NextResponse.json({ 
        error: 'Invalid role. Supported roles: pap, supervisor, cook' 
      }, { status: 400 });
    }

    // Fetch users with the specified role
    const users = await User.find({
      roles: roleId,
      isActive: true,
      deletedAt: null
    })
    .select('_id name email roles')
    .populate('roles', 'name')
    .lean();

    return NextResponse.json({ users });

  } catch (error) {
    console.error('GET events users error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}
