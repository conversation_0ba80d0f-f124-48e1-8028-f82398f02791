import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import * as RoleUtils from '@/lib/utils/role-utils';
import dbConnect from '@/lib/db';
import { Event } from '@/models/Event';
import { EventReport } from '@/models/EventReport';
import mongoose from 'mongoose';
import { canUserCreateEvents, canUserViewEvents } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  await dbConnect();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  try {

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const partnerId = searchParams.get('partnerId');
    const eventTypeId = searchParams.get('eventTypeId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const status = searchParams.get('status');
    const supervisorId = searchParams.get('supervisorId');
    const cookId = searchParams.get('cookId');
    const reportStatus = searchParams.get('reportStatus');
    const search = searchParams.get('search');
    
    if(!canUserViewEvents(session.user)){
      return NextResponse.json([],{status:200})
    }
    // Build filter object
    const filter: any = {};
    
    if (branchId && branchId !== 'all') {
      filter.branchId = new mongoose.Types.ObjectId(branchId);
    }
    
    if (partnerId && partnerId !== 'all') {
      filter.partnerId = new mongoose.Types.ObjectId(partnerId);
    }
    
    if (eventTypeId && eventTypeId !== 'all') {
      filter.eventTypeId = new mongoose.Types.ObjectId(eventTypeId);
    }
    
    if (status) {
      filter.status = status;
    }

    if (supervisorId) {
      filter.supervisors = new mongoose.Types.ObjectId(supervisorId);
    }

    if (cookId) {
      filter.cooks = new mongoose.Types.ObjectId(cookId);
    }

    if (startDate) {
      filter.startDate = { $gte: new Date(startDate) };
    }

    if (endDate) {
      const end = new Date(endDate);
      // If the string is a date only (no time), set to end of day
      if (/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
        end.setUTCHours(23, 59, 59, 999);
      }
      filter.endDate = { $lte: end };
    }

    // Add search functionality
    if (search && search.trim()) {
      filter.$or = [
        { name: { $regex: search.trim(), $options: 'i' } },
        { location: { $regex: search.trim(), $options: 'i' } },
        { notes: { $regex: search.trim(), $options: 'i' } }
      ];
    }
    
    // Get events with populated references
    const events = await Event.find(filter)
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate({
        path: 'reportId',
        populate: [
          { path: 'paps.userId', select: 'name email' },
          { path: 'cooks.userId', select: 'name email' }
        ]
      })
      .sort({ startDate: 1 })
      .lean();

    // Add summary information for each event (similar to supervisor API)
    let eventsWithSummary = events.map(event => {
      const report = event.reportId;
      let summary = {
        totalPAPs: 0,
        totalCooks: 0,
        reportStatus: 'not_created',
        canEdit: false,
        needsAttention: false
      };

      if (report) {
        summary.totalPAPs = report.paps?.length || 0;
        summary.totalCooks = report.cooks?.length || 0;
        summary.reportStatus = report.status;
        summary.canEdit = ['pending', 'processing'].includes(report.status);

        // Check if event needs attention (started but no report progress)
        const now = new Date();
        const eventStarted = now >= new Date(event.startDate);
        const eventEnded = now >= new Date(event.endDate);

        summary.needsAttention = eventEnded &&
          ['new', 'in_progress'].includes(event.status) &&
          ['pending', 'processing'].includes(report.status);
      }

      return {
        ...event,
        summary
      };
    });

    // Filter by report status if specified
    if (reportStatus && reportStatus !== 'all') {
      eventsWithSummary = eventsWithSummary.filter(event => {
        if (reportStatus === 'overdue') {
          // Check if report is overdue
          const now = new Date();
          const eventEndTime = new Date(event.endDate);
          const hoursSinceEnd = (now.getTime() - eventEndTime.getTime()) / (1000 * 60 * 60);
          return hoursSinceEnd > 24 &&
                 event.reportId &&
                 (!event.reportId.status || event.reportId.status === 'pending') &&
                 event.status !== 'done' &&
                 event.status !== 'cancelled';
        } else {
          return event.summary.reportStatus === reportStatus;
        }
      });
    }

    return NextResponse.json({ events: eventsWithSummary });
  } catch (error) {
    console.error('Error fetching events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch events' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if(session && !session.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Only super admins can create events
  if (!canUserCreateEvents(session.user)) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
    await dbConnect();
    const eventData = await request.json();

    // Validate event data
    if (!eventData.name || !eventData.branchId || !eventData.partnerId ||
        !eventData.eventTypeId || !eventData.startDate || !eventData.location) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate and normalize dates to ensure they are valid UTC dates
    let startDate: Date, endDate: Date;

    try {
      startDate = new Date(eventData.startDate);
      endDate = new Date(eventData.endDate);

      // Check if dates are valid
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid date format. Please provide valid ISO date strings.' },
          { status: 400 }
        );
      }

      // Validate that startDate is before endDate
      if (startDate >= endDate) {
        return NextResponse.json(
          { error: 'Event start time must be before end time' },
          { status: 400 }
        );
      }

      // Ensure dates are stored as UTC by converting to ISO string and back
      eventData.startDate = startDate.toISOString();
      eventData.endDate = endDate.toISOString();

    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid date format. Please provide valid ISO date strings.' },
        { status: 400 }
      );
    }
    
    // Convert string IDs to ObjectIds
    const newEventData = {
      ...eventData,
      branchId: new mongoose.Types.ObjectId(eventData.branchId),
      partnerId: new mongoose.Types.ObjectId(eventData.partnerId),
      eventTypeId: new mongoose.Types.ObjectId(eventData.eventTypeId),
      endDate: eventData.endDate || eventData.startDate, // Default end date to start date if not provided
      supervisors: eventData.supervisors ? eventData.supervisors.map((id: string) => new mongoose.Types.ObjectId(id)) : [],
      cooks: eventData.cooks ? eventData.cooks.map((id: string) => new mongoose.Types.ObjectId(id)) : [],
      // Store PAPs as simple array in Event model (agents.assignedAgents)
      agents: eventData.agents ? {
        requiredCount: eventData.agents.requiredCount || 0,
        assignedAgents: eventData.agents.assignedAgents ? eventData.agents.assignedAgents.map((id: string) => new mongoose.Types.ObjectId(id)) : []
      } : undefined,
    };
    
    // Create the new event
    const newEvent = new Event(newEventData);
    await newEvent.save();

    // Create EventReport for the new event
    // Initialize time ranges to full event duration as per original design
    const eventReport = new EventReport({
      eventId: newEvent._id,
      eventStartTime: newEvent.startDate,
      eventEndTime: newEvent.endDate,
      supervisors: newEvent.supervisors,
      // Initialize PAPs with time ranges spanning full event duration
      paps: newEvent.agents?.assignedAgents ? newEvent.agents.assignedAgents.map((agentId: any) => ({
        userId: agentId, // Already converted to ObjectId above
        timeRange: {
          startTime: newEvent.startDate,
          endTime: newEvent.endDate
        }
      })) : [],
      // Initialize cooks with time ranges spanning full event duration
      cooks: newEvent.cooks.map((cookId: any) => ({
        userId: cookId,
        timeRange: {
          startTime: newEvent.startDate,
          endTime: newEvent.endDate
        },
        percentage: newEvent.cooks.length > 0 ? Math.round(100 / newEvent.cooks.length) : 100 // Equal distribution by default
      })),
      history: [{
        action: 'Event created',
        changedBy: new mongoose.Types.ObjectId(session.user.id),
        changedAt: new Date()
      }]
    });

    await eventReport.save();

    // Update event with reportId
    newEvent.reportId = eventReport._id;
    await newEvent.save();

    // Populate references for the response
    const populatedEvent = await Event.findById(newEvent._id)
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate('reportId')
      .lean();
    
    return NextResponse.json(populatedEvent, { status: 201 });
  } catch (error) {
    console.error('Error creating event:', error);
    return NextResponse.json(
      { error: 'Failed to create event' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Only super admins can update events
  const isSuperAdmin = RoleUtils.isSuperAdmin(session);
  if (!isSuperAdmin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
    await dbConnect();
    const eventData = await request.json();
    
    if (!eventData._id) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }
    
    // Convert string IDs to ObjectIds if they exist
    const updateData: any = { ...eventData };
    delete updateData._id; // Remove _id from the update data

    if (updateData.branchId) {
      updateData.branchId = new mongoose.Types.ObjectId(updateData.branchId);
    }

    if (updateData.partnerId) {
      updateData.partnerId = new mongoose.Types.ObjectId(updateData.partnerId);
    }

    if (updateData.eventTypeId) {
      updateData.eventTypeId = new mongoose.Types.ObjectId(updateData.eventTypeId);
    }

    if (updateData.supervisors) {
      updateData.supervisors = updateData.supervisors.map((id: string) => new mongoose.Types.ObjectId(id));
    }

    if (updateData.cooks) {
      updateData.cooks = updateData.cooks.map((id: string) => new mongoose.Types.ObjectId(id));
    }

    // Validate and normalize dates if they are being updated
    if (updateData.startDate || updateData.endDate) {
      try {
        // Get current event to use existing dates if only one is being updated
        const currentEvent = await Event.findById(eventData._id);
        if (!currentEvent) {
          return NextResponse.json(
            { error: 'Event not found' },
            { status: 404 }
          );
        }

        const startDate = updateData.startDate ? new Date(updateData.startDate) : currentEvent.startDate;
        const endDate = updateData.endDate ? new Date(updateData.endDate) : currentEvent.endDate;

        // Check if dates are valid
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return NextResponse.json(
            { error: 'Invalid date format. Please provide valid ISO date strings.' },
            { status: 400 }
          );
        }

        // Validate that startDate is before endDate
        if (startDate >= endDate) {
          return NextResponse.json(
            { error: 'Event start time must be before end time' },
            { status: 400 }
          );
        }

        // Ensure dates are stored as UTC
        if (updateData.startDate) {
          updateData.startDate = startDate.toISOString();
        }
        if (updateData.endDate) {
          updateData.endDate = endDate.toISOString();
        }

      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid date format. Please provide valid ISO date strings.' },
          { status: 400 }
        );
      }
    }

    // Update the event
    const updatedEvent = await Event.findByIdAndUpdate(
      eventData._id,
      { $set: updateData },
      { new: true }
    )
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate('reportId')
      .lean();
    
    if (!updatedEvent) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedEvent);
  } catch (error) {
    console.error('Error updating event:', error);
    return NextResponse.json(
      { error: 'Failed to update event' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Only super admins can delete events
  const isSuperAdmin = RoleUtils.isSuperAdmin(session);
  if (!isSuperAdmin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
    await dbConnect();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }
    
    // Find and delete the event
    const deletedEvent = await Event.findByIdAndDelete(id).lean();
    
    if (!deletedEvent) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(deletedEvent);
  } catch (error) {
    console.error('Error deleting event:', error);
    return NextResponse.json(
      { error: 'Failed to delete event' },
      { status: 500 }
    );
  }
} 