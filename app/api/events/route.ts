import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/route';
import * as RoleUtils from '@/lib/utils/role-utils';
import dbConnect from '@/lib/db';
import { Event } from '@/models/Event';
import { EventReport } from '@/models/EventReport';
import mongoose from 'mongoose';
import { canUserCreateEvents, canUserViewEvents } from '@/lib/utils/permissions-utils';
import { getUserPermissions, getUserRoles } from '@/app/api/utils/server-permission-utils';
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  await dbConnect();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  try {

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branchId');
    const partnerId = searchParams.get('partnerId');
    const eventTypeId = searchParams.get('eventTypeId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const statusFilter = searchParams.get('status'); // Changed from 'status' to avoid confusion
    const supervisorId = searchParams.get('supervisorId');
    const cookId = searchParams.get('cookId');
    const reportStatus = searchParams.get('reportStatus');
    const search = searchParams.get('search');
    const dateFilter = searchParams.get('dateFilter');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Pagination parameters
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10); // Changed default from 1000 to 20
    const skip = (page - 1) * limit;

    if(!canUserViewEvents(session.user)){
      return NextResponse.json([],{status:200})
    }

    // Build base filter object (without status filtering)
    const baseFilter: any = {};

    if (branchId && branchId !== 'all') {
      baseFilter.branchId = new mongoose.Types.ObjectId(branchId);
    }

    if (partnerId && partnerId !== 'all') {
      baseFilter.partnerId = new mongoose.Types.ObjectId(partnerId);
    }

    if (eventTypeId && eventTypeId !== 'all') {
      baseFilter.eventTypeId = new mongoose.Types.ObjectId(eventTypeId);
    }

    if (supervisorId) {
      baseFilter.supervisors = new mongoose.Types.ObjectId(supervisorId);
    }

    if (cookId) {
      baseFilter.cooks = new mongoose.Types.ObjectId(cookId);
    }

    if (startDate) {
      baseFilter.startDate = { $gte: new Date(startDate) };
    }

    if (endDate) {
      const end = new Date(endDate);
      // If the string is a date only (no time), set to end of day
      if (/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
        end.setUTCHours(23, 59, 59, 999);
      }
      baseFilter.endDate = { $lte: end };
    }

    // Add search functionality
    if (search && search.trim()) {
      baseFilter.$or = [
        { name: { $regex: search.trim(), $options: 'i' } },
        { location: { $regex: search.trim(), $options: 'i' } },
        { notes: { $regex: search.trim(), $options: 'i' } }
      ];
    }

    // Add date filter functionality
    if (dateFilter && dateFilter !== 'all') {
      const now = new Date();
      switch (dateFilter) {
        case 'upcoming':
          baseFilter.startDate = { ...baseFilter.startDate, $gt: now };
          break;
        case 'this_month':
          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
          const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
          baseFilter.startDate = {
            ...baseFilter.startDate,
            $gte: monthStart,
            $lte: monthEnd
          };
          break;
        case 'next_month':
          const nextMonthStart = new Date(now.getFullYear(), now.getMonth() + 1, 1);
          const nextMonthEnd = new Date(now.getFullYear(), now.getMonth() + 2, 0, 23, 59, 59, 999);
          baseFilter.startDate = {
            ...baseFilter.startDate,
            $gte: nextMonthStart,
            $lte: nextMonthEnd
          };
          break;
        case 'past':
          baseFilter.startDate = { ...baseFilter.startDate, $lt: now };
          break;
      }
    }

    // Get counts for each status tab (unpaginated)
    const statusCounts = {
      all: await Event.countDocuments(baseFilter),
      active: await Event.countDocuments({ ...baseFilter, status: { $in: ['new', 'in_progress'] } }),
      reporting: await Event.countDocuments({ ...baseFilter, status: { $in: ['processing_report', 'awaiting_validation'] } }),
      completed: await Event.countDocuments({ ...baseFilter, status: 'done' }),
      overdue: 0 // Will be calculated after fetching events
    };

    // Apply status filter for the current tab
    const filter = { ...baseFilter };
    if (statusFilter && statusFilter !== 'all') {
      if (statusFilter === 'active') {
        filter.status = { $in: ['new', 'in_progress'] };
      } else if (statusFilter === 'reporting') {
        filter.status = { $in: ['processing_report', 'awaiting_validation'] };
      } else if (statusFilter === 'completed') {
        filter.status = 'done';
      } else if (statusFilter === 'overdue') {
        // For overdue, we'll filter after fetching since it's based on date comparison
        filter.status = { $nin: ['done', 'cancelled'] };
      } else {
        filter.status = statusFilter;
      }
    }

    // Get total count for current filter (for pagination)
    const total = await Event.countDocuments(filter);

    // Build sort object
    const sortObj: any = {};
    switch (sortBy) {
      case 'name':
        sortObj.name = sortOrder === 'desc' ? -1 : 1;
        break;
      case 'status':
        sortObj.status = sortOrder === 'desc' ? -1 : 1;
        break;
      case 'location':
        sortObj.location = sortOrder === 'desc' ? -1 : 1;
        break;
      case 'startDate':
        sortObj.startDate = sortOrder === 'desc' ? -1 : 1;
        break;
      case 'createdAt':
      default:
        sortObj.createdAt = sortOrder === 'desc' ? -1 : 1;
        break;
    }

    // Get events with populated references and pagination
    const events = await Event.find(filter)
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate('agents.assignedAgents', 'name email') // Populate PAP agents
      .populate({
        path: 'reportId',
        populate: [
          { path: 'paps.userId', select: 'name email' },
          { path: 'cooks.userId', select: 'name email' }
        ]
      })
      .sort(sortObj)
      .skip(skip)
      .limit(limit)
      .lean();

    // Add summary information for each event (similar to supervisor API)
    let eventsWithSummary = events.map(event => {
      const report = event.reportId;
      let summary = {
        totalPAPs: event.agents?.assignedAgents?.length || 0,
        totalCooks: event.cooks?.length || 0,
        reportStatus: 'not_created',
        canEdit: false,
        needsAttention: false
      };

      if (report) {
        summary.totalPAPs = report.paps?.length || 0;
        summary.totalCooks = report.cooks?.length || 0;
        summary.reportStatus = report.status;
        summary.canEdit = ['pending', 'processing'].includes(report.status);

        // Check if event needs attention (started but no report progress)
        const now = new Date();
        const eventEnded = now >= new Date(event.endDate);

        summary.needsAttention = eventEnded &&
          ['new', 'in_progress'].includes(event.status) &&
          ['pending', 'processing'].includes(report.status);
      }

      return {
        ...event,
        summary
      };
    });

    // Filter for overdue events if that's the selected status
    if (statusFilter === 'overdue') {
      const now = new Date();
      eventsWithSummary = eventsWithSummary.filter(event => {
        const eventEndTime = new Date((event as any).endDate);
        return now > eventEndTime && !['done', 'cancelled'].includes((event as any).status);
      });
    }

    // Calculate overdue count for status counts
    const now = new Date();
    const overdueEvents = await Event.find({
      ...baseFilter,
      status: { $nin: ['done', 'cancelled'] }
    }).lean();

    statusCounts.overdue = overdueEvents.filter(event => {
      const eventEndTime = new Date(event.endDate);
      return now > eventEndTime;
    }).length;

    // Filter by report status if specified
    if (reportStatus && reportStatus !== 'all') {
      eventsWithSummary = eventsWithSummary.filter(event => {
        if (reportStatus === 'overdue') {
          // Check if report is overdue
          const now = new Date();
          const eventEndTime = new Date((event as any).endDate);
          const hoursSinceEnd = (now.getTime() - eventEndTime.getTime()) / (1000 * 60 * 60);
          return hoursSinceEnd > 24 &&
                 (event as any).reportId &&
                 (!(event as any).reportId.status || (event as any).reportId.status === 'pending') &&
                 (event as any).status !== 'done' &&
                 (event as any).status !== 'cancelled';
        } else {
          return event.summary.reportStatus === reportStatus;
        }
      });
    }

    // Recalculate total and pagination for overdue filter
    let finalTotal = total;
    if (statusFilter === 'overdue') {
      finalTotal = eventsWithSummary.length;
    }

    // Calculate pagination metadata
    const totalPages = Math.ceil(finalTotal / limit);

    return NextResponse.json({
      events: eventsWithSummary,
      statusCounts,
      pagination: {
        page,
        limit,
        total: finalTotal,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch events' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if(session && !session.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Only super admins can create events
  if (!canUserCreateEvents(session.user)) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
    await dbConnect();
    const eventData = await request.json();

    // Validate event data
    if (!eventData.name || !eventData.branchId || !eventData.partnerId ||
        !eventData.eventTypeId || !eventData.startDate || !eventData.location) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Validate and normalize dates to ensure they are valid UTC dates
    let startDate: Date, endDate: Date;

    try {
      startDate = new Date(eventData.startDate);
      endDate = new Date(eventData.endDate);

      // Check if dates are valid
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid date format. Please provide valid ISO date strings.' },
          { status: 400 }
        );
      }

      // Validate that startDate is before endDate
      if (startDate >= endDate) {
        return NextResponse.json(
          { error: 'Event start time must be before end time' },
          { status: 400 }
        );
      }

      // Ensure dates are stored as UTC by converting to ISO string and back
      eventData.startDate = startDate.toISOString();
      eventData.endDate = endDate.toISOString();

    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid date format. Please provide valid ISO date strings.' },
        { status: 400 }
      );
    }
    
    // Convert string IDs to ObjectIds
    const newEventData = {
      ...eventData,
      branchId: mongoose.Types.ObjectId.createFromHexString(eventData.branchId),
      partnerId: mongoose.Types.ObjectId.createFromHexString(eventData.partnerId),
      eventTypeId: mongoose.Types.ObjectId.createFromHexString(eventData.eventTypeId),
      endDate: eventData.endDate || eventData.startDate, // Default end date to start date if not provided
      supervisors: eventData.supervisors ? eventData.supervisors.map((id: string) => mongoose.Types.ObjectId.createFromHexString(id)) : [],
      cooks: eventData.cooks ? eventData.cooks.map((id: string) => mongoose.Types.ObjectId.createFromHexString(id)) : [],
      // Store PAPs as simple array in Event model (agents.assignedAgents)
      agents: eventData.agents ? {
        requiredCount: eventData.agents.requiredCount || 0,
        assignedAgents: eventData.agents.assignedAgents ? eventData.agents.assignedAgents.map((id: string) => new mongoose.Types.ObjectId(id)) : []
      } : undefined,
    };
    
    // Create the new event
    const newEvent = new Event(newEventData);
    await newEvent.save();

    // Create EventReport for the new event
    // Initialize time ranges to full event duration as per original design
    const eventReport = new EventReport({
      eventId: newEvent._id,
      eventStartTime: newEvent.startDate,
      eventEndTime: newEvent.endDate,
      supervisors: newEvent.supervisors,
      // Initialize PAPs with time ranges spanning full event duration
      paps: newEvent.agents?.assignedAgents ? newEvent.agents.assignedAgents.map((agentId: any) => ({
        userId: agentId, // Already converted to ObjectId above
        timeRange: {
          startTime: newEvent.startDate,
          endTime: newEvent.endDate
        }
      })) : [],
      // Initialize cooks with time ranges spanning full event duration
      cooks: newEvent.cooks.map((cookId: any) => ({
        userId: cookId,
        timeRange: {
          startTime: newEvent.startDate,
          endTime: newEvent.endDate
        },
        percentage: newEvent.cooks.length > 0 ? Math.round(100 / newEvent.cooks.length) : 100 // Equal distribution by default
      })),
      history: [{
        action: 'Event created',
        changedBy: new mongoose.Types.ObjectId(session.user.id),
        changedAt: new Date()
      }]
    });

    await eventReport.save();

    // Update event with reportId
    newEvent.reportId = eventReport._id;
    await newEvent.save();

    // Populate references for the response
    const populatedEvent = await Event.findById(newEvent._id)
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate('reportId')
      .lean();
    
    return NextResponse.json(populatedEvent, { status: 201 });
  } catch (error) {
    console.error('Error creating event:', error);
    return NextResponse.json(
      { error: 'Failed to create event' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(!session.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if(!session.user.roles){
    session.user.roles = await getUserRoles(session);
  }
  // Only super admins can update events
  const isSuperAdmin = RoleUtils.isSuperAdmin(session);
  if (!isSuperAdmin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
    await dbConnect();
    const eventData = await request.json();
    
    if (!eventData._id) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }
    
    // Convert string IDs to ObjectIds if they exist
    const updateData: any = { ...eventData };
    delete updateData._id; // Remove _id from the update data

    if (updateData.branchId) {
      updateData.branchId = mongoose.Types.ObjectId.createFromHexString(updateData.branchId);
    }

    if (updateData.partnerId) {
      updateData.partnerId = mongoose.Types.ObjectId.createFromHexString(updateData.partnerId);
    }

    if (updateData.eventTypeId) {
      updateData.eventTypeId = mongoose.Types.ObjectId.createFromHexString(updateData.eventTypeId);
    }

    if (updateData.supervisors) {
      updateData.supervisors = updateData.supervisors.map((id: string) => mongoose.Types.ObjectId.createFromHexString(id));
    }

    if (updateData.cooks) {
      updateData.cooks = updateData.cooks.map((id: string) => mongoose.Types.ObjectId.createFromHexString(id));
    }

    // Validate and normalize dates if they are being updated
    if (updateData.startDate || updateData.endDate) {
      try {
        // Get current event to use existing dates if only one is being updated
        const currentEvent = await Event.findById(eventData._id);
        if (!currentEvent) {
          return NextResponse.json(
            { error: 'Event not found' },
            { status: 404 }
          );
        }

        const startDate = updateData.startDate ? new Date(updateData.startDate) : currentEvent.startDate;
        const endDate = updateData.endDate ? new Date(updateData.endDate) : currentEvent.endDate;

        // Check if dates are valid
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return NextResponse.json(
            { error: 'Invalid date format. Please provide valid ISO date strings.' },
            { status: 400 }
          );
        }

        // Validate that startDate is before endDate
        if (startDate >= endDate) {
          return NextResponse.json(
            { error: 'Event start time must be before end time' },
            { status: 400 }
          );
        }

        // Ensure dates are stored as UTC
        if (updateData.startDate) {
          updateData.startDate = startDate.toISOString();
        }
        if (updateData.endDate) {
          updateData.endDate = endDate.toISOString();
        }

      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid date format. Please provide valid ISO date strings.' },
          { status: 400 }
        );
      }
    }

    // Update the event
    const updatedEvent = await Event.findByIdAndUpdate(
      eventData._id,
      { $set: updateData },
      { new: true }
    )
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate('reportId')
      .lean();
    
    if (!updatedEvent) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedEvent);
  } catch (error) {
    console.error('Error updating event:', error);
    return NextResponse.json(
      { error: 'Failed to update event' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  // Only super admins can delete events
  const isSuperAdmin = RoleUtils.isSuperAdmin(session);
  if (!isSuperAdmin) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
    await dbConnect();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Event ID is required' },
        { status: 400 }
      );
    }
    
    // Find and delete the event
    const deletedEvent = await Event.findByIdAndDelete(id).lean();
    
    if (!deletedEvent) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(deletedEvent);
  } catch (error) {
    console.error('Error deleting event:', error);
    return NextResponse.json(
      { error: 'Failed to delete event' },
      { status: 500 }
    );
  }
} 