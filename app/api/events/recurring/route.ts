import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import dbConnect from '@/lib/db';
import { Event } from '@/models/Event';
import { EventReport } from '@/models/EventReport';
import mongoose from 'mongoose';
import { canUserCreateEvents } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';

function normalizeDate(date: Date) {
  const d = new Date(date);
  d.setHours(0, 0, 0, 0);
  return d;
}

function dateOnlyString(date: Date) {
  return normalizeDate(date).toISOString().slice(0, 10);
}

function getDatesByRule(start: Date, rule: any) {
  const dates: Date[] = [];
  if (!rule.enabled) return dates;
  const end = rule.endDate ? normalizeDate(new Date(rule.endDate)) : null;
  let count = 0;
  const startNorm = normalizeDate(start);
  if (rule.frequency === 'week' && Array.isArray(rule.dayOfWeek)) {
    let current = new Date(startNorm);
    while (!end || dateOnlyString(current) <= dateOnlyString(end)) {
      if (rule.dayOfWeek.includes(current.getDay()) && (!end || dateOnlyString(current) <= dateOnlyString(end)) && dateOnlyString(current) >= dateOnlyString(startNorm)) {
        dates.push(new Date(current));
      }
      current.setDate(current.getDate() + 1);
      count++;
      if (count > 1000) break;
    }
    return dates;
  }
  let current = new Date(startNorm);
  while (!end || dateOnlyString(current) <= dateOnlyString(end)) {
    if (rule.frequency === 'day') {
      if ((!end || dateOnlyString(current) <= dateOnlyString(end)) && dateOnlyString(current) >= dateOnlyString(startNorm)) {
        dates.push(new Date(current));
      }
      current.setDate(current.getDate() + 1);
    } else if (rule.frequency === 'month') {
      if (typeof rule.dayOfMonth === 'number') {
        let d = new Date(current);
        d.setDate(rule.dayOfMonth);
        d = normalizeDate(d);
        if ((!end || dateOnlyString(d) <= dateOnlyString(end)) && dateOnlyString(d) >= dateOnlyString(startNorm)) {
          dates.push(new Date(d));
        }
        current.setMonth(current.getMonth() + 1);
        current.setDate(1);
      } else {
        break;
      }
    } else if (rule.frequency === 'year') {
      if (rule.monthDay) {
        let d = new Date(current);
        d.setMonth(rule.monthDay.month, rule.monthDay.day);
        d = normalizeDate(d);
        if ((!end || dateOnlyString(d) <= dateOnlyString(end)) && dateOnlyString(d) >= dateOnlyString(startNorm)) {
          dates.push(new Date(d));
        }
        current.setFullYear(current.getFullYear() + 1);
        current.setMonth(0, 1);
      } else {
        break;
      }
    } else if (rule.frequency === 'week') {
      break;
    } else {
      break;
    }
    count++;
    if (count > 100) break;
  }
  return dates;
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if(session && !session.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Only users with create events permission can create recurring events
  if (!canUserCreateEvents(session.user)) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  try {
    await dbConnect();
    const { eventTemplate, recurringRule } = await request.json();

    if (!eventTemplate || !recurringRule) {
      return NextResponse.json({ error: 'Missing data' }, { status: 400 });
    }

    // Validate event template data
    if (!eventTemplate.name || !eventTemplate.branchId || !eventTemplate.partnerId ||
        !eventTemplate.eventTypeId || !eventTemplate.startDate || !eventTemplate.location) {
      return NextResponse.json(
        { error: 'Missing required fields in event template' },
        { status: 400 }
      );
    }

    // Validate that startDate is before endDate
    if (eventTemplate.startDate && eventTemplate.endDate) {
      const startDate = new Date(eventTemplate.startDate);
      const endDate = new Date(eventTemplate.endDate);

      if (startDate >= endDate) {
        return NextResponse.json(
          { error: 'Event start time must be before end time' },
          { status: 400 }
        );
      }
    }

    const startDate = new Date(eventTemplate.startDate);
    const endTime = new Date(eventTemplate.endDate).getTime() - new Date(eventTemplate.startDate).getTime();
    const dates = getDatesByRule(startDate, recurringRule);

    if (dates.length === 0) {
      return NextResponse.json({ error: 'No valid dates generated from recurring rule' }, { status: 400 });
    }

    if (dates.length > 100) {
      return NextResponse.json({ error: 'Too many events would be created (max 100)' }, { status: 400 });
    }

    const createdEvents = [];
    const createdReports = [];

    for (const date of dates) {
      // Prepare event data with proper ObjectId conversion
      const eventData = {
        ...eventTemplate,
        startDate: date,
        endDate: new Date(date.getTime() + endTime),
        branchId: new mongoose.Types.ObjectId(eventTemplate.branchId),
        partnerId: new mongoose.Types.ObjectId(eventTemplate.partnerId),
        eventTypeId: new mongoose.Types.ObjectId(eventTemplate.eventTypeId),
        supervisors: eventTemplate.supervisors ? eventTemplate.supervisors.map((id: string) => new mongoose.Types.ObjectId(id)) : [],
        cooks: eventTemplate.cooks ? eventTemplate.cooks.map((id: string) => new mongoose.Types.ObjectId(id)) : [],
        agents: eventTemplate.agents ? {
          requiredCount: eventTemplate.agents.requiredCount || 0,
          assignedAgents: eventTemplate.agents.assignedAgents ? eventTemplate.agents.assignedAgents.map((id: string) => new mongoose.Types.ObjectId(id)) : []
        } : undefined,
      };

      // Create the event
      const newEvent = new Event(eventData);
      await newEvent.save();

      // Create EventReport for the new event
      const eventReport = new EventReport({
        eventId: newEvent._id,
        eventStartTime: newEvent.startDate,
        eventEndTime: newEvent.endDate,
        supervisors: newEvent.supervisors,
        // Initialize PAPs with time ranges spanning full event duration
        paps: newEvent.agents?.assignedAgents ? newEvent.agents.assignedAgents.map((agentId: any) => ({
          userId: agentId,
          timeRange: {
            startTime: newEvent.startDate,
            endTime: newEvent.endDate
          }
        })) : [],
        // Initialize cooks with time ranges spanning full event duration
        cooks: newEvent.cooks.map((cookId: any) => ({
          userId: cookId,
          timeRange: {
            startTime: newEvent.startDate,
            endTime: newEvent.endDate
          },
          percentage: newEvent.cooks.length > 0 ? Math.round(100 / newEvent.cooks.length) : 100
        })),
        history: [{
          action: 'Recurring event created',
          changedBy: new mongoose.Types.ObjectId(session.user.id),
          changedAt: new Date()
        }]
      });

      await eventReport.save();

      // Update event with reportId
      newEvent.reportId = eventReport._id;
      await newEvent.save();

      createdEvents.push(newEvent);
      createdReports.push(eventReport);
    }

    // Populate references for the response
    const populatedEvents = await Event.find({
      _id: { $in: createdEvents.map(e => e._id) }
    })
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate('reportId')
      .lean();

    return NextResponse.json({
      created: createdEvents.length,
      events: populatedEvents,
      reports: createdReports.length
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating recurring events:', error);
    return NextResponse.json(
      { error: 'Failed to create recurring events' },
      { status: 500 }
    );
  }
}