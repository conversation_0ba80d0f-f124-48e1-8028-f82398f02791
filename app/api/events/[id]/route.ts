import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import * as RoleUtils from '@/lib/utils/role-utils';
import dbConnect from '@/lib/db';
import { Event } from '@/models/Event';
import { EventReport } from '@/models/EventReport';
import mongoose from 'mongoose';
import { canUserDeleteEvents, canUserEditEvents, canUserViewEvents } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
interface Params {
  params: {
    id: string;
  };
}

export async function GET(request: Request, { params }: Params) {
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const p =await params;
  if(!canUserViewEvents(session.user)){
    return NextResponse.json([],{status:200});
  }
  try {
    await dbConnect();
    
    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid event ID' },
        { status: 400 }
      );
    }
    
    const event = await Event.findById(p.id)
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate('reportId')
      .lean();

    // Auto-update status to "in_progress" if event has started and status is "new"
    if (event && event.status === 'new' && new Date() >= new Date(event.startDate)) {
      await Event.findByIdAndUpdate(p.id, { status: 'in_progress' });
      event.status = 'in_progress';
    }
    
    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(event);
  } catch (error) {
    console.error('Error fetching event:', error);
    return NextResponse.json(
      { error: 'Failed to fetch event' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request, { params }: Params) {
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const p =await params;
  if(!canUserEditEvents(session.user)){
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }
  
  try {
    await dbConnect();
    
    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid event ID' },
        { status: 400 }
      );
    }
    
    const eventData = await request.json();

    // Validate that startDate is before endDate if both are provided
    if (eventData.startDate && eventData.endDate) {
      const startDate = new Date(eventData.startDate);
      const endDate = new Date(eventData.endDate);

      if (startDate >= endDate) {
        return NextResponse.json(
          { error: 'Event start time must be before end time' },
          { status: 400 }
        );
      }
    }

    // Convert string IDs to ObjectIds if they exist
    const updateData: any = { ...eventData };
    
    if (updateData.branchId) {
      updateData.branchId = new mongoose.Types.ObjectId(updateData.branchId);
    }
    
    if (updateData.partnerId) {
      updateData.partnerId = new mongoose.Types.ObjectId(updateData.partnerId);
    }
    
    if (updateData.eventTypeId) {
      updateData.eventTypeId = new mongoose.Types.ObjectId(updateData.eventTypeId);
    }
    
    // Update the event
    const updatedEvent = await Event.findByIdAndUpdate(
      p.id,
      { $set: updateData },
      { new: true }
    )
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate('reportId')
      .lean();
    
    if (!updatedEvent) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(updatedEvent);
  } catch (error) {
    console.error('Error updating event:', error);
    return NextResponse.json(
      { error: 'Failed to update event' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: Request, { params }: Params) {
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const p = await params;
  if(!canUserEditEvents(session.user)){
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  try {
    await dbConnect();

    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid event ID' },
        { status: 400 }
      );
    }

    const { status } = await request.json();

    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Validate status value
    const validStatuses = ['new', 'in_progress', 'cancelled', 'processing_report', 'awaiting_validation', 'done'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status value' },
        { status: 400 }
      );
    }

    // Get current event to check current status
    const currentEvent = await Event.findById(p.id);
    if (!currentEvent) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    // Update the event status
    const updatedEvent = await Event.findByIdAndUpdate(
      p.id,
      { $set: { status } },
      { new: true }
    )
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate('reportId')
      .lean();

    // If transitioning from 'new' to 'in_progress', update the event report start time
    if (currentEvent.status === 'new' && status === 'in_progress') {
      // Find or create the event report
      let eventReport = await EventReport.findOne({ eventId: p.id });

      if (!eventReport) {
        // Create a new event report if it doesn't exist
        // Initialize with actual start time (current time) since event is being started
        eventReport = new EventReport({
          eventId: p.id,
          eventStartTime: new Date(), // Set actual start time to current time
          eventEndTime: currentEvent.endDate, // Use event's scheduled end time initially
          status: 'processing', // Set to processing since event is starting
          supervisors: currentEvent.supervisors || [],
          // Initialize PAPs with time ranges spanning full event duration
          paps: currentEvent.agents?.assignedAgents ? currentEvent.agents.assignedAgents.map((agentId: any) => ({
            userId: agentId,
            timeRange: {
              startTime: currentEvent.startDate,
              endTime: currentEvent.endDate
            }
          })) : [],
          // Initialize cooks with time ranges spanning full event duration
          cooks: currentEvent.cooks ? currentEvent.cooks.map((cookId: any) => ({
            userId: cookId,
            timeRange: {
              startTime: currentEvent.startDate,
              endTime: currentEvent.endDate
            },
            percentage: currentEvent.cooks.length > 0 ? Math.round(100 / currentEvent.cooks.length) : 100
          })) : [],
          history: [{
            action: 'Event started - Report created',
            changedBy: new mongoose.Types.ObjectId(session.user.id),
            changedAt: new Date(),
            newValue: { status: 'processing', eventStartTime: new Date(), eventEndTime: currentEvent.endDate }
          }]
        });
        await eventReport.save();
      } else {
        // Update existing report with actual start time
        await EventReport.findByIdAndUpdate(eventReport._id, {
          $set: {
            eventStartTime: new Date(), // Set actual start time to current time
            status: 'processing' // Update status to processing when event starts
          },
          $push: {
            history: {
              action: 'Event started - Start time updated',
              changedBy: new mongoose.Types.ObjectId(session.user.id),
              changedAt: new Date(),
              previousValue: { eventStartTime: eventReport.eventStartTime },
              newValue: { eventStartTime: new Date() }
            }
          }
        });
      }
    }

    return NextResponse.json(updatedEvent);
  } catch (error) {
    console.error('Error updating event status:', error);
    return NextResponse.json(
      { error: 'Failed to update event status' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request, { params }: Params) {
  const session = await getServerSession(authOptions);
  if(session &&!session?.user.permissions){
    session.user.permissions = await getUserPermissions(session);
  }
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if(!canUserDeleteEvents(session.user)){
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  try {
    await dbConnect();

    if (!params.id || !mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json(
        { error: 'Invalid event ID' },
        { status: 400 }
      );
    }

    // Find and delete the event
    const deletedEvent = await Event.findByIdAndDelete(params.id).lean();

    if (!deletedEvent) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(deletedEvent);
  } catch (error) {
    console.error('Error deleting event:', error);
    return NextResponse.json(
      { error: 'Failed to delete event' },
      { status: 500 }
    );
  }
}