import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import { Event } from '@/models/Event';
import { EventReport } from '@/models/EventReport';
import mongoose from 'mongoose';
import { canUserEditEvents, canUserValidateEventReports } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import dbConnect from '@/lib/db';

interface Params {
  params: {
    id: string;
  };
}

export async function PUT(request: Request, { params }: Params) {
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const p = await params;
  
  try {
  await dbConnect();
    
    if (!p.id || !mongoose.Types.ObjectId.isValid(p.id)) {
      return NextResponse.json(
        { error: 'Invalid event ID' },
        { status: 400 }
      );
    }

    const { status, reason } = await request.json();
    
    if (!status) {
      return NextResponse.json(
        { error: 'Status is required' },
        { status: 400 }
      );
    }

    // Validate status values
    const validStatuses = ['new', 'in_progress', 'cancelled', 'processing_report', 'awaiting_validation', 'done'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status value' },
        { status: 400 }
      );
    }

    // Get current event
    const event = await Event.findById(p.id).populate('reportId');
    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    // Check permissions based on status change
    if (status === 'done' && !canUserValidateEventReports(session.user)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to mark event as done' },
        { status: 403 }
      );
    }

    if (!canUserEditEvents(session.user)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to update event status' },
        { status: 403 }
      );
    }

    // Validate status transitions
    const currentStatus = event.status;
    const validTransitions: { [key: string]: string[] } = {
      'new': ['in_progress', 'cancelled'],
      'in_progress': ['processing_report', 'cancelled'],
      'processing_report': ['awaiting_validation', 'in_progress'],
      'awaiting_validation': ['done', 'processing_report'],
      'cancelled': ['new', 'in_progress'],
      'done': [] // Final status
    };

    if (!validTransitions[currentStatus]?.includes(status)) {
      return NextResponse.json(
        { error: `Invalid status transition from ${currentStatus} to ${status}` },
        { status: 400 }
      );
    }

    // Update event status
    const updatedEvent = await Event.findByIdAndUpdate(
      p.id,
      { 
        status,
        updatedAt: new Date()
      },
      { new: true }
    )
      .populate('branchId', 'name')
      .populate('partnerId', 'name')
      .populate('eventTypeId', 'code name')
      .populate('supervisors', 'name email')
      .populate('cooks', 'name email')
      .populate('reportId')
      .lean();

    // Update related EventReport status if needed
    if (event.reportId) {
      let reportStatus = null;
      switch (status) {
        case 'processing_report':
          reportStatus = 'processing';
          break;
        case 'awaiting_validation':
          reportStatus = 'submitted';
          break;
        case 'done':
          reportStatus = 'validated';
          break;
      }

      if (reportStatus) {
        await EventReport.findByIdAndUpdate(
          event.reportId,
          { 
            status: reportStatus,
            $push: {
              history: {
                action: `Status changed to ${status}`,
                changedBy: new mongoose.Types.ObjectId(session.user.id),
                changedAt: new Date(),
                previousValue: currentStatus,
                newValue: status
              }
            }
          }
        );
      }
    }

    return NextResponse.json({
      ...updatedEvent,
      statusChanged: {
        from: currentStatus,
        to: status,
        changedBy: session.user.id,
        changedAt: new Date(),
        reason
      }
    });

  } catch (error) {
    console.error('Error updating event status:', error);
    return NextResponse.json(
      { error: 'Failed to update event status' },
      { status: 500 }
    );
  }
}
