import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import { connectToDatabase } from '@/lib/mongodb';
import Branch from '@/models/Branch';
import dbConnect from '@/lib/db';

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  try {
  await dbConnect();
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const regionId = searchParams.get('regionId');
    
    // Build filter object
    const filter: any = { deletedAt: null };
    
    if (regionId && regionId !== 'all') {
      filter.regionId = regionId;
    }
    
    // Get branches with basic fields
    const branches = await Branch.find(filter)
      .select('_id name city province regionId')
      .populate('regionId', 'name')
      .sort({ name: 1 })
      .lean();
    
    return NextResponse.json(branches);
  } catch (error) {
    console.error('Error fetching branches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch branches' },
      { status: 500 }
    );
  }
} 