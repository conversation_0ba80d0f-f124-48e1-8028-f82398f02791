import { NextRequest, NextResponse } from 'next/server';
import { generateInvoicePDFDataURL } from '@/lib/utils/html-pdf-utils';

interface InvoiceItem {
  _id: string;
  title: string;
  quantity: number;
  date: string | Date;
  unitPrice: number;
  taxable: boolean;
  tps?: number;
  tvq?: number;
  total?: number;
}

interface InvoiceDetails {
  invoiceNumber: string;
  date: string | Date;
  dueDate?: string | Date;
  status: string;
  total: number;
  subtotal: number;
  totalTaxes?: number[]; // Pre-calculated tax amounts for contre factures
  signedAt?: Date | string; // Add signedAt field
  signedFromIP?: string; // Add IP address field
  issuerInfo?: {
    name: string;
    address?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    phone?: string;
  };
  // Tax registration numbers
  tpsRegistrationNumber?: string;
  qstRegistrationNumber?: string;
}

interface CompanyInfo {
  name: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  phone: string;
  email: string;
  website?: string;
}

interface TaxType {
  code: string;
  names: string[];
  percentages: number[];
}

export async function POST(request: NextRequest) {
  try {
    const {
      items,
      invoiceDetails,
      companyInfo,
      taxType,
      swapIssuerAndClient = false,
      signatureData
    }: {
      items: InvoiceItem[];
      invoiceDetails: InvoiceDetails;
      companyInfo: CompanyInfo;
      taxType: TaxType;
      swapIssuerAndClient?: boolean;
      signatureData?: string;
    } = await request.json();

    // Validate required fields
    if (!items || !invoiceDetails || !companyInfo || !taxType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Generate PDF using HTML template
    const pdfDataUrl = await generateInvoicePDFDataURL(
      items,
      invoiceDetails,
      companyInfo,
      taxType,
      swapIssuerAndClient,
      signatureData
    );

    return NextResponse.json({
      success: true,
      pdfDataUrl,
      fileName: `Invoice_${invoiceDetails.invoiceNumber}${swapIssuerAndClient ? '_ContreFacture' : ''}${signatureData ? '_Signed' : ''}.pdf`
    });

  } catch (error: any) {
    console.error('Error generating PDF:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate PDF',
        details: error.message
      },
      { status: 500 }
    );
  }
}
