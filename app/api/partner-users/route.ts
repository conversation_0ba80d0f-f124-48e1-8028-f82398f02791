import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import mongoose from 'mongoose';

export async function GET(request: Request) {
  await Promise.resolve(); // Ensure any future params are handled asynchronously
  
  try {
    await dbConnect();

    // Convert string IDs to ObjectId
    const partnerRoleId = new mongoose.Types.ObjectId('67cec8568f77ab38a0c94e61');
    const agentPartnerRoleId = new mongoose.Types.ObjectId('67cec8ce8f77ab38a0c94e6e');

    // Fetch users with Partner role (ID: 67cec8568f77ab38a0c94e61)
    const owners = await User.find({
      roles: { $in: [partnerRoleId] },
      isActive: true
    }).select('_id name email').lean();

    // Fetch users with AgentPartner role (ID: 67cec8ce8f77ab38a0c94e6e)
    const agents = await User.find({
      roles: { $in: [agentPartnerRoleId] },
      isActive: true
    }).select('_id name email').lean();

    return NextResponse.json({
      owners,
      agents
    });
  } catch (error) {
    console.error('Error fetching partner users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch partner users' },
      { status: 500 }
    );
  }
} 