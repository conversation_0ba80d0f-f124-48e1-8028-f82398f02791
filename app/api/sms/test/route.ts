import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { sendSMS } from '@/lib/twilio';
import { SETTINGS_PERMISSIONS } from '@/types/permission-codes';
import { processTemplate } from '@/lib/sms-templates';

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and has the required permissions
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check for required permission
    const userPermissions = session.user.permissions || [];
    if (!userPermissions.includes(SETTINGS_PERMISSIONS.EDIT_SETTINGS)) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      );
    }

    const { templateId, phoneNumber, testData } = await request.json();
    if (!templateId || !phoneNumber || !testData) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const db = await connectToDatabase();
    
    // Find the template in the database
    let template = await db.collection('sms_templates').findOne({ _id: templateId });
    
    // Get template content
    let templateContent = '';
    if (template) {
      templateContent = template.template;
    } else {
      // Check if this is a default template ID
      const defaultTemplate = await db.collection('sms_templates').findOne({ id: templateId });
      if (defaultTemplate) {
        templateContent = defaultTemplate.template;
    } else {
      // Define default templates as fallback
      const defaultTemplates = {
        'reservation_assignment': 'Bonjour {{sellerName}}, vous avez été affecté(e) à une réservation pour {{customerName}} le {{appointmentDate}}. Merci de vous connecter à l\'application pour plus de détails.',
          'thank_client': 'Bonjour {{customerName}}, nous vous remercions pour votre visite à {{branch.name}}. Nous espérons que vous avez apprécié l\'expérience et nous serions ravis de vous accueillir à nouveau prochainement!',
        'reservation_confirmation': 'Bonjour {{customerName}}, votre réservation pour le {{appointmentDate}} a été confirmée. Merci pour votre confiance.',
        'reservation_reminder': 'Rappel: vous avez un rendez-vous prévu le {{appointmentDate}}. En cas d\'empêchement, merci de nous contacter.'
      };
      
      templateContent = defaultTemplates[templateId as keyof typeof defaultTemplates];
      }
      
      if (!templateContent) {
        return NextResponse.json(
          { error: 'Template not found' },
          { status: 404 }
        );
      }
    }
    
    // Add sample branch data if not provided
    if (!testData['branch.name']) {
      // Add sample branch data with 'branch.' prefix
      const sampleBranchData = {
        'branch.name': 'Agence Paris Centre',
        'branch.city': 'Paris',
        'branch.phone': '+33123456789',
        'branch.address': '123 Avenue des Champs-Élysées',
        'branch.postalCode': '75001',
        'branch.province': 'Île-de-France'
      };
      
      // Add branch fields to test data
      Object.assign(testData, sampleBranchData);
    }
    
    // Process the template with the test data
    const message = processTemplate(templateContent, testData);
    
    // Send the test SMS
    await sendSMS(process.env.TWILIO_CONVERSATION_NUMBER || '', phoneNumber, message,false,session.user);
    
    return NextResponse.json({ 
      success: true,
      message: message
    });
  } catch (error) {
    console.error('Error sending test SMS:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}