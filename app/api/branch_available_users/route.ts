import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Branch from '@/models/Branch';

export async function GET(req: Request) {
  try {
    await dbConnect();
    const url = new URL(req.url);
    const branchId = url.searchParams.get('branchId');

    if (!branchId) {
      return NextResponse.json({ error: 'Missing branchId parameter' }, { status: 400 });
    }

    const branch = await Branch.findById(branchId)
      .populate('responsible', 'name email')
      .populate('agents', 'name email')
      .populate('sellers', 'name email');

    if (!branch) {
      return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
    }

    const users = [
      ...branch.responsible,
      ...branch.agents,
      ...branch.sellers,
    ];

    return NextResponse.json({ users });

  } catch (error) {
    console.error('Error fetching branch users:', error);
    return NextResponse.json({ error: 'Failed to fetch branch users' }, { status: 500 });
  }
}