import { NextRequest, NextResponse } from 'next/server';

const GOOGLE_MAPS_API_KEY = process.env.GOOGLE_MAPS_API_KEY;

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = searchParams.get('limit') || '5';

    if (!query || query.length < 2) {
      return NextResponse.json([]);
    }

    if (!GOOGLE_MAPS_API_KEY) {
      console.error('Google Maps API key not configured');
      return NextResponse.json(
        { error: 'Google Maps API key not configured' },
        { status: 500 }
      );
    }

    // Use Google Places API Autocomplete
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(query)}&key=${GOOGLE_MAPS_API_KEY}&components=country:ca&types=address`
    );

    if (!response.ok) {
      console.error('Google Places API error:', response.status, response.statusText);
      return NextResponse.json(
        { error: 'Failed to fetch address suggestions' },
        { status: response.status }
      );
    }

    const data = await response.json();

    if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
      console.error('Google Places API status error:', data.status, data.error_message);
      return NextResponse.json(
        { error: data.error_message || 'Google Places API error' },
        { status: 400 }
      );
    }

    // Get place details for each prediction to include lat/lng and address components
    const predictions = data.predictions?.slice(0, parseInt(limit)) || [];

    const transformedData = await Promise.all(
      predictions.map(async (prediction: any) => {
        try {
          // Fetch place details for each prediction
          const detailsResponse = await fetch(
            `https://maps.googleapis.com/maps/api/place/details/json?place_id=${encodeURIComponent(prediction.place_id)}&key=${GOOGLE_MAPS_API_KEY}&fields=geometry,address_components,formatted_address`
          );

          if (!detailsResponse.ok) {
            console.error(`Failed to fetch details for place ${prediction.place_id}`);
            return {
              place_id: prediction.place_id,
              display_name: prediction.description,
              display_place: prediction.structured_formatting?.main_text || prediction.description,
              display_address: prediction.structured_formatting?.secondary_text || '',
              lat: null,
              lon: null,
              address: {}
            };
          }

          const detailsData = await detailsResponse.json();

          if (detailsData.status !== 'OK') {
            console.error(`Place details API error for ${prediction.place_id}:`, detailsData.status);
            return {
              place_id: prediction.place_id,
              display_name: prediction.description,
              display_place: prediction.structured_formatting?.main_text || prediction.description,
              display_address: prediction.structured_formatting?.secondary_text || '',
              lat: null,
              lon: null,
              address: {}
            };
          }

          const result = detailsData.result;
          const location = result?.geometry?.location;

          // Extract address components
          const addressComponents = result.address_components || [];
          const address: any = {};

          addressComponents.forEach((component: any) => {
            const types = component.types;
            if (types.includes('street_number')) {
              address.house_number = component.long_name;
            } else if (types.includes('route')) {
              address.road = component.long_name;
            } else if (types.includes('neighborhood')) {
              address.neighbourhood = component.long_name;
            } else if (types.includes('sublocality') || types.includes('sublocality_level_1')) {
              address.suburb = component.long_name;
            } else if (types.includes('locality')) {
              address.city = component.long_name;
            } else if (types.includes('administrative_area_level_2')) {
              address.county = component.long_name;
            } else if (types.includes('administrative_area_level_1')) {
              address.state = component.long_name;
            } else if (types.includes('postal_code') || types.includes('postal_code_prefix')) {
              // Handle both full postal codes and postal code prefixes
              // For Canadian addresses, format as "A1A 1A1" if it's a full postal code
              let postalCode = component.long_name;
              console.log("postalCode",postalCode)
              // Check if it's a Canadian postal code pattern (6 characters, alphanumeric)
              const cleanPostal = postalCode.replace(/\s/g, '').toUpperCase();
              if (cleanPostal.length === 6 && /^[A-Z]\d[A-Z]\d[A-Z]\d$/.test(cleanPostal)) {
                // Format as A1A 1A1
                postalCode = `${cleanPostal.slice(0, 3)} ${cleanPostal.slice(3)}`;
              }

              address.postcode = postalCode;
            } else if (types.includes('country')) {
              address.country = component.long_name;
              address.country_code = component.short_name;
            }
          });

          // If we didn't get a postal code from address components, try multiple fallback methods
          if (!address.postcode) {
            const canadianPostalRegex = /([A-Z]\d[A-Z]\s?\d[A-Z]\d)/i;

            // Try formatted_address first
            if (result.formatted_address) {
              const match = result.formatted_address.match(canadianPostalRegex);
              if (match) {
                const cleanPostal = match[1].replace(/\s/g, '').toUpperCase();
                address.postcode = `${cleanPostal.slice(0, 3)} ${cleanPostal.slice(3)}`;
              }
            }

            // If still no postal code, try the original prediction description
            if (!address.postcode && prediction.description) {
              const match = prediction.description.match(canadianPostalRegex);
              if (match) {
                const cleanPostal = match[1].replace(/\s/g, '').toUpperCase();
                address.postcode = `${cleanPostal.slice(0, 3)} ${cleanPostal.slice(3)}`;
              }
            }
          }

          return {
            place_id: prediction.place_id,
            display_name: prediction.description,
            display_place: prediction.structured_formatting?.main_text || prediction.description,
            display_address: prediction.structured_formatting?.secondary_text || '',
            lat: location?.lat?.toString() || null,
            lon: location?.lng?.toString() || null,
            address: address
          };
        } catch (error) {
          console.error(`Error fetching details for place ${prediction.place_id}:`, error);
          return {
            place_id: prediction.place_id,
            display_name: prediction.description,
            display_place: prediction.structured_formatting?.main_text || prediction.description,
            display_address: prediction.structured_formatting?.secondary_text || '',
            lat: null,
            lon: null,
            address: {}
          };
        }
      })
    );

    return NextResponse.json(transformedData);
  } catch (error) {
    console.error('Address autocomplete error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}