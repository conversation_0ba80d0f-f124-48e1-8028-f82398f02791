import { NextResponse } from 'next/server';
import connectDB from '@/lib/db'; // Assuming you have a DB connection utility
import BugReport from '@/models/BugReport';
import * as z from 'zod';
import { getServerSession } from "next-auth/next"; // Import getServerSession
import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Import your authOptions

// Reuse the schema from the frontend for validation
const reportBugSchema = z.object({
  title: z.string().min(5).max(100),
  description: z.string().min(10).max(1000),
});

export async function POST(request: Request) {
  try {
    await connectDB();

    // Get server-side session
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id; // Extract userId if session exists

    const body = await request.json();

    // Validate input data
    const validation = reportBugSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { message: 'Invalid input data', errors: validation.error.errors },
        { status: 400 }
      );
    }

    const { title, description } = validation.data;

    // Create new bug report
    const newBugReportData: { title: string; description: string; userId?: string } = {
      title,
      description,
    };

    if (userId) {
      newBugReportData.userId = userId; // Add userId if available
    }

    const newBugReport = new BugReport(newBugReportData);

    await newBugReport.save();

    console.log('Bug report saved:', newBugReport._id);

    return NextResponse.json(
      { message: 'Bug report submitted successfully', bugReportId: newBugReport._id },
      { status: 201 } // 201 Created
    );
  } catch (error) {
    console.error('Error submitting bug report:', error);
    // Handle potential Mongoose validation errors specifically
    if (error instanceof Error && error.name === 'ValidationError') {
         return NextResponse.json(
            { message: 'Validation failed', errors: (error as any).errors },
            { status: 400 }
         );
    }
    return NextResponse.json(
      { message: 'Internal Server Error submitting bug report' },
      { status: 500 }
    );
  }
}

// Optional: Add a GET handler if you need to list bug reports later
// export async function GET(request: Request) { ... } 