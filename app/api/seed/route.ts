import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Permission from '@/models/Permission';
import Role from '@/models/Role';
import { 
  USER_PERMISSIONS, 
  ROLE_PERMISSIONS, 
  BRANCH_PERMISSIONS,
} from '@/types/permission-codes';

export async function GET() {
  try {
    await dbConnect();

    // Create all permissions
    const permissions = [
      // User permissions
      { 
        name: 'View Users',
        code: USER_PERMISSIONS.VIEW_USERS,
        description: 'Can view users'
      },
      { 
        name: 'Create Users',
        code: USER_PERMISSIONS.CREATE_USERS,
        description: 'Can create users'
      },
      { 
        name: 'Edit Users',
        code: USER_PERMISSIONS.EDIT_USERS,
        description: 'Can edit users'
      },
      { 
        name: 'Delete Users',
        code: USER_PERMISSIONS.DELETE_USERS,
        description: 'Can delete users'
      },
      { 
        name: 'Manage User Roles',
        code: USER_PERMISSIONS.MANAGE_USER_ROLES,
        description: 'Can manage user roles'
      },

      // Role permissions
      { 
        name: 'View Roles',
        code: ROLE_PERMISSIONS.VIEW_ROLES,
        description: 'Can view roles'
      },
      { 
        name: 'Create Roles',
        code: ROLE_PERMISSIONS.CREATE_ROLES,
        description: 'Can create roles'
      },
      { 
        name: 'Edit Roles',
        code: ROLE_PERMISSIONS.EDIT_ROLES,
        description: 'Can edit roles'
      },
      { 
        name: 'Delete Roles',
        code: ROLE_PERMISSIONS.DELETE_ROLES,
        description: 'Can delete roles'
      },
      { 
        name: 'Manage Permissions',
        code: ROLE_PERMISSIONS.MANAGE_PERMISSIONS,
        description: 'Can manage permissions'
      },

      // Branch permissions
      { 
        name: 'View Branches',
        code: BRANCH_PERMISSIONS.VIEW_BRANCHES,
        description: 'Can view branches'
      },
      { 
        name: 'Create Branches',
        code: BRANCH_PERMISSIONS.CREATE_BRANCHES,
        description: 'Can create branches'
      },
      { 
        name: 'Edit Branches',
        code: BRANCH_PERMISSIONS.EDIT_BRANCHES,
        description: 'Can edit branches'
      },
      { 
        name: 'Delete Branches',
        code: BRANCH_PERMISSIONS.DELETE_BRANCHES,
        description: 'Can delete branches'
      },
      { 
        name: 'Manage Branches',
        code: BRANCH_PERMISSIONS.MANAGE_BRANCHES,
        description: 'Can manage branches'
      }
    ];

    // Create permissions in database
    await Permission.insertMany(permissions, { ordered: false });

    // Get all permission IDs
    const allPermissions = await Permission.find().lean();
    const permissionIds = allPermissions.map(p => p._id);

    // Create admin role with all permissions
    await Role.create({
      name: 'Admin',
      permissions: permissionIds,
      description: 'Administrator with full access'
    });

    return NextResponse.json({ message: 'Seed completed successfully' });
  } catch (error) {
    console.error('Seed error:', error);
    return NextResponse.json({ error: 'Failed to seed database' }, { status: 500 });
  }
} 