import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

// Create or get the model (same schema as in the main route)
const overbookingSchema = new mongoose.Schema(
  {
    branch_id: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'Branch',
    },
    date: {
      type: String,
      required: true,
    },
    opened_hours: {
      type: Map,
      of: Number,
      default: {},
    },
    created_by: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

const Overbooking = mongoose.models.Overbooking || mongoose.model('Overbooking', overbookingSchema);

// PUT handler for updating overbooking
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    const { id } = params;
    const body = await request.json();

    if (!mongoose.isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid overbooking ID' },
        { status: 400 }
      );
    }

    // Find the overbooking first to ensure it exists
    const existingOverbooking = await Overbooking.findById(id);
    if (!existingOverbooking) {
      return NextResponse.json(
        { error: 'Overbooking not found' },
        { status: 404 }
      );
    }

    // Update only allowed fields
    const updatedOverbooking = await Overbooking.findByIdAndUpdate(
      id,
      {
        $set: {
          opened_hours: body.opened_hours
        }
      },
      { new: true }
    );

    return NextResponse.json(updatedOverbooking);
  } catch (error: any) {
    console.error('Error updating overbooking:', error);
    return NextResponse.json(
      { error: error.message || 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// DELETE handler for removing overbooking
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    const { id } = params;

    if (!mongoose.isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid overbooking ID' },
        { status: 400 }
      );
    }

    const overbooking = await Overbooking.findByIdAndDelete(id);
    if (!overbooking) {
      return NextResponse.json(
        { error: 'Overbooking not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Overbooking deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting overbooking:', error);
    return NextResponse.json(
      { error: error.message || 'Internal Server Error' },
      { status: 500 }
    );
  }
}