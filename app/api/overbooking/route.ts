import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

// Define the Overbooking schema
const overbookingSchema = new mongoose.Schema(
  {
    branch_id: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'Branch',
    },
    date: {
      type: String,
      required: true,
    },
    opened_hours: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
      validate: {
        validator: function(v: any) {
          return Object.values(v).every(val => typeof val === 'number');
        },
        message: 'opened_hours values must be numbers'
      }
    },
    created_by: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'User',
    },
  },
  {
    timestamps: true,
  }
);

// Create or get the model
const Overbooking = mongoose.models.Overbooking || mongoose.model('Overbooking', overbookingSchema);

// GET handler for fetching overbookings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    const { searchParams } = new URL(request.url);
    const branchId = searchParams.get('branch_id');
    const startDate = searchParams.get('date');
    const endDate = searchParams.get('endDate');

    if (!branchId || !startDate || !endDate) {
      return NextResponse.json(
        { error: 'Branch ID, start date, and end date are required' },
        { status: 400 }
      );
    }

    // Validate branch ID format
    if (!mongoose.isValidObjectId(branchId)) {
      return NextResponse.json(
        { error: 'Invalid branch ID format' },
        { status: 400 }
      );
    }

    // Validate date format
    const dateFormat = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateFormat.test(startDate) || !dateFormat.test(endDate)) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD' },
        { status: 400 }
      );
    }

    const overbookings = await Overbooking.find({
      branch_id: branchId,
      date: {
        $gte: startDate,
        $lte: endDate
      }
    }).sort({ date: 1 });

    return NextResponse.json(overbookings);
  } catch (error: any) {
    console.error('Error fetching overbookings:', error);
    return NextResponse.json(
      { error: error.message || 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// POST handler for creating new overbooking
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    const body = await request.json();

    if (!body.branch_id || !body.date) {
      return NextResponse.json(
        { error: 'Branch ID and date are required' },
        { status: 400 }
      );
    }

    // Validate branch ID format
    if (!mongoose.isValidObjectId(body.branch_id)) {
      return NextResponse.json(
        { error: 'Invalid branch ID format' },
        { status: 400 }
      );
    }

    // Validate date format
    const dateFormat = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateFormat.test(body.date)) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD' },
        { status: 400 }
      );
    }

    // Check for existing overbooking
    const existingOverbooking = await Overbooking.findOne({
      branch_id: body.branch_id,
      date: body.date
    });

    if (existingOverbooking) {
      return NextResponse.json(
        { error: 'Overbooking already exists for this date' },
        { status: 400 }
      );
    }

    const overbooking = await Overbooking.create({
      ...body,
      created_by: session.user.id,
    });

    return NextResponse.json(overbooking);
  } catch (error: any) {
    console.error('Error creating overbooking:', error);
    return NextResponse.json(
      { error: error.message || 'Internal Server Error' },
      { status: 500 }
    );
  }
}
