import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { BranchStatsAggregationService } from '@/lib/services/branch-stats-aggregation';
import { getBranchStatsEmailTemplate, processTemplateVariables, getDefaultTemplateVariables, getBranchStatsEmailSubject } from '@/lib/utils/branch-stats-email-templates';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import { getBrevoSender } from '@/lib/brevo/brevoUtils';

// Rate limiting map
const rateLimiter = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(userId: string, endpoint: string): boolean {
  const key = `${userId}:${endpoint}`;
  const now = Date.now();
  const windowMs = 300000; // 5 minutes for email sending
  const maxRequests = 5; // Very low limit for email sending
  
  if (!rateLimiter.has(key)) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  const limit = rateLimiter.get(key)!;
  if (now > limit.resetTime) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (limit.count >= maxRequests) {
    return false;
  }
  
  limit.count++;
  return true;
}

export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateAdminDashboardRequest(request);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'branch-stats-test-email')) {
      return NextResponse.json({ error: 'Rate limit exceeded. Please wait before sending another test email.' }, { status: 429 });
    }

    const body = await request.json();
    const { email, branchIds, targetDate, selectedStats, sendingHour } = body;

    // Validate email
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return NextResponse.json(
        { error: 'Valid email address is required' },
        { status: 400 }
      );
    }

    // Validate branch IDs
    if (!branchIds || !Array.isArray(branchIds) || branchIds.length === 0) {
      return NextResponse.json(
        { error: 'At least one branch ID is required' },
        { status: 400 }
      );
    }

    // Validate branch IDs format
    const invalidBranchIds = branchIds.filter(id => typeof id !== 'string' || id.length !== 24);
    if (invalidBranchIds.length > 0) {
      return NextResponse.json(
        { error: 'All branch IDs must be valid 24-character strings' },
        { status: 400 }
      );
    }

    // Validate sending hour if provided
    const hourToUse = sendingHour !== undefined ? sendingHour : 10;
    if (typeof hourToUse !== 'number' || hourToUse < 0 || hourToUse > 23) {
      return NextResponse.json(
        { error: 'sendingHour must be a number between 0 and 23' },
        { status: 400 }
      );
    }

    // Calculate target date based on sending hour if not provided
    const actualTargetDate = targetDate || 
      BranchStatsAggregationService.calculateTargetDate(hourToUse);

    // Validate date format if provided
    if (targetDate && !/^\d{4}-\d{2}-\d{2}$/.test(targetDate)) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD' },
        { status: 400 }
      );
    }

    // Validate selectedStats if provided
    const validStats = ['reservationCount', 'adultCount', 'childCount', 'allergies', 'serviceTypes'];
    const statsToUse = selectedStats || validStats;
    
    if (!Array.isArray(statsToUse)) {
      return NextResponse.json(
        { error: 'selectedStats must be an array' },
        { status: 400 }
      );
    }

    const invalidStats = statsToUse.filter(stat => !validStats.includes(stat));
    if (invalidStats.length > 0) {
      return NextResponse.json(
        { error: `Invalid stats: ${invalidStats.join(', ')}. Valid options: ${validStats.join(', ')}` },
        { status: 400 }
      );
    }

    // Aggregate branch stats
    const aggregationService = new BranchStatsAggregationService();
    const branchStats = await aggregationService.aggregateBranchStats({
      branchIds,
      targetDate: actualTargetDate,
      selectedStats: statsToUse
    });

    // Determine date logic
    const dateLogic: 'current' | 'next' = hourToUse < 12 ? 'current' : 'next';

    // Prepare email data
    const emailData = {
      targetDate: actualTargetDate,
      branchStats,
      sendingTime: `${hourToUse.toString().padStart(2, '0')}:00`,
      dateLogic,
      selectedStats: statsToUse,
      exportUrls: {
        csv: '#csv-export-placeholder',
        pdf: '#pdf-export-placeholder'
      }
    };

    // Generate email template
    const template = getBranchStatsEmailTemplate(emailData);
    const templateVariables = getDefaultTemplateVariables(emailData);
    const processedTemplate = processTemplateVariables(template, templateVariables);
    const subject = `[TEST] ${getBranchStatsEmailSubject(emailData)}`;

    // Send email via Brevo
    const brevoSender = getBrevoSender();
    
    try {
      const emailResult = await sendBrevoTemplatedEmail({
        to: [{ email }],
        subject,
        content: processedTemplate,
        sender: {
          email: brevoSender.email,
          name: 'AMQ Partners - Test Branch Stats'
        }
      });

      if (!emailResult.success) {
        throw new Error(emailResult.error || 'Email sending failed');
      }

      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully',
        details: {
          recipient: email,
          subject,
          branchCount: branchStats.length,
          totalReservations: branchStats.reduce((sum, bs) => sum + bs.summary.totalReservations, 0),
          messageId: emailResult.messageId,
          targetDate: actualTargetDate,
          dateLogic
        }
      });

    } catch (emailError) {
      console.error('Error sending test branch stats email:', emailError);
      return NextResponse.json(
        { 
          success: false,
          error: 'Failed to send test email',
          details: emailError instanceof Error ? emailError.message : 'Unknown email error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in branch stats test email endpoint:', error);
    return NextResponse.json(
      { error: 'Failed to send test email' },
      { status: 500 }
    );
  }
}
