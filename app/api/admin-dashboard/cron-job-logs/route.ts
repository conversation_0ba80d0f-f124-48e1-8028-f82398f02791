import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { CronScheduler } from '@/lib/services/cron-scheduler';
import { getUserRoles } from '@/app/api/utils/server-permission-utils';
import { isSuperAdmin } from '@/lib/utils/role-utils';

export async function GET(request: NextRequest) {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Debug endpoints only available in development' },
        { status: 403 }
      );
    }

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user roles and check if user is SuperAdmin
    if (!session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }

    const userIsSuperAdmin = isSuperAdmin(session.user.roles);

    if (!userIsSuperAdmin) {
      return NextResponse.json(
        { error: 'SuperAdmin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const jobName = searchParams.get('jobName');
    const limit = parseInt(searchParams.get('limit') || '10');

    if (!jobName) {
      return NextResponse.json(
        { error: 'Job name is required' },
        { status: 400 }
      );
    }

    const logs = CronScheduler.getCronJobLogs(jobName, limit);

    return NextResponse.json({
      jobName,
      logs,
      count: logs.length
    });

  } catch (error) {
    console.error('Cron job logs endpoint error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
