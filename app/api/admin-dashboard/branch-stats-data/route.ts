import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { BranchStatsAggregationService } from '@/lib/services/branch-stats-aggregation';

// Rate limiting map
const rateLimiter = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(userId: string, endpoint: string): boolean {
  const key = `${userId}:${endpoint}`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 10; // Lower limit for data-intensive endpoint
  
  if (!rateLimiter.has(key)) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  const limit = rateLimiter.get(key)!;
  if (now > limit.resetTime) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (limit.count >= maxRequests) {
    return false;
  }
  
  limit.count++;
  return true;
}

export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateAdminDashboardRequest(request);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'branch-stats-data')) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    const body = await request.json();
    const { branchIds, targetDate, selectedStats } = body;

    // Validate input
    if (!branchIds || !Array.isArray(branchIds) || branchIds.length === 0) {
      return NextResponse.json(
        { error: 'Branch IDs are required and must be a non-empty array' },
        { status: 400 }
      );
    }

    if (!targetDate) {
      return NextResponse.json(
        { error: 'Target date is required' },
        { status: 400 }
      );
    }

    // Validate date format (YYYY-MM-DD)
    if (!/^\d{4}-\d{2}-\d{2}$/.test(targetDate)) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD' },
        { status: 400 }
      );
    }

    // Validate date is not too far in the past or future
    const targetDateObj = new Date(targetDate);
    const now = new Date();
    const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
    const oneYearFromNow = new Date(now.getFullYear() + 1, now.getMonth(), now.getDate());

    if (targetDateObj < oneYearAgo || targetDateObj > oneYearFromNow) {
      return NextResponse.json(
        { error: 'Target date must be within one year of current date' },
        { status: 400 }
      );
    }

    // Validate selectedStats if provided
    const validStats = ['reservationCount', 'adultCount', 'childCount', 'allergies', 'serviceTypes'];
    const statsToUse = selectedStats || validStats;
    
    if (!Array.isArray(statsToUse)) {
      return NextResponse.json(
        { error: 'selectedStats must be an array' },
        { status: 400 }
      );
    }

    const invalidStats = statsToUse.filter(stat => !validStats.includes(stat));
    if (invalidStats.length > 0) {
      return NextResponse.json(
        { error: `Invalid stats: ${invalidStats.join(', ')}. Valid options: ${validStats.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate branch IDs format
    const invalidBranchIds = branchIds.filter(id => typeof id !== 'string' || id.length !== 24);
    if (invalidBranchIds.length > 0) {
      return NextResponse.json(
        { error: 'All branch IDs must be valid 24-character strings' },
        { status: 400 }
      );
    }

    const aggregationService = new BranchStatsAggregationService();
    const startTime = Date.now();
    
    const branchStats = await aggregationService.aggregateBranchStats({
      branchIds,
      targetDate,
      selectedStats: statsToUse
    });

    const executionTime = Date.now() - startTime;

    return NextResponse.json({
      success: true,
      data: branchStats,
      metadata: {
        targetDate,
        branchCount: branchStats.length,
        selectedStats: statsToUse,
        executionTime,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error aggregating branch stats:', error);
    return NextResponse.json(
      { error: 'Failed to aggregate branch statistics' },
      { status: 500 }
    );
  }
}
