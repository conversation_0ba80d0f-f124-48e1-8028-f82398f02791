import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { StatsEmailService } from '@/lib/services/stats-email-service';
import { getUserRoles } from '@/app/api/utils/server-permission-utils';
import { isSuperAdmin } from '@/lib/utils/role-utils';

export async function GET(request: NextRequest) {
  try {
    // Debug information
    const debugInfo = {
      nodeEnv: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    };

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        {
          error: 'Debug endpoints only available in development',
          debug: debugInfo
        },
        { status: 403 }
      );
    }

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        {
          error: 'Authentication required',
          debug: { ...debugInfo, hasSession: false }
        },
        { status: 401 }
      );
    }

    // Get user roles and check if user is SuperAdmin
    if (!session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }

    const userIsSuperAdmin = isSuperAdmin(session.user.roles);

    if (!userIsSuperAdmin) {
      return NextResponse.json(
        {
          error: 'SuperAdmin access required',
          debug: {
            ...debugInfo,
            hasSession: true,
            userRoles: session.user.roles?.map((r: any) => r.name || r._id),
            userId: session.user.id,
            isSuperAdmin: userIsSuperAdmin
          }
        },
        { status: 403 }
      );
    }

    const statsEmailService = StatsEmailService.getInstance();
    const healthResult = await statsEmailService.healthCheck();

    return NextResponse.json(healthResult);

  } catch (error) {
    console.error('Health check endpoint error:', error);
    return NextResponse.json(
      { 
        status: 'unhealthy',
        checks: {},
        errors: [error instanceof Error ? error.message : 'Unknown error']
      },
      { status: 500 }
    );
  }
}
