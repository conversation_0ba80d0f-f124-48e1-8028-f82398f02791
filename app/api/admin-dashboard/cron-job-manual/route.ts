import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { CronScheduler } from '@/lib/services/cron-scheduler';
import { getUserRoles } from '@/app/api/utils/server-permission-utils';
import { isSuperAdmin } from '@/lib/utils/role-utils';

export async function POST(request: NextRequest) {
  try {
    // Debug information
    const debugInfo = {
      nodeEnv: process.env.NODE_ENV,
      timestamp: new Date().toISOString()
    };

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        {
          error: 'Debug endpoints only available in development',
          debug: debugInfo
        },
        { status: 403 }
      );
    }

    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        {
          error: 'Authentication required',
          debug: { ...debugInfo, hasSession: false }
        },
        { status: 401 }
      );
    }

    // Get user roles and check if user is SuperAdmin
    if (!session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }

    const userIsSuperAdmin = isSuperAdmin(session.user.roles);

    if (!userIsSuperAdmin) {
      return NextResponse.json(
        {
          error: 'SuperAdmin access required',
          debug: {
            ...debugInfo,
            hasSession: true,
            userRoles: session.user.roles?.map((r: any) => r.name || r._id),
            userId: session.user.id,
            isSuperAdmin: userIsSuperAdmin
          }
        },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { jobName, date } = body;

    if (!jobName) {
      return NextResponse.json(
        { error: 'Job name is required' },
        { status: 400 }
      );
    }

    let result;
    const startTime = Date.now();

    try {
      console.log(`Executing manual job: ${jobName} with date: ${date}`);

      switch (jobName) {
        case 'daily-stats-email':
          result = await CronScheduler.executeDailyStatsEmailManually(date);
          break;
        default:
          return NextResponse.json(
            { error: `Unknown job name: ${jobName}` },
            { status: 400 }
          );
      }

      const executionTime = Date.now() - startTime;

      console.log(`Manual job execution completed:`, {
        jobName,
        executionTime,
        success: true,
        result
      });

      return NextResponse.json({
        success: true,
        jobName,
        executionTime,
        data: result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      console.error(`Manual job execution failed for ${jobName}:`, error);
      
      return NextResponse.json({
        success: false,
        jobName,
        executionTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Manual job execution endpoint error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
