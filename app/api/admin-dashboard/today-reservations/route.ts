import { NextRequest } from 'next/server';
import Reservation from '@/models/Reservation';
import { connectToDatabase } from '@/lib/mongodb';
import Branch from '@/models/Branch';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { fromZonedTime, toZonedTime } from 'date-fns-tz';
import dbConnect from '@/lib/db';

// Quebec timezone identifier (consistent with the rest of the application)
const QUEBEC_TIMEZONE = 'America/Toronto';

export async function GET(req: NextRequest) {
await dbConnect();

  // Authenticate request (session or token)
  const auth = await authenticateAdminDashboardRequest(req);
  if (!auth.isAuthenticated) {
    return new Response(JSON.stringify({ error: auth.error }), {
      status: auth.error === 'Forbidden' ? 403 : 401
    });
  }

  const { searchParams } = new URL(req.url);
  const branchId = searchParams.get('branchId');
  const branchIds = searchParams.get('branchIds')?.split(',').filter(Boolean);
  const allBranches = searchParams.get('allBranches') === 'true' || branchId === 'all';

  const { userId, isSuperAdminUser, isBranchesAdminUser } = auth;

  // If branch admin and all branches selected, get accessible branches
  let accessibleBranchIds: string[] = [];
  if (allBranches && !isSuperAdminUser && isBranchesAdminUser && userId) {
    const branchesResponse = await Branch.find(
      { responsible: userId, deletedAt: null },
      '_id'
    ).lean();
    accessibleBranchIds = branchesResponse.map(b => (b as any)._id.toString());

    if (accessibleBranchIds.length === 0) {
      return new Response(
        JSON.stringify({
          todayReservations: 0,
        }),
        { status: 200 }
      );
    }
  }

  // Build match condition based on role and branch selection
  let match: any = { $or: [{ deletedAt: { $exists: false } }, { deletedAt: null }] };

  if (branchIds && branchIds.length > 0) {
    // Multiple specific branches
    match['preferences.branchId'] = { $in: branchIds };
  } else if (branchId && branchId !== 'all') {
    // Single branch (backward compatibility)
    match['preferences.branchId'] = branchId;
  } else if (allBranches && !isSuperAdminUser && accessibleBranchIds.length > 0) {
    // Branch admin with "all" selected - restrict to accessible branches
    match['preferences.branchId'] = { $in: accessibleBranchIds };
  }

  try {
    // Get today's date in Quebec timezone (America/Toronto)
    // This approach is consistent with the weekly commissions date handling
    const quebecNow = toZonedTime(new Date(), QUEBEC_TIMEZONE);
    const todayStr = quebecNow.toISOString().split('T')[0]; // Format: YYYY-MM-DD

    // Create start of today in Quebec timezone, then convert to UTC
    const startOfTodayQuebec = `${todayStr} 00:00:00.000`;
    const startOfTodayUTC = fromZonedTime(startOfTodayQuebec, QUEBEC_TIMEZONE);
    
    // Create end of today in Quebec timezone, then convert to UTC
    const endOfTodayQuebec = `${todayStr} 23:59:59.999`;
    const endOfTodayUTC = fromZonedTime(endOfTodayQuebec, QUEBEC_TIMEZONE);

    // Add date filter for today's created reservations using UTC range
    const todayMatch = {
      ...match,
      createdAt: {
        $gte: startOfTodayUTC,
        $lte: endOfTodayUTC
      }
    };

    // Count reservations created today
    const todayReservations = await Reservation.countDocuments(todayMatch);

    return new Response(JSON.stringify({
      todayReservations,
    }), { status: 200 });
  } catch (error) {
    console.error('Error fetching today reservations:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to fetch today reservations data' 
    }), { status: 500 });
  }
}