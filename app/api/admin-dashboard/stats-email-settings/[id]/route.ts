import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import StatsEmailSettings from '@/models/StatsEmailSettings';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import mongoose from 'mongoose';

// Rate limiting map
const rateLimiter = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(userId: string, endpoint: string): boolean {
  const key = `${userId}:${endpoint}`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 30;
  
  if (!rateLimiter.has(key)) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  const limit = rateLimiter.get(key)!;
  if (now > limit.resetTime) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (limit.count >= maxRequests) {
    return false;
  }
  
  limit.count++;
  return true;
}

export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'stats-email-settings-put')) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    await dbConnect();

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid setting ID' }, { status: 400 });
    }

    const body = await req.json();
    const {
      email,
      selectedStats,
      isActive,
      weeklyEnabled,
      weeklySelectedStats,
      // New branch stats fields
      regularStatsEnabled,
      branchStatsEnabled,
      selectedBranches,
      branchStatsSelectedStats
    } = body;

    const setting = await StatsEmailSettings.findById(id);
    if (!setting) {
      return NextResponse.json({ error: 'Setting not found' }, { status: 404 });
    }

    // Update fields
    if (email) {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return NextResponse.json(
          { error: 'Invalid email format' },
          { status: 400 }
        );
      }
      setting.email = email;
    }
    
    if (selectedStats) {
      setting.selectedStats = selectedStats;
    }

    if (typeof isActive === 'boolean') {
      setting.isActive = isActive;
    }

    if (typeof weeklyEnabled === 'boolean') {
      setting.weeklyEnabled = weeklyEnabled;
    }

    if (weeklySelectedStats) {
      setting.weeklySelectedStats = weeklySelectedStats;
    }

    // Update branch stats fields
    if (typeof regularStatsEnabled === 'boolean') {
      setting.regularStatsEnabled = regularStatsEnabled;
    }

    if (typeof branchStatsEnabled === 'boolean') {
      setting.branchStatsEnabled = branchStatsEnabled;
    }

    if (selectedBranches !== undefined) {
      // Validate selectedBranches is an array
      if (!Array.isArray(selectedBranches)) {
        return NextResponse.json(
          { error: 'selectedBranches must be an array' },
          { status: 400 }
        );
      }
      setting.selectedBranches = selectedBranches;
    }

    if (branchStatsSelectedStats) {
      // Validate branchStatsSelectedStats structure
      const validBranchStats = ['reservationCount', 'adultCount', 'childCount', 'allergies', 'serviceTypes'];
      const providedStats = Object.keys(branchStatsSelectedStats);
      const invalidStats = providedStats.filter(stat => !validBranchStats.includes(stat));

      if (invalidStats.length > 0) {
        return NextResponse.json(
          { error: `Invalid branch stats: ${invalidStats.join(', ')}` },
          { status: 400 }
        );
      }

      setting.branchStatsSelectedStats = branchStatsSelectedStats;
    }

    await setting.save();

    return NextResponse.json({
      success: true,
      setting,
      message: 'Email setting updated successfully'
    });
  } catch (error: any) {
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 409 }
      );
    }
    
    console.error('Error updating stats email setting:', error);
    return NextResponse.json(
      { error: 'Failed to update setting' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;

    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'stats-email-settings-delete')) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    await dbConnect();

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid setting ID' }, { status: 400 });
    }

    const setting = await StatsEmailSettings.findById(id);
    if (!setting) {
      return NextResponse.json({ error: 'Setting not found' }, { status: 404 });
    }

    await StatsEmailSettings.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: 'Email setting deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting stats email setting:', error);
    return NextResponse.json(
      { error: 'Failed to delete setting' },
      { status: 500 }
    );
  }
}
