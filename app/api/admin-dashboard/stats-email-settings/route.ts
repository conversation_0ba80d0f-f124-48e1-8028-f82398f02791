import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import StatsEmailSettings from '@/models/StatsEmailSettings';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';

// Rate limiting map
const rateLimiter = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(userId: string, endpoint: string): boolean {
  const key = `${userId}:${endpoint}`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 30;
  
  if (!rateLimiter.has(key)) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  const limit = rateLimiter.get(key)!;
  if (now > limit.resetTime) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (limit.count >= maxRequests) {
    return false;
  }
  
  limit.count++;
  return true;
}

export async function GET(req: NextRequest) {
  try {
    // Authentication check
    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'stats-email-settings-get')) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    await dbConnect();

    const settings = await StatsEmailSettings.getActiveSettings();
    
    return NextResponse.json({
      settings,
      total: settings.length,
      debug: process.env.NODE_ENV === 'development' ? {
        userId: auth.userId,
        timestamp: new Date().toISOString()
      } : undefined
    });
  } catch (error) {
    console.error('Error fetching stats email settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch settings' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'stats-email-settings-post')) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    await dbConnect();

    const body = await req.json();
    const {
      email,
      selectedStats,
      weeklyEnabled,
      weeklySelectedStats,
      // New branch stats fields
      branchStatsEnabled,
      selectedBranches,
      branchStatsSelectedStats,
      regularStatsEnabled = true // Default to true for backward compatibility
    } = body;

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate branch selection if branch stats enabled
    if (branchStatsEnabled) {
      if (!selectedBranches || !Array.isArray(selectedBranches) || selectedBranches.length === 0) {
        return NextResponse.json(
          { error: 'At least one branch must be selected when branch stats are enabled' },
          { status: 400 }
        );
      }

      // Validate that branches exist
      const Branch = (await import('@/models/Branch')).default;
      const existingBranches = await Branch.find({
        _id: { $in: selectedBranches },
        deletedAt: null
      });

      if (existingBranches.length !== selectedBranches.length) {
        return NextResponse.json(
          { error: 'One or more selected branches do not exist' },
          { status: 400 }
        );
      }
    }

    // Create new setting
    const setting = new StatsEmailSettings({
      email,
      selectedStats: selectedStats || {},
      weeklyEnabled: weeklyEnabled || false,
      weeklySelectedStats: weeklySelectedStats || {},
      branchStatsEnabled: branchStatsEnabled || false,
      selectedBranches: branchStatsEnabled ? selectedBranches : [],
      branchStatsSelectedStats: branchStatsEnabled ? branchStatsSelectedStats : {
        reservationCount: true,
        adultCount: true,
        childCount: true,
        allergies: true,
        serviceTypes: true
      },
      regularStatsEnabled,
      createdBy: auth.userId || null
    });

    await setting.save();

    return NextResponse.json({
      success: true,
      setting,
      message: 'Email setting created successfully'
    });
  } catch (error: any) {
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 409 }
      );
    }
    
    console.error('Error creating stats email setting:', error);
    return NextResponse.json(
      { error: 'Failed to create setting' },
      { status: 500 }
    );
  }
}
