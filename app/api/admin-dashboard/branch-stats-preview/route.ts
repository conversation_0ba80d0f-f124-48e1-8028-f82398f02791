import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { BranchStatsAggregationService } from '@/lib/services/branch-stats-aggregation';
import { getBranchStatsEmailTemplate, processTemplateVariables, getDefaultTemplateVariables, getBranchStatsEmailSubject } from '@/lib/utils/branch-stats-email-templates';

// Rate limiting map
const rateLimiter = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(userId: string, endpoint: string): boolean {
  const key = `${userId}:${endpoint}`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 15; // Reasonable limit for preview generation
  
  if (!rateLimiter.has(key)) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  const limit = rateLimiter.get(key)!;
  if (now > limit.resetTime) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (limit.count >= maxRequests) {
    return false;
  }
  
  limit.count++;
  return true;
}

export async function POST(request: NextRequest) {
  try {
    const auth = await authenticateAdminDashboardRequest(request);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'branch-stats-preview')) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    const body = await request.json();
    const { branchIds, targetDate, selectedStats, sendingHour } = body;

    // Validate input
    if (!branchIds || !Array.isArray(branchIds) || branchIds.length === 0) {
      return NextResponse.json(
        { error: 'Branch IDs are required and must be a non-empty array' },
        { status: 400 }
      );
    }

    // Validate branch IDs format
    const invalidBranchIds = branchIds.filter(id => typeof id !== 'string' || id.length !== 24);
    if (invalidBranchIds.length > 0) {
      return NextResponse.json(
        { error: 'All branch IDs must be valid 24-character strings' },
        { status: 400 }
      );
    }

    // Validate sending hour if provided
    const hourToUse = sendingHour !== undefined ? sendingHour : 10;
    if (typeof hourToUse !== 'number' || hourToUse < 0 || hourToUse > 23) {
      return NextResponse.json(
        { error: 'sendingHour must be a number between 0 and 23' },
        { status: 400 }
      );
    }

    // Calculate target date based on sending hour if not provided
    const actualTargetDate = targetDate || 
      BranchStatsAggregationService.calculateTargetDate(hourToUse);

    // Validate date format if provided
    if (targetDate && !/^\d{4}-\d{2}-\d{2}$/.test(targetDate)) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD' },
        { status: 400 }
      );
    }

    // Validate selectedStats if provided
    const validStats = ['reservationCount', 'adultCount', 'childCount', 'allergies', 'serviceTypes'];
    const statsToUse = selectedStats || validStats;
    
    if (!Array.isArray(statsToUse)) {
      return NextResponse.json(
        { error: 'selectedStats must be an array' },
        { status: 400 }
      );
    }

    const invalidStats = statsToUse.filter(stat => !validStats.includes(stat));
    if (invalidStats.length > 0) {
      return NextResponse.json(
        { error: `Invalid stats: ${invalidStats.join(', ')}. Valid options: ${validStats.join(', ')}` },
        { status: 400 }
      );
    }

    // Aggregate branch stats
    const aggregationService = new BranchStatsAggregationService();
    const startTime = Date.now();
    
    const branchStats = await aggregationService.aggregateBranchStats({
      branchIds,
      targetDate: actualTargetDate,
      selectedStats: statsToUse
    });

    const aggregationTime = Date.now() - startTime;

    // Determine date logic
    const dateLogic: 'current' | 'next' = hourToUse < 12 ? 'current' : 'next';

    // Prepare email data
    const emailData = {
      targetDate: actualTargetDate,
      branchStats,
      sendingTime: `${hourToUse.toString().padStart(2, '0')}:00`,
      dateLogic,
      selectedStats: statsToUse,
      exportUrls: {
        csv: '#csv-export-placeholder',
        pdf: '#pdf-export-placeholder'
      }
    };

    // Generate email template
    const templateStartTime = Date.now();
    const template = getBranchStatsEmailTemplate(emailData);
    const templateVariables = getDefaultTemplateVariables(emailData);
    const processedTemplate = processTemplateVariables(template, templateVariables);
    const subject = getBranchStatsEmailSubject(emailData);
    const templateTime = Date.now() - templateStartTime;

    return NextResponse.json({
      success: true,
      preview: {
        subject,
        html: processedTemplate,
        templateSize: processedTemplate.length
      },
      summary: {
        branchCount: branchStats.length,
        totalReservations: branchStats.reduce((sum, bs) => sum + bs.summary.totalReservations, 0),
        totalAttendees: branchStats.reduce((sum, bs) => sum + bs.summary.totalAttendees, 0),
        branches: branchStats.map(bs => ({
          branchId: bs.branchId,
          branchName: bs.branchName,
          totalReservations: bs.summary.totalReservations,
          totalAttendees: bs.summary.totalAttendees
        }))
      },
      metadata: {
        dateLogic,
        targetDate: actualTargetDate,
        sendingHour: hourToUse,
        selectedStats: statsToUse,
        executionTime: {
          aggregation: aggregationTime,
          template: templateTime,
          total: aggregationTime + templateTime
        },
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error generating branch stats preview:', error);
    return NextResponse.json(
      { error: 'Failed to generate preview' },
      { status: 500 }
    );
  }
}
