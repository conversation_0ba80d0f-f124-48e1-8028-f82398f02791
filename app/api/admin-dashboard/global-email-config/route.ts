import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { GlobalEmailConfig } from '@/models/StatsEmailSettings';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { CronScheduler } from '@/lib/services/cron-scheduler';
import mongoose from 'mongoose';

// Rate limiting map
const rateLimiter = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(userId: string, endpoint: string): boolean {
  const key = `${userId}:${endpoint}`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 20;
  
  if (!rateLimiter.has(key)) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  const limit = rateLimiter.get(key)!;
  if (now > limit.resetTime) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (limit.count >= maxRequests) {
    return false;
  }
  
  limit.count++;
  return true;
}

export async function GET(req: NextRequest) {
  try {
    // Authentication check
    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'global-email-config-get')) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    await dbConnect();

    const config = await GlobalEmailConfig.getGlobalConfig();
    
    return NextResponse.json({
      config,
      debug: process.env.NODE_ENV === 'development' ? {
        userId: auth.userId,
        timestamp: new Date().toISOString()
      } : undefined
    });
  } catch (error) {
    console.error('Error fetching global email config:', error);
    return NextResponse.json(
      { error: 'Failed to fetch configuration' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'global-email-config-put')) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    await dbConnect();

    const body = await req.json();
    const {
      sendingHour,
      sendingMinute,
      timezone,
      isEnabled,
      weeklyEnabled,
      weeklyDay,
      weeklyHour,
      weeklyMinute,
      weeklyTimezone,
      // New branch stats fields
      branchStatsEnabled,
      branchStatsHour,
      branchStatsMinute,
      branchStatsTimezone
    } = body;

    // Validate sendingHour if provided
    if (sendingHour !== undefined) {
      if (typeof sendingHour !== 'number' || sendingHour < 0 || sendingHour > 23) {
        return NextResponse.json(
          { error: 'sendingHour must be a number between 0 and 23' },
          { status: 400 }
        );
      }
    }

    // Validate sendingMinute if provided
    if (sendingMinute !== undefined) {
      if (typeof sendingMinute !== 'number' || sendingMinute < 0 || sendingMinute > 59) {
        return NextResponse.json(
          { error: 'sendingMinute must be a number between 0 and 59' },
          { status: 400 }
        );
      }
    }

    // Validate timezone if provided
    if (timezone !== undefined) {
      if (typeof timezone !== 'string' || timezone.trim().length === 0) {
        return NextResponse.json(
          { error: 'timezone must be a non-empty string' },
          { status: 400 }
        );
      }
    }

    // Validate isEnabled if provided
    if (isEnabled !== undefined && typeof isEnabled !== 'boolean') {
      return NextResponse.json(
        { error: 'isEnabled must be a boolean' },
        { status: 400 }
      );
    }

    // Validate weekly fields
    if (weeklyEnabled !== undefined && typeof weeklyEnabled !== 'boolean') {
      return NextResponse.json(
        { error: 'weeklyEnabled must be a boolean' },
        { status: 400 }
      );
    }

    if (weeklyDay !== undefined) {
      if (typeof weeklyDay !== 'number' || weeklyDay < 0 || weeklyDay > 6) {
        return NextResponse.json(
          { error: 'weeklyDay must be a number between 0 and 6' },
          { status: 400 }
        );
      }
    }

    if (weeklyHour !== undefined) {
      if (typeof weeklyHour !== 'number' || weeklyHour < 0 || weeklyHour > 23) {
        return NextResponse.json(
          { error: 'weeklyHour must be a number between 0 and 23' },
          { status: 400 }
        );
      }
    }

    if (weeklyMinute !== undefined) {
      if (typeof weeklyMinute !== 'number' || weeklyMinute < 0 || weeklyMinute > 59) {
        return NextResponse.json(
          { error: 'weeklyMinute must be a number between 0 and 59' },
          { status: 400 }
        );
      }
    }

    if (weeklyTimezone !== undefined) {
      if (typeof weeklyTimezone !== 'string' || weeklyTimezone.trim().length === 0) {
        return NextResponse.json(
          { error: 'weeklyTimezone must be a non-empty string' },
          { status: 400 }
        );
      }
    }

    // Validate branch stats fields
    if (branchStatsEnabled !== undefined && typeof branchStatsEnabled !== 'boolean') {
      return NextResponse.json(
        { error: 'branchStatsEnabled must be a boolean' },
        { status: 400 }
      );
    }

    if (branchStatsHour !== undefined) {
      if (typeof branchStatsHour !== 'number' || branchStatsHour < 0 || branchStatsHour > 23) {
        return NextResponse.json(
          { error: 'branchStatsHour must be a number between 0 and 23' },
          { status: 400 }
        );
      }
    }

    if (branchStatsMinute !== undefined) {
      if (typeof branchStatsMinute !== 'number' || ![0, 15, 30, 45].includes(branchStatsMinute)) {
        return NextResponse.json(
          { error: 'branchStatsMinute must be 0, 15, 30, or 45' },
          { status: 400 }
        );
      }
    }

    if (branchStatsTimezone !== undefined) {
      if (typeof branchStatsTimezone !== 'string' || branchStatsTimezone.trim().length === 0) {
        return NextResponse.json(
          { error: 'branchStatsTimezone must be a non-empty string' },
          { status: 400 }
        );
      }
    }

    // Get current config to check if sending time is changing
    const currentConfig = await GlobalEmailConfig.getGlobalConfig();
    const hourChanged = sendingHour !== undefined && sendingHour !== currentConfig.sendingHour;
    const minuteChanged = sendingMinute !== undefined && sendingMinute !== currentConfig.sendingMinute;
    const timeChanged = hourChanged || minuteChanged;

    // Check if weekly time is changing
    const weeklyHourChanged = weeklyHour !== undefined && weeklyHour !== currentConfig.weeklyHour;
    const weeklyMinuteChanged = weeklyMinute !== undefined && weeklyMinute !== currentConfig.weeklyMinute;
    const weeklyDayChanged = weeklyDay !== undefined && weeklyDay !== currentConfig.weeklyDay;
    const weeklyTimeChanged = weeklyHourChanged || weeklyMinuteChanged || weeklyDayChanged;

    // Check if branch stats time is changing
    const branchStatsHourChanged = branchStatsHour !== undefined && branchStatsHour !== currentConfig.branchStatsHour;
    const branchStatsMinuteChanged = branchStatsMinute !== undefined && branchStatsMinute !== currentConfig.branchStatsMinute;
    const branchStatsEnabledChanged = branchStatsEnabled !== undefined && branchStatsEnabled !== currentConfig.branchStatsEnabled;
    const branchStatsTimeChanged = branchStatsHourChanged || branchStatsMinuteChanged || branchStatsEnabledChanged;

    // Update configuration
    const updates: any = {};
    if (sendingHour !== undefined) updates.sendingHour = sendingHour;
    if (sendingMinute !== undefined) updates.sendingMinute = sendingMinute;
    if (timezone !== undefined) updates.timezone = timezone;
    if (isEnabled !== undefined) updates.isEnabled = isEnabled;
    if (weeklyEnabled !== undefined) updates.weeklyEnabled = weeklyEnabled;
    if (weeklyDay !== undefined) updates.weeklyDay = weeklyDay;
    if (weeklyHour !== undefined) updates.weeklyHour = weeklyHour;
    if (weeklyMinute !== undefined) updates.weeklyMinute = weeklyMinute;
    if (weeklyTimezone !== undefined) updates.weeklyTimezone = weeklyTimezone;
    if (branchStatsEnabled !== undefined) updates.branchStatsEnabled = branchStatsEnabled;
    if (branchStatsHour !== undefined) updates.branchStatsHour = branchStatsHour;
    if (branchStatsMinute !== undefined) updates.branchStatsMinute = branchStatsMinute;
    if (branchStatsTimezone !== undefined) updates.branchStatsTimezone = branchStatsTimezone;

    const updatedConfig = await GlobalEmailConfig.updateGlobalConfig(updates, auth.userId ? new mongoose.Types.ObjectId(auth.userId) : null);

    // If the sending time changed, restart the cron scheduler to pick up the new schedule
    let cronRestarted = false;
    if (timeChanged) {
      try {
        const oldTime = `${currentConfig.sendingHour.toString().padStart(2, '0')}:${currentConfig.sendingMinute.toString().padStart(2, '0')}`;
        const newHour = sendingHour !== undefined ? sendingHour : currentConfig.sendingHour;
        const newMinute = sendingMinute !== undefined ? sendingMinute : currentConfig.sendingMinute;
        const newTime = `${newHour.toString().padStart(2, '0')}:${newMinute.toString().padStart(2, '0')}`;
        console.log(`Daily sending time changed from ${oldTime} to ${newTime}, restarting daily stats email automation...`);
        CronScheduler.stopDailyStatsEmailAutomation();
        await CronScheduler.startDailyStatsEmailAutomation();
        console.log('Daily stats email automation restarted successfully');
        cronRestarted = true;
      } catch (cronError) {
        console.error('Error restarting daily cron scheduler:', cronError);
        // Don't fail the request if cron restart fails, but log it
      }
    }

    // If weekly time changed, restart weekly cron scheduler
    if (weeklyTimeChanged) {
      try {
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const oldWeeklyTime = `${dayNames[currentConfig.weeklyDay]} ${currentConfig.weeklyHour.toString().padStart(2, '0')}:${currentConfig.weeklyMinute.toString().padStart(2, '0')}`;
        const newWeeklyDay = weeklyDay !== undefined ? weeklyDay : currentConfig.weeklyDay;
        const newWeeklyHour = weeklyHour !== undefined ? weeklyHour : currentConfig.weeklyHour;
        const newWeeklyMinute = weeklyMinute !== undefined ? weeklyMinute : currentConfig.weeklyMinute;
        const newWeeklyTime = `${dayNames[newWeeklyDay]} ${newWeeklyHour.toString().padStart(2, '0')}:${newWeeklyMinute.toString().padStart(2, '0')}`;
        console.log(`Weekly sending time changed from ${oldWeeklyTime} to ${newWeeklyTime}, restarting weekly stats email automation...`);
        CronScheduler.stopWeeklyStatsEmailAutomation();
        await CronScheduler.startWeeklyStatsEmailAutomation();
        console.log('Weekly stats email automation restarted successfully');
        cronRestarted = true;
      } catch (cronError) {
        console.error('Error restarting weekly cron scheduler:', cronError);
        // Don't fail the request if cron restart fails, but log it
      }
    }

    // If branch stats time changed, restart branch stats cron scheduler
    if (branchStatsTimeChanged) {
      try {
        const oldBranchStatsTime = `${currentConfig.branchStatsHour.toString().padStart(2, '0')}:${currentConfig.branchStatsMinute.toString().padStart(2, '0')}`;
        const newBranchStatsHour = branchStatsHour !== undefined ? branchStatsHour : currentConfig.branchStatsHour;
        const newBranchStatsMinute = branchStatsMinute !== undefined ? branchStatsMinute : currentConfig.branchStatsMinute;
        const newBranchStatsTime = `${newBranchStatsHour.toString().padStart(2, '0')}:${newBranchStatsMinute.toString().padStart(2, '0')}`;
        console.log(`Branch stats sending time changed from ${oldBranchStatsTime} to ${newBranchStatsTime}, restarting branch stats email automation...`);
        await CronScheduler.restartBranchStatsEmailAutomation();
        console.log('Branch stats email automation restarted successfully');
        cronRestarted = true;
      } catch (cronError) {
        console.error('Error restarting branch stats cron scheduler:', cronError);
        // Don't fail the request if cron restart fails, but log it
      }
    }

    return NextResponse.json({
      success: true,
      config: updatedConfig,
      message: 'Global email configuration updated successfully',
      cronRestarted: cronRestarted
    });
  } catch (error: any) {
    console.error('Error updating global email config:', error);
    return NextResponse.json(
      { error: 'Failed to update configuration' },
      { status: 500 }
    );
  }
}
