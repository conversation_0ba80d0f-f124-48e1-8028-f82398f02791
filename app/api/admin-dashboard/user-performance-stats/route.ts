import { NextRequest } from 'next/server';
import Reservation from '@/models/Reservation';
import User from '@/models/User';
import { connectToDatabase } from '@/lib/mongodb';
import Branch from '@/models/Branch';
import { ObjectId } from 'mongodb';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { getSalesStatusCodes, getPresenceStatusCodes } from '@/lib/utils/reservation-status-utils';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
await dbConnect();

  // Authenticate request (session or token)
  const auth = await authenticateAdminDashboardRequest(req);
  if (!auth.isAuthenticated) {
    return new Response(JSON.stringify({ error: auth.error }), {
      status: auth.error === 'Forbidden' ? 403 : 401
    });
  }

  const { searchParams } = new URL(req.url);
  const branchId = searchParams.get('branchId');
  const branchIds = searchParams.get('branchIds')?.split(',').filter(Boolean);
  const allBranches = searchParams.get('allBranches') === 'true' || branchId === 'all';
  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');
  const page = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);

  const { userId, isSuperAdminUser, isBranchesAdminUser } = auth;

  if (!isSuperAdminUser && !isBranchesAdminUser) {
    return new Response(JSON.stringify({ error: 'Forbidden' }), { status: 403 });
  }

  if (!startDate || !endDate) {
    return new Response(JSON.stringify({ error: 'Missing date range' }), { status: 400 });
  }

  // Convert branchId string to ObjectId if it exists
  let branchObjectId;
  let branchObjectIds: ObjectId[] = [];
  if (branchIds && branchIds.length > 0) {
    // Multiple specific branches
    branchObjectIds = branchIds.map(id => {
      try {
        return new ObjectId(id);
      } catch (e) {
        console.error("Invalid ObjectId format for branchId:", id);
        return null;
      }
    }).filter(Boolean) as ObjectId[];
  } else if (!allBranches && branchId) {
    try {
      branchObjectId = new ObjectId(branchId);
    } catch (e) {
      console.error("Invalid ObjectId format for branchId:", branchId);
      // Continue with string version if conversion fails
    }
  }

  // If branch admin and allBranches, get their responsible branches
  let accessibleBranchIds: string[] = [];
  if (allBranches && !isSuperAdminUser && isBranchesAdminUser && userId) {
    const branchesResponse = await Branch.find(
      { responsible: userId, deletedAt: null },
      '_id'
    ).lean();
    accessibleBranchIds = branchesResponse.map(b => (b as any)._id.toString());
    
    if (accessibleBranchIds.length === 0) {
      return new Response(
        JSON.stringify({ 
          stats: [], 
          totals: [], 
          totalCount: 0, 
          totalPages: 0, 
          users: [],
          branchMap: {}
        }),
        { status: 200 }
      );
    }
  }

  // Aggregate reservations by partnerId, date, and branchId if allBranches is true
  let agg;
  if (branchIds && branchIds.length > 0) {
    // Multiple specific branches
    const branchMatch = { 'appointment.branchId': { $in: branchObjectIds.length > 0 ? branchObjectIds : branchIds } };

    agg = [
      { $match: { $or: [{ deletedAt: { $exists: false } }, { deletedAt: null }] } },
      {
        $lookup: {
          from: 'appointments',
          localField: 'appointmentId',
          foreignField: '_id',
          as: 'appointment',
        },
      },
      { $unwind: '$appointment' },
      {
        $match: {
          'appointment.date': { $gte: startDate, $lte: endDate },
          ...branchMatch
        },
      },
      {
        $group: {
          _id: {
            userId: '$partnerId',
            date: '$appointment.date',
            branchId: '$appointment.branchId',
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id.userId': 1 as 1, '_id.date': 1 as 1, '_id.branchId': 1 as 1 } },
    ];
  } else if (allBranches) {
    // Branch admin with "all" selected needs branch restriction
    const branchMatch = !isSuperAdminUser && accessibleBranchIds.length > 0
      ? { 'appointment.branchId': { $in: accessibleBranchIds.map(id => new ObjectId(id)) } }
      : {};

    agg = [
      { $match: { $or: [{ deletedAt: { $exists: false } }, { deletedAt: null }] } },
      {
        $lookup: {
          from: 'appointments',
          localField: 'appointmentId',
          foreignField: '_id',
          as: 'appointment',
        },
      },
      { $unwind: '$appointment' },
      {
        $match: {
          'appointment.date': { $gte: startDate, $lte: endDate },
          ...branchMatch
        },
      },
      {
        $group: {
          _id: {
            userId: '$partnerId',
            date: '$appointment.date',
            branchId: '$appointment.branchId',
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id.userId': 1 as 1, '_id.date': 1 as 1, '_id.branchId': 1 as 1 } },
    ];
  } else {
    // Create a match condition that works with both ObjectId and string IDs
    const branchMatch = branchObjectId 
      ? { $or: [
          { 'appointment.branchId': branchObjectId },
          { 'appointment.branchId': branchId }
        ]}
      : { 'appointment.branchId': branchId };

    agg = [
      { $match: {   $or: [     { deletedAt: { $exists: false } },     { deletedAt: null }   ] } },
      {
        $lookup: {
          from: 'appointments',
          localField: 'appointmentId',
          foreignField: '_id',
          as: 'appointment',
        },
      },
      { $unwind: '$appointment' },
      {
        $match: {
          'appointment.date': { $gte: startDate, $lte: endDate },
          ...branchMatch
        },
      },
      {
        $group: {
          _id: { userId: '$partnerId', date: '$appointment.date' },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id.userId': 1 as 1, '_id.date': 1 as 1 } },
    ];
  }

  // For debug purposes
  if (process.env.NODE_ENV === 'development') {
    console.log('User performance stats query:', JSON.stringify({
      branchId,
      branchIds,
      branchObjectId: branchObjectId?.toString(),
      branchObjectIds: branchObjectIds.map(id => id.toString()),
      allBranches,
      accessibleBranchIds: accessibleBranchIds.length > 0 ? accessibleBranchIds : 'N/A',
      startDate,
      endDate
    }));
  }

  const statsRaw = await Reservation.aggregate(agg);

  // Generate branch map for allBranches or multiselect scenarios
  let branchMap: Record<string, string> = {};
  if (allBranches || (branchIds && branchIds.length > 0)) {
    const branchIdsInResult = Array.from(new Set(statsRaw.map(row => row._id.branchId?.toString()).filter(Boolean)));
    const branches = await Branch.find({ _id: { $in: branchIdsInResult } }, '_id name').lean();
    branchMap = Object.fromEntries(branches.map(b => [String(b._id as string), b.name]));
  }

  // Compute total per user
  const userTotals: Record<string, number> = {};
  const userSalesTotals: Record<string, number> = {};
  for (const row of statsRaw) {
    const userId = row._id.userId?.toString();
    userTotals[userId] = (userTotals[userId] || 0) + row.count;
  }

  // Compute sales count per user (using sales status codes)
  const salesStatusCodes = await getSalesStatusCodes();
  let salesAgg;
  if (branchIds && branchIds.length > 0) {
    // Multiple specific branches
    const branchMatch = { 'appointment.branchId': { $in: branchObjectIds.length > 0 ? branchObjectIds : branchIds } };

    salesAgg = [
      { $match: {  $or: [
    { deletedAt: { $exists: false } },
    { deletedAt: null }
  ], status: { $in: salesStatusCodes } } },
      {
        $lookup: {
          from: 'appointments',
          localField: 'appointmentId',
          foreignField: '_id',
          as: 'appointment',
        },
      },
      { $unwind: '$appointment' },
      {
        $match: {
          'appointment.date': { $gte: startDate, $lte: endDate },
          ...branchMatch
        },
      },
      {
        $group: {
          _id: {
            userId: '$partnerId',
          },
          salesCount: { $sum: 1 },
        },
      },
    ];
  } else if (allBranches) {
    // Branch admin with "all" selected needs branch restriction
    const branchMatch = !isSuperAdminUser && accessibleBranchIds.length > 0
      ? { 'appointment.branchId': { $in: accessibleBranchIds.map(id => new ObjectId(id)) } }
      : {};

    salesAgg = [
      { $match: {  $or: [
    { deletedAt: { $exists: false } },
    { deletedAt: null }
  ], status: { $in: salesStatusCodes } } },
      {
        $lookup: {
          from: 'appointments',
          localField: 'appointmentId',
          foreignField: '_id',
          as: 'appointment',
        },
      },
      { $unwind: '$appointment' },
      {
        $match: {
          'appointment.date': { $gte: startDate, $lte: endDate },
          ...branchMatch
        },
      },
      {
        $group: {
          _id: {
            userId: '$partnerId',
          },
          salesCount: { $sum: 1 },
        },
      },
    ];
  } else {
    const branchMatch = branchObjectId 
      ? { $or: [
          { 'appointment.branchId': branchObjectId },
          { 'appointment.branchId': branchId }
        ]}
      : { 'appointment.branchId': branchId };
    salesAgg = [
      { $match: {  $or: [
    { deletedAt: { $exists: false } },
    { deletedAt: null }
  ], status: { $in: salesStatusCodes } } },
      {
        $lookup: {
          from: 'appointments',
          localField: 'appointmentId',
          foreignField: '_id',
          as: 'appointment',
        },
      },
      { $unwind: '$appointment' },
      {
        $match: {
          'appointment.date': { $gte: startDate, $lte: endDate },
          ...branchMatch
        },
      },
      {
        $group: {
          _id: { userId: '$partnerId' },
          salesCount: { $sum: 1 },
        },
      },
    ];
  }
  const salesStats = await Reservation.aggregate(salesAgg);
  for (const row of salesStats) {
    const userId = row._id.userId?.toString();
    userSalesTotals[userId] = row.salesCount;
  }

  // Compute presence count per user (using presence status codes)
  const presenceStatusCodes = await getPresenceStatusCodes();
  let presenceAgg;
  if (branchIds && branchIds.length > 0) {
    // Multiple specific branches
    const branchMatch = { 'appointment.branchId': { $in: branchObjectIds.length > 0 ? branchObjectIds : branchIds } };

    presenceAgg = [
      { $match: {  $or: [
    { deletedAt: { $exists: false } },
    { deletedAt: null }
  ], status: { $in: presenceStatusCodes } } },
      {
        $lookup: {
          from: 'appointments',
          localField: 'appointmentId',
          foreignField: '_id',
          as: 'appointment',
        },
      },
      { $unwind: '$appointment' },
      {
        $match: {
          'appointment.date': { $gte: startDate, $lte: endDate },
          ...branchMatch
        },
      },
      {
        $group: {
          _id: {
            userId: '$partnerId',
          },
          presenceCount: { $sum: 1 },
        },
      },
    ];
  } else if (allBranches) {
    // Branch admin with "all" selected needs branch restriction
    const branchMatch = !isSuperAdminUser && accessibleBranchIds.length > 0
      ? { 'appointment.branchId': { $in: accessibleBranchIds.map(id => new ObjectId(id)) } }
      : {};

    presenceAgg = [
      { $match: {  $or: [
    { deletedAt: { $exists: false } },
    { deletedAt: null }
  ], status: { $in: presenceStatusCodes } } },
      {
        $lookup: {
          from: 'appointments',
          localField: 'appointmentId',
          foreignField: '_id',
          as: 'appointment',
        },
      },
      { $unwind: '$appointment' },
      {
        $match: {
          'appointment.date': { $gte: startDate, $lte: endDate },
          ...branchMatch
        },
      },
      {
        $group: {
          _id: {
            userId: '$partnerId',
          },
          presenceCount: { $sum: 1 },
        },
      },
    ];
  } else {
    const branchMatch = branchObjectId 
      ? { $or: [
          { 'appointment.branchId': branchObjectId },
          { 'appointment.branchId': branchId }
        ]}
      : { 'appointment.branchId': branchId };
    presenceAgg = [
      { $match: {  $or: [
    { deletedAt: { $exists: false } },
    { deletedAt: null }
  ], status: { $in: presenceStatusCodes } } },
      {
        $lookup: {
          from: 'appointments',
          localField: 'appointmentId',
          foreignField: '_id',
          as: 'appointment',
        },
      },
      { $unwind: '$appointment' },
      {
        $match: {
          'appointment.date': { $gte: startDate, $lte: endDate },
          ...branchMatch
        },
      },
      {
        $group: {
          _id: { userId: '$partnerId' },
          presenceCount: { $sum: 1 },
        },
      },
    ];
  }
  const presenceStats = await Reservation.aggregate(presenceAgg);
  const userPresenceTotals: Record<string, number> = {};
  for (const row of presenceStats) {
    const userId = row._id.userId?.toString();
    userPresenceTotals[userId] = row.presenceCount;
  }

  // Sort users by total desc
  const sortedUserIds = Object.entries(userTotals)
    .sort((a, b) => b[1] - a[1])
    .map(([userId]) => userId);

  // Pagination
  const totalCount = sortedUserIds.length;
  const totalPages = Math.ceil(totalCount / pageSize);
  const pagedUserIds = sortedUserIds.slice((page - 1) * pageSize, page * pageSize);

  // Filter stats to only include paged users
  const pagedStats = statsRaw.filter(row => pagedUserIds.includes(row._id.userId?.toString()));

  // Format stats for frontend
  const stats = pagedStats.map(row => {
    const base = {
      userId: row._id.userId?.toString(),
      date: row._id.date,
      count: row.count,
    };
    if (allBranches || (branchIds && branchIds.length > 0)) {
      const bId = row._id.branchId?.toString() || '';
      return {
        ...base,
        branchId: bId,
        branchName: branchMap[bId] || '',
      };
    }
    return base;
  });

  // Totals for paged users, now with salesCount and presenceCount
  const totals = pagedUserIds.map(userId => ({ userId, total: userTotals[userId], salesCount: userSalesTotals[userId] || 0, presenceCount: userPresenceTotals[userId] || 0 }));

  // Fetch all users for name resolution
  const users = await User.find({}, '_id name').lean();

  return new Response(
    JSON.stringify({
      stats,
      totals,
      totalCount,
      totalPages,
      users,
      branchMap,
    }),
    { status: 200 }
  );
} 