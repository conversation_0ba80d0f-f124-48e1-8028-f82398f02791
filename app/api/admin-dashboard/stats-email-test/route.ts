import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import dbConnect from '@/lib/db';
import StatsEmailSettings from '@/models/StatsEmailSettings';
import { DailyStatsAggregationService } from '@/lib/services/daily-stats-aggregation';
import { getDailyStatsEmailTemplate, processStatsEmailTemplate, StatsEmailData } from '@/lib/utils/stats-email-templates';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import { getBrevoSender } from '@/lib/brevo/brevoUtils';
import { StatsExportService } from '@/lib/services/stats-export-service';

export async function POST(req: NextRequest) {
  try {
    // Authentication check
    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    await dbConnect();

    const body = await req.json();
    const { emailAddress, emailSettingId } = body;

    if (!emailAddress) {
      return NextResponse.json({ error: 'Email address is required' }, { status: 400 });
    }

    if (!emailSettingId) {
      return NextResponse.json({ error: 'Email setting ID is required' }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailAddress)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    // Get the email setting
    const emailSetting = await StatsEmailSettings.findById(emailSettingId);
    if (!emailSetting) {
      return NextResponse.json({ error: 'Email setting not found' }, { status: 404 });
    }

    // Ensure test emails always show both reservation types for better user experience

    // Use yesterday's date for test email (ensure consistent timezone handling)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    // Use local timezone to avoid date shifts
    const year = yesterday.getFullYear();
    const month = String(yesterday.getMonth() + 1).padStart(2, '0');
    const day = String(yesterday.getDate()).padStart(2, '0');
    const dateStr = `${year}-${month}-${day}`;

    // Get stats aggregation service
    const statsService = DailyStatsAggregationService.getInstance();
    const statsData = await statsService.aggregateDailyStats(dateStr);

    // Get selected stats for export generation
    const originalSelectedStats = emailSetting.getSelectedStatsArray();

    // Ensure both reservation stats are included for test email
    const selectedStats = [...originalSelectedStats];
    if (!selectedStats.includes('totalReservationsScheduled')) {
      selectedStats.push('totalReservationsScheduled');
    }
    if (!selectedStats.includes('totalReservationsCreated')) {
      selectedStats.push('totalReservationsCreated');
    }

    // Generate export files and secure download URLs
    const exportService = StatsExportService.getInstance();

    // Generate CSV export
    const csvExportResult = await exportService.generateExport(statsData, {
      format: 'csv',
      date: dateStr,
      selectedStats,
      emailSettingId
    });

    // Generate PDF export
    const pdfExportResult = await exportService.generateExport(statsData, {
      format: 'pdf',
      date: dateStr,
      selectedStats,
      emailSettingId
    });

    // Prepare email data with selected stats only
    const emailData: StatsEmailData = {
      date: dateStr,
      totalSales: statsData.totalSales,
      totalReservationsScheduled: statsData.totalReservationsScheduled,
      totalReservationsCreated: statsData.totalReservationsCreated,
      totalPresence: statsData.totalPresence,
      salesByBranch: statsData.salesByBranch.map(branch => ({
        branchName: branch.branchName,
        amount: branch.amount,
        count: branch.count
      })),
      reservationsByBranch: statsData.reservationsByBranch.map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      reservationsCreatedByBranch: (statsData.reservationsCreatedByBranch || []).map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      presenceByBranch: statsData.presenceByBranch.map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      topSellers: statsData.topSellers.map(seller => ({
        name: seller.userName,
        amount: seller.amount || 0,
        count: seller.count
      })),
      topPaps: statsData.topPaps.map(pap => ({
        name: pap.userName,
        count: pap.count
      })),
      selectedStats,
      exportUrls: {
        csv: csvExportResult.success ? csvExportResult.downloadUrl! : '#csv-export-error',
        pdf: pdfExportResult.success ? pdfExportResult.downloadUrl! : '#pdf-export-error'
      }
    };

    // Generate and process email template
    const template = getDailyStatsEmailTemplate(emailData);
    const processedTemplate = processStatsEmailTemplate(template, emailData);

    // Format date for subject
    const formatDate = (dateString: string): string => {
      // Parse the date string as local date to avoid timezone shifts
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(year, month - 1, day); // month is 0-indexed
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    // Send test email via Brevo
    const brevoSender = getBrevoSender();
    const success = await sendBrevoTemplatedEmail({
      to: [{ email: emailAddress }],
      subject: `[TEST] Statistiques quotidiennes - ${formatDate(dateStr)}`,
      content: processedTemplate,
      sender: {
        email: brevoSender.email,
        name: 'AMQ Partners - Test Email'
      }
    });

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully',
        emailAddress,
        date: dateStr,
        selectedStatsCount: selectedStats.length,
        templateSize: processedTemplate.length,
        exports: {
          csv: {
            success: csvExportResult.success,
            url: csvExportResult.downloadUrl,
            fileName: csvExportResult.fileName,
            expiresAt: csvExportResult.expiresAt,
            error: csvExportResult.error
          },
          pdf: {
            success: pdfExportResult.success,
            url: pdfExportResult.downloadUrl,
            fileName: pdfExportResult.fileName,
            expiresAt: pdfExportResult.expiresAt,
            error: pdfExportResult.error
          }
        },
        debug: process.env.NODE_ENV === 'development' ? {
          emailSettingId,
          timestamp: new Date().toISOString(),
          selectedStats
        } : undefined
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Failed to send test email'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
