import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import Branch from '@/models/Branch';
import dbConnect from '@/lib/db';

// Rate limiting map
const rateLimiter = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(userId: string, endpoint: string): boolean {
  const key = `${userId}:${endpoint}`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 30; // Higher limit for simple data fetch
  
  if (!rateLimiter.has(key)) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  const limit = rateLimiter.get(key)!;
  if (now > limit.resetTime) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (limit.count >= maxRequests) {
    return false;
  }
  
  limit.count++;
  return true;
}

export async function GET(request: NextRequest) {
  try {
    const auth = await authenticateAdminDashboardRequest(request);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'available-branches')) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    await dbConnect();

    // Get all active branches
    const branches = await Branch.find({
      deletedAt: null
    })
    .select('_id name city province')
    .sort({ name: 1 })
    .lean();

    // Format for frontend consumption
    const formattedBranches = branches.map(branch => {
      const branchName = branch.name || 'Unknown Branch';
      const city = branch.city || '';
      const province = branch.province || '';
      
      // Create location string
      let location = '';
      if (city && province) {
        location = `${city}, ${province}`;
      } else if (city) {
        location = city;
      } else if (province) {
        location = province;
      }

      return {
        value: branch._id.toString(),
        label: location ? `${branchName} (${location})` : branchName,
        name: branchName,
        city: city,
        province: province,
        // Add sorting helper for branches starting with '*'
        sortOrder: branchName.startsWith('*') ? 1 : 0
      };
    });

    // Sort branches: regular branches first, then branches starting with '*'
    formattedBranches.sort((a, b) => {
      // First sort by sortOrder (0 for regular, 1 for *)
      if (a.sortOrder !== b.sortOrder) {
        return a.sortOrder - b.sortOrder;
      }
      // Then sort alphabetically by name
      return a.name.localeCompare(b.name);
    });

    // Remove sortOrder from final response
    const finalBranches = formattedBranches.map(({ sortOrder, ...branch }) => branch);

    return NextResponse.json({
      success: true,
      branches: finalBranches,
      count: finalBranches.length,
      debug: process.env.NODE_ENV === 'development' ? {
        userId: auth.userId,
        timestamp: new Date().toISOString(),
        totalFound: branches.length
      } : undefined
    });

  } catch (error) {
    console.error('Error fetching available branches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch branches' },
      { status: 500 }
    );
  }
}
