import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { CronScheduler } from '@/lib/services/cron-scheduler';

export async function POST(req: NextRequest) {
  try {
    // Authenticate request
    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('Manual export cleanup triggered by:', auth.user?.email);

    // Execute the cleanup job manually
    const result = await CronScheduler.executeExportCleanupManually();

    return NextResponse.json({
      success: true,
      message: 'Export cleanup completed successfully',
      executionTime: result.executionTime,
      triggeredBy: auth.user?.email,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in manual export cleanup:', error);
    
    return NextResponse.json(
      { 
        error: 'Export cleanup failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
