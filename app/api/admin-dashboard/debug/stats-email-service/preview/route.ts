import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { DailyStatsAggregationService } from '@/lib/services/daily-stats-aggregation';
import { getDailyStatsEmailTemplate, processStatsEmailTemplate, StatsEmailData } from '@/lib/utils/stats-email-templates';

export async function POST(req: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const body = await req.json();
    const { date } = body;

    if (!date) {
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }

    console.log(`[Debug] Generating email preview for date ${date}`);

    // Get stats aggregation service
    const statsService = DailyStatsAggregationService.getInstance();
    const statsData = await statsService.aggregateDailyStats(date);

    // Prepare email data with all stats selected for preview
    const emailData: StatsEmailData = {
      date,
      totalSales: statsData.totalSales,
      totalReservationsScheduled: statsData.totalReservationsScheduled,
      totalReservationsCreated: statsData.totalReservationsCreated,
      totalPresence: statsData.totalPresence,
      salesByBranch: statsData.salesByBranch.map(branch => ({
        branchName: branch.branchName,
        amount: branch.amount,
        count: branch.count
      })),
      reservationsByBranch: statsData.reservationsByBranch.map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      reservationsCreatedByBranch: (statsData.reservationsCreatedByBranch || []).map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      presenceByBranch: statsData.presenceByBranch.map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      topSellers: statsData.topSellers.map(seller => ({
        name: seller.userName,
        amount: seller.amount || 0,
        count: seller.count
      })),
      topPaps: statsData.topPaps.map(pap => ({
        name: pap.userName,
        count: pap.count
      })),
      selectedStats: [
        'totalSales', 'totalReservationsScheduled', 'totalReservationsCreated', 'totalPresence',
        'salesByBranch', 'reservationsByBranch', 'reservationsCreatedByBranch', 'presenceByBranch',
        'topSellers', 'topPaps'
      ],
      exportUrls: {
        csv: '#',
        pdf: '#'
      }
    };

    // Generate and process email template
    const template = getDailyStatsEmailTemplate(emailData);
    const processedTemplate = processStatsEmailTemplate(template, emailData);

    console.log(`[Debug] Email preview generated successfully (${processedTemplate.length} characters)`);

    return NextResponse.json({
      success: true,
      html: processedTemplate,
      templateSize: processedTemplate.length,
      statsData: {
        totalSales: statsData.totalSales,
        totalReservationsScheduled: statsData.totalReservationsScheduled,
        totalReservationsCreated: statsData.totalReservationsCreated,
        totalPresence: statsData.totalPresence,
        branchCount: statsData.salesByBranch.length,
        topSellersCount: statsData.topSellers.length,
        topPapsCount: statsData.topPaps.length
      }
    });

  } catch (error) {
    console.error('[Debug] Error generating email preview:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
