import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { StatsEmailService } from '@/lib/services/stats-email-service';
import { DailyStatsAggregationService } from '@/lib/services/daily-stats-aggregation';
import { getDailyStatsEmailTemplate, processStatsEmailTemplate, StatsEmailData } from '@/lib/utils/stats-email-templates';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import { getBrevoSender } from '@/lib/brevo/brevoUtils';
import { StatsExportService } from '@/lib/services/stats-export-service';

export async function POST(req: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const body = await req.json();
    const { email, date } = body;

    if (!email) {
      return NextResponse.json({ error: 'Email address is required' }, { status: 400 });
    }

    if (!date) {
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    console.log(`[Debug] Sending test stats email to ${email} for date ${date}`);

    // Get stats aggregation service
    const statsService = DailyStatsAggregationService.getInstance();
    const statsData = await statsService.aggregateDailyStats(date);

    // Define selected stats for test
    const selectedStats = [
      'totalSales', 'totalReservationsScheduled', 'totalReservationsCreated', 'totalPresence',
      'salesByBranch', 'reservationsByBranch', 'reservationsCreatedByBranch', 'presenceByBranch',
      'topSellers', 'topPaps'
    ];

    // Generate export files and secure download URLs
    const exportService = StatsExportService.getInstance();

    // Generate CSV export
    const csvExportResult = await exportService.generateExport(statsData, {
      format: 'csv',
      date,
      selectedStats,
      emailSettingId: 'debug-test'
    });

    // Generate PDF export
    const pdfExportResult = await exportService.generateExport(statsData, {
      format: 'pdf',
      date,
      selectedStats,
      emailSettingId: 'debug-test'
    });

    // Prepare email data with all stats selected for test
    const emailData: StatsEmailData = {
      date,
      totalSales: statsData.totalSales,
      totalReservationsScheduled: statsData.totalReservationsScheduled,
      totalReservationsCreated: statsData.totalReservationsCreated,
      totalPresence: statsData.totalPresence,
      salesByBranch: statsData.salesByBranch.map(branch => ({
        branchName: branch.branchName,
        amount: branch.amount,
        count: branch.count
      })),
      reservationsByBranch: statsData.reservationsByBranch.map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      reservationsCreatedByBranch: (statsData.reservationsCreatedByBranch || []).map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      presenceByBranch: statsData.presenceByBranch.map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      topSellers: statsData.topSellers.map(seller => ({
        name: seller.userName,
        amount: seller.amount || 0,
        count: seller.count
      })),
      topPaps: statsData.topPaps.map(pap => ({
        name: pap.userName,
        count: pap.count
      })),
      selectedStats,
      exportUrls: {
        csv: csvExportResult.success ? csvExportResult.downloadUrl! : '#csv-export-error',
        pdf: pdfExportResult.success ? pdfExportResult.downloadUrl! : '#pdf-export-error'
      }
    };

    // Generate and process email template
    const template = getDailyStatsEmailTemplate(emailData);
    const processedTemplate = processStatsEmailTemplate(template, emailData);

    // Format date for subject
    const formatDate = (dateString: string): string => {
      // Parse the date string as local date to avoid timezone shifts
      const [year, month, day] = dateString.split('-').map(Number);
      const date = new Date(year, month - 1, day); // month is 0-indexed
      return date.toLocaleDateString('fr-CA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    // Send test email via Brevo
    const brevoSender = getBrevoSender();
    const success = await sendBrevoTemplatedEmail({
      to: [{ email }],
      subject: `[TEST] Statistiques quotidiennes - ${formatDate(date)}`,
      content: processedTemplate,
      sender: {
        email: brevoSender.email,
        name: 'AMQ Partners - Test Email'
      }
    });

    if (success) {
      console.log(`[Debug] Test email sent successfully to ${email}`);
      return NextResponse.json({
        success: true,
        message: `Test email sent to ${email}`,
        templateSize: processedTemplate.length,
        statsData: {
          totalSales: statsData.totalSales,
          totalReservationsScheduled: statsData.totalReservationsScheduled,
          totalReservationsCreated: statsData.totalReservationsCreated,
          totalPresence: statsData.totalPresence,
          branchCount: statsData.salesByBranch.length,
          topSellersCount: statsData.topSellers.length,
          topPapsCount: statsData.topPaps.length
        }
      });
    } else {
      console.error(`[Debug] Failed to send test email to ${email}`);
      return NextResponse.json({
        success: false,
        error: 'Failed to send test email'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('[Debug] Error sending test email:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
