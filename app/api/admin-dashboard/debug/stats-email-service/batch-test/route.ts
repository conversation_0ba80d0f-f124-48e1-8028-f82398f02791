import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { StatsEmailService } from '@/lib/services/stats-email-service';

export async function POST(req: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const body = await req.json();
    const { date } = body;

    if (!date) {
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }

    console.log(`[Debug] Running batch email test for date ${date}`);

    // Get email service and run batch test
    const emailService = StatsEmailService.getInstance();
    const results = await emailService.sendDailyStatsEmails(date);

    console.log(`[Debug] Batch test completed: ${results.successCount}/${results.totalEmails} successful`);

    return NextResponse.json({
      success: true,
      results: {
        totalEmails: results.totalEmails,
        successCount: results.successCount,
        failureCount: results.failureCount,
        executionTime: results.executionTime,
        errors: results.errors,
        results: results.results.map(result => ({
          success: result.success,
          emailAddress: result.emailAddress,
          error: result.error,
          timestamp: result.timestamp
        }))
      }
    });

  } catch (error) {
    console.error('[Debug] Error running batch test:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
