import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { DailyStatsAggregationService } from '@/lib/services/daily-stats-aggregation';
import { getDailyStatsEmailTemplate, processStatsEmailTemplate, StatsEmailData } from '@/lib/utils/stats-email-templates';
import StatsEmailSettings from '@/models/StatsEmailSettings';

interface PerformanceTestResults {
  templateRenderingTime: number;
  emailSendingTime: number;
  batchProcessingTime: number;
  memoryUsage: number;
  successRate: number;
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const body = await req.json();
    const { date } = body;

    if (!date) {
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }

    console.log(`[Debug] Running performance test for date ${date}`);

    const startTime = Date.now();
    const initialMemory = process.memoryUsage().heapUsed;

    // Test 1: Template rendering performance
    const templateStartTime = Date.now();
    
    const statsService = DailyStatsAggregationService.getInstance();
    const statsData = await statsService.aggregateDailyStats(date);

    const emailData: StatsEmailData = {
      date,
      totalSales: statsData.totalSales,
      totalReservations: statsData.totalReservations,
      totalPresence: statsData.totalPresence,
      salesByBranch: statsData.salesByBranch.map(branch => ({
        branchName: branch.branchName,
        amount: branch.amount,
        count: branch.count
      })),
      reservationsByBranch: statsData.reservationsByBranch.map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      presenceByBranch: statsData.presenceByBranch.map(branch => ({
        branchName: branch.branchName,
        count: branch.count
      })),
      topSellers: statsData.topSellers.map(seller => ({
        name: seller.userName,
        amount: seller.amount || 0,
        count: seller.count
      })),
      topPaps: statsData.topPaps.map(pap => ({
        name: pap.userName,
        count: pap.count
      })),
      selectedStats: [
        'totalSales', 'totalReservations', 'totalPresence',
        'salesByBranch', 'reservationsByBranch', 'presenceByBranch',
        'topSellers', 'topPaps'
      ],
      exportUrls: {
        csv: '#',
        pdf: '#'
      }
    };

    // Render template multiple times to get average
    const renderIterations = 10;
    let totalRenderTime = 0;

    for (let i = 0; i < renderIterations; i++) {
      const renderStart = Date.now();
      const template = getDailyStatsEmailTemplate(emailData);
      const processedTemplate = processStatsEmailTemplate(template, emailData);
      totalRenderTime += Date.now() - renderStart;
    }

    const templateRenderingTime = totalRenderTime / renderIterations;
    const templateEndTime = Date.now();

    // Test 2: Email sending simulation (without actually sending)
    const emailStartTime = Date.now();
    
    // Get active email settings to simulate batch processing
    const emailSettings = await StatsEmailSettings.find({ isActive: true }).sort({ email: 1 });
    const emailCount = Math.max(emailSettings.length, 1); // At least 1 for testing
    
    // Simulate email preparation for each setting
    let successfulEmails = 0;
    for (const setting of emailSettings.slice(0, 5)) { // Limit to 5 for performance test
      try {
        const settingEmailData = {
          ...emailData,
          selectedStats: setting.getSelectedStatsArray()
        };
        
        const template = getDailyStatsEmailTemplate(settingEmailData);
        const processedTemplate = processStatsEmailTemplate(template, settingEmailData);
        
        // Simulate email sending delay (without actually sending)
        await new Promise(resolve => setTimeout(resolve, 50));
        successfulEmails++;
      } catch (error) {
        console.error('Error in performance test email simulation:', error);
      }
    }

    const emailSendingTime = Date.now() - emailStartTime;

    // Test 3: Batch processing time
    const batchProcessingTime = Date.now() - startTime;

    // Test 4: Memory usage
    const finalMemory = process.memoryUsage().heapUsed;
    const memoryUsage = finalMemory - initialMemory;

    // Test 5: Success rate
    const successRate = emailSettings.length > 0 ? (successfulEmails / Math.min(emailSettings.length, 5)) * 100 : 100;

    const results: PerformanceTestResults = {
      templateRenderingTime,
      emailSendingTime,
      batchProcessingTime,
      memoryUsage,
      successRate
    };

    console.log(`[Debug] Performance test completed:`, results);

    return NextResponse.json({
      success: true,
      results,
      testDetails: {
        renderIterations,
        emailCount: Math.min(emailSettings.length, 5),
        totalEmailSettings: emailSettings.length,
        statsDataSize: JSON.stringify(statsData).length,
        templateSize: getDailyStatsEmailTemplate(emailData).length
      }
    });

  } catch (error) {
    console.error('[Debug] Error running performance test:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
