import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { EmailLogger } from '@/lib/services/stats-email-service';

export async function GET(req: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '100');

    const logs = EmailLogger.getLogs(limit);

    return NextResponse.json({
      success: true,
      logs: logs.map(log => ({
        timestamp: log.timestamp,
        emailAddress: log.emailAddress,
        success: log.success,
        executionTime: log.executionTime,
        templateSize: log.templateSize,
        error: log.error,
        retryCount: log.retryCount
      }))
    });

  } catch (error) {
    console.error('[Debug] Error getting email logs:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
