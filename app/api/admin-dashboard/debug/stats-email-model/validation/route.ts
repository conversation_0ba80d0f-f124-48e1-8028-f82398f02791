import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import StatsEmailSettings from '@/models/StatsEmailSettings';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function POST(request: NextRequest) {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const validationResult = {
      emailValidation: true,
      uniqueConstraint: true,
      requiredFields: true,
      errors: [] as string[]
    };

    // Test email validation
    try {
      const invalidEmailTest = new StatsEmailSettings({
        email: 'invalid-email',
        createdBy: session.user.id
      });
      await invalidEmailTest.validate();
      validationResult.emailValidation = false;
      validationResult.errors.push('Email validation failed - invalid email was accepted');
    } catch (error: any) {
      if (!error.message.includes('valid email address')) {
        validationResult.emailValidation = false;
        validationResult.errors.push('Email validation error: ' + error.message);
      }
    }

    // Test required fields
    try {
      const missingFieldsTest = new StatsEmailSettings({});
      await missingFieldsTest.validate();
      validationResult.requiredFields = false;
      validationResult.errors.push('Required fields validation failed - missing fields were accepted');
    } catch (error: any) {
      if (!error.message.includes('required')) {
        validationResult.requiredFields = false;
        validationResult.errors.push('Required fields validation error: ' + error.message);
      }
    }

    // Test unique constraint
    try {
      const testEmail = '<EMAIL>';
      
      // Clean up any existing test record
      await StatsEmailSettings.deleteOne({ email: testEmail });
      
      // Create first record
      const firstRecord = new StatsEmailSettings({
        email: testEmail,
        createdBy: session.user.id
      });
      await firstRecord.save();

      // Try to create duplicate
      const duplicateRecord = new StatsEmailSettings({
        email: testEmail,
        createdBy: session.user.id
      });
      await duplicateRecord.save();
      
      validationResult.uniqueConstraint = false;
      validationResult.errors.push('Unique constraint failed - duplicate email was accepted');
      
      // Clean up
      await StatsEmailSettings.deleteOne({ email: testEmail });
    } catch (error: any) {
      if (!error.message.includes('duplicate') && !error.code === 11000) {
        validationResult.uniqueConstraint = false;
        validationResult.errors.push('Unique constraint error: ' + error.message);
      }
      // Clean up in case of error
      await StatsEmailSettings.deleteOne({ email: '<EMAIL>' });
    }

    return NextResponse.json(validationResult);
  } catch (error) {
    console.error('Debug validation error:', error);
    return NextResponse.json(
      { error: 'Failed to run validation tests' },
      { status: 500 }
    );
  }
}
