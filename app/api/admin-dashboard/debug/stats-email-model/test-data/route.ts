import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import StatsEmailSettings from '@/models/StatsEmailSettings';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function POST(request: NextRequest) {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Clean up any existing debug test data
    await StatsEmailSettings.deleteMany({ 
      email: { $regex: /^debug-test-\d+@example\.com$/ } 
    });

    const testEmails = [
      {
        email: '<EMAIL>',
        isActive: true,
        selectedStats: {
          totalSales: true,
          totalReservations: true,
          totalPresence: false,
          salesByBranch: true,
          reservationsByBranch: false,
          presenceByBranch: true,
          topSellers: true,
          topPaps: false
        },
        emailSendCount: 5,
        lastEmailSent: new Date(Date.now() - 24 * 60 * 60 * 1000) // Yesterday
      },
      {
        email: '<EMAIL>',
        isActive: true,
        selectedStats: {
          totalSales: true,
          totalReservations: true,
          totalPresence: true,
          salesByBranch: true,
          reservationsByBranch: true,
          presenceByBranch: true,
          topSellers: true,
          topPaps: true
        },
        emailSendCount: 12,
        lastEmailSent: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
      },
      {
        email: '<EMAIL>',
        isActive: false,
        selectedStats: {
          totalSales: false,
          totalReservations: true,
          totalPresence: false,
          salesByBranch: false,
          reservationsByBranch: true,
          presenceByBranch: false,
          topSellers: false,
          topPaps: true
        },
        emailSendCount: 0,
        lastEmailSent: null
      },
      {
        email: '<EMAIL>',
        isActive: true,
        selectedStats: {
          totalSales: true,
          totalReservations: false,
          totalPresence: true,
          salesByBranch: false,
          reservationsByBranch: false,
          presenceByBranch: true,
          topSellers: true,
          topPaps: false
        },
        emailSendCount: 3,
        lastEmailSent: null // Never sent
      },
      {
        email: '<EMAIL>',
        isActive: true,
        selectedStats: {
          totalSales: true,
          totalReservationsScheduled: true,
          totalReservationsCreated: true,
          totalPresence: true,
          salesByBranch: true,
          reservationsByBranch: true,
          presenceByBranch: true,
          topSellers: true,
          topPaps: true
        },
        emailSendCount: 25,
        lastEmailSent: new Date() // Today
      }
    ];

    const createdRecords = [];
    for (const emailData of testEmails) {
      const record = new StatsEmailSettings({
        ...emailData,
        createdBy: session.user.id
      });
      await record.save();
      createdRecords.push(record);
    }

    return NextResponse.json({
      success: true,
      message: `Created ${createdRecords.length} test records`,
      data: createdRecords
    });
  } catch (error) {
    console.error('Debug test data creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create test data' },
      { status: 500 }
    );
  }
}
