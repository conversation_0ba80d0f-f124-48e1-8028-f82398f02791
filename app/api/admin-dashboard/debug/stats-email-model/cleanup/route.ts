import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import StatsEmailSettings from '@/models/StatsEmailSettings';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function DELETE(request: NextRequest) {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Delete all debug test data
    const deleteResult = await StatsEmailSettings.deleteMany({ 
      email: { $regex: /^debug-test-\d+@example\.com$/ } 
    });

    // Also clean up any other debug records
    await StatsEmailSettings.deleteMany({ 
      email: '<EMAIL>'
    });

    return NextResponse.json({
      success: true,
      message: `Cleaned up ${deleteResult.deletedCount} debug records`
    });
  } catch (error) {
    console.error('Debug cleanup error:', error);
    return NextResponse.json(
      { error: 'Failed to cleanup test data' },
      { status: 500 }
    );
  }
}
