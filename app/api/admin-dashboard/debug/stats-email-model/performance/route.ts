import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import StatsEmailSettings from '@/models/StatsEmailSettings';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function POST(request: NextRequest) {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const performanceResult = {
      queryTimes: {} as Record<string, number>,
      indexUsage: {} as Record<string, boolean>,
      recommendations: [] as string[]
    };

    // Test query performance
    const queries = [
      {
        name: 'getActiveSettings',
        query: () => StatsEmailSettings.find({ isActive: true }).sort({ email: 1 })
      },
      {
        name: 'findByEmail',
        query: () => StatsEmailSettings.findOne({ email: '<EMAIL>' })
      },
      {
        name: 'getPendingForToday',
        query: () => {
          const startOfDay = new Date();
          startOfDay.setHours(0, 0, 0, 0);
          return StatsEmailSettings.find({
            isActive: true,
            $or: [
              { lastEmailSent: null },
              { lastEmailSent: { $lt: startOfDay } }
            ]
          });
        }
      }
    ];

    for (const { name, query } of queries) {
      const startTime = Date.now();
      try {
        await query().explain('executionStats');
        const endTime = Date.now();
        performanceResult.queryTimes[name] = endTime - startTime;
      } catch (error) {
        performanceResult.queryTimes[name] = -1;
        performanceResult.recommendations.push(`Failed to test query: ${name}`);
      }
    }

    // Check index usage
    try {
      const collection = StatsEmailSettings.collection;
      const indexes = await collection.listIndexes().toArray();
      
      const expectedIndexes = [
        'email_1',
        'isActive_1_email_1',
        'lastEmailSent_1_isActive_1'
      ];

      for (const expectedIndex of expectedIndexes) {
        const indexExists = indexes.some(index => 
          index.name === expectedIndex || 
          JSON.stringify(index.key) === JSON.stringify(getIndexKey(expectedIndex))
        );
        performanceResult.indexUsage[expectedIndex] = indexExists;
        
        if (!indexExists) {
          performanceResult.recommendations.push(`Missing index: ${expectedIndex}`);
        }
      }
    } catch (error) {
      performanceResult.recommendations.push('Failed to check index usage');
    }

    // Performance recommendations
    if (performanceResult.queryTimes.getActiveSettings > 50) {
      performanceResult.recommendations.push('getActiveSettings query is slow - check compound index');
    }
    
    if (performanceResult.queryTimes.findByEmail > 20) {
      performanceResult.recommendations.push('findByEmail query is slow - check email index');
    }

    if (performanceResult.queryTimes.getPendingForToday > 100) {
      performanceResult.recommendations.push('getPendingForToday query is slow - check compound index on lastEmailSent and isActive');
    }

    return NextResponse.json(performanceResult);
  } catch (error) {
    console.error('Debug performance error:', error);
    return NextResponse.json(
      { error: 'Failed to run performance tests' },
      { status: 500 }
    );
  }
}

function getIndexKey(indexName: string): Record<string, number> {
  switch (indexName) {
    case 'email_1':
      return { email: 1 };
    case 'isActive_1_email_1':
      return { isActive: 1, email: 1 };
    case 'lastEmailSent_1_isActive_1':
      return { lastEmailSent: 1, isActive: 1 };
    default:
      return {};
  }
}
