import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import dbConnect from '@/lib/db';
import StatsEmailSettings, { GlobalEmailConfig } from '@/models/StatsEmailSettings';
import mongoose from 'mongoose';
import { getUserRoles } from '@/app/api/utils/server-permission-utils';
import { isSuperAdmin } from '@/lib/utils/role-utils';

/**
 * Debug API for Branch Stats Model
 * 
 * This endpoint provides debugging and testing functionality for the new branch stats
 * fields in StatsEmailSettings and GlobalEmailConfig models.
 * 
 * GET: Test model functionality and validate schema
 * POST: Create test email setting with branch stats
 * PUT: Update existing setting with branch stats
 * DELETE: Clean up test data
 */

interface BranchStatsTestData {
  email: string;
  branchStatsEnabled: boolean;
  selectedBranches: string[];
  branchStatsSelectedStats: {
    reservationCount: boolean;
    adultCount: boolean;
    childCount: boolean;
    allergies: boolean;
    serviceTypes: boolean;
  };
  regularStatsEnabled: boolean;
}

// GET: Test model functionality
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user roles if not already loaded
    if (!session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }

    // Check if user is SuperAdmin
    const userIsSuperAdmin = isSuperAdmin(session.user.roles);
    if (!userIsSuperAdmin) {
      return NextResponse.json({ error: 'SuperAdmin access required' }, { status: 403 });
    }

    await dbConnect();

    const results = {
      timestamp: new Date().toISOString(),
      tests: [] as any[],
      summary: {
        passed: 0,
        failed: 0,
        total: 0
      }
    };

    // Test 1: Check if branch stats fields exist in schema
    try {
      const sampleSetting = new StatsEmailSettings({
        email: '<EMAIL>',
        createdBy: new mongoose.Types.ObjectId()
      });

      const hasFields = {
        branchStatsEnabled: sampleSetting.branchStatsEnabled !== undefined,
        selectedBranches: Array.isArray(sampleSetting.selectedBranches),
        branchStatsSelectedStats: typeof sampleSetting.branchStatsSelectedStats === 'object',
        regularStatsEnabled: sampleSetting.regularStatsEnabled !== undefined
      };

      results.tests.push({
        name: 'Schema Fields Test',
        status: Object.values(hasFields).every(Boolean) ? 'PASS' : 'FAIL',
        details: hasFields
      });

      if (Object.values(hasFields).every(Boolean)) {
        results.summary.passed++;
      } else {
        results.summary.failed++;
      }
    } catch (error) {
      results.tests.push({
        name: 'Schema Fields Test',
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      results.summary.failed++;
    }

    // Test 2: Check GlobalEmailConfig branch stats fields
    try {
      const globalConfig = await GlobalEmailConfig.getGlobalConfig();
      
      const hasGlobalFields = {
        branchStatsEnabled: globalConfig.branchStatsEnabled !== undefined,
        branchStatsHour: typeof globalConfig.branchStatsHour === 'number',
        branchStatsMinute: typeof globalConfig.branchStatsMinute === 'number',
        branchStatsTimezone: typeof globalConfig.branchStatsTimezone === 'string'
      };

      results.tests.push({
        name: 'GlobalEmailConfig Fields Test',
        status: Object.values(hasGlobalFields).every(Boolean) ? 'PASS' : 'FAIL',
        details: hasGlobalFields,
        currentConfig: {
          branchStatsEnabled: globalConfig.branchStatsEnabled,
          branchStatsHour: globalConfig.branchStatsHour,
          branchStatsMinute: globalConfig.branchStatsMinute,
          branchStatsTimezone: globalConfig.branchStatsTimezone
        }
      });

      if (Object.values(hasGlobalFields).every(Boolean)) {
        results.summary.passed++;
      } else {
        results.summary.failed++;
      }
    } catch (error) {
      results.tests.push({
        name: 'GlobalEmailConfig Fields Test',
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      results.summary.failed++;
    }

    // Test 3: Test new instance methods
    try {
      const testSetting = new StatsEmailSettings({
        email: '<EMAIL>',
        branchStatsSelectedStats: {
          reservationCount: true,
          adultCount: false,
          childCount: true,
          allergies: false,
          serviceTypes: true
        },
        createdBy: new mongoose.Types.ObjectId()
      });

      const selectedStats = testSetting.getSelectedBranchStatsArray();
      const expectedStats = ['reservationCount', 'childCount', 'serviceTypes'];
      const methodsWork = {
        getSelectedBranchStatsArray: JSON.stringify(selectedStats.sort()) === JSON.stringify(expectedStats.sort()),
        recordBranchStatsEmailSent: typeof testSetting.recordBranchStatsEmailSent === 'function'
      };

      results.tests.push({
        name: 'Instance Methods Test',
        status: Object.values(methodsWork).every(Boolean) ? 'PASS' : 'FAIL',
        details: {
          ...methodsWork,
          selectedStats,
          expectedStats
        }
      });

      if (Object.values(methodsWork).every(Boolean)) {
        results.summary.passed++;
      } else {
        results.summary.failed++;
      }
    } catch (error) {
      results.tests.push({
        name: 'Instance Methods Test',
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      results.summary.failed++;
    }

    // Test 4: Test static methods
    try {
      const activeBranchStatsSettings = await StatsEmailSettings.getActiveBranchStatsSettings();
      const pendingBranchStats = await StatsEmailSettings.getPendingBranchStatsForToday();

      const staticMethodsWork = {
        getActiveBranchStatsSettings: Array.isArray(activeBranchStatsSettings),
        getPendingBranchStatsForToday: Array.isArray(pendingBranchStats)
      };

      results.tests.push({
        name: 'Static Methods Test',
        status: Object.values(staticMethodsWork).every(Boolean) ? 'PASS' : 'FAIL',
        details: {
          ...staticMethodsWork,
          activeBranchStatsCount: activeBranchStatsSettings.length,
          pendingBranchStatsCount: pendingBranchStats.length
        }
      });

      if (Object.values(staticMethodsWork).every(Boolean)) {
        results.summary.passed++;
      } else {
        results.summary.failed++;
      }
    } catch (error) {
      results.tests.push({
        name: 'Static Methods Test',
        status: 'FAIL',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      results.summary.failed++;
    }

    results.summary.total = results.tests.length;

    return NextResponse.json(results);

  } catch (error) {
    console.error('Branch stats model debug error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// POST: Create test email setting with branch stats
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user roles if not already loaded
    if (!session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }

    // Check if user is SuperAdmin
    const userIsSuperAdmin = isSuperAdmin(session.user.roles);
    if (!userIsSuperAdmin) {
      return NextResponse.json({ error: 'SuperAdmin access required' }, { status: 403 });
    }

    const body: BranchStatsTestData = await request.json();
    await dbConnect();

    // Validate branch IDs
    if (body.selectedBranches && body.selectedBranches.length > 0) {
      const invalidBranches = body.selectedBranches.filter(id => !mongoose.Types.ObjectId.isValid(id));
      if (invalidBranches.length > 0) {
        return NextResponse.json(
          { error: 'Invalid branch IDs', invalidBranches },
          { status: 400 }
        );
      }
    }

    // Create test setting
    const testSetting = new StatsEmailSettings({
      email: body.email,
      branchStatsEnabled: body.branchStatsEnabled,
      selectedBranches: body.selectedBranches || [],
      branchStatsSelectedStats: body.branchStatsSelectedStats || {
        reservationCount: true,
        adultCount: true,
        childCount: true,
        allergies: true,
        serviceTypes: true
      },
      regularStatsEnabled: body.regularStatsEnabled !== undefined ? body.regularStatsEnabled : true,
      createdBy: new mongoose.Types.ObjectId(session.user.id)
    });

    await testSetting.save();

    return NextResponse.json({
      success: true,
      message: 'Test email setting created successfully',
      setting: {
        id: testSetting._id,
        email: testSetting.email,
        branchStatsEnabled: testSetting.branchStatsEnabled,
        selectedBranches: testSetting.selectedBranches,
        branchStatsSelectedStats: testSetting.branchStatsSelectedStats,
        regularStatsEnabled: testSetting.regularStatsEnabled
      }
    });

  } catch (error) {
    console.error('Create test setting error:', error);
    return NextResponse.json(
      { error: 'Failed to create test setting', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// DELETE: Clean up test data
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user roles if not already loaded
    if (!session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }

    // Check if user is SuperAdmin
    const userIsSuperAdmin = isSuperAdmin(session.user.roles);
    if (!userIsSuperAdmin) {
      return NextResponse.json({ error: 'SuperAdmin access required' }, { status: 403 });
    }

    await dbConnect();

    // Delete test email settings (emails containing 'test' or 'debug')
    const deleteResult = await StatsEmailSettings.deleteMany({
      email: { $regex: /(test|debug)/i }
    });

    return NextResponse.json({
      success: true,
      message: 'Test data cleaned up successfully',
      deletedCount: deleteResult.deletedCount
    });

  } catch (error) {
    console.error('Cleanup test data error:', error);
    return NextResponse.json(
      { error: 'Failed to cleanup test data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
