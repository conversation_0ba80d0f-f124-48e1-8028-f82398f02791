import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { CronScheduler } from '@/lib/services/cron-scheduler';
import { GlobalEmailConfig } from '@/models/StatsEmailSettings';
import dbConnect from '@/lib/db';

// Only allow in development environment
const isDevelopment = process.env.NODE_ENV === 'development';

export async function POST(request: NextRequest) {
  if (!isDevelopment) {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'SuperAdmin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action, targetDate } = body;

    switch (action) {
      case 'execute':
        console.log('[Debug] Manually executing branch stats email job...');
        const result = await CronScheduler.executeBranchStatsEmailJobManually(targetDate);
        
        return NextResponse.json({
          success: true,
          action: 'execute',
          result,
          timestamp: new Date().toISOString()
        });

      case 'restart':
        console.log('[Debug] Restarting branch stats email automation...');
        await CronScheduler.restartBranchStatsEmailAutomation();
        
        return NextResponse.json({
          success: true,
          action: 'restart',
          message: 'Branch stats email automation restarted',
          timestamp: new Date().toISOString()
        });

      case 'status':
        const jobStatus = CronScheduler.getJobStatus();
        const nextRunTimes = CronScheduler.getNextRunTimes();
        const stats = CronScheduler.getCronJobStats('branch-stats-email');
        
        return NextResponse.json({
          success: true,
          action: 'status',
          status: {
            branchStatsEmail: jobStatus.branchStatsEmail,
            nextRun: nextRunTimes.branchStatsEmail,
            statistics: stats
          },
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: execute, restart, or status' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in branch stats cron debug endpoint:', error);
    return NextResponse.json(
      { 
        error: 'Debug operation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  if (!isDevelopment) {
    return NextResponse.json(
      { error: 'Debug endpoints only available in development' },
      { status: 403 }
    );
  }

  try {
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'SuperAdmin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get current status and configuration
    const jobStatus = CronScheduler.getJobStatus();
    const nextRunTimes = CronScheduler.getNextRunTimes();
    const stats = CronScheduler.getCronJobStats('branch-stats-email');
    
    // Get global configuration
    await dbConnect();
    const globalConfig = await GlobalEmailConfig.getGlobalConfig();

    return NextResponse.json({
      success: true,
      branchStatsConfig: {
        enabled: globalConfig.branchStatsEnabled,
        hour: globalConfig.branchStatsHour,
        minute: globalConfig.branchStatsMinute,
        timezone: globalConfig.branchStatsTimezone
      },
      cronStatus: {
        job: jobStatus.branchStatsEmail,
        nextRun: nextRunTimes.branchStatsEmail,
        statistics: stats
      },
      debugActions: [
        { action: 'execute', description: 'Manually execute branch stats email job' },
        { action: 'restart', description: 'Restart branch stats email automation' },
        { action: 'status', description: 'Get current status and statistics' }
      ],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting branch stats cron debug info:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get debug information',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
