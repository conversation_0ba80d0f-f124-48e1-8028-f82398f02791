import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { dailyStatsService } from '@/lib/services/daily-stats-aggregation';
import dbConnect from '@/lib/db';
import Reservation from '@/models/Reservation';

export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const { date } = await request.json();
    
    if (!date) {
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }

    await dbConnect();

    // Get aggregated stats
    const aggregatedStats = await dailyStatsService.aggregateDailyStats(date);
    
    const comparisons = [];

    // Compare total sales (using same logic as aggregation service)
    try {
      const directSalesQuery = await Reservation.aggregate([
        {
          $match: {
            $expr: {
              $and: [
                {
                  $gte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    date
                  ]
                },
                {
                  $lte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    date
                  ]
                }
              ]
            },
            $or: [
              { deletedAt: null },
              { deletedAt: { $exists: false } }
            ]
          }
        },
        {
          $group: {
            _id: null,
            amount: {
              $sum: {
                $cond: [
                  { $ifNull: ['$sellingAmount', false] },
                  '$sellingAmount',
                  0
                ]
              }
            },
            count: {
              $sum: {
                $cond: [
                  { $ifNull: ['$sellingAmount', false] },
                  1,
                  0
                ]
              }
            }
          }
        }
      ]);

      const directSales = directSalesQuery[0] || { amount: 0, count: 0 };
      
      comparisons.push({
        metric: 'Total Sales Amount',
        aggregationValue: aggregatedStats.totalSales.amount,
        directQueryValue: directSales.amount,
        match: Math.abs(directSales.amount - aggregatedStats.totalSales.amount) < 0.01,
        difference: Math.abs(directSales.amount - aggregatedStats.totalSales.amount)
      });

      comparisons.push({
        metric: 'Total Sales Count',
        aggregationValue: aggregatedStats.totalSales.count,
        directQueryValue: directSales.count,
        match: directSales.count === aggregatedStats.totalSales.count,
        difference: Math.abs(directSales.count - aggregatedStats.totalSales.count)
      });
    } catch (error) {
      console.error('Error comparing total sales:', error);
    }

    // Compare total reservations (using same logic as aggregation service)
    try {
      const directReservationsQuery = await Reservation.aggregate([
        {
          $match: {
            $expr: {
              $and: [
                {
                  $gte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    date
                  ]
                },
                {
                  $lte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    date
                  ]
                }
              ]
            },
            $or: [
              { deletedAt: null },
              { deletedAt: { $exists: false } }
            ]
          }
        },
        {
          $count: "total"
        }
      ]);

      const directReservationsCount = directReservationsQuery[0]?.total || 0;

      comparisons.push({
        metric: 'Total Reservations',
        aggregationValue: aggregatedStats.totalReservations,
        directQueryValue: directReservationsCount,
        match: directReservationsCount === aggregatedStats.totalReservations,
        difference: Math.abs(directReservationsCount - aggregatedStats.totalReservations)
      });
    } catch (error) {
      console.error('Error comparing total reservations:', error);
    }

    // Compare total presence (using same logic as aggregation service)
    try {
      const directPresenceQuery = await Reservation.aggregate([
        {
          $match: {
            $expr: {
              $and: [
                {
                  $gte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    date
                  ]
                },
                {
                  $lte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    date
                  ]
                }
              ]
            },
            presentAt: { $ne: null },
            $or: [
              { deletedAt: null },
              { deletedAt: { $exists: false } }
            ]
          }
        },
        {
          $count: "total"
        }
      ]);

      const directPresenceCount = directPresenceQuery[0]?.total || 0;

      comparisons.push({
        metric: 'Total Presence',
        aggregationValue: aggregatedStats.totalPresence,
        directQueryValue: directPresenceCount,
        match: directPresenceCount === aggregatedStats.totalPresence,
        difference: Math.abs(directPresenceCount - aggregatedStats.totalPresence)
      });
    } catch (error) {
      console.error('Error comparing total presence:', error);
    }

    // Compare top sellers count (using same logic as aggregation service)
    try {
      const directTopSellersQuery = await Reservation.aggregate([
        {
          $match: {
            $expr: {
              $and: [
                {
                  $gte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    date
                  ]
                },
                {
                  $lte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    date
                  ]
                }
              ]
            },
            assigned_user_id: { $ne: null },
            $or: [
              { deletedAt: null },
              { deletedAt: { $exists: false } }
            ]
          }
        },
        {
          $group: {
            _id: '$assigned_user_id',
            amount: { $sum: { $ifNull: ['$sellingAmount', 0] } },
            count: { $sum: 1 }
          }
        },
        {
          $sort: { amount: -1 }
        },
        {
          $limit: 5
        }
      ]);

      const topSellerAmount = directTopSellersQuery[0]?.amount || 0;
      const aggregatedTopSellerAmount = aggregatedStats.topSellers[0]?.amount || 0;

      comparisons.push({
        metric: 'Top Seller Amount',
        aggregationValue: aggregatedTopSellerAmount,
        directQueryValue: topSellerAmount,
        match: Math.abs(topSellerAmount - aggregatedTopSellerAmount) < 0.01,
        difference: Math.abs(topSellerAmount - aggregatedTopSellerAmount)
      });
    } catch (error) {
      console.error('Error comparing top sellers:', error);
    }

    // Compare top PAPs count (using same logic as aggregation service)
    try {
      const directTopPapsQuery = await Reservation.aggregate([
        {
          $match: {
            $expr: {
              $and: [
                {
                  $gte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    date
                  ]
                },
                {
                  $lte: [
                    { $substr: ['$preferences.visitDate', 0, 10] },
                    date
                  ]
                }
              ]
            },
            partnerId: { $ne: null },
            $or: [
              { deletedAt: null },
              { deletedAt: { $exists: false } }
            ]
          }
        },
        {
          $group: {
            _id: '$partnerId',
            count: { $sum: 1 }
          }
        },
        {
          $sort: { count: -1 }
        },
        {
          $limit: 5
        }
      ]);

      const topPapCount = directTopPapsQuery[0]?.count || 0;
      const aggregatedTopPapCount = aggregatedStats.topPaps[0]?.count || 0;

      comparisons.push({
        metric: 'Top PAP Count',
        aggregationValue: aggregatedTopPapCount,
        directQueryValue: topPapCount,
        match: topPapCount === aggregatedTopPapCount,
        difference: Math.abs(topPapCount - aggregatedTopPapCount)
      });
    } catch (error) {
      console.error('Error comparing top PAPs:', error);
    }

    return NextResponse.json({ comparisons });

  } catch (error) {
    console.error('Comparison test error:', error);
    return NextResponse.json(
      { error: 'Failed to run comparison test' },
      { status: 500 }
    );
  }
}
