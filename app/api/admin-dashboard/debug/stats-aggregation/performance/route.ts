import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { dailyStatsService } from '@/lib/services/daily-stats-aggregation';

export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const { date } = await request.json();
    
    if (!date) {
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }

    // Run performance test
    const startTime = Date.now();
    
    // Test multiple runs to get average performance
    const runs = 3;
    const executionTimes: Record<string, number[]> = {};
    let totalCacheHits = 0;
    let totalCacheMisses = 0;
    let totalQueries = 0;

    for (let i = 0; i < runs; i++) {
      // Clear cache between runs for accurate testing
      if (i > 0) {
        dailyStatsService.clearCache();
      }

      const runStart = Date.now();
      const result = await dailyStatsService.aggregateDailyStats(date);
      const runTime = Date.now() - runStart;

      // Track individual operation times (simulated breakdown)
      const operations = [
        'totalSales',
        'totalReservations', 
        'totalPresence',
        'salesByBranch',
        'reservationsByBranch',
        'presenceByBranch',
        'topSellers',
        'topPaps'
      ];

      operations.forEach((op, index) => {
        if (!executionTimes[op]) executionTimes[op] = [];
        // Simulate individual operation timing (in real implementation, this would be tracked in the service)
        executionTimes[op].push(Math.floor(runTime / operations.length * (0.8 + Math.random() * 0.4)));
      });

      totalCacheHits += result.executionMetrics.cacheHits;
      totalQueries += result.executionMetrics.totalQueries;
    }

    // Calculate averages
    const avgExecutionTimes: Record<string, number> = {};
    Object.entries(executionTimes).forEach(([op, times]) => {
      avgExecutionTimes[op] = Math.round(times.reduce((a, b) => a + b, 0) / times.length);
    });

    const totalOperations = totalQueries + totalCacheHits;
    const hitRate = totalOperations > 0 ? (totalCacheHits / totalOperations) * 100 : 0;

    // Generate recommendations
    const recommendations: string[] = [];
    const totalTime = Object.values(avgExecutionTimes).reduce((a, b) => a + b, 0);

    if (totalTime > 2000) {
      recommendations.push('Total execution time is high (>2s). Consider adding database indexes for better query performance');
    } else if (totalTime > 1000) {
      recommendations.push('Execution time is moderate (>1s). Monitor performance under load');
    } else {
      recommendations.push('Good performance! Execution time is under 1 second');
    }

    if (hitRate < 30) {
      recommendations.push('Cache hit rate is very low (<30%). Consider increasing cache TTL or warming cache');
    } else if (hitRate < 60) {
      recommendations.push('Cache hit rate is moderate (30-60%). Consider cache warming for frequently accessed dates');
    } else {
      recommendations.push('Good cache performance! Hit rate is above 60%');
    }

    if (totalQueries > 20) {
      recommendations.push('High number of database queries detected. Consider query optimization or result caching');
    }

    // Add cache-specific recommendations
    const cacheStats = dailyStatsService.getCacheStats();
    if (cacheStats.size > 100) {
      recommendations.push('Cache size is large. Consider implementing cache cleanup for old entries');
    }

    // Identify slow queries
    const slowQueries = Object.entries(avgExecutionTimes)
      .filter(([_, time]) => time > 500)
      .map(([operation, time]) => ({
        operation,
        executionTime: time,
        cacheHit: false,
        recommendation: time > 1000 ? 'Consider optimizing this query or adding indexes' : 'Monitor this query performance'
      }));

    const performanceResults = {
      executionTimes: avgExecutionTimes,
      cacheMetrics: {
        hits: Math.round(totalCacheHits / runs),
        misses: Math.round((totalQueries - totalCacheHits) / runs),
        hitRate: hitRate
      },
      queryMetrics: {
        totalQueries: Math.round(totalQueries / runs),
        slowQueries
      },
      recommendations
    };

    return NextResponse.json(performanceResults);

  } catch (error) {
    console.error('Performance test error:', error);
    return NextResponse.json(
      { error: 'Failed to run performance test' },
      { status: 500 }
    );
  }
}
