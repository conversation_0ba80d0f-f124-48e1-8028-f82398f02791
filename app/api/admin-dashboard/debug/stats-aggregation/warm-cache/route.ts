import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { dailyStatsService } from '@/lib/services/daily-stats-aggregation';

export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const { dates } = await request.json();
    
    if (!dates || !Array.isArray(dates)) {
      return NextResponse.json({ error: 'Dates array is required' }, { status: 400 });
    }

    const startTime = Date.now();
    
    // Warm the cache for the specified dates
    await dailyStatsService.warmCache(dates);
    
    const executionTime = Date.now() - startTime;
    const cacheStats = dailyStatsService.getCacheStats();

    return NextResponse.json({ 
      success: true, 
      message: `Cache warmed successfully for ${dates.length} dates`,
      executionTime,
      cacheStats,
      datesWarmed: dates,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Cache warming error:', error);
    return NextResponse.json(
      { error: 'Failed to warm cache' },
      { status: 500 }
    );
  }
}
