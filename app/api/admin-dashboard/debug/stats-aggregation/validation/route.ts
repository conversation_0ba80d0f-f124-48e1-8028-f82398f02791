import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { dailyStatsService } from '@/lib/services/daily-stats-aggregation';
import dbConnect from '@/lib/db';
import Reservation from '@/models/Reservation';
import Branch from '@/models/Branch';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  try {
    // Check authentication and permissions
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const { date } = await request.json();
    
    if (!date) {
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }

    await dbConnect();

    // Get aggregated stats
    const aggregatedStats = await dailyStatsService.aggregateDailyStats(date);
    
    const errors: string[] = [];
    let totalSalesAccuracy = true;
    let branchDataConsistency = true;
    let topPerformersValidity = true;
    let dataIntegrityChecks = true;

    // Validate total sales accuracy
    try {
      const directSalesQuery = await Reservation.aggregate([
        {
          $match: {
            'preferences.visitDate': date,
            sellingAmount: { $gt: 0 },
            $or: [
              { deletedAt: null },
              { deletedAt: { $exists: false } }
            ]
          }
        },
        {
          $group: {
            _id: null,
            amount: { $sum: '$sellingAmount' },
            count: { $sum: 1 }
          }
        }
      ]);

      const directSales = directSalesQuery[0] || { amount: 0, count: 0 };
      
      if (Math.abs(directSales.amount - aggregatedStats.totalSales.amount) > 0.01) {
        totalSalesAccuracy = false;
        errors.push(`Total sales amount mismatch: Direct=${directSales.amount}, Aggregated=${aggregatedStats.totalSales.amount}`);
      }
      
      if (directSales.count !== aggregatedStats.totalSales.count) {
        totalSalesAccuracy = false;
        errors.push(`Total sales count mismatch: Direct=${directSales.count}, Aggregated=${aggregatedStats.totalSales.count}`);
      }
    } catch (error) {
      totalSalesAccuracy = false;
      errors.push('Failed to validate total sales accuracy');
    }

    // Validate branch data consistency
    try {
      const branchIds = aggregatedStats.salesByBranch.map(b => b.branchId);
      const existingBranches = await Branch.find({ 
        _id: { $in: branchIds.map(id => id) },
        $or: [
          { deletedAt: null },
          { deletedAt: { $exists: false } }
        ]
      });

      if (existingBranches.length !== branchIds.length) {
        branchDataConsistency = false;
        errors.push('Some branches in sales data do not exist in database');
      }

      // Check if branch names match
      for (const salesBranch of aggregatedStats.salesByBranch) {
        const dbBranch = existingBranches.find(b => b._id.toString() === salesBranch.branchId);
        if (dbBranch && dbBranch.name !== salesBranch.branchName) {
          branchDataConsistency = false;
          errors.push(`Branch name mismatch for ${salesBranch.branchId}: DB=${dbBranch.name}, Aggregated=${salesBranch.branchName}`);
        }
      }
    } catch (error) {
      branchDataConsistency = false;
      errors.push('Failed to validate branch data consistency');
    }

    // Validate top performers validity
    try {
      const topSellerIds = aggregatedStats.topSellers.map(s => s.userId);
      const topPapIds = aggregatedStats.topPaps.map(p => p.userId);
      
      const allUserIds = [...topSellerIds, ...topPapIds];
      const existingUsers = await User.find({ 
        _id: { $in: allUserIds },
        $or: [
          { deletedAt: null },
          { deletedAt: { $exists: false } }
        ]
      });

      if (existingUsers.length !== allUserIds.length) {
        topPerformersValidity = false;
        errors.push('Some users in top performers do not exist in database');
      }

      // Validate that top sellers have actual sales
      for (const seller of aggregatedStats.topSellers) {
        if (!seller.amount || seller.amount <= 0) {
          topPerformersValidity = false;
          errors.push(`Top seller ${seller.userName} has no sales amount`);
        }
      }

      // Validate that top PAPs have actual reservations
      for (const pap of aggregatedStats.topPaps) {
        if (!pap.count || pap.count <= 0) {
          topPerformersValidity = false;
          errors.push(`Top PAP ${pap.userName} has no reservation count`);
        }
      }
    } catch (error) {
      topPerformersValidity = false;
      errors.push('Failed to validate top performers validity');
    }

    // Data integrity checks
    try {
      // Check if total reservations >= total presence
      if (aggregatedStats.totalPresence > aggregatedStats.totalReservations) {
        dataIntegrityChecks = false;
        errors.push('Total presence cannot be greater than total reservations');
      }

      // Check if branch totals add up correctly
      const branchSalesTotal = aggregatedStats.salesByBranch.reduce((sum, branch) => sum + branch.amount, 0);
      if (Math.abs(branchSalesTotal - aggregatedStats.totalSales.amount) > 0.01) {
        dataIntegrityChecks = false;
        errors.push(`Branch sales total (${branchSalesTotal}) doesn't match total sales (${aggregatedStats.totalSales.amount})`);
      }

      const branchReservationsTotal = aggregatedStats.reservationsByBranch.reduce((sum, branch) => sum + branch.count, 0);
      if (branchReservationsTotal !== aggregatedStats.totalReservations) {
        dataIntegrityChecks = false;
        errors.push(`Branch reservations total (${branchReservationsTotal}) doesn't match total reservations (${aggregatedStats.totalReservations})`);
      }
    } catch (error) {
      dataIntegrityChecks = false;
      errors.push('Failed to run data integrity checks');
    }

    const validationResults = {
      totalSalesAccuracy,
      branchDataConsistency,
      topPerformersValidity,
      dataIntegrityChecks,
      errors
    };

    return NextResponse.json(validationResults);

  } catch (error) {
    console.error('Validation test error:', error);
    return NextResponse.json(
      { error: 'Failed to run validation test' },
      { status: 500 }
    );
  }
}
