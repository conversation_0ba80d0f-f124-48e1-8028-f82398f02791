// Debug API for testing branch stats email templates
import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { 
  generateEmailPreview, 
  generateSampleBranchStatsData,
  validateEmailTemplateData 
} from '@/lib/utils/branch-stats-email-preview';
import { BranchStatsEmailData, BranchStatsTemplateOptions } from '@/types/branch-stats-email';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
      const session = await getServerSession(authOptions);
       if (!session?.user) {
         return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
       }
   
    const { searchParams } = new URL(request.url);
    const mode = searchParams.get('mode') || 'sample';
    const format = searchParams.get('format') || 'json';

    let emailData: BranchStatsEmailData;
    let templateOptions: BranchStatsTemplateOptions = {
      includeDetailedReservations: searchParams.get('includeReservations') !== 'false',
      includeAllergiesBreakdown: searchParams.get('includeAllergies') !== 'false',
      includeServiceTypesBreakdown: searchParams.get('includeServices') !== 'false',
      compactMode: searchParams.get('compact') === 'true'
    };

    if (mode === 'sample') {
      // Generate sample data
      emailData = generateSampleBranchStatsData();
    } else {
      return NextResponse.json({ 
        error: 'Only sample mode is currently supported' 
      }, { status: 400 });
    }

    // Validate data
    const validationErrors = validateEmailTemplateData(emailData);
    if (validationErrors.length > 0) {
      return NextResponse.json({ 
        error: 'Validation failed',
        details: validationErrors 
      }, { status: 400 });
    }

    // Generate preview
    const preview = generateEmailPreview(emailData, templateOptions);

    if (format === 'html') {
      // Return HTML for direct preview
      return new NextResponse(preview.html, {
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
        },
      });
    }

    // Return JSON with metadata
    return NextResponse.json({
      success: true,
      preview: {
        subject: preview.subject,
        html: preview.html,
        metadata: preview.metadata,
        variables: preview.variables
      },
      templateOptions,
      emailData: {
        targetDate: emailData.targetDate,
        branchCount: emailData.branchStats.length,
        selectedStats: emailData.selectedStats,
        dateLogic: emailData.dateLogic
      }
    });

  } catch (error) {
    console.error('Branch stats email template test error:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
      const session = await getServerSession(authOptions);
       if (!session?.user) {
         return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
       }
   
    const body = await request.json();
    const { emailData, templateOptions, customVariables } = body;

    // Validate required data
    if (!emailData) {
      return NextResponse.json({ 
        error: 'Email data is required' 
      }, { status: 400 });
    }

    // Validate email data
    const validationErrors = validateEmailTemplateData(emailData);
    if (validationErrors.length > 0) {
      return NextResponse.json({ 
        error: 'Validation failed',
        details: validationErrors 
      }, { status: 400 });
    }

    // Generate preview with custom data
    const preview = generateEmailPreview(emailData, templateOptions, customVariables);

    return NextResponse.json({
      success: true,
      preview: {
        subject: preview.subject,
        html: preview.html,
        metadata: preview.metadata,
        variables: preview.variables
      },
      templateOptions: templateOptions || {},
      validation: {
        passed: true,
        errors: []
      }
    });

  } catch (error) {
    console.error('Branch stats email template generation error:', error);
    return NextResponse.json({ 
      error: 'Template generation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Test different template configurations
export async function PUT(request: NextRequest) {
  try {
      const session = await getServerSession(authOptions);
       if (!session?.user) {
         return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
       }
   

    const body = await request.json();
    const { testType } = body;

    let results: any[] = [];

    if (testType === 'all-configurations') {
      // Test different template configurations
      const baseData = generateSampleBranchStatsData();
      
      const configurations = [
        {
          name: 'Full Template',
          options: {
            includeDetailedReservations: true,
            includeAllergiesBreakdown: true,
            includeServiceTypesBreakdown: true,
            compactMode: false
          }
        },
        {
          name: 'Compact Mode',
          options: {
            includeDetailedReservations: false,
            includeAllergiesBreakdown: true,
            includeServiceTypesBreakdown: true,
            compactMode: true
          }
        },
        {
          name: 'Summary Only',
          options: {
            includeDetailedReservations: false,
            includeAllergiesBreakdown: false,
            includeServiceTypesBreakdown: false,
            compactMode: true
          }
        }
      ];

      for (const config of configurations) {
        const preview = generateEmailPreview(baseData, config.options);
        results.push({
          configuration: config.name,
          options: config.options,
          metadata: preview.metadata,
          subject: preview.subject
        });
      }
    }

    return NextResponse.json({
      success: true,
      testType,
      results,
      summary: {
        configurationstested: results.length,
        averageGenerationTime: results.reduce((sum, r) => sum + r.metadata.generationTime, 0) / results.length,
        averageTemplateSize: results.reduce((sum, r) => sum + r.metadata.templateSize, 0) / results.length
      }
    });

  } catch (error) {
    console.error('Branch stats email template configuration test error:', error);
    return NextResponse.json({ 
      error: 'Configuration test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
