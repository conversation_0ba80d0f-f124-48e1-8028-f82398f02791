import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import dbConnect from '@/lib/db';
import { BranchStatsAggregationService } from '@/lib/services/branch-stats-aggregation';
import { getUserRoles } from '@/app/api/utils/server-permission-utils';
import { isSuperAdmin } from '@/lib/utils/role-utils';
import Branch from '@/models/Branch';

/**
 * Debug API for Branch Stats Aggregation Service
 * 
 * This endpoint provides debugging and testing functionality for the branch stats
 * aggregation service, including date calculation, demographics, allergies, and service types.
 * 
 * GET: Test various aggregation functions
 * POST: Run performance tests and validation
 */

interface BranchStatsTestRequest {
  branchIds: string[];
  targetDate?: string;
  sendingHour?: number;
  selectedStats?: string[];
  testType: 'dateCalculation' | 'demographics' | 'allergies' | 'serviceTypes' | 'fullAggregation' | 'performance';
}

// GET: Test branch stats functionality
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user roles if not already loaded
    if (!session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }

    // Check if user is SuperAdmin
    const userIsSuperAdmin = isSuperAdmin(session.user.roles);
    if (!userIsSuperAdmin) {
      return NextResponse.json({ error: 'SuperAdmin access required' }, { status: 403 });
    }

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const testType = searchParams.get('testType') || 'dateCalculation';
    const sendingHour = parseInt(searchParams.get('sendingHour') || '14');
    const branchId = searchParams.get('branchId');
    const targetDate = searchParams.get('targetDate') || BranchStatsAggregationService.calculateTargetDate(sendingHour);

    const results = {
      timestamp: new Date().toISOString(),
      testType,
      results: {} as any
    };

    switch (testType) {
      case 'dateCalculation':
        results.results = {
          sendingHour,
          targetDate: BranchStatsAggregationService.calculateTargetDate(sendingHour),
          logic: sendingHour < 12 ? 'current day' : 'next day',
          examples: {
            morning: {
              hour: 9,
              targetDate: BranchStatsAggregationService.calculateTargetDate(9),
              logic: 'current day'
            },
            afternoon: {
              hour: 14,
              targetDate: BranchStatsAggregationService.calculateTargetDate(14),
              logic: 'next day'
            }
          }
        };
        break;

      case 'demographics':
      case 'allergies':
      case 'serviceTypes':
      case 'fullAggregation':
        if (!branchId) {
          return NextResponse.json({ error: 'branchId parameter required for this test' }, { status: 400 });
        }

        const service = new BranchStatsAggregationService();
        const selectedStats = testType === 'fullAggregation' 
          ? ['reservationCount', 'adultCount', 'childCount', 'allergies', 'serviceTypes']
          : [testType === 'demographics' ? 'adultCount' : testType];

        const aggregationResults = await service.aggregateBranchStats({
          branchIds: [branchId],
          targetDate,
          selectedStats
        });

        results.results = {
          branchId,
          targetDate,
          selectedStats,
          data: aggregationResults[0] || null
        };
        break;

      case 'performance':
        // Get all branches for performance testing
        const branches = await Branch.find({ deletedAt: null }).limit(5);
        const branchIds = branches.map(b => b._id.toString());

        const perfService = new BranchStatsAggregationService();
        const startTime = Date.now();

        const perfResults = await perfService.aggregateBranchStats({
          branchIds,
          targetDate,
          selectedStats: ['reservationCount', 'adultCount', 'childCount', 'allergies', 'serviceTypes']
        });

        const executionTime = Date.now() - startTime;

        results.results = {
          branchCount: branchIds.length,
          targetDate,
          executionTime,
          averageTimePerBranch: executionTime / branchIds.length,
          totalQueries: perfResults[0]?.executionMetrics.totalQueries || 0,
          cacheHits: perfResults[0]?.executionMetrics.cacheHits || 0,
          branches: perfResults.map(r => ({
            branchId: r.branchId,
            branchName: r.branchName,
            totalReservations: r.summary.totalReservations,
            totalAttendees: r.summary.totalAttendees
          }))
        };
        break;

      default:
        return NextResponse.json({ error: 'Invalid test type' }, { status: 400 });
    }

    return NextResponse.json(results);

  } catch (error) {
    console.error('Branch stats aggregation debug error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// POST: Run custom tests and validation
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user roles if not already loaded
    if (!session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }

    // Check if user is SuperAdmin
    const userIsSuperAdmin = isSuperAdmin(session.user.roles);
    if (!userIsSuperAdmin) {
      return NextResponse.json({ error: 'SuperAdmin access required' }, { status: 403 });
    }

    const body: BranchStatsTestRequest = await request.json();
    await dbConnect();

    // Validate branch IDs
    if (body.branchIds && body.branchIds.length > 0) {
      const validBranches = await Branch.find({ 
        _id: { $in: body.branchIds },
        deletedAt: null 
      });
      
      if (validBranches.length !== body.branchIds.length) {
        const validIds = validBranches.map(b => b._id.toString());
        const invalidIds = body.branchIds.filter(id => !validIds.includes(id));
        return NextResponse.json(
          { error: 'Invalid branch IDs', invalidBranches: invalidIds },
          { status: 400 }
        );
      }
    }

    const service = new BranchStatsAggregationService();
    const targetDate = body.targetDate || BranchStatsAggregationService.calculateTargetDate(body.sendingHour || 14);
    const selectedStats = body.selectedStats || ['reservationCount', 'adultCount', 'childCount', 'allergies', 'serviceTypes'];

    const startTime = Date.now();
    const results = await service.aggregateBranchStats({
      branchIds: body.branchIds,
      targetDate,
      selectedStats
    });
    const executionTime = Date.now() - startTime;

    return NextResponse.json({
      success: true,
      testType: body.testType,
      parameters: {
        branchIds: body.branchIds,
        targetDate,
        selectedStats,
        sendingHour: body.sendingHour
      },
      executionTime,
      results,
      summary: {
        totalBranches: results.length,
        totalReservations: results.reduce((sum, r) => sum + r.summary.totalReservations, 0),
        totalAttendees: results.reduce((sum, r) => sum + r.summary.totalAttendees, 0),
        averageExecutionTime: executionTime / results.length
      }
    });

  } catch (error) {
    console.error('Branch stats aggregation test error:', error);
    return NextResponse.json(
      { error: 'Failed to run test', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
