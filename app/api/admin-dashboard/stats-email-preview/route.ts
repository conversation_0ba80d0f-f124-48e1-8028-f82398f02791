import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import dbConnect from '@/lib/db';
import StatsEmailSettings from '@/models/StatsEmailSettings';
import { DailyStatsAggregationService } from '@/lib/services/daily-stats-aggregation';
import { getDailyStatsEmailTemplate, processStatsEmailTemplate, StatsEmailData } from '@/lib/utils/stats-email-templates';

export async function POST(req: NextRequest) {
  try {
    // Authentication check
    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    await dbConnect();

    const body = await req.json();
    const { emailSettingId, useSampleData = false } = body;

    if (!emailSettingId) {
      return NextResponse.json({ error: 'Email setting ID is required' }, { status: 400 });
    }

    // Get the email setting
    const emailSetting = await StatsEmailSettings.findById(emailSettingId);
    if (!emailSetting) {
      return NextResponse.json({ error: 'Email setting not found' }, { status: 404 });
    }

    // Ensure preview always shows both reservation types for better user experience

    let statsData;
    let emailData: StatsEmailData;

    if (useSampleData) {
      // Use sample data for preview
      // Use local timezone to avoid date shifts
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      const dateStr = `${year}-${month}-${day}`;

      emailData = {
        date: dateStr,
        totalSales: { amount: 15750.50, count: 42 },
        totalReservationsScheduled: 38,
        totalReservationsCreated: 25,
        totalPresence: 35,
        salesByBranch: [
          { branchName: 'Succursale Centre-Ville', amount: 8500.25, count: 22 },
          { branchName: 'Succursale Nord', amount: 4250.15, count: 12 },
          { branchName: 'Succursale Sud', amount: 3000.10, count: 8 }
        ],
        reservationsByBranch: [
          { branchName: 'Succursale Centre-Ville', count: 20 },
          { branchName: 'Succursale Nord', count: 10 },
          { branchName: 'Succursale Sud', count: 8 }
        ],
        presenceByBranch: [
          { branchName: 'Succursale Centre-Ville', count: 18 },
          { branchName: 'Succursale Nord', count: 9 },
          { branchName: 'Succursale Sud', count: 8 }
        ],
        topSellers: [
          { name: 'Marie Dubois', amount: 3500.75, count: 12 },
          { name: 'Jean Martin', amount: 2800.50, count: 9 },
          { name: 'Sophie Tremblay', amount: 2200.25, count: 7 },
          { name: 'Pierre Lavoie', amount: 1900.00, count: 6 },
          { name: 'Julie Gagnon', amount: 1650.75, count: 5 }
        ],
        topPaps: [
          { name: 'PAP Centre-Ville', count: 15 },
          { name: 'PAP Nord', count: 12 },
          { name: 'PAP Sud', count: 8 },
          { name: 'PAP Est', count: 6 },
          { name: 'PAP Ouest', count: 4 }
        ],
        selectedStats: (() => {
          const selectedStats = emailSetting.getSelectedStatsArray();

          // Ensure both reservation stats are included for preview
          const enhancedStats = [...selectedStats];
          if (!enhancedStats.includes('totalReservationsScheduled')) {
            enhancedStats.push('totalReservationsScheduled');
          }
          if (!enhancedStats.includes('totalReservationsCreated')) {
            enhancedStats.push('totalReservationsCreated');
          }

          return enhancedStats;
        })(),
        exportUrls: {
          csv: '#sample-csv-export',
          pdf: '#sample-pdf-export'
        }
      };
    } else {
      // Use real data from yesterday
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const dateStr = yesterday.toISOString().split('T')[0];

      const statsService = DailyStatsAggregationService.getInstance();
      statsData = await statsService.aggregateDailyStats(dateStr);

      emailData = {
        date: dateStr,
        totalSales: statsData.totalSales,
        totalReservationsScheduled: statsData.totalReservationsScheduled,
        totalReservationsCreated: statsData.totalReservationsCreated,
        totalPresence: statsData.totalPresence,
        salesByBranch: statsData.salesByBranch.map(branch => ({
          branchName: branch.branchName,
          amount: branch.amount,
          count: branch.count
        })),
        reservationsByBranch: statsData.reservationsByBranch.map(branch => ({
          branchName: branch.branchName,
          count: branch.count
        })),
        reservationsCreatedByBranch: (statsData.reservationsCreatedByBranch || []).map(branch => ({
          branchName: branch.branchName,
          count: branch.count
        })),
        presenceByBranch: statsData.presenceByBranch.map(branch => ({
          branchName: branch.branchName,
          count: branch.count
        })),
        topSellers: statsData.topSellers.map(seller => ({
          name: seller.userName,
          amount: seller.amount || 0,
          count: seller.count
        })),
        topPaps: statsData.topPaps.map(pap => ({
          name: pap.userName,
          count: pap.count
        })),
        selectedStats: (() => {
          const selectedStats = emailSetting.getSelectedStatsArray();

          // Ensure all reservation stats are included for preview
          const enhancedStats = [...selectedStats];
          if (!enhancedStats.includes('totalReservationsScheduled')) {
            enhancedStats.push('totalReservationsScheduled');
          }
          if (!enhancedStats.includes('totalReservationsCreated')) {
            enhancedStats.push('totalReservationsCreated');
          }
          if (!enhancedStats.includes('reservationsCreatedByBranch')) {
            enhancedStats.push('reservationsCreatedByBranch');
          }

          return enhancedStats;
        })(),
        exportUrls: {
          csv: `/api/admin-dashboard/stats-export/csv?date=${dateStr}&settingId=${emailSettingId}`,
          pdf: `/api/admin-dashboard/stats-export/pdf?date=${dateStr}&settingId=${emailSettingId}`
        }
      };
    }

    // Generate and process email template
    const template = getDailyStatsEmailTemplate(emailData);
    const processedTemplate = processStatsEmailTemplate(template, emailData);

    return NextResponse.json({
      success: true,
      html: processedTemplate,
      templateSize: processedTemplate.length,
      usedSampleData: useSampleData,
      selectedStatsCount: emailData.selectedStats.length,
      debug: process.env.NODE_ENV === 'development' ? {
        emailSettingId,
        selectedStats: emailData.selectedStats,
        timestamp: new Date().toISOString()
      } : undefined
    });

  } catch (error) {
    console.error('Error generating email preview:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
