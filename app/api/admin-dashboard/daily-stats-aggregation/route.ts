import { NextRequest, NextResponse } from 'next/server';
import { authenticateAdminDashboardRequest } from '@/lib/utils/admin-dashboard-auth';
import { dailyStatsService, getPreviousDay } from '@/lib/services/daily-stats-aggregation';
import Branch from '@/models/Branch';

// Rate limiting map
const rateLimiter = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(userId: string, endpoint: string): boolean {
  const key = `${userId}:${endpoint}`;
  const now = Date.now();
  const windowMs = 60000; // 1 minute
  const maxRequests = 10; // Lower limit for stats aggregation due to computational cost
  
  if (!rateLimiter.has(key)) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  const limit = rateLimiter.get(key)!;
  if (now > limit.resetTime) {
    rateLimiter.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (limit.count >= maxRequests) {
    return false;
  }
  
  limit.count++;
  return true;
}

export async function GET(req: NextRequest) {
  const startTime = Date.now();
  
  try {
    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Rate limiting
    if (auth.userId && !checkRateLimit(auth.userId, 'daily-stats-aggregation')) {
      return NextResponse.json({ error: 'Rate limit exceeded' }, { status: 429 });
    }

    const { searchParams } = new URL(req.url);
    const date = searchParams.get('date') || getPreviousDay();
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const branchId = searchParams.get('branchId');
    const branchIds = searchParams.get('branchIds');
    const includeDebug = searchParams.get('debug') === 'true';

    // Extract auth info for branch filtering
    const { userId, isSuperAdminUser, isBranchesAdminUser } = auth;
    const allBranches = branchId === 'all';

    // Determine if we're using date range or single date
    let dateToUse = date;
    let isDateRange = false;

    if (startDate && endDate) {
      // Use date range mode - for now, we'll use startDate as the primary date
      // TODO: Extend service to support date ranges
      dateToUse = startDate;
      isDateRange = true;
    }
    // Apply branch access control (same logic as reservation-stats API)
    let accessibleBranchIds: string[] = [];
    if (allBranches && !isSuperAdminUser && isBranchesAdminUser && userId) {
      const branchesResponse = await Branch.find(
        { responsible: userId, deletedAt: null },
        '_id'
      ).lean();
      accessibleBranchIds = branchesResponse.map(b => (b as any)._id.toString());

      if (accessibleBranchIds.length === 0) {
        return NextResponse.json({
          date: dateToUse,
          stats: {
            date: dateToUse,
            totalSales: { amount: 0, count: 0 },
            totalReservations: 0,
            totalPresence: 0,
            salesByBranch: [],
            reservationsByBranch: [],
            presenceByBranch: [],
            topSellers: [],
            topPaps: [],
            executionMetrics: { totalQueries: 0, executionTime: 0, cacheHits: 0 }
          },
          generatedAt: new Date().toISOString(),
          debug: includeDebug && process.env.NODE_ENV === 'development' ? {
            message: 'No accessible branches for branch admin user',
            userId,
            isSuperAdminUser,
            isBranchesAdminUser
          } : undefined
        });
      }
    }


    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(dateToUse)) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD' },
        { status: 400 }
      );
    }

    // Validate date is not in the future
    const requestedDate = new Date(dateToUse);
    const today = new Date();
    today.setHours(23, 59, 59, 999);

    if (requestedDate > today) {
      return NextResponse.json(
        { error: 'Cannot aggregate stats for future dates' },
        { status: 400 }
      );
    }

    // Prepare branch filtering options
    const branchOptions = {
      branchId,
      branchIds: branchIds ? branchIds.split(',') : undefined,
      accessibleBranchIds,
      startDate: startDate || dateToUse,
      endDate: endDate || dateToUse
    };

    // Aggregate all required statistics
    const stats = await dailyStatsService.aggregateDailyStats(dateToUse, branchOptions);
    
    const response = {
      date: dateToUse,
      stats,
      generatedAt: new Date().toISOString(),
      debug: includeDebug && process.env.NODE_ENV === 'development' ? {
        executionTime: Date.now() - startTime,
        queryCount: stats.executionMetrics?.totalQueries,
        cacheHits: stats.executionMetrics?.cacheHits,
        userId: auth.userId,
        isTokenAuth: auth.isTokenAuth,
        isDateRange,
        originalDate: date,
        startDate,
        endDate,
        branchId,
        branchIds,
        accessibleBranchIds: accessibleBranchIds.length > 0 ? accessibleBranchIds : 'N/A',
        branchOptions
      } : undefined
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error aggregating daily stats:', error);
    
    // Log additional debug info in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Stats aggregation error details:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        executionTime: Date.now() - startTime
      });
    }
    
    return NextResponse.json(
      { error: 'Failed to aggregate statistics' },
      { status: 500 }
    );
  }
}

// POST endpoint for manual stats generation (debug purposes)
export async function POST(req: NextRequest) {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Debug endpoints only available in development' }, { status: 403 });
    }

    const auth = await authenticateAdminDashboardRequest(req);
    if (!auth.isAuthenticated || !auth.isSuperAdminUser) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await req.json();
    const { date, clearCache } = body;

    if (!date) {
      return NextResponse.json(
        { error: 'Date is required' },
        { status: 400 }
      );
    }

    // Clear cache if requested
    if (clearCache) {
      // Access private cache through a method if needed
      console.log('Cache clear requested for debug stats generation');
    }

    const stats = await dailyStatsService.aggregateDailyStats(date);
    
    return NextResponse.json({
      success: true,
      date,
      stats,
      message: 'Debug stats generation completed',
      generatedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in debug stats generation:', error);
    return NextResponse.json(
      { error: 'Failed to generate debug stats' },
      { status: 500 }
    );
  }
}
