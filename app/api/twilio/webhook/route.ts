import { NextResponse } from 'next/server';
import twilio from 'twilio';
import { processMessage } from '@/app/lib/genai';
import { connectToDatabase } from '@/lib/mongodb';
import Branch from '@/models/Branch';
import { ProcessedMessageResponse, RequestDocument } from '@/app/types/request';
import mongoose from 'mongoose';
import { Db } from 'mongodb';
import { Contact } from '@/app/models/Contact';
import { createTwilioConversation } from '@/lib/conversations/createTwilioConversation';

import { formatInTimeZone } from 'date-fns-tz';
import { normalizePhoneNumber } from '@/lib/twilio';
/**
 * Finds the most recent reservation matching a normalized phone number.
 * @param db MongoDB database instance
 * @param phoneToFind Normalized phone number (digits only)
 * @returns Reservation ID as ObjectId or null if not found.
 */
async function findReservationByPhone(db: Db, phoneToFind: string): Promise<mongoose.Types.ObjectId | null> {
  try {
    // Check if the Reservation collection exists
    const collections = await db.listCollections({ name: 'reservations' }).toArray();
    if (collections.length === 0) {
      console.log('Reservations collection does not exist.');
      return null;
    }

    // Find reservation with matching phone number using the same robust method as by-phone route
    const reservations = await db.collection('reservations').aggregate([
      {
        // First, add a normalized phone field
        $addFields: {
          normalizedPhone: {
            $let: {
              vars: {
                chars: {
                  $map: {
                    input: { $range: [0, { $strLenCP: "$customerInfo.phone" }] },
                    as: "i",
                    in: { $substrCP: ["$customerInfo.phone", "$$i", 1] }
                  }
                }
              },
              in: {
                $let: {
                  vars: {
                    digitsOnly: {
                      $reduce: {
                        input: "$$chars",
                        initialValue: "",
                        in: {
                          $cond: [
                            { $in: ["$$this", ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]] },
                            { $concat: ["$$value", "$$this"] },
                            "$$value"
                          ]
                        }
                      }
                    }
                  },
                  in: {
                    $cond: [
                      { $eq: [{ $substr: ["$$digitsOnly", 0, 1] }, "1"] },
                      { $substr: ["$$digitsOnly", 1, { $subtract: [{ $strLenCP: "$$digitsOnly" }, 1] }] },
                      "$$digitsOnly"
                    ]
                  }
                }
              }
            }
          }
        }
      },
     {
      $match: {
        normalizedPhone: phoneToFind
      }
     },
      {
        // Sort by most recent first
        $sort: { createdAt: -1 }
      },
      {
        // Limit to the most recent reservation
        $limit: 1
      },
      {
        // Only return the ID field we need
        $project: {
          _id: 1
        }
      }
    ]).toArray();

    if (reservations.length > 0) {
      const reservationId = reservations[0]._id;
      console.log(`Found matching reservation: ${reservationId.toString()}`);
      return reservationId;
    }

    console.log(`No reservation found for phone: ${phoneToFind}`);
    return null;
  } catch (error) {
    console.error('Error finding reservation by phone:', error);
    return null;
  }
}

/**
 * Finds the branch and customer info associated with a reservation
 * @param db MongoDB database instance
 * @param reservationId Reservation ID as ObjectId
 * @returns Object containing branchId and customerName
 */
async function getReservationDetails(db: Db, reservationId: mongoose.Types.ObjectId): Promise<{ branchId: string | null, customerName: string | null }> {
  try {
    const reservation = await db.collection('reservations').findOne(
      { _id: reservationId },
      { projection: { branchId: 1,  "customerInfo.client1Name": 1 } }
    );
    
    // Build customer name using available fields
    let customerName = null;
    if (reservation?.customerInfo?.client1Name) {
      customerName = reservation.customerInfo.client1Name;
    }
    
    return {
      branchId: reservation?.branchId?.toString() || null,
      customerName: customerName
    };
  } catch (error) {
    console.error('Error finding details for reservation:', error);
    return { branchId: null, customerName: null };
  }
}

/**
 * Processes a message from a partner number, extracts information, finds a matching reservation,
 * and saves the request to the database.
 * @param db MongoDB database instance
 * @param messageSid Twilio message SID
 * @param normalizedFrom Normalized sender phone number
 * @param body Message body
 * @param prompt The prompt to use for processing the message
 * @param tags The tags to use for processing the message
 * @param branchId The branch ID associated with the message
 * @returns NextResponse indicating success or failure, with debug info in development.
 */
async function handleRequestMessage(db: Db, messageSid: string, normalizedFrom: string, body: string, prompt: string, tags: string[], branchId: string | null): Promise<NextResponse> {
  let aiResponseData: string | null = null;
  let dbErrorData: any = null;
  let aiErrorData: any = null;
  let parsedResponseData: ProcessedMessageResponse | null = null;
  let reservationIdFound: mongoose.Types.ObjectId | null = null;
  let dbSaveSuccess = false;

  try {
    aiResponseData = await processMessage(body, prompt, tags);

    // Save the processed message to the database
    try {
      const requestsCollection = db.collection('requests');

      // Parse the JSON response
      parsedResponseData = JSON.parse(aiResponseData) as ProcessedMessageResponse;

      // Try to find a matching reservation by phone number
      if (parsedResponseData.phoneNumber) {
        // Normalize the phone number extracted from AI *before* searching
        const extractedPhone = parsedResponseData.phoneNumber.replace(/\D/g, '');
        const phoneToFind = extractedPhone.startsWith('1') ? extractedPhone.substring(1) : extractedPhone; // Use the same logic as aggregation

        console.log(`Normalized phone number from AI to search: ${phoneToFind}`); // Add log for debugging

        // findReservationByPhone now returns ObjectId | null
        reservationIdFound = await findReservationByPhone(db, phoneToFind);
      }

      const requestDocument: RequestDocument = {
        messageSid,
        from: normalizedFrom,
        body,
        processed: parsedResponseData,
        createdAt: new Date(),
        status: "new",
        reservationId: reservationIdFound,
        branchId: branchId || undefined,
      };

      await requestsCollection.insertOne(requestDocument as any);
      console.log('Request document saved to database.');
      dbSaveSuccess = true;
      // Don't return here yet, fall through

    } catch (dbError) {
      console.error('Error saving to database:', dbError);
      dbErrorData = dbError instanceof Error ? dbError.message : String(dbError);
    }
  } catch (aiError) {
    console.error('Error processing message with AI:', aiError);
    aiErrorData = aiError instanceof Error ? aiError.message : String(aiError);
  }
  
  // Return detailed response in development, simple success in production
  if (process.env.NODE_ENV === 'development') {
    return NextResponse.json({
      success: !aiErrorData && !dbErrorData && dbSaveSuccess,
      debug: {
        aiResponse: aiResponseData,
        parsedResponse: parsedResponseData,
        foundReservationId: reservationIdFound ? reservationIdFound.toString() : null,
        dbSaveStatus: dbSaveSuccess ? 'Success' : 'Failed',
        aiError: aiErrorData,
        dbError: dbErrorData,
      }
    });
  } else {
    // Always return success to Twilio in production to acknowledge receipt
    return NextResponse.json({ success: true });
  }
}

/**
 * Checks if a normalized phone number matches any branch's primaryRequestPhone or backupRequestPhone.
 * @param fromNormalized Normalized phone number (digits only, no leading 1)
 * @returns true if a match is found, false otherwise
 */
async function isBranchRequestPhone(db: Db, fromNormalized: string): Promise<boolean> {
  // Only select phone fields for performance
  const allBranches = await db.collection('branches').find({
    $or: [
      { primaryRequestPhone: { $exists: true, $ne: '' } },
      { backupRequestPhone: { $exists: true, $ne: '' } }
    ]
  }).toArray();
  for (const b of allBranches) {
    const primaryNormalized = b.primaryRequestPhone?.replace(/\D/g, '').replace(/^1/, '');
    const backupNormalized = b.backupRequestPhone?.replace(/\D/g, '').replace(/^1/, '');
    if (fromNormalized === primaryNormalized || fromNormalized === backupNormalized) {
      return true;
    }
  }
  return false;
}

// Utility to get the branch prompt, tags, and branchId for a phone number
async function getBranchPromptAndTagsForPhone(db: Db, fromNormalized: string): Promise<{ prompt: string | null, tags: string[], branchId: string | null }> {
  const allBranches = await db.collection('branches').find({
    $or: [
      { primaryRequestPhone: { $exists: true, $ne: '' } },
      { backupRequestPhone: { $exists: true, $ne: '' } }
    ]
  }).toArray();
  for (const b of allBranches) {
    const primaryNormalized = b.primaryRequestPhone?.replace(/\D/g, '').replace(/^1/, '');
    const backupNormalized = b.backupRequestPhone?.replace(/\D/g, '').replace(/^1/, '');
    if (fromNormalized === primaryNormalized || fromNormalized === backupNormalized) {
      return { prompt: b.requestPrompt || null, tags: Array.isArray(b.requestTags) ? b.requestTags : [], branchId: b._id?.toString() || null };
    }
  }
  return { prompt: null, tags: [], branchId: null };
}

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const body = formData.get('Body') as string;
    const fromRaw = formData.get('From') as string;
    const to = formData.get('To') as string;
    const messageSid = formData.get('MessageSid') as string;

    // Verify the request is coming from Twilio
    const twilioSignature = request.headers.get('x-twilio-signature');
    if (!twilioSignature) {
      return NextResponse.json({ error: 'Missing Twilio signature' }, { status: 403 });
    }

    const url = `${process.env.NEXT_PUBLIC_APP_URL}/api/twilio/webhook`;
    const params = Object.fromEntries(formData.entries());

    // Skip signature validation in development environment
    if (process.env.NODE_ENV !== 'development') {
      const isValid = twilio.validateRequest(
        process.env.TWILIO_AUTH_TOKEN || '',
        twilioSignature,
        url,
        params
      );
  
      if (!isValid) {
        return NextResponse.json({ error: 'Invalid signature' }, { status: 403 });
      }
    }

    // Normalize 'from' robustly: remove non-digits, remove leading '1' (common for US/Canada E.164)
    const fromNormalized = fromRaw?.replace(/\D/g, '').replace(/^1/, '');
    const db = await connectToDatabase(); // sets up mongoose
    if (await isBranchRequestPhone(db, fromNormalized)) {
      console.log('Branch request phone detected');
      // Get the branch prompt, tags, and branchId
      const branchData = await getBranchPromptAndTagsForPhone(db, fromNormalized);
      const prompt = branchData.prompt;
      const tags = branchData.tags;
      const branchId = branchData.branchId;
      if (!(prompt && tags)) {
        return NextResponse.json({success: true});
      }
      return await handleRequestMessage(db, messageSid, fromRaw.trim(), body, prompt, tags, branchId);
    }
    
    // Fallback: handle messages from other numbers (e.g., send socket notification)
    // --- CONTACT LOOKUP AND CONVERSATION CREATION LOGIC ---
    // Look for contact by normalized phone (10 digits)
    let contact = await Contact.findOne({ phone: fromNormalized });
    
    // Check if there's a reservation for this phone number
    const reservationId = await findReservationByPhone(db, fromNormalized);
    let branchId = null;
    let customerName = null;
    
    // If we found a reservation, get its associated details
    if (reservationId) {
      const details = await getReservationDetails(db, reservationId);
      branchId = details.branchId;
      customerName = details.customerName;
      console.log(`Found reservation ${reservationId} with branch ${branchId} and customer ${customerName} for phone ${fromNormalized}`);
    }
    if(contact ) {
       // --- Add message persistence for existing contact ---
       const msg = {
        sid: messageSid,
        author: fromRaw.trim(),
        body: body || '',
        dateCreated: new Date().toISOString(),
        direction: 'inbound',
        archivedAt: null,
        archivedBy: [],
      };
      contact.conversation.messages = contact.conversation.messages || [];
      contact.conversation.messages.push(msg);
      contact.lastMessage = msg;
      contact.conversation.archivedBy = [];
      await contact.save();
    }
    if (!contact) {
      // Create the contact first to get the contactId
      contact = await Contact.create({
        conversation: {
          sid: '', // Will update after Twilio conversation is created
          linkedReservationId: reservationId ? reservationId.toString() : null,
          linkedBranch: branchId,
          customerName: customerName,
        },
        phone: fromNormalized,
        fullname: customerName || '',
      });
      try {
        // Normalize both numbers to E.164 format (assume US/Canada)
        let participantAddress = normalizePhoneNumber(fromRaw);
        participantAddress+="+1";
        let proxyAddress = normalizePhoneNumber(to);
        proxyAddress+="+1";
        // Create the Twilio conversation, passing contactId as attribute
        const conversation = await createTwilioConversation({
          friendlyName: `Contact ${fromNormalized}`,
          contactId: contact._id.toString(),
          participants: [
            { address: participantAddress, proxyAddress },
          ],
        });
        // Update the contact with the Twilio conversation SID
        contact.conversation.sid = conversation.sid;
        // --- Add message persistence here ---
        const msg = {
          sid: messageSid,
          author: fromRaw.trim(),
          body: body || '',
          dateCreated: new Date().toISOString(),
          direction: 'inbound',
          archivedAt: null,
          archivedBy: [],
        };
        contact.conversation.messages = contact.conversation.messages || [];
        contact.conversation.messages.push(msg);
        contact.lastMessage = msg;
        contact.conversation.archivedBy = [];
        await contact.save();
      } catch (err) {
        console.error('Failed to create Twilio conversation or contact:', err);
      }
    } 
    if (reservationId && contact && (
      !contact.conversation.linkedReservationId || 
      !contact.conversation.linkedBranch || 
      (!contact.conversation.customerName && customerName)
    )) {
      // Update existing contact with reservation/branch/customer info if not already set
      contact.conversation.linkedReservationId = reservationId.toString();
      contact.conversation.linkedBranch = branchId;
      // Update customer name if we have it and it's not already set
      if (customerName && !contact.conversation.customerName) {
        contact.conversation.customerName = customerName;
        // Also update fullname if it's empty
        if (!contact.fullname) {
          contact.fullname = customerName;
        }
      }
      contact.conversation.archivedBy = [];
      await contact.save();
      console.log(`Updated contact ${contact._id} with reservation, branch, and customer info`);
    }
    // --- END CONTACT LOGIC ---
    try {
      const notificationPayload = {
        type: 'new_message',
        data: {
          messageSid,
          from: fromRaw.trim(),
          body: body.substring(0, 50) + (body.length > 50 ? '...' : ''),
          createdAt: formatInTimeZone(new Date(), 'America/Toronto', "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        },
      };

      // Post to the socket API to emit the event
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL?.endsWith('/')?process.env.NEXT_PUBLIC_APP_URL:process.env.NEXT_PUBLIC_APP_URL+'/'}api/socket/emit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(notificationPayload),
      });
    } catch (socketError) {
      console.error('Failed to send socket notification:', socketError);
      // Continue execution even if socket notification fails
    }
    return NextResponse.json({ success: true }); // Return success for non-partner messages
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json(
      { error: 'Failed to process webhook' },
      { status: 500 }
    );
  }
} 