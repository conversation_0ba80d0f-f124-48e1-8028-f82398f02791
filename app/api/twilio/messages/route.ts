import { NextResponse } from 'next/server';
import twilio from 'twilio';
import { TwilioMessage } from '@/types/twilio';
import { connectToDatabase } from '@/lib/mongodb';
import Message from '@/models/Message';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getMessagesForPhoneNumber, normalizePhoneNumber } from '@/lib/twilio';
import { validateBranchAccess, handleBranchValidationError } from '@/lib/branch-utils';
import dbConnect from '@/lib/db';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    let phoneNumber = searchParams.get('phoneNumber');
    const showArchived = searchParams.get('showArchived') === 'true';
    const branchId = searchParams.get('branchId');
    
    // Get user from session instead of query params
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    // Return unauthorized error if no session found
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!phoneNumber) {
      return NextResponse.json({ error: 'Phone number is required' }, { status: 400 });
    }
    
    if (!branchId) {
      return NextResponse.json({ error: 'Branch ID is required' }, { status: 400 });
    }
    
    phoneNumber = normalizePhoneNumber(phoneNumber);
    
    // Connect to database
  await dbConnect();
    
    // Validate branch access and get phone number
    let branchPhone;
    try {
      branchPhone = await validateBranchAccess(branchId, userId,undefined,session);
    } catch (error) {
      const { message, status } = handleBranchValidationError(error);
      return NextResponse.json({ error: message }, { status });
    }
    
    // Get all messages for this phone number using centralized function
    const allMessages = await getMessagesForPhoneNumber(branchPhone, phoneNumber);

    // Get archive information from database
    const messageSids = allMessages.map(msg => msg.sid);
    const storedMessages = await Message.find(
      { sid: { $in: messageSids } },
      { sid: 1, archivedAt: 1, archivedBy: 1 }
    );

    // Create a map of message SIDs to archive information
    const archiveInfoMap = new Map();
    storedMessages.forEach(msg => {
      archiveInfoMap.set(msg.sid, {
        archivedAt: msg.archivedAt,
        archivedBy: msg.archivedBy || []
      });
    });

    const formattedMessages: TwilioMessage[] = allMessages.map(msg => {
      const archiveInfo = archiveInfoMap.get(msg.sid);
      const isArchivedByUser = userId && archiveInfo && archiveInfo.archivedBy && archiveInfo.archivedBy.includes(userId);
      
      // Filter out archived messages if not showing archived and the message is archived by current user
      if (!showArchived && isArchivedByUser) {
        return null;
      }
      
      return {
        sid: msg.sid,
        body: msg.body || '',
        author: msg.from || '',
        dateCreated: msg.dateCreated.toISOString(),
        direction: msg.direction as 'inbound' | 'outbound-api' | 'outbound-call' | 'outbound-reply',
        archivedAt: archiveInfo ? archiveInfo.archivedAt : null,
        archivedBy: archiveInfo ? archiveInfo.archivedBy : []
      };
    }).filter(Boolean) as TwilioMessage[];

    return NextResponse.json(formattedMessages);
  } catch (error) {
    console.error('Error fetching messages:', error);
    return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 });
  }
} 