import { NextResponse } from 'next/server';
import twilio from 'twilio';
import { TwilioMessage, TwilioConversation } from '@/types/twilio';
import { connectToDatabase } from '@/lib/mongodb';
import Reservation from '@/models/Reservation';
import Message from '@/models/Message';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { getMessagesForPhoneNumber, normalizePhoneNumber, sendSMS } from '@/lib/twilio';
import { validateBranchAccess, handleBranchValidationError } from '@/lib/branch-utils';
import Branch from '@/models/Branch';
import User from '@/models/User';
import dbConnect from '@/lib/db';

const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

// Helper function to format phone number to E.164
function formatToE164(phone: string): string {
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // If the number doesn't start with 1, add it
  const withCountryCode = digits.startsWith('1') ? digits : `1${digits}`;
  
  // Add the + prefix
  return `+${withCountryCode}`;
}

export async function GET(req: Request) {
  try {
    // Get user from session instead of query params
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    // Return unauthorized error if no session found
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse the query parameters for archived filter and branch ID
    const url = new URL(req.url);
    const showArchived = url.searchParams.get('showArchived') === 'true';
    const branchId = url.searchParams.get('branchId');
    
    // Check if branchId is provided
    if (!branchId) {
      return NextResponse.json(
        { error: 'Branch ID is required' },
        { status: 400 }
      );
    }
    
    // Connect to database
  await dbConnect();
    
    // Validate branch access and get phone number
    let branchPhone;
    try {
      branchPhone = await validateBranchAccess(branchId, userId,undefined,session);
    } catch (error) {
      const { message, status } = handleBranchValidationError(error);
      return NextResponse.json({ error: message }, { status });
    }
    const branch = await Branch.findById(branchId);
    // Get messages using branch phone as first parameter and no specific other phone as second parameter
    let messages = await getMessagesForPhoneNumber(branchPhone, undefined);
    messages = messages.filter(msg=>{
      // Filter out messages where the other phone is the branch's automated phone, requests phone, or backup requests phone
      const branchAutomatedPhone = branch?.automatedPhone;
      const requestsPhone = process.env.REQUESTS_NUMBER;
      const backupRequestsPhone = process.env.REQUESTS_NUMBER_BACKUP;

     

      const normalizedBranchPhone = "1"+normalizePhoneNumber(branchPhone);
      const normalizedAutomatedPhone = "1"+normalizePhoneNumber(branchAutomatedPhone);
      const normalizedRequestsPhone = "1"+normalizePhoneNumber(requestsPhone);
      const normalizedBackupRequestsPhone = "1"+normalizePhoneNumber(backupRequestsPhone);

      // Exclude messages where the "other" phone is one of the branch/system numbers
      // "Other" phone is the one that is NOT the branch phone in the message
      const otherPhone = msg.from === branchPhone ? msg.to : msg.from;
      const normalizedOtherPhone = "1"+normalizePhoneNumber(otherPhone);

      if (
        normalizedOtherPhone === normalizedBranchPhone ||
        normalizedOtherPhone === normalizedAutomatedPhone ||
        normalizedOtherPhone === normalizedRequestsPhone ||
        normalizedOtherPhone === normalizedBackupRequestsPhone
      ) {
        return false;
      }
      // Only include messages where:
      // - The sender and recipient are not the same
      // - The recipient is not the generic "+1" number
      // - The message status is either "delivered" (outbound) or "received" (inbound)
      const isNotSelfMessage = msg.from !== msg.to;
      const isNotGenericRecipient = msg.to !== "+1";
      const isDeliveredOrReceived = msg.status === "delivered" || msg.status === "received";
      return isNotSelfMessage && isNotGenericRecipient && isDeliveredOrReceived;
    })
   
    // Get read status and archived status from database
    const messageSids = messages.map(msg => msg.sid);
    const storedMessages = await Message.find(
      { sid: { $in: messageSids } },
      { sid: 1, isRead: 1, archivedBy: 1, archivedAt: 1 }
    );
    
    const readMessageMap = new Map(storedMessages.map(msg => [msg.sid, msg.isRead]));
    const archivedMessageMap = new Map(storedMessages.map(msg => [msg.sid, {
      archivedAt: msg.archivedAt,
      archivedBy: msg.archivedBy || []
    }]));
    
    // Group messages by phone number
    const conversationsMap = new Map<string, TwilioConversation>();

    for (const message of messages) {
      const phoneNumber = message.from;
      const existingConversation = conversationsMap.get(phoneNumber);
      const isRead = readMessageMap.get(message.sid) || false;
      const archiveInfo = archivedMessageMap.get(message.sid);
      const isArchivedByUser = userId && archiveInfo && archiveInfo.archivedBy.includes(userId);
      
      // Skip this message if it's archived by the current user and we're not showing archived
      if (!showArchived && isArchivedByUser) {
        continue;
      }

      if (existingConversation) {
        // Update existing conversation
        if (new Date(message.dateCreated) > new Date(existingConversation.lastMessage)) {
          existingConversation.lastMessage = message.dateCreated.toISOString();
        }
        if (message.direction === 'inbound' && !isRead) {
          existingConversation.unreadCount++;
        }
        // Update archived status if any message is archived by the user
        if (isArchivedByUser) {
          existingConversation.isArchived = true;
        }
      } else {
        // Create new conversation
        conversationsMap.set(phoneNumber, {
          sid: message.sid,
          phoneNumber,
          lastMessage: message.dateCreated.toISOString(),
          unreadCount: message.direction === 'inbound' && !isRead ? 1 : 0,
          isArchived: isArchivedByUser || false
        });
      }
    }

    // Filter out conversations that are archived by user if not showing archived
    if (!showArchived && userId) {
      Array.from(conversationsMap.keys()).forEach(phoneNumber => {
        const conversation = conversationsMap.get(phoneNumber);
        if (conversation && conversation.isArchived) {
          conversationsMap.delete(phoneNumber);
        }
      });
    }
    
    // Get unique normalized phone numbers from conversations
    const phoneNumbers = Array.from(conversationsMap.keys()).map(phone => normalizePhoneNumber(phone));
    
    // Use advanced aggregation pipeline to find reservations by phone number
    // This uses a robust pattern to normalize phone numbers in the database and compare
    const reservations = await Reservation.aggregate([
      {
        // First, add a normalized phone field
        $addFields: {
          normalizedPhone: {
            $let: {
              vars: {
                chars: {
                  $map: {
                    input: { $range: [0, { $strLenCP: "$customerInfo.phone" }] },
                    as: "i",
                    in: { $substrCP: ["$customerInfo.phone", "$$i", 1] }
                  }
                }
              },
              in: {
                $let: {
                  vars: {
                    digitsOnly: {
                      $reduce: {
                        input: "$$chars",
                        initialValue: "",
                        in: {
                          $cond: [
                            { $in: ["$$this", ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]] },
                            { $concat: ["$$value", "$$this"] },
                            "$$value"
                          ]
                        }
                      }
                    }
                  },
                  in: {
                    $cond: [
                      { $eq: [{ $substr: ["$$digitsOnly", 0, 1] }, "1"] },
                      { $substr: ["$$digitsOnly", 1, { $subtract: [{ $strLenCP: "$$digitsOnly" }, 1] }] },
                      "$$digitsOnly"
                    ]
                  }
                }
              }
            }
          }
        }
      },
      {
        // Then match against our normalized phone numbers
        $match: {
          normalizedPhone: { $in: phoneNumbers }
        }
      },
      {
        // Sort by most recent first for each phone number
        $sort: { createdAt: -1 }
      },
      {
        // Group by normalized phone to get the most recent reservation
        $group: {
          _id: "$normalizedPhone",
          reservation: { $first: "$$ROOT" }
        }
      },
      {
        // Project only what we need
        $project: {
          _id: "$reservation._id",
          branchId: "$reservation.preferences.branchId",
          normalizedPhone: "$reservation.normalizedPhone",
          "customerInfo.phone": "$reservation.customerInfo.phone",
          "customerInfo.client1Name": "$reservation.customerInfo.client1Name"
        }
      }
    ]);
    console.log("reservations", reservations);
    // Create a map of normalized phone number to reservation ID
    const phoneToReservationMap = new Map();
    reservations.forEach(reservation => {
      phoneToReservationMap.set(reservation['normalizedPhone'], {
        _id: reservation['_id'].toString(),
        branchId: reservation['branchId']
      });
    });
    

    conversationsMap.forEach((conversation, phoneNumber) => {
      const normalizedPhone = normalizePhoneNumber(phoneNumber);
      // Check if the reservation exists and belongs to a different branch
      if (phoneToReservationMap.has(normalizedPhone) && 
          phoneToReservationMap.get(normalizedPhone).branchId && 
          phoneToReservationMap.get(normalizedPhone).branchId !== branchId) {
       
        // Delete the conversation from the map
        conversationsMap.delete(phoneNumber);
      }
      if (phoneToReservationMap.has(normalizedPhone) ) {
        conversation.reservationId = phoneToReservationMap.get(normalizedPhone)._id;
      }
    });
    console.log("conversationsMap", conversationsMap);
    // Convert map to array and sort by last message date
    const conversations = Array.from(conversationsMap.values()).sort(
      (a, b) => new Date(b.lastMessage).getTime() - new Date(a.lastMessage).getTime()
    );     // your reservations data
    
    
   return NextResponse.json(conversations);
  } catch (error) {
    console.error('Error fetching conversations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch conversations' },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  try {
    const { phoneNumber, message, reservationId, branchId } = await req.json();

    // Get user from session
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!phoneNumber || !message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Connect to database
  await dbConnect();
    const user = session?.user;
    // Validate branch access and get phone number
    let branchPhone;
    try {
      branchPhone = await validateBranchAccess(branchId, userId,undefined,session);
    } catch (error) {
      const { message, status } = handleBranchValidationError(error);
      return NextResponse.json({ error: message }, { status });
    }

    // Format the phone number to E.164 format
    const formattedPhoneNumber = formatToE164(phoneNumber);
/*
    // Send SMS using the Messages API with branch phone as the from number
    const messageInstance = await twilioClient.messages.create({
      body: message,
      to: formattedPhoneNumber,
      from: branchPhone
    });

    // Store message in database with reference to reservation if provided
    await Message.create({
      sid: messageInstance.sid,
      body: messageInstance.body,
      from: messageInstance.from,
      to: messageInstance.to,
      dateCreated: messageInstance.dateCreated,
      direction: messageInstance.direction,
      isRead: true, // Outbound messages are always read
      reservationId: reservationId || null
    });*/
    const messageInstance = await sendSMS(branchPhone, formattedPhoneNumber, message,false,user);
    // If we have a reservationId, update the customer info to make sure phone numbers match
    if (reservationId) {
      const normalizedPhone = normalizePhoneNumber(phoneNumber);
      
      // Find the reservation
      const reservation = await Reservation.findById(reservationId);
      
      // If the reservation exists and phone numbers don't match, update it
      if (reservation && normalizePhoneNumber(reservation.customerInfo.phone) !== normalizedPhone) {
        await Reservation.findByIdAndUpdate(
          reservationId,
          { 'customerInfo.phone': phoneNumber }
        );
      }
    }


    return NextResponse.json(messageInstance);
  } catch (error) {
    console.error('Error sending message:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to send message' },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const { phoneNumber, branchId } = await req.json();

    // Get user from session
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!phoneNumber) {
      return NextResponse.json(
        { error: 'Missing phone number' },
        { status: 400 }
      );
    }

    // Connect to database
  await dbConnect();

    // Validate branch access and get phone number
    let branchPhone;
    try {
      branchPhone = await validateBranchAccess(branchId, userId,undefined,session);
    } catch (error) {
      const { message, status } = handleBranchValidationError(error);
      return NextResponse.json({ error: message }, { status });
    }

    // Get all messages for this phone number
    const messages = await getMessagesForPhoneNumber(branchPhone, phoneNumber);

    // Update read status in database for each message
    await Promise.all(
      messages.map(async (message) => {
        try {
          // Find and update the message in the database
          await Message.findOneAndUpdate(
            { sid: message.sid },
            { $set: { isRead: true } },
            { upsert: true }
          );
        } catch (error) {
          console.error(`Error updating message ${message.sid}:`, error);
        }
      })
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error marking messages as read:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to mark messages as read' },
      { status: 500 }
    );
  }
}

// Add PATCH endpoint to archive a conversation's messages
export async function PATCH(req: Request) {
  try {
    const { phoneNumber, archive, branchId } = await req.json();
    
    // Get user from session instead of request body
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    if (!phoneNumber) {
      return NextResponse.json(
        { error: 'Missing phone number' },
        { status: 400 }
      );
    }

    // Connect to database
  await dbConnect();

    // Validate branch access and get phone number
    let branchPhone;
    try {
      branchPhone = await validateBranchAccess(branchId, userId,undefined,session);
    } catch (error) {
      const { message, status } = handleBranchValidationError(error);
      return NextResponse.json({ error: message }, { status });
    }

    // Get messages for this phone number using centralized function
    const twilioMessages = await getMessagesForPhoneNumber(branchPhone, phoneNumber);

    // Get all message SIDs
    const allMessageSids = twilioMessages.map(msg => msg.sid);

    // Get current time
    const now = new Date();

    if (archive) {
      // Archive conversations by updating documents in the messages collection
      await Message.updateMany(
        { sid: { $in: allMessageSids } },
        { 
          $set: { archivedAt: now },
          $addToSet: { archivedBy: userId }
        }
      );
    } else {
      // Unarchive conversations by removing the current user from archivedBy
      await Message.updateMany(
        { sid: { $in: allMessageSids } },
        { 
          $pull: { archivedBy: userId }
        }
      );
      
      // If archivedBy becomes empty, remove archivedAt as well
      await Message.updateMany(
        { sid: { $in: allMessageSids }, archivedBy: { $size: 0 } },
        { $set: { archivedAt: null } }
      );
    }

    return NextResponse.json({ 
      success: true,
      action: archive ? 'archived' : 'unarchived',
      phoneNumber
    });
  } catch (error) {
    console.error('Error archiving conversation:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to archive conversation' },
      { status: 500 }
    );
  }
} 