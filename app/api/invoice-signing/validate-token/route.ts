import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Invoice from '@/models/Invoice';
import InvoiceSigningToken from '@/models/InvoiceSigningToken';
import { InvoiceAuditLogger } from '@/lib/utils/audit-utils';
import { isValidStatusTransition } from '@/lib/utils/invoice-status-utils';

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    // Find the token in the database
    const signingToken = await InvoiceSigningToken.findOne({ token });

    if (!signingToken) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 404 }
      );
    }

    // Check if this is the first visit
    const isFirstVisit = !signingToken.firstVisitedAt;

    // Fetch the invoice data first to check status
    const invoice = await Invoice.findById(signingToken.invoiceId)
      .populate('userId', 'firstName lastName email');

    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    // If it's the first visit, update the token with firstVisitedAt and potentially update invoice status
    if (isFirstVisit) {
      const now = new Date();

      signingToken.firstVisitedAt = now;
      await signingToken.save();

      // If invoice status is "verifie", automatically set it to "envoye" on first visit
      if (invoice.status === 'verifie' && isValidStatusTransition('verifie', 'envoye')) {
        const oldStatus = invoice.status;

        // Update invoice status to "envoyé"
        invoice.addStatusChange('envoye', undefined, true, 'Invoice signing page first visited');
        await invoice.save();

        // Log audit event for status change (no session for public signing)
        try {
          await InvoiceAuditLogger.logInvoiceStatusChanged(
            request,
            invoice._id.toString(),
            invoice.invoiceNumber,
            oldStatus,
            'envoye',
            null // No session for public signing
          );
        } catch (auditError) {
          console.error('Failed to log status change audit:', auditError);
          // Don't fail the request if audit logging fails
        }
      }
    }

    // Check if the invoice is signed (either in token or invoice document)
    const isSigned = !!signingToken.signedAt || !!invoice.signedAt;
    const signatureData = signingToken.signatureData || invoice.signatureData;

    return NextResponse.json({
      success: true,
      invoice,
      tokenInfo: {
        userId: signingToken.userId,
        invoiceId: signingToken.invoiceId,
        firstVisitedAt: signingToken.firstVisitedAt,
        isSigned: isSigned,
        signedAt: signingToken.signedAt || invoice.signedAt,
        signatureData: signatureData,
        isExpired: false // Tokens never expire
      }
    });

  } catch (error: any) {
    console.error('Error validating signing token:', error);
    return NextResponse.json(
      {
        error: 'Failed to validate token',
        details: error.message
      },
      { status: 500 }
    );
  }
}