import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import InvoiceSigningToken from '@/models/InvoiceSigningToken';
import Invoice from '@/models/Invoice';
import InvoiceItem from '@/models/InvoiceItem';
import { generateInvoicePDFDataURL } from '@/lib/utils/html-pdf-utils';
import * as BillingUtils from '@/lib/utils/billing-utils';
import TaxType from '@/models/TaxType';

// Define invoice interface to help with TypeScript
interface InvoiceDocument {
  _id: string;
  invoiceNumber: string;
  title?: string;
  date: Date;
  dueDate?: Date;
  status: string;
  tps: number;
  tvq: number;
  manualItems?: string[];
  userId: {
    _id: string;
    firstName?: string;
    lastName?: string;
    name?: string;
    email?: string;
    address?: string;
    city?: string;
    province?: string;
    postalCode?: string;
    phone?: string;
  };
}

// Define invoice item interface for PDF generation
interface InvoiceItemForPDF {
  _id: string;
  title: string;
  quantity: number;
  date: string | Date;
  unitPrice: number;
  taxable: boolean;
}

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    // Find the token in the database
    const signingToken = await InvoiceSigningToken.findOne({ token });

    if (!signingToken) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 404 }
      );
    }

    // Get the invoice and related data
    const rawInvoiceData = await Invoice.findById(signingToken.invoiceId)
      .populate('userId')
      .lean();

    if (!rawInvoiceData) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    // Get invoice with calculated totals
    const invoiceData = await BillingUtils.getInvoiceWithCalculatedTotals(rawInvoiceData, true);

    // Cast to our interface to fix TypeScript errors
    const invoice = invoiceData as unknown as InvoiceDocument;

    // Use the items from the calculated invoice if available, otherwise fetch them
    let invoiceItems: InvoiceItemForPDF[] = [];
    let invoiceItemsData = invoiceData.items || [];

    if (!invoiceItemsData || invoiceItemsData.length === 0) {
      if (invoice.manualItems && invoice.manualItems.length > 0) {
        // If invoice has manualItems, fetch them by their IDs
        invoiceItemsData = await InvoiceItem.find({
          _id: { $in: invoice.manualItems }
        }).lean();
      } else {
        // Try the old way as fallback
        invoiceItemsData = await InvoiceItem.find({
          invoiceId: signingToken.invoiceId
        }).lean();
      }
    }

    if (!invoiceItemsData || invoiceItemsData.length === 0) {
      // Create a default item if none found
      invoiceItems = [{
        _id: "default-item",
        title: invoice.title || "Invoice",
        quantity: 1,
        date: invoice.date,
        unitPrice: invoiceData.total || 0,
        taxable: false
      }];
    } else {
      // Transform the raw MongoDB items to our expected format
      invoiceItems = invoiceItemsData.map((item: any) => ({
        _id: item._id.toString(),
        title: item.title || "Item",
        quantity: item.quantity || 1,
        date: item.date || invoice.date,
        unitPrice: item.unitPrice || 0,
        taxable: item.taxable || false
      }));
    }

    // Get tax type for the invoice
    const taxType = await TaxType.findById(invoiceData.taxTypeId).lean();
    if (!taxType) {
      return NextResponse.json(
        { error: 'Tax type not found for invoice' },
        { status: 404 }
      );
    }

    // Prepare invoice details for HTML PDF generation
    const user = invoice.userId;

    // Calculate subtotal and total
    const subtotal = BillingUtils.calculateSubtotal(invoiceItems);
    const total = BillingUtils.calculateGrandTotalWithTaxType(invoiceItems, taxType);

    const invoiceDetails = {
      invoiceNumber: invoice.invoiceNumber,
      date: invoice.date,
      dueDate: invoice.dueDate,
      status: invoice.status,
      total,
      subtotal,
      signedAt: invoice.signedAt, // Include signedAt for proper signature date display
      signedFromIP: invoice.signedFromIP, // Include IP address for signature display
      issuerInfo: {
        name: (user as any).companyName || `${user.firstName || user.name || 'Client'}`,
        address: user.address,
        city: user.city,
        province: user.province,
        postalCode: user.postalCode,
        phone: user.phone
      },
      // Include tax registration numbers if available
      tpsRegistrationNumber: invoiceData.tpsRegistrationNumber,
      qstRegistrationNumber: invoiceData.qstRegistrationNumber,
    };

    // Company info
     const companyInfo = {
      name: "Alimentation Mon Quartier",
      address: "200 rue Principale, local 8",
      city: "St-Sauveur",
      province: "Québec",
      postalCode: "J0R 1R0",
      phone: "**************",
      email: "<EMAIL>",
    };

    // Generate PDF using HTML template
    const pdfBase64 = await generateInvoicePDFDataURL(
      invoiceItems,
      invoiceDetails,
      companyInfo,
      taxType,
      false // swapIssuerAndClient
    );

    return NextResponse.json({
      success: true,
      pdfBase64,
      fileName: `Invoice_${invoice.invoiceNumber}_Preview.pdf`
    });

  } catch (error: any) {
    console.error('Error generating PDF preview:', error);
    return NextResponse.json(
      {
        error: 'Failed to generate PDF preview',
        details: error.message
      },
      { status: 500 }
    );
  }
}