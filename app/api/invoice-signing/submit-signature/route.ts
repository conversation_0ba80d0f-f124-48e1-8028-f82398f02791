import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import InvoiceSigningToken from '@/models/InvoiceSigningToken';
import Invoice from '@/models/Invoice';
import { InvoiceAuditLogger } from '@/lib/utils/audit-utils';

// Utility function to extract client IP address
function getClientIP(request: NextRequest): string {
  // Check various headers for the real IP address
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip'); // Cloudflare

  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Fallback to unknown if no IP can be determined
  return 'unknown';
}

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { token, signatureData } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    if (!signatureData) {
      return NextResponse.json(
        { error: 'Signature data is required' },
        { status: 400 }
      );
    }

    // Find the token in the database
    const signingToken = await InvoiceSigningToken.findOne({ token });

    if (!signingToken) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 404 }
      );
    }

    // Check if the invoice has already been signed
    if (signingToken.signedAt) {
      return NextResponse.json(
        { error: 'Invoice has already been signed' },
        { status: 400 }
      );
    }

    // Get the current timestamp and client IP
    const now = new Date();
    const clientIP = getClientIP(request);

    // Save the signature data to the token document
    signingToken.signedAt = now;
    signingToken.signatureData = signatureData;
    signingToken.signedFromIP = clientIP;
    await signingToken.save();

    // Also update the Invoice document to mark it as signed
    const invoice = await Invoice.findById(signingToken.invoiceId);
    if (invoice) {
      const oldStatus = invoice.status;
      invoice.signedAt = now;
      invoice.signatureData = signatureData;
      invoice.signedFromIP = clientIP;

      // Fix any existing contre-facture with invalid status before saving
      invoice.fixContreFactureStatus();

      // Update status to "Signé" after successful signing, but only if not already "en_retard"
      if (oldStatus !== 'en_retard') {
        invoice.addStatusChange('signe', undefined, false, 'Invoice signed');
        await invoice.save();

        // Log audit event for status change (no session for public signing)
        await InvoiceAuditLogger.logInvoiceStatusChanged(
          request,
          invoice._id.toString(),
          invoice.invoiceNumber,
          oldStatus,
          'signe',
          null // No session for public signing
        );
      } else {
        // For "en_retard" invoices, just save the signature data without changing status
        await invoice.save();
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Signature submitted successfully',
      tokenInfo: {
        userId: signingToken.userId,
        invoiceId: signingToken.invoiceId,
        firstVisitedAt: signingToken.firstVisitedAt,
        signedAt: signingToken.signedAt,
        signedFromIP: signingToken.signedFromIP,
        signatureData: signingToken.signatureData,
        isExpired: false // Tokens never expire
      }
    });

  } catch (error: any) {
    console.error('Error submitting signature:', error);
    return NextResponse.json(
      {
        error: 'Failed to submit signature',
        details: error.message
      },
      { status: 500 }
    );
  }
}