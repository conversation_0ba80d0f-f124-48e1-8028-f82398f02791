import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import InvoiceSigningToken from '@/models/InvoiceSigningToken';
import Invoice from '@/models/Invoice';
import InvoiceItem from '@/models/InvoiceItem';
import * as BillingUtils from '@/lib/utils/billing-utils';
import TaxType from '@/models/TaxType';

// Default company info
const DEFAULT_COMPANY_INFO = {
      name: "Alimentation Mon Quartier",
      address: "200 rue Principale, local 8",
      city: "St-Sauveur",
      province: "Québec",
      postalCode: "J0R 1R0",
      phone: "**************",
      email: "<EMAIL>",
    };

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Token is required' },
        { status: 400 }
      );
    }

    // Find the token in the database
    const signingToken = await InvoiceSigningToken.findOne({ token });

    if (!signingToken) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 404 }
      );
    }

    // Get the invoice and related data
    const rawInvoiceData = await Invoice.findById(signingToken.invoiceId)
      .populate('userId')
      .lean();

    if (!rawInvoiceData) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      );
    }

    // Get invoice with calculated totals
    const invoiceData = await BillingUtils.getInvoiceWithCalculatedTotals(rawInvoiceData, true);

    // Fetch tax type for dynamic taxes
    let taxType = { code: 'NO_TAX', names: [], percentages: [] };
    if (invoiceData.taxTypeId) {
      const foundTaxType = await TaxType.findById(invoiceData.taxTypeId).lean();
      if (foundTaxType && !Array.isArray(foundTaxType)) {
        taxType = {
          code: foundTaxType.code,
          names: foundTaxType.names,
          percentages: foundTaxType.percentages
        };
      }
    } else {
      // fallback to no_tax if available
      const noTaxType = await TaxType.findOne({ code: 'no_tax' }).lean();
      if (noTaxType && !Array.isArray(noTaxType)) {
        taxType = {
          code: noTaxType.code,
          names: noTaxType.names,
          percentages: noTaxType.percentages
        };
      }
    }

    // Format invoice data
    const invoice = {
      _id: invoiceData._id.toString(),
      invoiceNumber: invoiceData.invoiceNumber,
      title: invoiceData.title || 'Invoice',
      date: invoiceData.date,
      dueDate: invoiceData.dueDate,
      status: invoiceData.status,
      total: invoiceData.total || 0,
      tps: invoiceData.tps || 0,
      tvq: invoiceData.tvq || 0,
      subtotal: invoiceData.subtotal || 0,
      clientInfo: {
        name: (invoiceData.userId as any).companyName || `${invoiceData.userId.firstName || invoiceData.userId.name || 'Client'}`,
        address: invoiceData.userId.address,
        city: invoiceData.userId.city,
        province: invoiceData.userId.province,
        postalCode: invoiceData.userId.postalCode,
        phone: invoiceData.userId.phone
      },
      taxType,
      // Include tax registration numbers if available
      tpsRegistrationNumber: invoiceData.tpsRegistrationNumber,
      qstRegistrationNumber: invoiceData.qstRegistrationNumber,
      // Include signature information if signed
      signedAt: invoiceData.signedAt,
      signedFromIP: invoiceData.signedFromIP,
    };

    // Get invoice items from the provided items or fetch them if not included
    let invoiceItemsData = invoiceData.items || [];

    if (!invoiceItemsData || invoiceItemsData.length === 0) {
      if (invoiceData.manualItems && invoiceData.manualItems.length > 0) {
        // If invoice has manualItems, fetch them by their IDs
        invoiceItemsData = await InvoiceItem.find({
          _id: { $in: invoiceData.manualItems }
        }).lean();
      } else {
        // Try the old way as fallback
        invoiceItemsData = await InvoiceItem.find({
          invoiceId: signingToken.invoiceId
        }).lean();
      }
    }

    // Format invoice items
    const items = invoiceItemsData.length > 0
      ? invoiceItemsData.map((item: any) => ({
          _id: item._id.toString(),
          title: item.title || "Item",
          quantity: item.quantity || 1,
          date: item.date || invoice.date,
          unitPrice: item.unitPrice || 0,
          taxable: item.taxable || false,
          // Calculate tps and tvq for each item using billing utils
          tps: BillingUtils.calculateItemTps(item, invoice.tps),
          tvq: BillingUtils.calculateItemTvq(item, invoice.tvq, invoice.tps),
          total: BillingUtils.calculateItemTotal(item, invoice.tps, invoice.tvq)
        }))
      : [{
          _id: "default-item",
          title: invoice.title || "Invoice",
          quantity: 1,
          date: invoice.date,
          unitPrice: invoice.total || 0,
          taxable: false,
          tps: 0,
          tvq: 0,
          total: invoice.total || 0
        }];

    return NextResponse.json({
      success: true,
      invoice,
      items,
      companyInfo: DEFAULT_COMPANY_INFO,
      tokenInfo: {
        isSigned: signingToken.isSigned,
        signedAt: signingToken.signedAt,
        signedFromIP: signingToken.signedFromIP,
        signatureData: signingToken.signatureData,
        firstVisitedAt: signingToken.firstVisitedAt,
        expiresAt: signingToken.expiresAt
      }
    });

  } catch (error: any) {
    console.error('Error fetching invoice data:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch invoice data',
        details: error.message
      },
      { status: 500 }
    );
  }
}