import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ReservationStatus from '@/models/ReservationStatus';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { isBranchesAdmin, isBranchesAgent, isSeller, isSuperAdmin } from '@/lib/utils/role-utils';
import { canUserViewPhoneReservationStatuses, canUserViewReservationStatuses, canUserViewSellerReservationStatuses } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { clearExcludedStatusCodesCache } from '@/lib/utils/reservation-status-utils';
export async function GET() {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    if(session &&!session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
    }
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const statuses = await ReservationStatus.find().sort({ order: 1 }).lean();
    const filteredStatuses = statuses.filter(status => {
      if(isSuperAdmin(session) || isBranchesAgent(session) || isBranchesAdmin(session)) {
        return true;
      }
      let allowedCodes:Set<string>=new Set();
      if(canUserViewPhoneReservationStatuses(session.user)&& status.code in ['new', 'pasreponse', 'recontact', 'present', 'wrongnumbe', 'canceled','sales','3times']) {
        allowedCodes.add(status.code);
      }
      if(canUserViewSellerReservationStatuses(session.user) && ['confirmed', 'sales', 'en-suivi', 'non-vendu'].includes(status.code)) {
        allowedCodes.add(status.code);
      }
      if(canUserViewReservationStatuses(session.user)) {
        allowedCodes.add(status.code);
      }
      return allowedCodes.has(status.code);
    });
    return NextResponse.json(filteredStatuses);
  } catch (error) {
    console.error('GET reservation statuses error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reservation statuses' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    if(session &&!session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
    }
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.name_en || !body.code) {
      return NextResponse.json(
        { error: 'Name, English name and code are required' },
        { status: 400 }
      );
    }

    // Create new status
    const status = await ReservationStatus.create({
      name: body.name,
      name_en: body.name_en,
      code: body.code.toLowerCase(),
      color: body.color || '#6E6E6E',
      order: body.order || 0,
      excludeFromAffectations: body.excludeFromAffectations || false,
      isSalesStatus: body.isSalesStatus || false,
      isPresenceStatus: body.isPresenceStatus || false,
      isRecontactStatus: body.isRecontactStatus || false
    });

    // Clear cache since status configuration changed
    clearExcludedStatusCodesCache();

    return NextResponse.json(status);
  } catch (error: any) {
    console.error('Create reservation status error:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A reservation status with this name or code already exists' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to create reservation status' },
      { status: 500 }
    );
  }
} 