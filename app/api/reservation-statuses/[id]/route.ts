import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ReservationStatus from '@/models/ReservationStatus';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { isValidObjectId } from 'mongoose';
import { clearExcludedStatusCodesCache } from '@/lib/utils/reservation-status-utils';

export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;
    
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid reservation status ID' },
        { status: 400 }
      );
    }

    const status = await ReservationStatus.findById(id).lean();
    
    if (!status) {
      return NextResponse.json(
        { error: 'Reservation status not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(status);
  } catch (error) {
    console.error('GET reservation status error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reservation status' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = await context.params;
    
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid reservation status ID' },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.name_en || !body.code) {
      return NextResponse.json(
        { error: 'Name, English name and code are required' },
        { status: 400 }
      );
    }

    const updatedStatus = await ReservationStatus.findByIdAndUpdate(
      id,
      {
        name: body.name,
        name_en: body.name_en,
        code: body.code.toLowerCase(),
        color: body.color || '#6E6E6E',
        order: body.order || 0,
        excludeFromAffectations: body.excludeFromAffectations || false,
        isSalesStatus: body.isSalesStatus || false,
        isPresenceStatus: body.isPresenceStatus || false,
        isRecontactStatus: body.isRecontactStatus || false
      },
      { new: true, runValidators: true }
    ).lean();

    if (!updatedStatus) {
      return NextResponse.json(
        { error: 'Reservation status not found' },
        { status: 404 }
      );
    }

    // Clear cache since status configuration changed
    clearExcludedStatusCodesCache();

    return NextResponse.json(updatedStatus);
  } catch (error: any) {
    console.error('Update reservation status error:', error);
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'A reservation status with this name or code already exists' },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to update reservation status' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;
    
    await dbConnect();
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: 'Invalid reservation status ID' },
        { status: 400 }
      );
    }

    const deletedStatus = await ReservationStatus.findByIdAndDelete(id);

    if (!deletedStatus) {
      return NextResponse.json(
        { error: 'Reservation status not found' },
        { status: 404 }
      );
    }

    // Clear cache since status configuration changed
    clearExcludedStatusCodesCache();

    return NextResponse.json({ message: 'Reservation status deleted successfully' });
  } catch (error) {
    console.error('Delete reservation status error:', error);
    return NextResponse.json(
      { error: 'Failed to delete reservation status' },
      { status: 500 }
    );
  }
} 