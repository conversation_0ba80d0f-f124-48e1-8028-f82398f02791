import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { Contact } from '@/app/models/Contact';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
  await dbConnect();
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const q = searchParams.get('q')?.trim();
    const skip = (page - 1) * limit;
    // Build query
    const query: Record<string, any> = {};
    if (q) {
      const phoneDigits = q.replace(/\D/g, '');
      query['$or'] = [
        { fullname: { $regex: q, $options: 'i' } },
        ...(phoneDigits ? [{ phone: { $regex: phoneDigits, $options: 'i' } }] : [])
      ];
    }
    const total = await Contact.countDocuments(query);
    const contacts = await Contact.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();
    const pages = Math.ceil(total / limit);
    return NextResponse.json({
      contacts,
      total,
      page,
      pages,
      limit,
      hasNextPage: page < pages,
      hasPrevPage: page > 1
    });
  } catch (error: any) {
    return NextResponse.json({ error: error.message || 'Failed to fetch contacts' }, { status: 500 });
  }
} 