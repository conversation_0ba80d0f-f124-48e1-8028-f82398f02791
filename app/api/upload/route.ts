import { NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const type = formData.get('type') as string;
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Déterminer le dossier de destination
    const uploadDir = type === 'checkSpecimen' 
      ? 'public/uploads/specimencheque'
      : 'public/uploads/autresdocuments';

    // Générer un nom de fichier unique
    const uniqueFilename = `${Date.now()}-${file.name}`;
    const filePath = path.join(process.cwd(), uploadDir, uniqueFilename);

    // Convertir le fichier en buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Écrire le fichier
    await writeFile(filePath, buffer);

    // Retourner le chemin relatif du fichier
    const relativePath = path.join(uploadDir.replace('public', ''), uniqueFilename);

    return NextResponse.json({ 
      success: true,
      path: relativePath.replace(/\\/g, '/') 
    });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}