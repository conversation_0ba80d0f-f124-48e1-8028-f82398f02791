import { NextResponse } from 'next/server';
import mongoose from 'mongoose';
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { ObjectId } from "mongodb";
import Reservation from "@/models/Reservation";
import { canUserViewAssignedReservations, canUserViewOwnBranchReservations, canUserViewOwnReservations, canUserViewPhoneReservationStatuses, canUserViewAllReservations, canUserViewReservationStatuses, doesUserHaveReservationAccess, canUserViewOnlyOwnReservations, canUserViewOnlyAssignedReservations, canUserViewSellerReservationStatuses } from '@/lib/utils/permissions-utils';
import ReservationStatus from '@/models/ReservationStatus';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { convertDateToTimezone, createDateRange } from '@/lib/utils/timezone-utils';
import dbConnect  from '@/lib/db';

export async function GET(request: Request) {
  try {
     const db =( await dbConnect()).connection;

    const { searchParams } = new URL(request.url);

    // Get the user session
    const session = await getServerSession(authOptions);
    
    if(session && !session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
    }

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a SuperAdmin
    const isSuperAdmin = session.user.roles?.some((role: any) =>
      (typeof role === 'object' ? role._id?.toString() === '67add3214badd3283e873329' : role?.toString() === '67add3214badd3283e873329')
    );


    if(!doesUserHaveReservationAccess(session.user)){
      return NextResponse.json({ totalCount: 0 });
    }



    // Get all the same filtering parameters as the main API
    const status = searchParams.get('status');
    const statusCodes = searchParams.get('statusCodes');
    const type = searchParams.get('type');
    const search = searchParams.get('search');
    const includeDeleted = searchParams.get('includeDeleted') === 'true';
    const branchId = searchParams.get('branchId');
    const assignedTo = searchParams.get('assignedTo');
    const createdAtDate = searchParams.get('createdAtDate');
    const createdAtStartDate = searchParams.get('createdAtStartDate');
    const createdAtEndDate = searchParams.get('createdAtEndDate');
    const partnerId = searchParams.get('partnerId');
    const visitTime = searchParams.get('visitTime');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const allDates = searchParams.get('allDates') === 'true';
    const clientTimezone = searchParams.get('timezone') || 'America/Toronto'; // Default to Quebec timezone

    //---Branch Filter---
    const branchCollection = db.collection('branches');
    let accessibleBranchIds: string[] = [];
    if (canUserViewAllReservations(session.user) || canUserViewOnlyOwnReservations(session.user) || isSuperAdmin) {
      accessibleBranchIds = (await branchCollection.find({}).project({ _id: 1 }).toArray()).map(branch => branch._id.toString());
    } else if (canUserViewOwnBranchReservations(session.user) || canUserViewAssignedReservations(session.user)) {
      const userBranches = await branchCollection.find({
        $or: [
          { responsible: new ObjectId(session.user.id) },
          { agents: new ObjectId(session.user.id) },
          { sellers: new ObjectId(session.user.id) }
        ]
      }).toArray();
      accessibleBranchIds = userBranches.map(branch => branch._id.toString());
    }

    // Build the same pipeline as the main API but only for counting
    const pipeline: any[] = [];

    // Add search filter
    if (search) {
      const searchRegex = new RegExp(search, 'i');
      pipeline.push({
        $match: {
          $or: [
            { 'customerInfo.client1Name': searchRegex },
            { 'customerInfo.client2Name': searchRegex },
            { 'customerInfo.phone': searchRegex },
            { 'customerInfo.email': searchRegex },
          ]
        }
      });
    }

    pipeline.push(
      // Add a field to extract the date part from visitDate for filtering
      {
        $addFields: {
          visitDateOnly: {
            $substr: [
              "$preferences.visitDate",
              0,
              10
            ]
          }
        }
      },
      // Match by date if date range is provided and allDates is false
      ...((!allDates && (startDate || endDate)) ? [{
        $match: {
          $and: [
            ...(startDate ? [{ 'visitDateOnly': { $gte: startDate } }] : []),
            ...(endDate ? [{ 'visitDateOnly': { $lte: endDate } }] : [])
          ]
        }
      }] : []),
      // Match by delete status
      {
        $match: includeDeleted
          ? { isDeleted: true }
          : { isDeleted: { $ne: true }}
      },
      // Lookup reservation statuses
      {
        $lookup: {
          from: 'reservationstatuses',
          localField: 'status',
          foreignField: '_id',
          as: 'statusObject'
        }
      },
      // Add a field to track the status code for easier filtering
      {
        $addFields: {
          statusCode: {
            $cond: {
              if: { $gt: [{ $size: '$statusObject' }, 0] },
              then: { $arrayElemAt: ['$statusObject.code', 0] },
              else: '$status'
            }
          }
        }
      }
    );

    // Apply all the same filters as the main API
    // Branch filter
    if (accessibleBranchIds.length > 0) {
      if (branchId && branchId !== 'all') {
        const requestedBranchIds = branchId.split(',');
        const filteredBranchIds = requestedBranchIds.filter(id =>
          accessibleBranchIds.includes(id)
        );

        if (filteredBranchIds.length > 0) {
          const objectIds = filteredBranchIds.map(id => new ObjectId(id));
          pipeline.push({
            $match: {
              $or: [
                { 'preferences.branchId': { $in: filteredBranchIds } },
                { 'preferences.branchId': { $in: objectIds } }
              ]
            }
          });
        } else {
          pipeline.push({
            $match: { 'preferences.branchId': 'non-existent-id' }
          });
        }
      } else {
        pipeline.push({
          $match: {
            $or: [
              { 'preferences.branchId': { $in: accessibleBranchIds } },
              { 'preferences.branchId': { $in: accessibleBranchIds.map(id => new ObjectId(id)) } }
            ]
          }
        });
      }
    }

    // Creation date filtering (with timezone adjustment)
    if (createdAtDate) {
      const { startOfDay, endOfDay } = createDateRange(createdAtDate, clientTimezone);

      pipeline.push({
        $match: {
          createdAt: {
            $gte: startOfDay,
            $lte: endOfDay
          }
        }
      });
    }

    // Creation date range filter (with timezone adjustment)
    if (createdAtStartDate || createdAtEndDate) {
      const createdAtMatch: any = {};
      if (createdAtStartDate) {
        // Handle both ISO datetime strings and YYYY-MM-DD date strings
        const startDateStr = createdAtStartDate.includes('T')
          ? createdAtStartDate.split('T')[0]
          : createdAtStartDate;
        createdAtMatch.$gte = convertDateToTimezone(startDateStr, false, clientTimezone);
      }
      if (createdAtEndDate) {
        // Handle both ISO datetime strings and YYYY-MM-DD date strings
        const endDateStr = createdAtEndDate.includes('T')
          ? createdAtEndDate.split('T')[0]
          : createdAtEndDate;
        createdAtMatch.$lte = convertDateToTimezone(endDateStr, true, clientTimezone);
      }
      pipeline.push({
        $match: { createdAt: createdAtMatch }
      });
    }

    // Assigned user filter (handle comma-separated values)
    if (assignedTo) {
      const assignedToIds = assignedTo.split(',').map(id => new ObjectId(id));
      pipeline.push({
        $match: {
          assigned_user_id: assignedToIds.length > 1 ? { $in: assignedToIds } : assignedToIds[0]
        }
      });
    }

    // Status filter
    if (status && status !== 'all') {
      const statusArray = status.split(',');
      pipeline.push({
        $match: { status: { $in: statusArray } }
      });
    }

    // Status codes filter
    if (statusCodes && statusCodes !== 'all') {
      const statusCodesArray = statusCodes.split(',');
      pipeline.push({
        $match: { status: { $in: statusCodesArray } }
      });
    }

    // Type filter
    if (type && type !== 'all') {
      const typeArray = type.split(',');
      pipeline.push({
        $match: { type: { $in: typeArray } }
      });
    }

    // Partner filter
    if (partnerId && partnerId !== 'all') {
      const partnerArray = partnerId.split(',');
      const partnerObjectIds = partnerArray.map(id => new ObjectId(id));
      pipeline.push({
        $match: { partnerId: { $in: partnerObjectIds } }
      });
    }

    // Visit time filter (matches original API logic)
    if (visitTime && visitTime !== 'all') {
      const visitTimes = visitTime.split(',');
      const timePatterns = visitTimes.map(time => `${time}:00`);

      pipeline.push({
        $match: {
          $expr: {
            $let: {
              vars: {
                startTime: { $arrayElemAt: [{ $split: ["$preferences.visitTime", "-"] }, 0] }
              },
              in: { $in: ["$$startTime", timePatterns] }
            }
          }
        }
      });
    }

    // Get all reservation statuses to identify sales and presence statuses
    const allStatuses = await ReservationStatus.find({});
    const salesStatusCodes = allStatuses.filter(status => status.isSalesStatus).map(status => status.code);
    const presenceStatusCodes = allStatuses.filter(status => status.isPresenceStatus).map(status => status.code);
    const presentStatusCode = 'present';
    const absentStatusCode = 'absent';

    // Create separate pipelines for different counts
    const totalPipeline = [...pipeline, { $count: 'total' }];
    const salesPipeline = [...pipeline,
      { $match: { status: { $in: salesStatusCodes } } },
      { $count: 'total' }
    ];
    const presencePipeline = [...pipeline,
      { $match: { status: { $in: presenceStatusCodes } } },
      { $count: 'total' }
    ];

    // For confirmed count: presence + present + absent
    const confirmedStatusCodes = [...presenceStatusCodes, presentStatusCode, absentStatusCode];

    const confirmedPipeline = [...pipeline,
      { $match: { status: { $in: confirmedStatusCodes } } },
      { $count: 'total' }
    ];

    // Execute all aggregations
    const [totalResult] = await Reservation.aggregate(totalPipeline);
    const [salesResult] = await Reservation.aggregate(salesPipeline);
    const [presenceResult] = await Reservation.aggregate(presencePipeline);
    const [confirmedResult] = await Reservation.aggregate(confirmedPipeline);

    const totalCount = totalResult?.total || 0;
    const salesCount = salesResult?.total || 0;
    const presenceCount = presenceResult?.total || 0;
    const confirmedCount = confirmedResult?.total || 0;

    // Calculate closing rate (sales / presence)
    const closingRate = presenceCount > 0 ? (salesCount / presenceCount) * 100 : 0;

    return NextResponse.json({
      totalCount,
      salesCount,
      presenceCount,
      confirmedCount,
      closingRate: Math.round(closingRate * 100) / 100 // Round to 2 decimal places
    });

  } catch (error) {
    console.error('Error fetching reservations count:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      {
        error: 'Failed to fetch reservations count',
        details: errorMessage
      },
      { status: 500 }
    );
  }
}
