import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import Reservation from "@/models/Reservation";
import { normalizePhoneNumber } from '@/lib/twilio';
import dbConnect from '@/lib/db';

export async function POST(
  request: Request
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get the phone numbers from request body
    const { phoneNumbers } = await request.json();
    
    if (!phoneNumbers || !Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
      return NextResponse.json(
        { error: 'Valid array of phone numbers is required' },
        { status: 400 }
      );
    }

    // Normalize the phone numbers to ensure consistent matching
    const normalizedPhones = phoneNumbers.map(phone => {
      return normalizePhoneNumber(phone);
    });

    // Connect to database
  await dbConnect();

    // Use aggregation pipeline to find reservations with robust phone number matching
    const reservations = await Reservation.aggregate([
      {
        // First, add a normalized phone field
        $addFields: {
          normalizedPhone: {
            $let: {
              vars: {
                chars: {
                  $map: {
                    input: { $range: [0, { $strLenCP: "$customerInfo.phone" }] },
                    as: "i",
                    in: { $substrCP: ["$customerInfo.phone", "$$i", 1] }
                  }
                }
              },
              in: {
                $let: {
                  vars: {
                    digitsOnly: {
                      $reduce: {
                        input: "$$chars",
                        initialValue: "",
                        in: {
                          $cond: [
                            { $in: ["$$this", ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]] },
                            { $concat: ["$$value", "$$this"] },
                            "$$value"
                          ]
                        }
                      }
                    }
                  },
                  in: {
                    $cond: [
                      { $eq: [{ $substr: ["$$digitsOnly", 0, 1] }, "1"] },
                      { $substr: ["$$digitsOnly", 1, { $subtract: [{ $strLenCP: "$$digitsOnly" }, 1] }] },
                      "$$digitsOnly"
                    ]
                  }
                }
              }
            }
          }
        }
      },
      {
        // Then match normalized phone against our input numbers
        // Use regex to match the end of the phone number strings
        $match: {
          $or: normalizedPhones.map(phone => ({
            normalizedPhone: { $regex: phone + "$" } // Match phone number at the end of string
          }))
        }
      },
      {
        // Sort by most recent first
        $sort: { createdAt: -1 }
      },
      {
        // Group by normalized phone to get the most recent reservation for each
        $group: {
          _id: "$normalizedPhone",
          reservation: { $first: "$$ROOT" }
        }
      },
      {
        // Return the full reservation data we need
        $project: {
          _id: "$reservation._id",
          normalizedPhone: "$_id",
          customerInfo: "$reservation.customerInfo",
          status: "$reservation.status",
          preferences: "$reservation.preferences"
        }
      }
    ]);

    // Convert array to map for easier client-side lookup
    const resultMap: {
      [phoneNumber: string]: {
        _id: string;
        customerInfo: {
          client1Name: string;
          phone: string;
          [key: string]: any;
        };
        status: string | { code: string; name: string; [key: string]: any };
        preferences: {
          visitDate: string;
          visitTime: string;
          branchId: string;
          [key: string]: any;
        };
      }
    } = {};
    
    reservations.forEach(reservation => {
      // Store by the full normalized phone in our results
      resultMap[reservation.normalizedPhone] = {
        _id: reservation._id,
        customerInfo: reservation.customerInfo,
        status: reservation.status,
        preferences: reservation.preferences
      };
    });

    return NextResponse.json(resultMap);
  } catch (error) {
    console.error("Error finding reservations by batch phone numbers:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 