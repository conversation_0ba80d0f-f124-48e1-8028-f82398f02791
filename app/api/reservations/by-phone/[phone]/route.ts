import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import Reservation from "@/models/Reservation";
import { normalizePhoneNumber } from '@/lib/twilio';
import dbConnect from '@/lib/db';

export async function GET(
  request: Request,
  { params }: { params: { phone: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { phone } = await params;
    
    if (!phone) {
      return NextResponse.json(
        { error: 'Phone number is required' },
        { status: 400 }
      );
    }

    // Connect to database
  await dbConnect();
    const normalizedPhone = normalizePhoneNumber(phone);
    // Use aggregation pipeline to find reservations with robust phone number matching
    const reservations = await Reservation.aggregate([
      {
        // First, add a normalized phone field
        $addFields: {
          normalizedPhone: {
            $let: {
              vars: {
                chars: {
                  $map: {
                    input: { $range: [0, { $strLenCP: "$customerInfo.phone" }] },
                    as: "i",
                    in: { $substrCP: ["$customerInfo.phone", "$$i", 1] }
                  }
                }
              },
              in: {
                $let: {
                  vars: {
                    digitsOnly: {
                      $reduce: {
                        input: "$$chars",
                        initialValue: "",
                        in: {
                          $cond: [
                            { $in: ["$$this", ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"]] },
                            { $concat: ["$$value", "$$this"] },
                            "$$value"
                          ]
                        }
                      }
                    }
                  },
                  in: {
                    $cond: [
                      { $eq: [{ $substr: ["$$digitsOnly", 0, 1] }, "1"] },
                      { $substr: ["$$digitsOnly", 1, { $subtract: [{ $strLenCP: "$$digitsOnly" }, 1] }] },
                      "$$digitsOnly"
                    ]
                  }
                }
              }
            }
          }
        }
      },
      {
        // Match the normalized phone against the input phone
        $match: {
          $expr: {
            $eq: ["$normalizedPhone", normalizedPhone]
          }
        }
      },
      {
        // Sort by most recent first
        $sort: { createdAt: -1 }
      },
      {
        // Limit to the most recent reservation
        $limit: 1
      },
      {
        // Use lookup to get branch name
        $lookup: {
          from: "branches",
          localField: "preferences.branchId",
          foreignField: "_id",
          as: "branch"
        }
      },
      {
        // Use lookup to get partner details
        $lookup: {
          from: "users",
          localField: "partnerId",
          foreignField: "_id",
          as: "partner"
        }
      },
      {
        // Unwind the arrays (or null if empty)
        $project: {
          _id: 1,
          customerInfo: 1,
          status: 1,
          preferences: 1,
          normalizedPhone: 1,
          branch: { $arrayElemAt: ["$branch", 0] },
          partner: { $arrayElemAt: ["$partner", 0] }
        }
      }
    ]);

    if (reservations.length === 0) {
      return NextResponse.json(null);
    }

    return NextResponse.json(reservations[0]);
  } catch (error) {
    console.error("Error finding reservation by phone:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 