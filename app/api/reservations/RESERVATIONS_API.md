# Reservations API – POST /api/reservations

## Purpose
Creates a new reservation in the system.

---

## Endpoint

```
POST /api/reservations
```

---

## Required Headers
- **Authorization**: Bearer token (user must be authenticated)
- **Content-Type**: application/json

---

## Required Body Fields

| Field                                 | Type     | Description                                 |
|----------------------------------------|----------|---------------------------------------------|
| `appointmentId`                        | string   | The appointment ID (MongoDB ObjectId)       |
| `customerInfo.client1Name`             | string   | Name of the main client                     |
| `customerInfo.phone`                   | string   | Main client phone number                    |
| `preferences.branchId`                 | string   | Branch ID for the reservation               |
| `preferences.visitDate`                | string   | Visit date (YYYY-MM-DD)                     |
| `preferences.visitTime`                | string   | Visit time (HH:MM-HH:MM)                    |
| `isMobile`                             | boolean  | **MUST be set** (true if request is from mobile) |

**Note:**
- All fields above are required.
- `isMobile` must be included in the request body and set appropriately.

---

## Optional Body Fields
- `partnerId` (string)
- `type` (string, default: 'branch')
- `status` (string, default: 'new')
- `customerInfo.client2Name` (string)
- `customerInfo.city` (string)
- `customerInfo.postalCode` (string)
- `customerInfo.phone2` (string)
- `customerInfo.email` (string)
- `customerInfo.isPostalCodeValid` (boolean)
- `preferences.preferredLanguage` (string, default: 'fr')
- `preferences.allergies` (string)
- `preferences.hasChildren` (boolean)
- `preferences.childrenAges` (object: `{ age0to5, age6to12, age13to17 }`)
- `preferences.adultServiceTypes` (object)
- `preferences.childServiceTypes` (object)
- `notes` (string)

---

## Behavior
- **Validates** all required fields.
- **Normalizes** phone numbers for duplicate checking.
- **Checks for duplicate reservations** by phone number (ignores deleted reservations).
- **Creates** a new reservation if no duplicate is found.
- **Creates/updates** a Contact and Twilio conversation for the client.
- **Returns** the new reservation's ID on success.

---

## Responses
- `201 Created` (on success):
  ```json
  { "message": "Reservation created successfully", "_id": "<reservationId>" }
  ```
- `400 Bad Request`:
  - Missing required fields
  - Validation errors
- `401 Unauthorized`:
  - If user is not authenticated
- `409 Conflict`:
  - If a reservation with the same phone already exists
- `500 Internal Server Error`:
  - On unexpected errors

---

## Error Handling
- All errors are returned as JSON with an `error` field and, if available, a `details` field.
- Validation and duplicate errors return appropriate HTTP status codes.

---

## Notes
- **isMobile**:
  - The POST endpoint requires the `isMobile` field in the request body.
  - Set `isMobile: true` if the request is coming from a mobile client, otherwise `false`.
- **Phone Numbers**:
  - All phone numbers are normalized before storage and duplicate checking.

---

## Example POST Request

```json
POST /api/reservations
Content-Type: application/json
Authorization: Bearer <token>

{
  "appointmentId": "662f1b2c3a4e5f6a7b8c9d0e",
  "customerInfo": {
    "client1Name": "John Doe",
    "phone": "******-555-1234"
  },
  "preferences": {
    "branchId": "662f1b2c3a4e5f6a7b8c9d0f",
    "visitDate": "2024-06-01",
    "visitTime": "10:00-11:00"
  },
  "isMobile": true
}
``` 