// app/api/reservations/[id]/status/route.ts
import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/db'; // Default import
import Reservation from '@/models/Reservation';
import ReservationStatus from '@/models/ReservationStatus'; // Import the status model
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { hasPermission } from '@/lib/permissions'; // Assuming you have a permission check helper
import { PermissionCode, RESERVATION_PERMISSIONS } from '@/types/permission-codes'; // Import the constant object
import ReservationNote from '@/models/ReservationNote';
import { Partner } from '@/models/Partner'; // Use named import
import { generateCommissions } from '@/app/api/utils/commission-utils';
import Commission from '@/models/Commission';
import User from '@/models/User';
import ScheduledSMS from '@/models/ScheduledSMS';
import { globalPlaceholderData } from '@/app/sms/utils';
import { connectToDatabase as dbConnect } from '@/lib/mongodb';
import { processTemplate } from '@/lib/sms-templates';
import { sendSMS } from '@/lib/twilio';

import Appointment from '@/models/Appointment';
import { withApiLogging } from '../../../utils/with-api-logging';
import { emitSocketEvent } from '@/app/api/utils/socket-utils';
import { ReservationAuditLogger } from '@/lib/utils/audit-utils';
export const PATCH = withApiLogging(async (
    request: NextRequest,
    context: { params: Promise<{ id: string }> }
) => {
    try {
      await dbConnect();
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
        }

        // Await the params
        const { id } = await context.params;

        if (!mongoose.Types.ObjectId.isValid(id)) {
            return NextResponse.json({ message: 'Invalid reservation ID' }, { status: 400 });
        }

        const body = await request.json();
        console.log("body", body);
        if (!body.statusId || !mongoose.Types.ObjectId.isValid(body.statusId)) {
            return NextResponse.json({ message: 'Invalid status ID' }, { status: 400 });
        }

        // Fetch the status and ensure we get a single document
        const status = await ReservationStatus.findById(body.statusId);

        if (!status) {
            return NextResponse.json({ message: 'Status not found' }, { status: 404 });
        }

        // Find the reservation first to make sure it exists
        const reservation = await Reservation.findById(id);

        if (!reservation) {
            return NextResponse.json({ message: 'Reservation not found' }, { status: 404 });
        }

        // Check if the status is "sales" (sold) by checking both code and name
        const isSalesStatus = status.code.toLowerCase() === 'sales';
        // Check if the status is "present"
        const isPresentStatus = status.code.toLowerCase() === 'present';
        // Check if the status is "confirmed"
        const isConfirmedStatus = status.code.toLowerCase() === 'confirmed';
        // Check if the status is "non_qualif"
        const isNonQualifStatus = status.code.toLowerCase() === 'non_qualif';
        // Check if the status is "wrongnumbe"
        const isWrongNumberStatus = status.code.toLowerCase() === 'wrongnumbe';
        // --- VALIDATION PHASE: Perform all validations that could cause early returns ---

        // Validate "sales" status requirements
        if (isSalesStatus) {
            // Check if selling amount is provided
            if (body.sellingAmount === undefined || body.sellingAmount === null) {
                return NextResponse.json({
                    message: 'Selling amount is required for "Sales" status'
                }, { status: 400 });
            }

            // Validate selling amount
            const sellingAmount = parseFloat(body.sellingAmount);
            if (isNaN(sellingAmount) || sellingAmount <= 0) {
                return NextResponse.json({
                    message: 'Invalid selling amount. Must be a positive number.'
                }, { status: 400 });
            }

            // Handle user assignment validation
            if (body.assigned_user_id) {
                // Validate assigned_user_id is a valid ObjectId
                if (!mongoose.Types.ObjectId.isValid(body.assigned_user_id)) {
                    return NextResponse.json({
                        message: 'Invalid assigned user ID'
                    }, { status: 400 });
                }
            } else if (!reservation.assigned_user_id) {
                // Sales status requires an assigned user
                return NextResponse.json({
                    message: 'An assigned user is required for "Sales" status'
                }, { status: 400 });
            }
        }

        // Validate "present" status requirements
        if (isPresentStatus) {
            if (!body.confirmationType || !['sms', 'call', 'email'].includes(body.confirmationType)) {
                return NextResponse.json({
                    message: 'Valid confirmation type (sms, call, email) is required for "Present" status'
                }, { status: 400 });
            }
        }

        // --- COMMISSION DELETION PHASE ---

        // Delete all commissions if changing to "non_qualif" status
        if (isNonQualifStatus || isWrongNumberStatus) {
            try {
                const deletedCommissions = await Commission.deleteMany({
                    reservationId: new mongoose.Types.ObjectId(id)
                });

                if (deletedCommissions.deletedCount > 0) {
                    console.log(`Deleted ${deletedCommissions.deletedCount} commissions for reservation ${id} due to non_qualif or wrongnumber status`);

                    // Create a note about the commission deletion
                    await ReservationNote.create({
                        reservationId: new mongoose.Types.ObjectId(id),
                        userId: new mongoose.Types.ObjectId(session.user.id),
                        content: `${deletedCommissions.deletedCount} commission(s) deleted due to status change to "Non qualifiable" or "Wrong number"`,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                }
            } catch (error) {
                console.error('Error deleting commissions for non_qualif status or wrongnumber:', error);
                // Don't fail the status change if commission deletion fails
            }
        }

        // --- CAPACITY ADJUSTMENT PHASE ---

        // Handle capacity adjustments for excludeFromAffectations status changes
        if (reservation.appointmentId) {
            try {
                // Get the old status details
                const oldStatus = await ReservationStatus.findOne({ code: reservation.status });
                const newStatus = status;

                const oldExcluded = oldStatus?.excludeFromAffectations === true;
                const newExcluded = newStatus?.excludeFromAffectations === true;

                // Only adjust capacity if the excludeFromAffectations flag changes
                if (oldExcluded !== newExcluded) {
                    const appointment = await Appointment.findById(reservation.appointmentId);
                    if (appointment && typeof appointment.capacity === 'number') {
                        if (newExcluded && !oldExcluded) {
                            // Moving TO excluded status - increment capacity (reservation no longer counts)
                            appointment.capacity += 1;
                            console.log(`Incremented appointment capacity from ${appointment.capacity - 1} to ${appointment.capacity} (status changed to excluded: ${newStatus.code})`);
                        } else if (!newExcluded && oldExcluded) {
                            // Moving FROM excluded status - decrement capacity (reservation now counts)
                            appointment.capacity = Math.max(0, appointment.capacity - 1);
                            console.log(`Decremented appointment capacity from ${appointment.capacity + 1} to ${appointment.capacity} (status changed from excluded: ${reservation.status})`);
                        }
                        await appointment.save();
                    }
                }
            } catch (err) {
                console.error('Failed to adjust appointment capacity on excludeFromAffectations status change:', err);
            }
        }



        // --- PREVIOUS STATUSES TRACKING PHASE ---

        // Add to previousStatuses - this now happens after all validations pass
        const previousStatuses = Array.isArray(reservation.previousStatuses) ? [...reservation.previousStatuses] : [];
        previousStatuses.push({
            status: status.code.toLowerCase(),
            statusId: status._id,
            changedAt: new Date(),
            changedBy: session.user.id ? new mongoose.Types.ObjectId(session.user.id) : undefined,
        });

        // Create update object
        const updateData: any = { status: status.code.toLowerCase() };
        updateData.previousStatuses = previousStatuses;

        // --- STATUS-SPECIFIC DATA PREPARATION PHASE ---

        // Prepare data for "sales" status
        if (isSalesStatus) {
            const sellingAmount = parseFloat(body.sellingAmount);
            updateData.sellingAmount = sellingAmount;
            updateData.soldAt = new Date();

            // Handle user assignment
            if (body.assigned_user_id) {
                updateData.assigned_user_id = new mongoose.Types.ObjectId(body.assigned_user_id);
            }
        }

        // Prepare data for "present" status
        if (isPresentStatus) {
            updateData.confirmationType = body.confirmationType;
        }

        // Prepare data for "confirmed" status
        if (isConfirmedStatus) {
            updateData.presentAt = new Date();
        }

        // --- NOTE CREATION PHASE ---

        // Create notes for status changes
        if (isSalesStatus) {
            const sellingAmount = parseFloat(body.sellingAmount);

            // Check if sellingAmount changed
            const amountChanged = reservation.sellingAmount !== sellingAmount;

            // Create a note about the status change with selling amount
            let noteContent = '';

            if (reservation.status === 'sales' && amountChanged) {
                // If already in sales status but amount changed
                noteContent = `Selling amount updated from $${reservation.sellingAmount?.toFixed(2) || '0.00'} to $${sellingAmount.toFixed(2)} CAD`;
            } else {
                // New sales status or same amount
                noteContent = `Status changed to "${status.name}" with selling amount: $${sellingAmount.toFixed(2)} CAD`;
            }

            // Add user assignment information to note if a new user was assigned
            if (body.assigned_user_id &&
                (!reservation.assigned_user_id ||
                body.assigned_user_id !== reservation.assigned_user_id?.toString())) {
                // Fetch user details to get the name
                const assignedUser = await User.findById(body.assigned_user_id);
                const userName = assignedUser ? assignedUser.name : `Unknown User `;
                noteContent += ` and assigned to ${userName}`;
            }

            await ReservationNote.create({
                reservationId: new mongoose.Types.ObjectId(id),
                userId: new mongoose.Types.ObjectId(session.user.id),
                content: noteContent,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        } else if (isPresentStatus) {
            // Create a note about the status change with confirmation type
            const noteContent = `Status changed to "${status.name}" with confirmation type: ${body.confirmationType}`;

            await ReservationNote.create({
                reservationId: new mongoose.Types.ObjectId(id),
                userId: new mongoose.Types.ObjectId(session.user.id),
                content: noteContent,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        } else if (status.code.toLowerCase() === 'canceled') {
            // Legacy canceled status handling - capacity adjustment is now handled by excludeFromAffectations logic above
            // Only create the note here
            await ReservationNote.create({
                reservationId: new mongoose.Types.ObjectId(id),
                userId: new mongoose.Types.ObjectId(session.user.id),
                content: 'Reservation canceled',
                createdAt: new Date(),
                updatedAt: new Date()
            });
        } else {
            // For other status changes, create a note about the status change
            let noteContent = `Status changed to "${status.name}"`;

            await ReservationNote.create({
                reservationId: new mongoose.Types.ObjectId(id),
                userId: new mongoose.Types.ObjectId(session.user.id),
                content: noteContent,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        // --- DATABASE UPDATE PHASE ---

        // Before calling findByIdAndUpdate, always set updatedAt to new Date() in updateData
        updateData.updatedAt = new Date();

        // Use $set operator explicitly to ensure fields are created if they don't exist
        const updatedReservation = await Reservation.findByIdAndUpdate(
            id,
            { $set: updateData },
            { new: true }
        );
        //---emit socket event
        if (updatedReservation && updatedReservation.status !== reservation.status){
            console.log("updateData", updateData);
            if(!updateData.assigned_user_id){
                updateData.assigned_user_id=updatedReservation.assigned_user_id
            }
            if(!updateData.sellingAmount){
                updateData.sellingAmount=updatedReservation.sellingAmount
            }
            if(!updateData.confirmationType){
                updateData.confirmationType=updatedReservation.confirmationType
            }
            if(!updateData.appointmentId){
                updateData.appointmentId=updatedReservation.appointmentId
            }
            if(!updateData.preferences){
                updateData.preferences={}
            }
            if(!updateData.preferences.visitDate && !updateData.preferences.visitTime){
                updateData.preferences.visitDate=updatedReservation.preferences.visitDate;
                updateData.preferences.visitTime=updatedReservation.preferences.visitTime;


            }
            await emitSocketEvent('status_change', {reservationId: id, status: updateData,userId: session.user.id});
        }

        // Log audit event for reservation status change
        if (updatedReservation && updatedReservation.status !== reservation.status) {
            try {
                await ReservationAuditLogger.logReservationStatusChanged(
                    request,
                    updatedReservation._id.toString(),
                    updatedReservation.customerInfo.client1Name,
                    reservation.status,
                    status.code.toLowerCase(),
                    body.statusId,
                    body.assigned_user_id,
                    body.sellingAmount,
                    session
                );
            } catch (auditError) {
                console.error('Failed to log reservation status change audit:', auditError);
                // Don't fail the request if audit logging fails
            }
        }

        // --- SMS Scheduling Logic ---
        try {
            const db = await dbConnect();
            const smsTemplates = await db.collection('sms_templates').find({ type: 'status-trigger', disabled: false }).toArray();
            const reservationObj = typeof updatedReservation.toObject === 'function'
                ? updatedReservation.toObject()
                : updatedReservation;
            const flattenReservationFields = (obj: any, prefix = ''): Record<string, string> => {
                let fields: Record<string, string> = {};
                for (const key in obj) {
                    if (!Object.prototype.hasOwnProperty.call(obj, key)) continue;
                    const value = obj[key];
                    if (value === null || value === undefined) {
                        fields[`${prefix}${key}`] = '';
                    } else if (Array.isArray(value)) {
                        fields[`${prefix}${key}`] = value.join(', ');
                    } else if (typeof value === 'object') {
                        if (
                            typeof value.toString === 'function' &&
                            (
                                value.constructor?.name === 'ObjectId' ||
                                value.constructor?.name === 'Date'
                            )
                        ) {
                            fields[`${prefix}${key}`] = value.toString();
                        } else if (Object.prototype.toString.call(value) === '[object Object]') {
                            Object.assign(fields, flattenReservationFields(value, `${prefix}${key}.`));
                        } else {
                            fields[`${prefix}${key}`] = String(value);
                        }
                    } else {
                        fields[`${prefix}${key}`] = String(value);
                    }
                }
                return fields;
            };
            const reservationData = flattenReservationFields(reservationObj);
            console.debug('[SMS Scheduling] reservationData keys:', Object.keys(reservationData));
            const scheduledSMSPromises = smsTemplates.map(async (tpl) => {
                if (tpl.triggerStatus && tpl.triggerStatus.toLowerCase() === status.code.toLowerCase()) {
                    const data: Record<string, string> = {};
                    if (tpl.variables && Array.isArray(tpl.variables)) {
                        for (const variable of tpl.variables) {
                            data[variable] = reservationData[variable] ?? '';
                        }
                    }

                    // Add branch data if needed for branch variables
                    const hasBranchVariables = tpl.variables?.some((v: string) => v.startsWith('branch.')) || false;
                    if (hasBranchVariables && reservationData.appointmentId) {
                        try {
                            // Get appointment and branch data
                            const appointment = await mongoose.model('Appointment').findById(reservationData.appointmentId);
                            if (appointment && appointment.branchId) {
                                const branch = await mongoose.model('Branch').findById(appointment.branchId);
                                if (branch) {
                                    // Flatten branch data for template variables
                                    const flattenBranchFields = (obj: any, prefix = 'branch.'): Record<string, string> => {
                                        let fields: Record<string, string> = {};
                                        for (const key in obj) {
                                            if (!Object.prototype.hasOwnProperty.call(obj, key)) continue;
                                            const value = obj[key];

                                            // Skip MongoDB internal fields for branch object
                                            if (key === '__v' || key === '$init') continue;

                                            if (value === null || value === undefined) {
                                                fields[`${prefix}${key}`] = '';
                                            } else if (typeof value === 'object') {
                                                if (value instanceof mongoose.Types.ObjectId) {
                                                    fields[`${prefix}${key}`] = value.toString();
                                                } else if (value instanceof Date) {
                                                    fields[`${prefix}${key}`] = value.toISOString();
                                                } else if (Array.isArray(value)) {
                                                    fields[`${prefix}${key}`] = value.join(', ');
                                                } else if (Object.prototype.toString.call(value) === '[object Object]') {
                                                    Object.assign(fields, flattenBranchFields(value, `${prefix}${key}.`));
                                                } else {
                                                    fields[`${prefix}${key}`] = String(value);
                                                }
                                            } else {
                                                fields[`${prefix}${key}`] = String(value);
                                            }
                                        }
                                        return fields;
                                    };

                                    // Get branch data as a plain object
                                    const branchData = typeof branch.toObject === 'function'
                                        ? branch.toObject()
                                        : branch;

                                    // Add branch fields to template data
                                    const branchFields = flattenBranchFields(branchData);
                                    Object.assign(data, branchFields);

                                    console.debug('[SMS Scheduling] Added branch data fields:', Object.keys(branchFields));
                                }
                            }
                        } catch (err) {
                            console.error('[SMS Scheduling] Error fetching branch data for SMS template:', err);
                        }
                    }

                    console.debug('[SMS Scheduling] processTemplate data:', data);
                    const body = processTemplate(tpl.template, data);
                    // Calculate scheduledAt based on delayHours or exactHour + dayDelay
                    let scheduledAt;
                    const hasExactHour = tpl.exactHour !== undefined && tpl.exactHour !== null;
                    let delayHours = typeof tpl.delayHours === 'number' ? tpl.delayHours : 0;
                    if (hasExactHour) {
                        const now = new Date();
                        const dayDelay = typeof tpl.dayDelay === 'number' && tpl.dayDelay > 0 ? tpl.dayDelay : 0;
                        scheduledAt = new Date(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() + dayDelay, tpl.exactHour, 0, 0, 0);
                        if (scheduledAt < now) {
                            scheduledAt.setDate(scheduledAt.getDate() + 1); // Schedule for the next day if the time has already passed
                        }
                    } else {
                        scheduledAt = new Date(Date.now() + delayHours * 60 * 60 * 1000);
                    }

                    // If delayHours is 0 and no exactHour, send SMS immediately
                    if (!hasExactHour && delayHours === 0) {
                        // Determine sender and recipient

                        // Get appointment and branch data to fetch the automatedPhone number
                        let from = tpl.from || process.env.TWILIO_CONVERSATION_NUMBER || '';

                        // Try to get branch's automatedPhone from the appointment
                        if (reservationData.appointmentId) {
                            try {
                                const appointment = await mongoose.model('Appointment').findById(reservationData.appointmentId);
                                if (appointment && appointment.branchId) {
                                    const branch = await mongoose.model('Branch').findById(appointment.branchId);
                                    if (branch && branch.automatedPhone) {
                                        from = branch.automatedPhone;
                                        console.log('[SMS Scheduling] Using branch automatedPhone:', from);
                                    }
                                }
                            } catch (err) {
                                console.error('[SMS Scheduling] Error fetching branch automatedPhone:', err);
                            }
                        }

                        const to = reservationData['customerInfo.phone'];
                        let sendStatus: 'sent' | 'failed' = 'sent';
                        let errorMsg = '';
                        try {
                            const message = await sendSMS(from, to, body, true);
                            const sid=message?.sid;
                            if (!sid) sendStatus = 'failed';
                        } catch (err: any) {
                            sendStatus = 'failed';
                            errorMsg = err?.message || String(err);
                        }
                        return ScheduledSMS.create({
                            templateId: tpl._id,
                            body,
                            reservationId: updatedReservation._id,
                            status: sendStatus,
                            error: errorMsg || undefined,
                            createdAt: new Date(),
                            scheduledAt: new Date(),
                        });
                    } else {
                        // Otherwise, schedule as before
                        return ScheduledSMS.create({
                            templateId: tpl._id,
                            body,
                            reservationId: updatedReservation._id,
                            status: 'pending',
                            createdAt: new Date(),
                            scheduledAt,
                        });
                    }
                }
            });
            await Promise.all(scheduledSMSPromises);
        } catch (err) {
            console.error('Error scheduling SMS:', err);
        }
        // --- End SMS Scheduling Logic ---

        // --- POST-UPDATE PROCESSING PHASE ---

        // Generate commissions based on the new status
        // Only generate if the status has actually changed
        if (updatedReservation && updatedReservation.status !== reservation.status) {
            await generateCommissions(updatedReservation, body.statusId, session.user.id);
        }



        return NextResponse.json(updatedReservation);
    } catch (error) {
        console.error('Error updating reservation status:', error);

        // Return more detailed error message for better debugging
        if (error instanceof Error) {
            return NextResponse.json({
                message: 'Failed to update status',
                error: error.message
            }, { status: 500 });
        }

        return NextResponse.json({ message: 'Internal Server Error' }, { status: 500 });
    }
});