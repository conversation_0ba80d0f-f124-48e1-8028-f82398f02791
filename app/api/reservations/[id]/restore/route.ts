import { NextResponse } from 'next/server';
import { authOptions } from '@/lib/auth';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';
import { hasPermission } from '@/lib/permissions';
import { RESERVATION_PERMISSIONS, PermissionCode } from '@/types/permission-codes';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { formatInTimeZone } from 'date-fns-tz';
import { ReservationAuditLogger } from '@/lib/utils/audit-utils';
// Define the required permission for restoring.
// If a specific RESTORE_RESERVATION permission doesn't exist,
// we might reuse EDIT_RESERVATIONS or create a new one.
// Let's assume we need a new one for clarity, but check types/permission-codes.ts if needed.
const REQUIRED_PERMISSION = RESERVATION_PERMISSIONS.EDIT_RESERVATIONS; // Placeholder - adjust if RESTORE_RESERVATION is added

export async function POST(
  request: Request,
  context: { params: { id: string } }
) {
  // Await params in Next.js route handlers
  const { id } = await Promise.resolve(context.params);
  const session = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }
  if (!session.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  // Check permission
  const userPermissions = session.user.permissions as PermissionCode[] | undefined;
  if (!userPermissions || !hasPermission(userPermissions, REQUIRED_PERMISSION)) {
    return NextResponse.json({ message: 'Forbidden: You do not have permission to restore reservations.' }, { status: 403 });
  }
  if (!id || typeof id !== 'string') {
    return NextResponse.json({ message: 'Invalid reservation ID' }, { status: 400 });
  }

  console.log('Attempting to restore reservation:', id);


  try {
    // Connect to database
    await dbConnect();

    if (!mongoose.connection.db) {
      throw new Error('Database connection not established');
    }

    // Convert string ID to ObjectId
    const objectId = new mongoose.Types.ObjectId(id);

    // First check if document exists and is deleted
    const existingDoc = await mongoose.connection.db.collection('reservations')
      .findOne({ _id: objectId });

    console.log('Current document state:', JSON.stringify(existingDoc, null, 2));

    if (!existingDoc) {
      return NextResponse.json({
        message: 'Reservation not found'
      }, { status: 404 });
    }

    // Explicitly check the isDeleted flag and its type
    console.log('Checking isDeleted flag:', {
      value: existingDoc.isDeleted,
      type: typeof existingDoc.isDeleted
    });

    if (existingDoc.isDeleted !== true) {
      return NextResponse.json({
        message: 'Reservation is not marked as deleted (or value is not boolean true)',
        debug: { isDeletedValue: existingDoc.isDeleted }
      }, { status: 400 });
    }

    // Perform restore operation
    const updateResult = await mongoose.connection.db.collection('reservations')
      .findOneAndUpdate(
        {
          _id: objectId,
          isDeleted: true // Only update if it's actually deleted
        },
        {
          $set: {
            isDeleted: false,
            deletedAt: null
          }
        },
        {
          returnDocument: 'after'
        }
      );

    // Check if the operation found and modified a document
    if (!updateResult || !updateResult.value) {
      // Log the raw result for detailed debugging
      console.error('Restore operation failed: findOneAndUpdate did not return the updated document.', {
        filter: { _id: objectId, isDeleted: true },
        rawResult: updateResult // Log the whole result object
      });

      // Check again if the document exists but wasn't updated (maybe already restored?)
      const checkDoc = await mongoose.connection.db.collection('reservations').findOne({ _id: objectId });
      if (checkDoc && !checkDoc.isDeleted) {
        return NextResponse.json({ message: 'Reservation already restored or was not deleted.' }, { status: 400 });
      } else if (!checkDoc) {
        return NextResponse.json({ message: 'Reservation not found.' }, { status: 404 });
      } else {
        // If it exists and is still deleted, the update failed for another reason
        throw new Error('Failed to restore reservation - update operation unsuccessful.');
      }
    }

    // If we reach here, updateResult.value contains the successfully updated document
    const restoredDoc = updateResult.value;

    console.log('Restore operation successful:', {
      success: true,
      document: restoredDoc
    });

    // Log audit event for reservation restoration
    try {
      await ReservationAuditLogger.logReservationRestored(
        request as any, // Cast to NextRequest for audit logging
        restoredDoc._id.toString(),
        restoredDoc.customerInfo?.client1Name || 'Unknown Customer',
        session
      );
    } catch (auditError) {
      console.error('Failed to log reservation restoration audit:', auditError);
      // Don't fail the request if audit logging fails
    }

    // Return the successful response using the confirmed restored document
    return NextResponse.json({
      message: 'Reservation restored successfully',
      reservation: {
        id: restoredDoc._id.toString(),
        isDeleted: false, // Explicitly state it's restored
        restoredAt: new Date()
      }
    }, { status: 200 });

  } catch (error) {
    console.error('Error restoring reservation:', {
      id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    // Handle specific error cases
    if (error instanceof mongoose.Error.ValidationError) {
      return NextResponse.json({
        message: 'Invalid reservation data',
        details: error.message
      }, { status: 400 });
    }

    if (error instanceof mongoose.Error.CastError) {
      return NextResponse.json({
        message: 'Invalid reservation ID format',
        details: error.message
      }, { status: 400 });
    }
    return NextResponse.json({
      message: 'Internal Server Error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}