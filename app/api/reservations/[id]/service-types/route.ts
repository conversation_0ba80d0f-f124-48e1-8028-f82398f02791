import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import Reservation from '@/models/Reservation';
import { RESERVATION_PERMISSIONS } from '@/types/permission-codes';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { logAuditEvent } from '@/lib/utils/audit-utils';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check authentication
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if (!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    // Check permissions - users need EDIT_RESERVATIONS permission
    if (!session.user.permissions?.includes(RESERVATION_PERMISSIONS.EDIT_RESERVATIONS)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Connect to database
    await dbConnect();

    const reservationId = params.id;

    // Parse request body
    const body = await request.json();
    const { adultServiceTypes, childServiceTypes } = body;

    // Validate input
    if (!adultServiceTypes && !childServiceTypes) {
      return NextResponse.json(
        { error: 'Either adultServiceTypes or childServiceTypes must be provided' },
        { status: 400 }
      );
    }

    // Find the reservation first to check if it exists and get current data
    const existingReservation = await Reservation.findById(reservationId);

    if (!existingReservation) {
      return NextResponse.json({ error: 'Reservation not found' }, { status: 404 });
    }

    // Prepare update object
    const updateData: any = {};

    // Handle allergies - ensure it's a string, not an array
    if (existingReservation.preferences && existingReservation.preferences.allergies) {
      if (Array.isArray(existingReservation.preferences.allergies)) {
        updateData['preferences.allergies'] = existingReservation.preferences.allergies.join(', ');
      }
    }

    // Update service types
    updateData['preferences.adultServiceTypes'] = adultServiceTypes || {};
    updateData['preferences.childServiceTypes'] = childServiceTypes || {};

    // Use findByIdAndUpdate to avoid validation issues
    const reservation = await Reservation.findByIdAndUpdate(
      reservationId,
      updateData,
      { new: true, runValidators: false } // Disable validators to avoid the allergies validation issue
    );

    if (!reservation) {
      return NextResponse.json({ error: 'Failed to update reservation' }, { status: 500 });
    }

    // Log audit event for service types update
    try {
      await logAuditEvent(request, {
        action: 'UPDATED',
        entityType: 'Reservation',
        entityId: reservationId,
        entityName: reservation.customerInfo?.client1Name || 'Unknown Customer',
        description: `Service types updated for reservation`,
        metadata: {
          adultServiceTypes: adultServiceTypes || {},
          childServiceTypes: childServiceTypes || {},
          serviceTypesAction: 'updated'
        }
      }, session);
    } catch (auditError) {
      console.error('Failed to log service types update audit:', auditError);
      // Don't fail the request if audit logging fails
    }

    // Return success
    return NextResponse.json({
      success: true,
      message: 'Service types updated successfully'
    });

  } catch (error) {
    console.error('Error updating service types:', error);
    return NextResponse.json(
      { error: 'Failed to update service types' },
      { status: 500 }
    );
  }
}