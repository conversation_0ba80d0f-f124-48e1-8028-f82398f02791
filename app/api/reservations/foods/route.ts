import { NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/mongodb";
import Food from "@/models/Food";
import dbConnect from '@/lib/db';

export async function GET(request: Request) {
  try {
    // Get the language from query params
    const { searchParams } = new URL(request.url);
    const lang = searchParams.get('lang') || 'fr';

    // Connect to database
  await dbConnect();

    // Fetch foods and await the result
    const foods = await Food
      .find({ active: { $ne: false } })
      .select({
        _id: 1,
        name: 1,
        [`name_${lang}`]: 1,
        code: 1
      })
      .sort({ name: 1 })
      .lean();

    // Transform the response
    const activeFoods = foods.map(food => ({
      _id: food._id,
      name: food[`name_${lang}`] || food.name,
      code: food.code
    }));

    return NextResponse.json(activeFoods);
  } catch (error) {
    console.error("[RESERVATIONS_FOODS_GET]", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 