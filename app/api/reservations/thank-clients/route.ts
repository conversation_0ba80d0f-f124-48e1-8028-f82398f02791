import { NextRequest, NextResponse } from 'next/server';
import connectToDatabase from '@/lib/db';
import Reservation from '@/models/Reservation';
import ReservationStatus from '@/models/ReservationStatus';
import ReservationNote from '@/models/ReservationNote';
import mongoose from 'mongoose';
import { getTemplate, processTemplate, sendTemplatedSMS } from '@/lib/sms-templates';
import { IReservation } from '@/models/Reservation';
import Branch from '@/models/Branch';
import Appointment from '@/models/Appointment';
import dbConnect from '@/lib/db';

export async function GET(request: NextRequest) {
  const logs: string[] = [];
  const stepResults: any[] = [];
  const startTime = Date.now();
  try {
    logs.push('Starting thank-clients API endpoint execution');
    
    // Check API key for security (should match what will be in bash script)
    const apiKey = request.headers.get('x-api-key');
    
    if (apiKey !== process.env.CRON_API_KEY) {
      logs.push('Unauthorized access attempt with invalid API key');
      return NextResponse.json({ error: 'Unauthorized', logs }, { status: 401 });
    }

    logs.push('API key validated, connecting to database...');
  await dbConnect();
    
    const twoHoursAgo = new Date();
    twoHoursAgo.setUTCHours(twoHoursAgo.getUTCHours() - 2);
    logs.push(`Looking for reservations with presentAt before ${twoHoursAgo.toString()} UTC`);
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    
    // Find reservations that:
    // 1. Have presentAt value
    // 2. presentAt is older than 2 hours ago
    // 3. Don't have thankYouMessageSent flag set to true
    // to send a thank you sms to the client
    const relavantReservations = await Reservation.find({
      presentAt: { 
        $exists: true, 
        $ne: null,
        $lt: twoHoursAgo 
      },
      status: { $in: ['confirmed', 'sales'] },
      thankYouMessageSent: { $ne: true }
    });
    
    logs.push(`Found ${relavantReservations.length} reservations that need thank you messages`);
    
    // Check if there are any reservations that have the presentAt flag but already have the thankYouMessageSent flag
    // This helps with monitoring and debugging
    const alreadySentCount = await Reservation.countDocuments({
      presentAt: { 
        $exists: true, 
        $ne: null,
        $lt: twoHoursAgo,
        $gt: oneWeekAgo
      },
      assigned_user_id: { $exists: true, $ne: null },
      thankYouMessageSent: true
    });
    
    logs.push(`Found ${alreadySentCount} reservations that already received thank you messages`);
    
    if (relavantReservations.length === 0) {
      logs.push('No messages to send, returning response');
      return NextResponse.json({ 
        message: 'No messages to send', 
        count: 0,
        alreadySentCount,
        logs
      });
    }
    
    // Function to send thank you message to a client
    const sendThankYouMessage = async (reservation: mongoose.Document & IReservation) => {
      const resLog: string[] = [];
      resLog.push(`Processing reservation ${reservation._id}`);
      try {
        resLog.push('reservation.presentAt: ' + reservation.presentAt);
        // Get client phone number
        let phoneNumber = reservation.customerInfo.phone;

        // Attempt to get branch phone number from the appointmentId relationship
        let branchPhone = '';
        try {
          const appointment = await Appointment.findById(reservation.appointmentId);

          if (appointment && appointment.branchId) {
            const branch = await Branch.findById(appointment.branchId);
            if (branch && branch.automatedPhone) {
              branchPhone = branch.automatedPhone;
              resLog.push(`Found branch phone: ${branchPhone}`);
            } else {
              resLog.push(`Branch not found or missing automatedPhone for branchId: ${appointment.branchId}, falling back to client phone.`);
            }
          } else {
            resLog.push(`Appointment not found or missing branchId for appointmentId: ${reservation.appointmentId}, falling back to client phone.`);
          }
        } catch (error) {
          resLog.push('Error retrieving branch phone number: ' + error);
        }
        
        // Get the branch name from the appointmentId relationship
        let branchName = '';
        try {
          const appointment = await Appointment.findById(reservation.appointmentId);
          
          if (appointment && appointment.branchId) {
            const branch = await Branch.findById(appointment.branchId);
            if (branch) {
              branchName = branch.name;
              resLog.push(`Found branch name: ${branchName} for reservation ${reservation._id}`);
            } else {
              resLog.push(`Branch not found for branchId: ${appointment.branchId}, using branchId from preferences as fallback`);
            }
          } else {
            resLog.push(`Appointment not found or missing branchId for appointmentId: ${reservation.appointmentId}, using branchId from preferences as fallback`);
          }
        } catch (error) {
          resLog.push('Error retrieving branch name: ' + error);
          // Continue with the fallback
        }
        
        // Prepare data for template
        const templateData: Record<string, string> = {
          customerName: reservation.customerInfo.client1Name,
          appointmentDate: reservation.preferences.visitDate,
          'branchName': branchName || "notre établissement"
        };
        
        // Add more branch fields if they exist
        try {
          const appointmentObj = await Appointment.findById(reservation.appointmentId);
          if (appointmentObj && appointmentObj.branchId) {
            const branch = await Branch.findById(appointmentObj.branchId);
            if (branch) {
              // Add all branch fields with the 'branch.' prefix
              const branchObj = typeof branch.toObject === 'function' ? branch.toObject() : branch;
              
              for (const [key, value] of Object.entries(branchObj)) {
                // Skip MongoDB internal fields and arrays/objects
                if (key !== '_id' && key !== '__v' && typeof value !== 'object') {
                  templateData[`branch.${key}`] = String(value);
                } else if (key === '_id') {
                  templateData['branch._id'] = String(value);
                }
              }
              
              resLog.push(`Added branch fields to template data: ${Object.keys(templateData).filter(k => k.startsWith('branch.')).join(', ')}`);
            }
          }
        } catch (error) {
          resLog.push(`Error adding branch fields: ${error}`);
        }
        
        let result = false;
        let message = '';
        // Send templated SMS
        //
        if( process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DEV_SERVER !== 'true') {
          result = await sendTemplatedSMS(branchPhone, phoneNumber, 'thank_client', templateData);
        } else {
          // In test/development mode, we still mark messages as sent for testing
          const templateContent = await getTemplate('thank_client');
          message =processTemplate(templateContent, templateData);
          resLog.push(`[TEST MODE] Would send thank you SMS to ${phoneNumber}: ${message}`);
          result = true; // Simulate successful sending in test mode
        }
        //--------------------------------
        if (result) {
          resLog.push(`Thank you SMS ${process.env.NODE_ENV === 'production' ? 'sent to' : 'would be sent to'} ${phoneNumber}`);
          // Update reservation with a note that thank you SMS was sent
          await ReservationNote.create({
            userId: "67eab74ed78036c9f68c7686",
            reservationId: reservation._id,
            content: `[${process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DEV_SERVER !== 'true' ? 'PROD' : 'TEST'}]Thank you SMS ${process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_DEV_SERVER !== 'true' ? 'sent' : 'would be sent'} to client: ${message}`,
            type: 'system',
            createdBy: 'system',
            createdAt: new Date(),
          });
          
          // Mark the reservation as having received the thank you message
          await Reservation.findByIdAndUpdate(
            reservation._id,
            { $set: { thankYouMessageSent: true } }
          );
          
          resLog.push(`Reservation ${reservation._id} marked as having received thank you message`);
          return { reservationId: reservation._id, status: 'success', logs: resLog };
        } else {
          resLog.push(`Failed to send thank you SMS to ${phoneNumber}`);
          return { reservationId: reservation._id, status: 'failed', logs: resLog };
        }
        
      } catch (error) {
        resLog.push('Error sending thank you message: ' + error);
        return { reservationId: reservation._id, status: 'error', logs: resLog };
      }
    };
    
    // Send thank you messages to the clients
    const results = await Promise.all(relavantReservations.map(sendThankYouMessage));
    const successCount = results.filter((result) => result.status === 'success').length;
    const errorCount = results.filter((result) => result.status === 'error').length;
    const failCount = results.filter((result) => result.status === 'failed').length;
    logs.push(`Processed ${relavantReservations.length} reservations. Success: ${successCount}, Error: ${errorCount}, Failed: ${failCount}`);
    const endTime = Date.now();
    logs.push(`Total execution time: ${endTime - startTime}ms`);
    return NextResponse.json({
      message: 'Successfully processed thank you messages',
      count: relavantReservations.length,
      successCount,
      errorCount,
      failCount,
      alreadySentCount,
      stepResults: results,
      logs
    });
    
  } catch (error) {
    logs.push('Error in reservation timeout check: ' + error);
    
    return NextResponse.json({ 
      error: 'Failed to process reservation timeouts',
      message: error instanceof Error ? error.message : 'Unknown error',
      logs
    }, { status: 500 });
  }
} 