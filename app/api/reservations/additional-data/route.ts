import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { getMessagesForContact, normalizePhoneNumber } from '@/lib/twilio';
import Branch from '@/models/Branch';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import { createDateRange } from '@/lib/utils/timezone-utils';
import { format } from 'date-fns';
import dbConnect from '@/lib/db';

export async function POST(request: Request) {
  try {
    await dbConnect();

    // Get the user session
    const session = await getServerSession(authOptions);
    
    if(session && !session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
    }

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { reservationIds } = body;

    if (!reservationIds || !Array.isArray(reservationIds)) {
      return NextResponse.json(
        { error: 'reservationIds array is required' }, 
        { status: 400 }
      );
    }

    // Limit the number of reservations to prevent abuse
    if (reservationIds.length > 100) {
      return NextResponse.json(
        { error: 'Too many reservation IDs. Maximum 100 allowed.' }, 
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const includeMessages = searchParams.get('includeMessages') !== 'false';
    const includeBranchPhones = searchParams.get('includeBranchPhones') !== 'false';
    const includeTodayCount = searchParams.get('includeTodayCount') === 'true';

    // Get reservations data we need for processing
    const reservationsData = body.reservations || [];
    
    if (reservationsData.length === 0) {
      return NextResponse.json(
        { error: 'reservations data is required for processing' }, 
        { status: 400 }
      );
    }

    const additionalData: Record<string, {
      messagesCount?: number;
      branchPhone?: string;
    }> = {};

    // Batch fetch branch phones if needed
    let branchPhones: Record<string, string> = {};
    if (includeBranchPhones) {
      const branchIds = [...new Set(
        reservationsData
          .map((r: any) => r.preferences?.branchId)
          .filter(Boolean)
      )];
      
      if (branchIds.length > 0) {
        const branches = await Branch.find(
          { _id: { $in: branchIds } },
          { _id: 1, phone: 1 }
        ).lean();
        
        branchPhones = branches.reduce((acc, branch) => {
          if (branch.phone) {
            acc[branch._id.toString()] = branch.phone;
          }
          return acc;
        }, {} as Record<string, string>);
      }
    }

    // Process each reservation
    for (const reservation of reservationsData) {
      const reservationId = reservation._id;
      const data: any = {};

      // Add branch phone if available
      if (includeBranchPhones && reservation.preferences?.branchId) {
        const branchPhone = branchPhones[reservation.preferences.branchId];
        if (branchPhone) {
          data.branchPhone = branchPhone;
        }
      }

      // Fetch messages count if requested
      if (includeMessages && reservation.customerInfo?.phone) {
        try {
          const phoneNumber = normalizePhoneNumber(reservation.customerInfo.phone);
          if (phoneNumber) {
            const messages = await getMessagesForContact(phoneNumber);
            data.messagesCount = messages.length;
          } else {
            data.messagesCount = 0;
          }
        } catch (error) {
          console.error(`Error fetching messages for reservation ${reservationId}:`, error);
          data.messagesCount = 0;
        }
      }

      additionalData[reservationId] = data;
    }

    // Calculate today's reservations count if requested
    let todayCount = 0;
    if (includeTodayCount) {
      try {
        // Get client timezone from request URL params
        const { searchParams: urlParams } = new URL(request.url);
        const clientTimezone = urlParams.get('timezone') || 'America/Toronto';

        // Get today's date in YYYY-MM-DD format
        const today = format(new Date(), 'yyyy-MM-dd');

        // Use timezone-aware date range calculation (same as count API)
        const { startOfDay, endOfDay } = createDateRange(today, clientTimezone);

        // Import Reservation model
        const Reservation = (await import('@/models/Reservation')).default;

        // Build query for today's reservations
        const todayQuery: any = {
          createdAt: {
            $gte: startOfDay,
            $lte: endOfDay
          },
          isDeleted: { $ne: true }
        };

        // If user is a seller, only count their assigned reservations
        if (session.user.roles?.some((role: any) =>
          (typeof role === 'object' ? role.name : role) === 'Vendeur'
        ) && session.user.id) {
          const { ObjectId } = await import('mongodb');
          todayQuery.assigned_user_id = new ObjectId(session.user.id);
        }

        todayCount = await Reservation.countDocuments(todayQuery);
      } catch (error) {
        console.error('Error calculating today count:', error);
        todayCount = 0;
      }
    }

    return NextResponse.json({
      success: true,
      data: additionalData,
      todayCount: includeTodayCount ? todayCount : undefined
    });

  } catch (error) {
    console.error('Error fetching additional reservation data:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    return NextResponse.json(
      {
        error: 'Failed to fetch additional reservation data',
        details: errorMessage
      },
      { status: 500 }
    );
  }
}
