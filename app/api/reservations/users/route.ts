import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import bcrypt from 'bcryptjs';
import Role from '@/models/Role';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { Types } from 'mongoose';
import Branch from '@/models/Branch';
import { canUserManageUsers, canUserViewOwnBranchUsers, canUserViewUsers } from '@/lib/utils/permissions-utils';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
interface Role {
  id?: string;
  _id?: string;
  name: string;
}

interface UserQuery {
  roles?: Types.ObjectId | Types.ObjectId[] | { $in: Types.ObjectId[] };
  _id?: { $in: string[] };
}

export async function GET(request: Request) {
  try {
    await dbConnect();
    const { searchParams } = new URL(request.url);
    const roleName = searchParams.get('role');
    const session = await getServerSession(authOptions);
    if(session &&!session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
    }
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is SuperAdmin
    const isSuperAdmin = session.user.roles?.some((role: any) => {
      const roleId = role.id || role._id;
      return roleId?.toString() === '67add3214badd3283e873329';
    });

    // Check if user is BranchesAgent
    const isBranchesAgent = session.user.roles?.some((role: any) => {
      const roleId = role.id || role._id;
      return roleId?.toString() === '67c032fe0af5117479e27737';
    });

    // Check if user is BranchesAdmin
    const isBranchesAdmin = session.user.roles?.some((role: any) => {
      const roleId = role.id || role._id;
      return roleId?.toString() === '67c032e90af5117479e27731';
    });

    let query: UserQuery = {};

    if (roleName && roleName !== 'all') {
      // Find the role first
      const role = await Role.findOne({ name: roleName });
      if (!role) {
        return NextResponse.json({ users: [] });
      }
      query.roles = role._id;
    }

    // For SuperAdmin, return all users with optional role filtering
    if (isSuperAdmin || canUserViewUsers(session.user)) {
      const users = await User.find(query)
        .select('name email roles directPermissions isActive')
        .populate('roles', 'name')
        .populate('directPermissions', 'name')
        .lean();

      return NextResponse.json({ users });
    }
    
    // For non-SuperAdmin users, only return users from the same branches
    // First, find all branches where the user has a role
    
    const userBranches = await Branch.find({
      $or: [
        { responsible: session.user.id },
        { agents: session.user.id },
        { sellers: session.user.id }
      ]
    }).lean();
    
    // Get all user IDs from these branches
    const branchUserIds = userBranches.reduce((acc: string[], branch) => {
      // Extract all user IDs from different branch roles
      const responsibleIds = Array.isArray(branch.responsible) ? branch.responsible : [branch.responsible];
      const agentIds = Array.isArray(branch.agents) ? branch.agents : [branch.agents];
      const sellerIds = Array.isArray(branch.sellers) ? branch.sellers : [branch.sellers];
      
      // Combine all IDs
      return [...acc, ...responsibleIds.filter(Boolean), ...agentIds.filter(Boolean), ...sellerIds.filter(Boolean)];
    }, []);

    // Remove duplicates
    const uniqueUserIds = Array.from(new Set(branchUserIds));
    
    // Add user ID filter to the query
    query._id = { $in: uniqueUserIds };
    
    // Fetch users with the combined query
    const branchUsers = await User.find(query)
      .select('name email roles directPermissions isActive')
      .populate('roles', 'name')
      .populate('directPermissions', 'name')
      .lean();
    if(canUserViewOwnBranchUsers(session.user)){
      return NextResponse.json({ users: branchUsers });
    }
    return NextResponse.json({ users: [] });
  } catch (error) {
    console.error('GET users error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    await dbConnect();
    const body = await request.json();
    const session = await getServerSession(authOptions);
    if(session &&!session?.user.permissions){
      session.user.permissions = await getUserPermissions(session);
    }
    if(!session || !canUserManageUsers(session.user)){
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Hash password
    if (body.password) {
      body.password = await bcrypt.hash(body.password, 10);
    }

    // Validate required fields
    if (!body.name || !body.email || !body.password) {
      return NextResponse.json(
        { error: 'Name, email and password are required' },
        { status: 400 }
      );
    }

    // Get the requesting user's roles
    const requestingUserRoles = await Role.find({
      _id: { $in: body.requestingUserRoles || [] }
    });

    // Check if user is trying to assign restricted roles
    const restrictedRoles = ['SuperAdmin', 'BranchesAdmin'];
    const isBranchAdmin = requestingUserRoles.some(role => role.name === 'BranchesAdmin');

    if (isBranchAdmin) {
      // If the user is a BranchAdmin, check if they're trying to assign restricted roles
      const rolesToAssign = await Role.find({ _id: { $in: body.roles } });
      const hasRestrictedRole = rolesToAssign.some(role => restrictedRoles.includes(role.name));

      if (hasRestrictedRole) {
        return NextResponse.json(
          { error: 'You do not have permission to assign these roles' },
          { status: 403 }
        );
      }
    }

    // Check if email already exists
    const existingUser = await User.findOne({ email: body.email });
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 400 }
      );
    }

    const user = await User.create(body);
    const userWithoutPassword = await User.findById(user._id)
      .populate('roles')
      .populate('directPermissions')
      .select('-password');

    return NextResponse.json(userWithoutPassword);
  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json({ error: 'Failed to create user' }, { status: 500 });
  }
} 