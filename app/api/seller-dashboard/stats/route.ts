import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { MongoClient, ObjectId } from 'mongodb';
import mongoose from 'mongoose';
import { getUserPermissions, getUserRoles } from '../../utils/server-permission-utils';
import { canUserViewSellerDashboard } from '@/lib/utils/permissions-utils';
import { Session } from 'inspector';

// Add global type declaration for MongoDB client
declare global {
  var _mongoClientPromise: Promise<MongoClient>;
}

// Use direct MongoClient since that seems to be what's used in the codebase
const uri = process.env.MONGODB_URI!;
let clientPromise: Promise<MongoClient>;

if (!global._mongoClientPromise) {
  const client = new MongoClient(uri);
  global._mongoClientPromise = client.connect();
}
clientPromise = global._mongoClientPromise;

export async function GET(request: NextRequest) {
  try {
    // Get the current user's session
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if(session && !session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }
    console.log(session.user.roles);
    if(session && !session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    // Check if the user is a seller
    const isSeller = session.user.roles && session.user.roles.some(
      (role: any) => role.name === 'Seller' || (typeof role === 'string' && role === 'Seller') || role.toString()=="67e0aad60f0a3bdeba18542c"
    );
    if (!isSeller && !canUserViewSellerDashboard(session.user)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get the user ID from the session
    const userId = session.user.id;
    
    // Connect to MongoDB
    const client = await clientPromise;
    const db = client.db();
    
    // Query the reservations collection to count assignments for this seller
    const totalAssignments = await db.collection('reservations').countDocuments({
      assigned_user_id: new mongoose.Types.ObjectId(userId),
      // We're not filtering by deleted status to include all assignments
    });

    return NextResponse.json({
      totalAssignments
    });
  } catch (error) {
    console.error('Error in seller dashboard stats API:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
} 