import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import AppointmentDisponibility from '@/models/AppointmentDisponibility';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    
    const id = await Promise.resolve(params.id);
    
    const disponibility = await AppointmentDisponibility.findById(id)
      .populate('user_id', 'name email')
      .populate('created_by', 'name email');
      
    if (!disponibility) {
      return NextResponse.json({ error: 'Disponibility not found' }, { status: 404 });
    }
    
    return NextResponse.json(disponibility);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch disponibility' }, { status: 500 });
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    
    const body = await request.json();
    const id = await Promise.resolve(params.id);
    
    const disponibility = await AppointmentDisponibility.findByIdAndUpdate(
      id,
      { ...body, updated_at: new Date() },
      { new: true }
    ).populate('user_id', 'name email')
     .populate('created_by', 'name email');
    
    if (!disponibility) {
      return NextResponse.json({ error: 'Disponibility not found' }, { status: 404 });
    }
    
    return NextResponse.json(disponibility);
  } catch (error) {
    console.error('Error updating disponibility:', error);
    return NextResponse.json({ error: 'Failed to update disponibility' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    
    const id = await Promise.resolve(params.id);
    
    const disponibility = await AppointmentDisponibility.findByIdAndDelete(id);
    
    if (!disponibility) {
      return NextResponse.json({ error: 'Disponibility not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Disponibility deleted successfully' });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to delete disponibility' }, { status: 500 });
  }
} 