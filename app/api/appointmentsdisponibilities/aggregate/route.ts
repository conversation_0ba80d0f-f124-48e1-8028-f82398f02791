import { NextResponse } from 'next/server';
import AppointmentDisponibility from '@/models/AppointmentDisponibility';
import Branch from '@/models/Branch';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';

import { formatInTimeZone } from 'date-fns-tz';
interface AggregatedHours {
  [hour: string]: number;
}

interface AggregatedResponse {
  date: string;
  hours: AggregatedHours;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') || formatInTimeZone(new Date(), 'America/Toronto', "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").split('T')[0];
    const endDate = searchParams.get('endDate') || startDate;
    const branchId = searchParams.get('branchId');
    const regionId = searchParams.get('regionId');

    await dbConnect();

    let userIds: mongoose.Types.ObjectId[] = [];

    // If regionId is provided, get all users from branches in that region
    if (regionId && regionId !== 'all') {
      const branchesInRegion = await Branch.find({ regionId });
      userIds = branchesInRegion.reduce((acc: mongoose.Types.ObjectId[], branch) => {
        const responsibleIds = branch.responsible || [];
        const agentIds = branch.agents || [];
        return [
          ...acc,
          ...(Array.isArray(responsibleIds) ? responsibleIds : [responsibleIds]),
          ...(Array.isArray(agentIds) ? agentIds : [agentIds])
        ];
      }, []);
    }
    // If branchId is provided, get users from that specific branch
    else if (branchId && branchId !== 'all') {
      const branch = await Branch.findById(branchId);
      if (branch) {
        const responsibleIds = branch.responsible || [];
        const agentIds = branch.agents || [];
        userIds = [
          ...(Array.isArray(responsibleIds) ? responsibleIds : [responsibleIds]),
          ...(Array.isArray(agentIds) ? agentIds : [agentIds])
        ];
      }
    }

    const query: any = {
      date: {
        $gte: startDate,
        $lte: endDate,
      },
    };

    // Add user filter if we have userIds from branch or region
    if (userIds.length > 0) {
      query.user_id = { $in: userIds };
    }

    const disponibilities = await AppointmentDisponibility.find(query).lean();

    const aggregatedData = new Map<string, AggregatedHours>();

    // Initialize dates in the range
    const currentDate = new Date(startDate);
    const lastDate = new Date(endDate);
    while (currentDate <= lastDate) {
      const dateString = currentDate.toISOString().split('T')[0];
      aggregatedData.set(dateString, {});
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Aggregate availability counts
    for (const doc of disponibilities) {
      const dateHours = aggregatedData.get(doc.date) || {};
      
      const openedHours = doc.opened_hours instanceof Map 
        ? Object.fromEntries(doc.opened_hours)
        : doc.opened_hours;
      
      for (const [hour, isAvailable] of Object.entries(openedHours)) {
        if (isAvailable) {
          dateHours[hour] = (dateHours[hour] || 0) + 1;
        }
      }
      
      aggregatedData.set(doc.date, dateHours);
    }

    // Convert to array format
    const response: AggregatedResponse[] = Array.from(aggregatedData.entries()).map(
      ([date, hours]) => ({
        date,
        hours,
      })
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error aggregating disponibilities:', error);
    return NextResponse.json(
      { error: 'Failed to aggregate disponibilities' },
      { status: 500 }
    );
  }
}