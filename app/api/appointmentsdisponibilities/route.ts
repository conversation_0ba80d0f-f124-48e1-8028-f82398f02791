import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import AppointmentDisponibility from '@/models/AppointmentDisponibility';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

interface PopulatedUser {
  _id: string;
  name: string;
  email: string;
}

interface DisponibilityDocument {
  _id: string;
  user_id: PopulatedUser;
  created_by: PopulatedUser;
  date: string;
  opened_hours: Record<string, boolean>;
  created_at: Date;
  updated_at: Date;
}

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Find all disponibilities for the user in the date range
    const disponibilities = await AppointmentDisponibility.find({
      user_id: userId
    });

    return NextResponse.json(disponibilities);
  } catch (error) {
    console.error('GET disponibilities error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch disponibilities' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const body = await request.json();
    
    // Ensure required fields are present
    if (!body.user_id || !body.date || !body.opened_hours) {
      return NextResponse.json(
        { error: 'Missing required fields' }, 
        { status: 400 }
      );
    }

    // Add created_by field from session
    body.created_by = session.user.id;

    // Check if a record already exists for this user and date
    const existingRecord = await AppointmentDisponibility.findOne({
      user_id: body.user_id,
      date: body.date
    });

    if (existingRecord) {
      // Update the existing record
      const updated = await AppointmentDisponibility.findByIdAndUpdate(
        existingRecord._id,
        { 
          $set: { 
            opened_hours: body.opened_hours,
            updated_at: new Date(),
            // Keep the original user_id and created_by
            user_id: body.user_id
          }
        },
        { new: true }
      )
      .populate('user_id', 'name email')
      .populate('created_by', 'name email');

      return NextResponse.json(updated);
    }

    // Create new record
    const newDisponibility = await AppointmentDisponibility.create({
      ...body,
      created_by: session.user.id,
      user_id: body.user_id
    });

    const populated = await AppointmentDisponibility.findById(newDisponibility._id)
      .populate('user_id', 'name email')
      .populate('created_by', 'name email');

    return NextResponse.json(populated, { status: 201 });
  } catch (error) {
    console.error('Error creating disponibility:', error);
    return NextResponse.json(
      { error: 'Failed to create disponibility' }, 
      { status: 500 }
    );
  }
}

// Add PUT endpoint for updating existing disponibilities
export async function PUT(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const body = await request.json();
    
    if (!body._id || !body.user_id || !body.opened_hours) {
      return NextResponse.json(
        { error: 'Missing required fields' }, 
        { status: 400 }
      );
    }

    const updated = await AppointmentDisponibility.findByIdAndUpdate(
      body._id,
      { 
        $set: { 
          opened_hours: body.opened_hours,
          user_id: body.user_id,
          updated_at: new Date()
        }
      },
      { new: true }
    )
    .populate('user_id', 'name email')
    .populate('created_by', 'name email');

    if (!updated) {
      return NextResponse.json(
        { error: 'Disponibility not found' }, 
        { status: 404 }
      );
    }

    return NextResponse.json(updated);
  } catch (error) {
    console.error('Error updating disponibility:', error);
    return NextResponse.json(
      { error: 'Failed to update disponibility' }, 
      { status: 500 }
    );
  }
} 