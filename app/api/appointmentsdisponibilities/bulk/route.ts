import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import AppointmentDisponibility from '@/models/AppointmentDisponibility';
import dbConnect from '@/lib/db';
import mongoose from 'mongoose';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { disponibilities } = await req.json();
    
    if (!Array.isArray(disponibilities) || disponibilities.length === 0) {
      return NextResponse.json({ error: 'Invalid data format' }, { status: 400 });
    }
    
    await dbConnect();
    
    // Process each disponibility
    const results = {
      success: true,
      updated: 0,
      created: 0,
      errors: 0,
      details: []
    };
    
    for (const disp of disponibilities) {
      try {
        // Check if the disponibility already exists
        const existing = await AppointmentDisponibility.findOne({
          user_id: disp.user_id,
          appointment_id: disp.appointment_id
        });
        
        if (existing) {
          // Update existing record
          existing.disponibility = disp.disponibility;
          existing.updated_at = new Date();
          await existing.save();
          results.updated++;
          results.details.push({
            appointment_id: disp.appointment_id,
            status: 'updated',
            disponibility: disp.disponibility
          });
        } else {
          // Create new record
          const newDisp = new AppointmentDisponibility({
            user_id: disp.user_id,
            appointment_id: disp.appointment_id,
            disponibility: disp.disponibility,
            created_by: session.user.id,
            created_at: new Date(),
            updated_at: new Date()
          });
          
          await newDisp.save();
          results.created++;
          results.details.push({
            appointment_id: disp.appointment_id,
            status: 'created',
            disponibility: disp.disponibility
          });
        }
      } catch (error) {
        console.error('Error processing disponibility:', error);
        results.errors++;
        results.details.push({
          appointment_id: disp.appointment_id,
          status: 'error',
          error: error.message
        });
      }
    }
    
    return NextResponse.json(results);
  } catch (error) {
    console.error('Error in bulk disponibilities update:', error);
    return NextResponse.json(
      { error: 'Failed to update disponibilities', details: error.message },
      { status: 500 }
    );
  }
} 