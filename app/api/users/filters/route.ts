import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SETTINGS_PERMISSIONS } from '@/types/permission-codes';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';

export async function GET(request: Request) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    if (!session.user.permissions?.includes(SETTINGS_PERMISSIONS.MANAGE_RESERVATION_SETTINGS)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Parse query parameters for pagination
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const search = url.searchParams.get('search') || '';
    
    const skip = (page - 1) * limit;

    // Build search query
    const searchQuery = search ? {
      $or: [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ]
    } : {};

    // Add filter to exclude deleted users
    const query = {
      ...searchQuery,
      deletedAt: null
    };

    const [users, total] = await Promise.all([
      User.find(query)
        .select('name email roles reservationFilters')
        .populate('roles', 'name reservationFilters')
        .skip(skip)
        .limit(limit)
        .lean(),
      User.countDocuments(query)
    ]);

    return NextResponse.json({ 
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('GET user filters error:', error);
    return NextResponse.json({ error: 'Failed to fetch user filters' }, { status: 500 });
  }
}
