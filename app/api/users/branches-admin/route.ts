import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import Branch from '@/models/Branch';
import bcrypt from 'bcryptjs';
import Role from '@/models/Role';

export async function POST(request: Request) {
  try {
    await dbConnect();
    const body = await request.json();
    const { 
      name, 
      email, 
      password, 
      branchId, 
      phone,
      isTaxable,
      tvq,
      tps,
      taxationType,
      ...otherFields 
    } = body;

    // Check if email exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already exists' },
        { status: 400 }
      );
    }

    // Validate phone number format
    if (!phone || typeof phone !== 'string' || !phone.startsWith('+1') || phone.length < 12) {
      return NextResponse.json(
        { error: 'Phone number must be in format +1XXXXXXXXXX' },
        { status: 400 }
      );
    }

    // Find the BranchesAdmin role
    const branchAdminRole = await Role.findOne({ name: 'BranchesAdmin' });
    if (!branchAdminRole) {
      return NextResponse.json(
        { error: 'BranchesAdmin role not found' },
        { status: 500 }
      );
    }
//TODO:use random password and send it to the user email
    // Create user with BranchesAdmin role
    const hashedPassword = await bcrypt.hash(password, 10);
    const user = await User.create({
      name,
      email,
      password: hashedPassword,
      phone,
      roles: [branchAdminRole._id], // Use the found role ID
      isActive: true,
      taxInfo: {
        isTaxable,
        tvq: isTaxable === 'yes' ? Number(tvq) : undefined,
        tps: isTaxable === 'yes' ? Number(tps) : undefined,
        taxationType
      },
      metadata: otherFields,
    });

    // Add user as branch responsible - already validated branch ID exists
    if (branchId) {
      await Branch.findByIdAndUpdate(branchId, {
        $addToSet: { responsible: user._id } // Use $addToSet to prevent duplicates
      });
    }

    // Return user without password
    const userWithoutPassword = await User.findById(user._id)
      .select('-password')
      .populate('roles');

    return NextResponse.json(userWithoutPassword);
  } catch (error) {
    console.error('Create BranchesAdmin error:', error);
    return NextResponse.json(
      { error: 'Failed to create BranchesAdmin' },
      { status: 500 }
    );
  }
} 