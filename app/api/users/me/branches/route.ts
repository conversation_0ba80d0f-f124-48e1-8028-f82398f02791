import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import dbConnect from '@/lib/db';
import Branch from '@/models/Branch';

export async function GET() {
  try {
    await dbConnect();
    const session = await getServerSession();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Find all branches where the user is a responsible (admin)
    const branches = await Branch.find({
      responsible: session.user.id
    })
    .select('_id name')
    .lean();

    // For BranchAdmin, we typically expect one branch
    const branch = branches[0];

    if (!branch) {
      ////console.log('No branches found for user:', session.user.id);
      return NextResponse.json({ error: 'No branch found' }, { status: 404 });
    }

    ////console.log('Found branch:', branch);
    return NextResponse.json({ branch, branches });
  } catch (error) {
    console.error('GET user branches error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user branches' },
      { status: 500 }
    );
  }
} 