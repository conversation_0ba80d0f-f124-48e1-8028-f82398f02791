import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import Role from '@/models/Role';
import Branch from '@/models/Branch';
import { Types } from 'mongoose';

// Type for Mongoose documents after lean()
interface LeanDocument {
  _id: Types.ObjectId;
  __v?: number;
  [key: string]: any;
}

// User ID type that can handle different formats
type UserId = string | Types.ObjectId | undefined | null;

export async function GET() {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get all users except deleted ones
    const users = await User.find({ isDeleted: { $ne: true } })
      .select('_id name email roles isActive')
      .populate({ path: 'roles', select: 'name' })
      .lean();
      
    // Get all branches with populated users
    const branches = await Branch.find()
      .select('_id name responsible agents sellers')
      .lean();
    
    // Map users to include their branch associations
    const usersWithBranches = users.map((user: LeanDocument) => {
      const userBranches = [];
      
      // Check if user is in any branch's responsible, agents, or sellers array
      for (const branch of branches as LeanDocument[]) {
        
        // Check responsible array
        if (branch.responsible) {
          const responsibleIds = Array.isArray(branch.responsible) 
            ? branch.responsible 
            : [branch.responsible];
            
          if (responsibleIds.some((id: UserId) => id?.toString() === user._id?.toString())) {
            userBranches.push({
              id: branch._id.toString(),
              name: branch.name,
              role: 'responsible'
            });
          }
        }
        
        // Check agents array
        if (branch.agents && Array.isArray(branch.agents)) {
          if (branch.agents.some((id: UserId) => id?.toString() === user._id?.toString())) {
            userBranches.push({
              id: branch._id.toString(),
              name: branch.name,
              role: 'agent'
            });
          }
        }
        
        // Check sellers array
        if (branch.sellers && Array.isArray(branch.sellers)) {
          if (branch.sellers.some((id: UserId) => id?.toString() === user._id?.toString())) {
            userBranches.push({
              id: branch._id.toString(),
              name: branch.name,
              role: 'seller'
            });
          }
        }
      }
      
      return {
        _id: user._id.toString(),
        name: user.name,
        email: user.email,
        roles: user.roles,
        isActive: user.isActive,
        branches: userBranches
      };
    });
    
    return NextResponse.json({ users: usersWithBranches });
  } catch (error) {
    console.error('Error fetching users with branches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
} 