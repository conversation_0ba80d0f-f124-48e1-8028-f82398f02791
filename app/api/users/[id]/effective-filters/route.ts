import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SETTINGS_PERMISSIONS } from '@/types/permission-codes';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import mongoose from 'mongoose';

interface RouteParams {
  params: Promise<{ id: string }>;
}

interface ReservationFilters {
  statusCodes?: string[];
  branchAccess?: 'all' | 'assigned' | 'none';
  assignmentFilter?: 'all' | 'self' | 'none';
  partnerFilter?: 'all' | 'self' | 'none';
  availableColumns?: string[];
}

// Filter merging logic as per design document
function mergeFilters(roleFilters: ReservationFilters[], userFilters?: ReservationFilters): ReservationFilters {
  const effectiveFilters: ReservationFilters = {};

  // 1. Start with the union of all status codes from the user's roles
  const allStatusCodes = new Set<string>();
  roleFilters.forEach(filter => {
    if (filter.statusCodes) {
      filter.statusCodes.forEach(code => allStatusCodes.add(code));
    }
  });
  effectiveFilters.statusCodes = Array.from(allStatusCodes);

  // 2. Apply the most permissive branch access setting from roles
  const branchAccessPriority = { 'all': 3, 'assigned': 2, 'none': 1 };
  let maxBranchAccess: 'all' | 'assigned' | 'none' = 'none';
  roleFilters.forEach(filter => {
    if (filter.branchAccess && branchAccessPriority[filter.branchAccess] > branchAccessPriority[maxBranchAccess]) {
      maxBranchAccess = filter.branchAccess;
    }
  });
  effectiveFilters.branchAccess = maxBranchAccess;

  // 3. Apply the most restrictive assignment filter from roles
  const assignmentPriority = { 'none': 3, 'self': 2, 'all': 1 };
  let maxAssignmentFilter: 'all' | 'self' | 'none' = 'all';
  roleFilters.forEach(filter => {
    if (filter.assignmentFilter && assignmentPriority[filter.assignmentFilter] > assignmentPriority[maxAssignmentFilter]) {
      maxAssignmentFilter = filter.assignmentFilter;
    }
  });
  effectiveFilters.assignmentFilter = maxAssignmentFilter;

  // 4. Apply the most restrictive partner filter from roles
  const partnerPriority = { 'none': 3, 'self': 2, 'all': 1 };
  let maxPartnerFilter: 'all' | 'self' | 'none' = 'all';
  roleFilters.forEach(filter => {
    if (filter.partnerFilter && partnerPriority[filter.partnerFilter] > partnerPriority[maxPartnerFilter]) {
      maxPartnerFilter = filter.partnerFilter;
    }
  });
  effectiveFilters.partnerFilter = maxPartnerFilter;

  // Handle availableColumns - union of all columns from roles
  const allAvailableColumns = new Set<string>();
  roleFilters.forEach(filter => {
    if (filter.availableColumns) {
      filter.availableColumns.forEach(column => allAvailableColumns.add(column));
    }
  });
  effectiveFilters.availableColumns = Array.from(allAvailableColumns);

  // 5. Override with any user-specific filters
  if (userFilters) {
    if (userFilters.statusCodes && userFilters.statusCodes.length > 0) {
      effectiveFilters.statusCodes = userFilters.statusCodes;
    }
    if (userFilters.branchAccess) {
      effectiveFilters.branchAccess = userFilters.branchAccess;
    }
    if (userFilters.assignmentFilter) {
      effectiveFilters.assignmentFilter = userFilters.assignmentFilter;
    }
    if (userFilters.partnerFilter) {
      effectiveFilters.partnerFilter = userFilters.partnerFilter;
    }
    // For availableColumns, user-specific columns override role columns completely
    if (userFilters.availableColumns && userFilters.availableColumns.length > 0) {
      effectiveFilters.availableColumns = userFilters.availableColumns;
    }
  }

  return effectiveFilters;
}

export async function GET(_request: NextRequest, { params }: RouteParams) {
  try {
    await dbConnect();

    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    if (!session.user.permissions?.includes(SETTINGS_PERMISSIONS.MANAGE_RESERVATION_SETTINGS)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    const user = await User.findById(id)
      .select('name email roles reservationFilters')
      .populate('roles', 'name reservationFilters')
      .lean();

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Type assertion for the populated user data
    const typedUser = user as any;

    // Extract role filters
    const roleFilters: ReservationFilters[] = (typedUser.roles || [])
      .map((role: any) => role.reservationFilters)
      .filter(Boolean);

    // Merge filters according to the design document logic
    const effectiveFilters = mergeFilters(roleFilters, typedUser.reservationFilters);

    return NextResponse.json({
      user: {
        _id: typedUser._id,
        name: typedUser.name,
        email: typedUser.email
      },
      roleFilters,
      userFilters: typedUser.reservationFilters,
      effectiveFilters
    });
  } catch (error) {
    console.error('GET effective filters error:', error);
    return NextResponse.json({ error: 'Failed to fetch effective filters' }, { status: 500 });
  }
}
