import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SETTINGS_PERMISSIONS } from '@/types/permission-codes';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import mongoose from 'mongoose';

interface RouteParams {
  params: Promise<{ id: string }>;
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    await dbConnect();

    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    if (!session.user.permissions?.includes(SETTINGS_PERMISSIONS.MANAGE_RESERVATION_SETTINGS)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Validate reservation filters structure
    const { reservationFilters } = body;
    if (reservationFilters) {
      const validBranchAccess = ['all', 'assigned', 'none'];
      const validAssignmentFilter = ['all', 'self', 'none'];
      const validPartnerFilter = ['all', 'self', 'none'];

      if (reservationFilters.branchAccess && !validBranchAccess.includes(reservationFilters.branchAccess)) {
        return NextResponse.json({ error: 'Invalid branchAccess value' }, { status: 400 });
      }

      if (reservationFilters.assignmentFilter && !validAssignmentFilter.includes(reservationFilters.assignmentFilter)) {
        return NextResponse.json({ error: 'Invalid assignmentFilter value' }, { status: 400 });
      }

      if (reservationFilters.partnerFilter && !validPartnerFilter.includes(reservationFilters.partnerFilter)) {
        return NextResponse.json({ error: 'Invalid partnerFilter value' }, { status: 400 });
      }

      if (reservationFilters.statusCodes && !Array.isArray(reservationFilters.statusCodes)) {
        return NextResponse.json({ error: 'statusCodes must be an array' }, { status: 400 });
      }

      if (reservationFilters.availableColumns && !Array.isArray(reservationFilters.availableColumns)) {
        return NextResponse.json({ error: 'availableColumns must be an array' }, { status: 400 });
      }
    }

    const updatedUser = await User.findByIdAndUpdate(
      id,
      { $set: { reservationFilters } },
      { new: true, runValidators: true }
    ).select('name email roles reservationFilters')
      .populate('roles', 'name reservationFilters');

    if (!updatedUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ user: updatedUser });
  } catch (error) {
    console.error('PUT user filters error:', error);
    return NextResponse.json({ error: 'Failed to update user filters' }, { status: 500 });
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    await dbConnect();

    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    if (!session.user.permissions?.includes(SETTINGS_PERMISSIONS.MANAGE_RESERVATION_SETTINGS)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    const user = await User.findById(id)
      .select('name email roles reservationFilters')
      .populate('roles', 'name reservationFilters')
      .lean();

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ user });
  } catch (error) {
    console.error('GET user filters error:', error);
    return NextResponse.json({ error: 'Failed to fetch user filters' }, { status: 500 });
  }
}
