import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { isValidObjectId } from 'mongoose';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// GET handler to fetch user SMS templates
export async function GET(
  request: Request,
  context: { params: { id: string } }
) {
  const { id } = await context.params;
  
  try {
    await dbConnect();
    
    if (!isValidObjectId(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure the user is either an admin or the same user
    if (session.user.id !== id && !session.user.permissions?.includes('MANAGE_USERS')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    const user = await User.findById(id).select('smsTemplates');
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ smsTemplates: user.smsTemplates || [] });
  } catch (error) {
    console.error('GET user SMS templates error:', error);
    return NextResponse.json({ error: 'Failed to fetch SMS templates' }, { status: 500 });
  }
}

// POST handler to save a user SMS template
export async function POST(
  request: Request,
  context: { params: { id: string } }
) {
  const { id } = await context.params;
  
  try {
    await dbConnect();
    
    if (!isValidObjectId(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure the user is either an admin or the same user
    if (session.user.id !== id && !session.user.permissions?.includes('MANAGE_USERS')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    const { name, content } = await request.json();
    
    if (!name || !content) {
      return NextResponse.json(
        { error: 'Template name and content are required' },
        { status: 400 }
      );
    }

    const user = await User.findById(id);
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Initialize smsTemplates array if it doesn't exist
    if (!user.smsTemplates) {
      user.smsTemplates = [];
    }

    // Create a new template with unique ID
    const templateId = new Date().getTime().toString();
    const newTemplate = {
      id: templateId,
      name,
      content,
      createdAt: new Date()
    };

    // Add the new template
    user.smsTemplates.push(newTemplate);
    user.markModified('smsTemplates'); // Important for mixed types in Mongoose
    await user.save();

    return NextResponse.json({
      success: true,
      template: newTemplate
    });
  } catch (error) {
    console.error('Create SMS template error:', error);
    return NextResponse.json({ error: 'Failed to create SMS template' }, { status: 500 });
  }
}

// DELETE handler to remove a user SMS template
export async function DELETE(
  request: Request,
  context: { params: { id: string } }
) {
  const { id } = context.params;
  const url = new URL(request.url);
  const templateId = url.searchParams.get('templateId');
  
  if (!templateId) {
    return NextResponse.json({ error: 'Template ID is required' }, { status: 400 });
  }
  
  try {
    await dbConnect();
    
    if (!isValidObjectId(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure the user is either an admin or the same user
    if (session.user.id !== id && !session.user.permissions?.includes('MANAGE_USERS')) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }

    const user = await User.findById(id);
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Filter out the template with the specified ID
    if (user.smsTemplates && Array.isArray(user.smsTemplates)) {
      user.smsTemplates = user.smsTemplates.filter(
        (template: any) => template.id !== templateId
      );
      user.markModified('smsTemplates');
      await user.save();
    }

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully'
    });
  } catch (error) {
    console.error('Delete SMS template error:', error);
    return NextResponse.json({ error: 'Failed to delete SMS template' }, { status: 500 });
  }
} 