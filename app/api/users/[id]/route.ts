import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { isValidObjectId, Types } from 'mongoose';
import bcrypt from 'bcryptjs';
import Role from '@/models/Role';
import Branch from '@/models/Branch';
import { withApiLogging } from '../../utils/with-api-logging';

export async function GET(
  request: Request,
  context: { params: { id: string } }
) {
  const { id } = await context.params;
  try {
    await dbConnect();
    
    if (!isValidObjectId(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    const user = await User.findById(id)
      .populate('roles')
      .populate('directPermissions')
      .select('-password');
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('GET user error:', error);
    return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 });
  }
}

export const PUT = withApiLogging(async (request: Request, context: { params: { id: string } }) => {
  const { id } = await context.params;
  try {
    await dbConnect();
    
    if (!isValidObjectId(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    const body = await request.json();
    
    // Create a clean update object with proper typing
    interface UpdateData {
      name: string;
      email: string;
      updatedAt: Date;
      roles: string[];
      directPermissions: string[];
      isActive: boolean;
      phone?: string;
      metadata?: Map<string, any>;
      password?: string;
      branchIds?: Types.ObjectId[];
      [key: string]: any; // Allow additional properties
    }
    
    // Get current user data to compare roles
    const currentUser = await User.findById(id).populate('roles', 'name');
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Extract current role names for comparison
    const currentRoleNames = currentUser.roles.map((role: any) => 
      typeof role === 'string' ? role : role.name
    );
    
    const updateData: UpdateData = {
      name: body.name,
      email: body.email.toLowerCase(),
      updatedAt: new Date(),
      roles: Array.isArray(body.roles) ? body.roles : [],
      directPermissions: Array.isArray(body.directPermissions) ? body.directPermissions : [],
      isActive: body.isActive !== undefined ? body.isActive : true
    };

    // Handle optional fields
    if (body.phone) {
      updateData.phone = body.phone;
    }
    if (body.branchIds !== undefined) {
      if (Array.isArray(body.branchIds) && body.branchIds.every((id: string) => isValidObjectId(id))) {
        updateData.branchIds = body.branchIds;
      } else {
        return NextResponse.json(
          { error: 'Invalid branchIds' },
          { status: 400 }
        );
      }
    }
    
    // Handle metadata field - parse JSON string and convert to Map for Mongoose
    if (body.metadata) {
      try {
        // Parse the JSON string to get a regular object
        const metadataObj = JSON.parse(body.metadata);
        // Convert the object to a Map that Mongoose can handle
        updateData.metadata = new Map(Object.entries(metadataObj));
      } catch (error) {
        console.error('Error parsing metadata:', error);
        // If there's an error parsing the JSON, we won't update the metadata field
      }
    }

    // If updating password, hash it
    if (body.password) {
      updateData.password = await bcrypt.hash(body.password, 10);
    }

    // Validate phone number format if provided
    if (body.phone && (typeof body.phone !== 'string' || !body.phone.startsWith('+1') || body.phone.length < 12)) {
      return NextResponse.json(
        { error: 'Phone number must be in format +1XXXXXXXXXX' },
        { status: 400 }
      );
    }

    // Check if email is being changed and if it's already in use
    if (body.email) {
      const existingUser = await User.findOne({ 
        email: body.email.toLowerCase(),
        _id: { $ne: id }
      });
      if (existingUser) {
        return NextResponse.json(
          { error: 'Email already exists' },
          { status: 400 }
        );
      }
    }

    // Update user
    const user = await User.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    )
      .populate('roles', 'name')
      .populate('directPermissions', 'name')
      .select('-password');

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get the user's new roles
    const newRoles = await Role.find({ _id: { $in: updateData.roles } });
    const newRoleNames = newRoles.map(role => role.name);

    // Check which role changes occurred
    const hasBecomeAdmin = !currentRoleNames.includes('BranchesAdmin') && newRoleNames.includes('BranchesAdmin');
    const hasBecomeAgent = !currentRoleNames.includes('BranchesAgent') && newRoleNames.includes('BranchesAgent');
    const hasBecomeSeller = !currentRoleNames.includes('Seller') && newRoleNames.includes('Seller');
    
    const noLongerAdmin = currentRoleNames.includes('BranchesAdmin') && !newRoleNames.includes('BranchesAdmin');
    const noLongerAgent = currentRoleNames.includes('BranchesAgent') && !newRoleNames.includes('BranchesAgent');
    const noLongerSeller = currentRoleNames.includes('Seller') && !newRoleNames.includes('Seller');


    // Update branches based on role changes
    if (hasBecomeAdmin || hasBecomeAgent || hasBecomeSeller || 
        noLongerAdmin || noLongerAgent || noLongerSeller) {
      
      console.log('Updating branch relationships for user:', id);
      
      // Get all branches - we'll need to update relevant ones
      const branches = await Branch.find();
      let updatedBranches = 0;
      
      for (const branch of branches) {
        let branchUpdated = false;
        
        // Add user to appropriate arrays based on new roles
        if (hasBecomeAdmin) {
          if (!branch.responsible.some((respId: Types.ObjectId) => respId.toString() === id)) {
            branch.responsible.push(id);
            branchUpdated = true;
          }
        }
        
        if (hasBecomeAgent) {
          if (!branch.agents.some((agentId: Types.ObjectId) => agentId.toString() === id)) {
            branch.agents.push(id);
            branchUpdated = true;
          }
        }
        
        if (hasBecomeSeller) {
          if (!branch.sellers.some((sellerId: Types.ObjectId) => sellerId.toString() === id)) {
            branch.sellers.push(id);
            branchUpdated = true;
          }
        }
        
        // Remove user from arrays they should no longer be in
        if (noLongerAdmin) {
          branch.responsible = branch.responsible.filter(
            (respId: Types.ObjectId) => respId.toString() !== id
          );
          branchUpdated = true;
        }
        
        if (noLongerAgent) {
          branch.agents = branch.agents.filter(
            (agentId: Types.ObjectId) => agentId.toString() !== id
          );
          branchUpdated = true;
        }
        
        if (noLongerSeller) {
          branch.sellers = branch.sellers.filter(
            (sellerId: Types.ObjectId) => sellerId.toString() !== id
          );
          branchUpdated = true;
        }
        
        // Save the branch if changes were made
        if (branchUpdated) {
          await branch.save();
          updatedBranches++;
        }
      }
      
      console.log(`Updated ${updatedBranches} branches to reflect role changes`);
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Update user error:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      return NextResponse.json({ 
        error: 'Failed to update user', 
        message: error.message 
      }, { status: 500 });
    }
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
});

export const DELETE = withApiLogging(async (request: Request, context: { params: { id: string } }) => {
  const { id } = await context.params;
  try {
    await dbConnect();
    
    if (!isValidObjectId(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Find the user first to check their roles
    const user = await User.findById(id).populate('roles', 'name');
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Soft delete the user by setting deletedAt timestamp
    await User.findByIdAndUpdate(id, {
      deletedAt: new Date(),
      isActive: false // Also mark as inactive
    });

    // Clean up branch relationships
    console.log('Removing user from all branches:', id);
    const branches = await Branch.find();
    let updatedBranches = 0;
    
    for (const branch of branches) {
      let branchUpdated = false;
      
      // Remove from responsible array
      if (branch.responsible.some((respId: Types.ObjectId) => respId.toString() === id)) {
        branch.responsible = branch.responsible.filter(
          (respId: Types.ObjectId) => respId.toString() !== id
        );
        branchUpdated = true;
      }
      
      // Remove from agents array
      if (branch.agents.some((agentId: Types.ObjectId) => agentId.toString() === id)) {
        branch.agents = branch.agents.filter(
          (agentId: Types.ObjectId) => agentId.toString() !== id
        );
        branchUpdated = true;
      }
      
      // Remove from sellers array
      if (branch.sellers.some((sellerId: Types.ObjectId) => sellerId.toString() === id)) {
        branch.sellers = branch.sellers.filter(
          (sellerId: Types.ObjectId) => sellerId.toString() !== id
        );
        branchUpdated = true;
      }
      
      // Save the branch if changes were made
      if (branchUpdated) {
        await branch.save();
        updatedBranches++;
      }
    }
    
    console.log(`Updated ${updatedBranches} branches to remove soft-deleted user`);
    
    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 });
  }
}); 