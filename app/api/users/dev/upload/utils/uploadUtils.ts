import path from 'path';
import crypto from 'crypto';

/**
 * Returns the absolute upload directory path, joining base and subdirectory.
 * Ensures cross-platform compatibility.
 * @param baseDir - The base upload directory (absolute or relative)
 * @param subDir - The subdirectory for file type/category
 * @returns Absolute path to the upload directory
 */
export function getUploadDir(baseDir: string, subDir: string): string {
  // If baseDir is absolute, use as is; otherwise, resolve from process.cwd()
  const base = path.isAbsolute(baseDir) ? baseDir : path.join(process.cwd(), baseDir);
  return path.join(base, subDir);
}

/**
 * Sanitizes a file name by removing dangerous or unwanted characters.
 * Keeps only alphanumeric, dash, underscore, and dot. Truncates long names.
 * @param fileName - The original file name
 * @returns Sanitized file name
 */
export function sanitizeFileName(fileName: string): string {
  // Remove path separators and unwanted characters
  let name = fileName.replace(/[^a-zA-Z0-9._-]/g, '_');
  // Prevent directory traversal
  name = name.replace(/\.+/g, '.');
  // Truncate to 100 chars max
  if (name.length > 100) {
    const ext = path.extname(name);
    name = name.slice(0, 100 - ext.length) + ext;
  }
  return name;
}

/**
 * Generates a unique filename using crypto hash and timestamp
 * @param originalFileName - The original file name
 * @param userId - The user ID for additional uniqueness
 * @param fileBuffer - The file buffer for content-based hashing
 * @returns Unique hashed filename
 */
export function generateUniqueFileName(
  originalFileName: string,
  userId: string,
  fileBuffer: Buffer
): string {
  const sanitizedName = sanitizeFileName(originalFileName);
  const ext = path.extname(sanitizedName);
  const nameWithoutExt = path.basename(sanitizedName, ext);

  // Create a hash based on file content, user ID, and timestamp
  const contentHash = crypto.createHash('sha256')
    .update(fileBuffer)
    .update(userId)
    .update(Date.now().toString())
    .digest('hex');

  // Use first 16 characters of hash for uniqueness while keeping readable length
  const shortHash = contentHash.substring(0, 16);

  // Generate timestamp for additional uniqueness
  const timestamp = Date.now();

  // Combine: timestamp-hash-originalname.ext
  return `${timestamp}-${shortHash}-${nameWithoutExt}${ext}`;
}

/**
 * Generates a secure random filename that doesn't expose original filename
 * @param originalFileName - The original file name (used only for extension)
 * @param userId - The user ID for additional entropy
 * @param fileBuffer - The file buffer for content-based hashing
 * @returns Secure random filename
 */
export function generateSecureFileName(
  originalFileName: string,
  userId: string,
  fileBuffer: Buffer
): string {
  const ext = path.extname(sanitizeFileName(originalFileName));

  // Create a secure hash
  const hash = crypto.createHash('sha256')
    .update(fileBuffer)
    .update(userId)
    .update(crypto.randomBytes(16))
    .update(Date.now().toString())
    .digest('hex');

  // Use first 32 characters for a good balance of security and readability
  const secureHash = hash.substring(0, 32);

  return `${secureHash}${ext}`;
}

/**
 * Generates a unique filename with collision detection
 * @param originalFileName - The original file name
 * @param userId - The user ID for additional entropy
 * @param fileBuffer - The file buffer for content-based hashing
 * @param uploadDir - The upload directory to check for collisions
 * @returns Promise<string> - Unique filename guaranteed not to exist
 */
export async function generateUniqueFileNameWithCollisionCheck(
  originalFileName: string,
  userId: string,
  fileBuffer: Buffer,
  uploadDir: string
): Promise<string> {
  const fs = await import('fs/promises');

  let filename = generateSecureFileName(originalFileName, userId, fileBuffer);
  let filePath = path.join(uploadDir, filename);
  let counter = 1;

  // Check for collisions and add counter if needed
  while (await fileExists(filePath)) {
    const ext = path.extname(filename);
    const nameWithoutExt = path.basename(filename, ext);
    filename = `${nameWithoutExt}_${counter}${ext}`;
    filePath = path.join(uploadDir, filename);
    counter++;
  }

  return filename;
}

/**
 * Checks if a file exists
 * @param filePath - The file path to check
 * @returns Promise<boolean> - True if file exists, false otherwise
 */
async function fileExists(filePath: string): Promise<boolean> {
  const fs = await import('fs/promises');
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validates file type and size
 * @param file - The file to validate
 * @param maxSize - Maximum file size in bytes (default: 5MB)
 * @param allowedTypes - Array of allowed MIME types
 * @returns Object with validation result and error message if any
 */
export function validateFile(
  file: File,
  maxSize: number = 5 * 1024 * 1024, // 5MB default
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf']
): { isValid: boolean; error?: string } {
  // Check file size
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size exceeds maximum allowed size of ${Math.round(maxSize / 1024 / 1024)}MB`
    };
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }

  // Check file extension matches MIME type
  const ext = path.extname(file.name).toLowerCase();
  const mimeToExt: Record<string, string[]> = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'application/pdf': ['.pdf']
  };

  const expectedExts = mimeToExt[file.type];
  if (expectedExts && !expectedExts.includes(ext)) {
    return {
      isValid: false,
      error: `File extension ${ext} does not match MIME type ${file.type}`
    };
  }

  return { isValid: true };
}