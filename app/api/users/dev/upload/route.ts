import { NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';
import { getUploadDir, generateUniqueFileNameWithCollisionCheck, validateFile } from '@/app/api/users/dev/upload/utils/uploadUtils';
import User from '@/models/User';
import dbConnect from '@/lib/db';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const type = formData.get('type') as string;
    const file = formData.get('file') as File;
    const userId = formData.get('userId') as string;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }
    if (!userId) {
      return NextResponse.json(
        { error: 'No userId provided' },
        { status: 400 }
      );
    }

    // Validate file type and size
    const validation = validateFile(file);
    if (!validation.isValid) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }

    await dbConnect();
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Use env variable for base upload dir, fallback to 'uploads' (NOT public)
    const baseUploadDir = process.env.UPLOADS_DIR || 'uploads';
    const subDir = type === 'checkSpecimen' ? 'specimencheque' : 'autresdocuments';
    const uploadDir = getUploadDir(baseUploadDir, subDir);

    await mkdir(uploadDir, { recursive: true });

    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // Generate secure unique filename with collision detection
    const uniqueFilename = await generateUniqueFileNameWithCollisionCheck(
      file.name,
      userId,
      buffer,
      uploadDir
    );
    const filePath = path.join(uploadDir, uniqueFilename);

    await writeFile(filePath, buffer as unknown as Uint8Array);

    // Update user's document reference
    const reference = `${subDir}/${uniqueFilename}`;
    if (type === 'checkSpecimen') {
      user.documents = user.documents || {};
      user.documents.checkSpecimen = reference;
    } else if (type === 'otherDocuments') {
      user.documents = user.documents || {};
      if (!Array.isArray(user.documents.otherDocuments)) {
        user.documents.otherDocuments = [];
      }
      user.documents.otherDocuments.push(reference);
    }

    // Also add to uploads object with key-value pair using existing structure
    if (!user.uploads || typeof user.uploads !== 'object') {
      user.uploads = {};
    }

    // Add the upload to the uploads object using the existing structure (key: filename)
    user.uploads[type] = reference;

    // Mark the uploads field as modified to ensure Mongoose saves it
    user.markModified('uploads');

    await user.save();

    // Only return a backend reference (not a public URL)
    return NextResponse.json({
      success: true,
      file: uniqueFilename,
      type,
      reference
    });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
} 