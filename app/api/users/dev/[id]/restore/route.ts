import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { isValidObjectId } from 'mongoose';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserRoles } from '@/app/api/utils/server-permission-utils';

export async function POST(
  _request: Request,
  context: { params: { id: string } }
) {
  const { id } = context.params;
  
  try {
    await dbConnect();
    
    // Validate user ID
    if (!isValidObjectId(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Get current user session for authorization
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user roles for authorization
    if (!session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }
    
    const userRoles = session.user.roles || [];
    
    // Check if user is a SuperAdmin
    const isSuperAdmin = userRoles.some((role: any) =>
      role.name === 'SuperAdmin' || role._id?.toString() === '67add3214badd3283e873329'
    );
    
    // Check if user is a BranchesAdmin
    const isBranchAdmin = userRoles.some((role: any) =>
      role.name === 'BranchesAdmin' || role._id?.toString() === '67c032e90af5117479e27731'
    );

    // Only SuperAdmin and BranchesAdmin can restore users
    if (!isSuperAdmin && !isBranchAdmin) {
      return NextResponse.json({
        error: 'You do not have permission to restore users'
      }, { status: 403 });
    }

    // Find the user to restore
    const user = await User.findById(id);
    
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is actually deleted
    if (!user.deletedAt) {
      return NextResponse.json({ 
        error: 'User is not deleted' 
      }, { status: 400 });
    }

    // For BranchesAdmin, check if they can manage this user
    // (This would require additional logic to check if the user belongs to their branches)
    // For now, we'll allow BranchesAdmin to restore any user, but this could be restricted

    // Restore the user by unsetting deletedAt and setting isActive to true
    const restoredUser = await User.findByIdAndUpdate(
      id,
      {
        $unset: { deletedAt: 1 },
        isActive: true,
        updatedAt: new Date()
      },
      { 
        new: true,
        runValidators: true
      }
    ).populate('roles', 'name').select('-password');

    if (!restoredUser) {
      return NextResponse.json({ 
        error: 'Failed to restore user' 
      }, { status: 500 });
    }

    return NextResponse.json({ 
      message: 'User restored successfully',
      user: restoredUser
    });

  } catch (error) {
    console.error('Error restoring user:', error);
    return NextResponse.json({ 
      error: 'Failed to restore user' 
    }, { status: 500 });
  }
}
