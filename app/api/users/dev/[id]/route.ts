import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { isValidObjectId, Types } from 'mongoose';
import bcrypt from 'bcryptjs';
import Role from '@/models/Role';
import Branch from '@/models/Branch';
import { Partner } from '@/models/Partner';
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { getUserPermissions, getUserRoles } from '@/app/api/utils/server-permission-utils';
import * as RoleUtils from '@/lib/utils/role-utils';
import TaxType from '@/models/TaxType';
import { validateQSTRegistrationNumber } from '../../../utils/qst-validation';
import { getUserFromToken } from '@/app/api/affectations/utils/mobile_auth_utils';
import { normalizePhoneNumber } from '@/lib/twilio';
import { validateTPSTVQTaxInfo, userRolesRequireTaxInfo } from '@/app/api/utils/tax-validation';

export async function PUT(
  request: Request,
  context: { params: { id: string } }
) {
  const { id } = await context.params;
  const requestBody = await request.json();
  const requestHeaders = request.headers;
  let session=await getUserFromToken(requestBody,requestHeaders);
  try {
    await dbConnect();
    if(!session){

      session = await getServerSession(authOptions);
    }
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    if (session && !session?.user.roles) {
      session.user.roles = await getUserRoles(session);
    }
    // Check if user is a SuperAdmin
    const userRoles = session.user.roles || [];
    const isSuperAdmin = RoleUtils.isSuperAdmin(session);

    // Check if user is a BranchesAdmin
    const isBranchAdmin = RoleUtils.isBranchesAdmin(session);

    // Check if user is trying to edit their own profile
    const isEditingSelf = session.user._id.toString() === id;

    // If user is not a SuperAdmin, BranchesAdmin, or editing their own profile, they cannot modify users
    if (!isSuperAdmin && !isBranchAdmin && !isEditingSelf) {
      return NextResponse.json({
        error: 'You do not have permission to modify users'
      }, { status: 403 });
    }

    if (!isValidObjectId(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    const body = requestBody;

    // Create a clean update object with proper typing
    interface UpdateData {
      name: string;
      email: string;
      updatedAt: Date;
      roles?: string[];
      directPermissions?: string[];
      isActive?: boolean;
      phone?: string;
      metadata?: Map<string, any>;
      password?: string;
      partnerId?: string;
      // Additional user profile fields
      birthDate?: string;
      companyName?: string;
      address?: string;
      city?: string;
      postalCode?: string;
      emergencyContact?: {
        name: string;
        phone: string;
        relationship?: string;
      };
      taxInfo?: {
        qstRegistrationNumber?: string;
        tpsRegistrationNumber?: string;
        isTaxable?: string;
        taxtypeid?: string;
        qstValidation?: {
          isValid: boolean;
          validatedAt: Date;
          status?: string;
          statusDescription?: string;
          apiResponse?: any;
          errorMessage?: string;
        };
      };
      socialAssurance?: {
        nas: string;
        expiry: string;
      };
      fcmTokens?: string[];
      [key: string]: any; // Allow additional properties
    }

    // Get current user data to compare roles
    const currentUser = await User.findById(id).populate('roles', 'name');
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // For tax info validation, we need the actual role objects or IDs, not just names
    const currentUserRoles = currentUser.roles;

    // Define fields that users can edit on their own profiles
    const selfEditableFields = [
      'name', 'email', 'phone', 'birthDate', 'companyName', 'address',
      'city', 'postalCode', 'emergencyContact', 'taxInfo', 'socialAssurance', 'fcmTokens'
    ];

    // Define fields that should be ignored (client-side fields, not actual user data)
    const ignoredFields = [
      'id', 'isMobile', 'updatedAt', '_id'
    ];

    // If user is editing their own profile, restrict to self-editable fields only
    if (isEditingSelf && !isSuperAdmin && !isBranchAdmin) {
      // Check if user is trying to modify restricted fields
      const restrictedFields = Object.keys(body).filter(field =>
        !selfEditableFields.includes(field) && !ignoredFields.includes(field)
      );

      if (restrictedFields.length > 0) {
        return NextResponse.json({
          error: 'You can only modify your own profile information',
          restrictedFields: restrictedFields
        }, { status: 403 });
      }
    }

    const updateData: UpdateData = {
      name: body.name,
      email: body.email,
      updatedAt: new Date()
    };

    // For admin users, include admin-only fields
    if (isSuperAdmin || isBranchAdmin) {
      updateData.roles = Array.isArray(body.roles) ? body.roles : [];
      updateData.directPermissions = Array.isArray(body.directPermissions) ? body.directPermissions : [];
      updateData.isActive = body.isActive !== undefined ? body.isActive : true;
    }
    // For self-editing users, these fields are not included in updateData

    // Handle optional fields
    if (body.phone) {
      updateData.phone = body.phone;
    }

    // Handle partnerId field (admin-only)
    if (body.partnerId !== undefined && (isSuperAdmin || isBranchAdmin)) {
      updateData.partnerId = body.partnerId;
    }

    // Handle additional user profile fields
    if (body.birthDate !== undefined) {
      updateData.birthDate = body.birthDate;
    }
    if (body.companyName !== undefined) {
      updateData.companyName = body.companyName;
    }
    if (body.address !== undefined) {
      updateData.address = body.address;
    }
    if (body.city !== undefined) {
      updateData.city = body.city;
    }
    if (body.postalCode !== undefined) {
      updateData.postalCode = body.postalCode;
    }
    if (body.emergencyContact) {
      // Normalize emergencyContact.phone using the standard function
      let phone = body.emergencyContact.phone || '';
      if (phone) {
        const normalizedPhone = normalizePhoneNumber(phone);
        if (normalizedPhone && normalizedPhone.length === 10) {
          phone = normalizedPhone;
        } else {
          // fallback: if normalization doesn't result in 10 digits, keep as is
          phone = body.emergencyContact.phone;
        }
      }
      updateData.emergencyContact = {
        name: body.emergencyContact.name || '',
        phone,
        relationship: body.emergencyContact.relationship || ''
      };
    }
    if (body.taxInfo) {
      let taxtypeCode = '';
      let taxtypeid = body.taxInfo.taxtypeid;

      // Check if user's roles require tax information
      // For self-editing users, body.roles is not provided, so use current user's roles
      const userRoles = body.roles || currentUserRoles;
      const rolesRequireTaxInfo = userRolesRequireTaxInfo(userRoles);

      console.log('Tax info validation - userRoles:', userRoles);
      console.log('Tax info validation - rolesRequireTaxInfo:', rolesRequireTaxInfo);

      // If user's roles don't require tax info OR user is not taxable, clear all tax-related data
      if (!rolesRequireTaxInfo || body.taxInfo.isTaxable === 'no') {
        updateData.taxInfo = {
          isTaxable: 'no',
          taxtypeid: undefined,
          qstRegistrationNumber: '',
          tpsRegistrationNumber: '',
        };
        taxtypeCode = ''; // Ensure no validation is triggered
      } else {
        // Only process tax type if user is taxable
        if (taxtypeid && isValidObjectId(taxtypeid)) {
          const taxTypeDoc = await TaxType.findById(taxtypeid).lean();
          if (taxTypeDoc && !Array.isArray(taxTypeDoc) && typeof taxTypeDoc.code === 'string') {
            taxtypeCode = taxTypeDoc.code;
          }
        } else {
          taxtypeid = undefined; // Not valid, don't include
        }

        // Set up taxInfo with all provided fields
        updateData.taxInfo = {
          ...body.taxInfo,
          taxtypeid, // Only valid if not undefined
          qstRegistrationNumber: body.taxInfo.qstRegistrationNumber || '',
          tpsRegistrationNumber: body.taxInfo.tpsRegistrationNumber || '',
        };
        // Remove taxtypeid if undefined
        if (!taxtypeid && updateData.taxInfo) delete updateData.taxInfo.taxtypeid;
      }

      // Strict validation for tpstvq tax type - only when roles require tax info AND isTaxable is 'yes'
      if (rolesRequireTaxInfo && taxtypeCode === 'tpstvq' && updateData.taxInfo && updateData.taxInfo.isTaxable === 'yes') {

        const validation = await validateTPSTVQTaxInfo(updateData.taxInfo, taxtypeCode);

        if (!validation.isValid) {
          return NextResponse.json(
            {
              error: 'Tax registration validation failed for TPSTVQ tax type',
              details: validation.errors
            },
            { status: 400 }
          );
        }

        // Store the QST validation result
        if (validation.qstValidation) {
          updateData.taxInfo.qstValidation = {
            isValid: validation.qstValidation.isValid,
            validatedAt: new Date(),
            status: validation.qstValidation.status,
            statusDescription: validation.qstValidation.statusDescription,
            apiResponse: validation.qstValidation.apiResponse,
            errorMessage: validation.qstValidation.errorMessage
          };
        }
      } else {
        // For non-tpstvq tax types, validate QST if provided (non-blocking)
        if (updateData.taxInfo && updateData.taxInfo.qstRegistrationNumber) {
          try {
            const qstValidation = await validateQSTRegistrationNumber(updateData.taxInfo.qstRegistrationNumber);
            updateData.taxInfo.qstValidation = {
              isValid: qstValidation.isValid,
              validatedAt: new Date(),
              status: qstValidation.status,
              statusDescription: qstValidation.statusDescription,
              apiResponse: qstValidation.apiResponse,
              errorMessage: qstValidation.errorMessage
            };
          } catch (validationError) {
            console.error('QST validation failed:', validationError);
            updateData.taxInfo.qstValidation = {
              isValid: false,
              validatedAt: new Date(),
              errorMessage: validationError instanceof Error ? validationError.message : 'Validation service unavailable'
            };
          }
        }
      }
    }
    // If taxInfo is empty after filtering, remove it from updateData
    if (!updateData.taxInfo || Object.keys(updateData.taxInfo).length === 0) {
      delete updateData.taxInfo;
    }


    // Handle socialAssurance field
    if (body.socialAssurance) {
      updateData.socialAssurance = {
        nas: body.socialAssurance.nas || '',
        expiry: body.socialAssurance.expiry || ''
      };
    }

    // Handle fcmTokens field
    if (body.fcmTokens !== undefined) {
      updateData.fcmTokens = Array.isArray(body.fcmTokens) ? body.fcmTokens : [];
    }

    // Handle metadata field
    if (body.metadata) {
      if (typeof body.metadata === 'string') {
        try {
          // Parse the JSON string to get a regular object
          const metadataObj = JSON.parse(body.metadata);
          // Convert the object to a Map that Mongoose can handle
          updateData.metadata = new Map(Object.entries(metadataObj));
        } catch (error) {
          console.error('Error parsing metadata string:', error);
        }
      } else if (typeof body.metadata === 'object') {
        // Already an object, convert to Map
        updateData.metadata = new Map(Object.entries(body.metadata));
      }
    }

    // If updating password, hash it (admin-only)
    if (body.password && (isSuperAdmin || isBranchAdmin)) {
      updateData.password = await bcrypt.hash(body.password, 10);
    }

    // Validate phone number format if provided
    if (body.phone && (typeof body.phone !== 'string' || !/^\d{10}$/.test(body.phone))) {
      return NextResponse.json(
        { error: 'Phone number must be 10 digits (e.g. 5141234567)' },
        { status: 400 }
      );
    }
    // Only process phone number if it's provided
    if (body.phone) {
      const phone = body.phone.startsWith('+1') ? body.phone : `+1${body.phone}`;
      body.phone = phone;
    }
    // Check if email is being changed and if it's already in use
    if (body.email) {
      const existingUser = await User.findOne({
        email: body.email.toLowerCase(),
        _id: { $ne: id },
        $or: [
          { deletedAt: { $exists: false } },
          { deletedAt: null }
        ]
      });
      if (existingUser) {
        return NextResponse.json(
          { error: 'Email already exists' },
          { status: 400 }
        );
      }
    }

    // For branch admins, only modify roles for their branches
    // Skip this logic for self-editing users
    if (!isSuperAdmin && isBranchAdmin && !isEditingSelf) {
      // Get the admin's branches
      const adminBranches = await Branch.find({
        responsible: session.user.id
      }).select('_id').lean();

      const adminBranchIds = adminBranches.map(branch => branch._id?.toString());

      if (adminBranchIds.length === 0) {
        return NextResponse.json({
          error: 'You do not have admin access to any branches'
        }, { status: 403 });
      }

      // For branch admins, don't modify the global roles array
      // We'll handle role assignments at the branch level
      delete updateData.roles;
    }

    // For self-editing users, remove admin-only fields from updateData
    if (isEditingSelf && !isSuperAdmin && !isBranchAdmin) {
      delete updateData.roles;
      delete updateData.directPermissions;
      delete updateData.isActive;
    }

    // Update user (without roles for branch admins)
    const user = await User.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('roles', 'name');

    // Handle branch roles and assignments if branchIds are provided
    // Skip this for self-editing users who are not admins
    if (Array.isArray(body.branchIds) && body.branchIds.length > 0 && body.branchRoles &&
        (isSuperAdmin || isBranchAdmin)) {
      const { isAdmin, isAgent, isSeller, isPAP } = body.branchRoles;

      // Only proceed if user has at least one branch-specific role
      if (isAdmin || isAgent || isSeller || isPAP) {
        // Get all branches to update
        const allBranches = await Branch.find().lean();

        // Process each branch
        for (const branch of allBranches) {
          const branchId = branch._id.toString();
          const shouldHave = body.branchIds.includes(branchId);
          let needsUpdate = false;

          // Helper function to safely process arrays
          const processArray = (arrayName: string, shouldInclude: boolean) => {
            let array = branch[arrayName] || [];
            if (!Array.isArray(array)) array = [];

            const userId = user._id.toString();
            const hasUser = array.some((id: any) => id?.toString() === userId);

            if (shouldInclude && !hasUser) {
              // Add user to array
              array.push(userId);
              needsUpdate = true;
            } else if (!shouldInclude && hasUser) {
              // Remove user from array
              array = array.filter((id: any) => id?.toString() !== userId);
              needsUpdate = true;
            }

            return array;
          };

          // Update arrays based on roles and branch selection
          const responsible = processArray('responsible', shouldHave && isAdmin);
          const agents = processArray('agents', shouldHave && isAgent);
          const sellers = processArray('sellers', shouldHave && isSeller);
          const paps = processArray('paps', shouldHave && isPAP);

          // Update branch if any changes were made
          if (needsUpdate) {
            await Branch.findByIdAndUpdate(branchId, {
              responsible,
              agents,
              sellers,
              paps
            });
          }
        }
      }
    }

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Ensure socialAssurance and emergencyContact are always present in the response
    if (user && !Array.isArray(user)) {
      if (!user.socialAssurance) user.socialAssurance = { nas: '', expiry: '' };
      if (!user.emergencyContact) user.emergencyContact = { name: '', phone: '', relationship: '' };
      if (!user.taxInfo) user.taxInfo = { isTaxable: 'no', taxtypeid: null, qstRegistrationNumber: '' };
      // Ensure branchIds is always present in the response
      if (!user.branchIds) {
        user.branchIds = [];
      }
    }

    // Check if user has branch-related roles but branchIds is not provided
    // Only apply this validation for SuperAdmin users - branch admins can remove users from their branches
    // Skip this validation for self-editing users
    if (isSuperAdmin && !isEditingSelf && body.roles && Array.isArray(body.roles) && (!body.branchIds || !Array.isArray(body.branchIds) || body.branchIds.length === 0)) {
      const userRoles = await Role.find({ _id: { $in: body.roles } });
      const roleNames = userRoles.map(role => role.name);

      const isAdmin = roleNames.includes('BranchesAdmin');
      const isAgent = roleNames.includes('BranchesAgent');
      const isSeller = roleNames.includes('Seller');

      if (isAdmin || isAgent || isSeller) {
        return NextResponse.json({
          error: 'Branch assignment required',
          details: 'At least one branch must be assigned for branch-related roles'
        }, { status: 400 });
      }
    }

    // Handle branch assignments if branchIds is provided
    // Skip this for self-editing users who are not admins
    if (body.branchIds && Array.isArray(body.branchIds) && (isSuperAdmin || isBranchAdmin)) {
      // Deduplicate branch IDs to prevent double-assignments
      const uniqueBranchIds = Array.from(new Set(body.branchIds));
      console.log(`Processing branch assignments for user ${id}. Branch IDs:`, uniqueBranchIds);

      try {
        // Get all branches the user is currently associated with
        const userBranches = await Branch.find({
          $or: [
            { responsible: id },
            { agents: id },
            { sellers: id }
          ]
        }).lean();

        console.log(`User is currently in ${userBranches.length} branches`);

        // For branch admins, handle branches differently
        if (!isSuperAdmin && isBranchAdmin) {
          // Find branches where the requesting admin is an admin
          const adminBranches = await Branch.find({
            responsible: session.user.id
          }).select('_id name').lean();

          const adminBranchIds = adminBranches.map(branch => branch._id?.toString());
          console.log(`Admin has access to ${adminBranchIds.length} branches:`, adminBranchIds);

          // Filter branchIds to only include branches this admin has access to
          const adminAccessibleBranchIds = uniqueBranchIds.filter(branchId =>
            adminBranchIds.includes(branchId as string)
          );
          console.log(`Admin is submitting ${adminAccessibleBranchIds.length} branches they control:`, adminAccessibleBranchIds);

          // Get current user's branches that this admin doesn't control
          const otherBranchIds = userBranches
            .filter(branch => !adminBranchIds.includes(branch._id?.toString() || ''))
            .map(branch => branch._id?.toString() || '');
          console.log(`User has ${otherBranchIds.length} branches admin doesn't control:`, otherBranchIds);

          // Combine admin's selected branches with user's other branches
          const combinedBranchIds = [...adminAccessibleBranchIds, ...otherBranchIds];
          console.log(`Combined branch IDs for processing: ${combinedBranchIds.length}`, combinedBranchIds);

          // Get all branches the admin has access to
          const branchesToUpdate = await Branch.find({
            responsible: session.user.id
          });

          console.log(`Processing ${branchesToUpdate.length} branches admin has access to`);

          // For each branch admin controls, update user associations
          for (const branch of branchesToUpdate) {
            const branchId = branch._id?.toString() || '';
            let branchUpdated = false;

            // First, remove user from all role arrays for this branch
            branch.responsible = branch.responsible.filter(
              (respId: Types.ObjectId) => respId.toString() !== id
            );
            branch.agents = branch.agents.filter(
              (agentId: Types.ObjectId) => agentId.toString() !== id
            );
            branch.sellers = branch.sellers.filter(
              (sellerId: Types.ObjectId) => sellerId.toString() !== id
            );

            // If this branch should have the user, add them to the appropriate arrays
            if (adminAccessibleBranchIds.includes(branchId)) {
              // Get the roles that are being assigned
              const userRoles = await Role.find({ _id: { $in: body.roles } });
              const roleNames = userRoles.map(role => role.name);

              // Determine role associations
              const isAdmin = roleNames.includes('BranchesAdmin');
              const isAgent = roleNames.includes('BranchesAgent');
              const isSeller = roleNames.includes('Seller');

              // Now add user to the appropriate arrays based on roles
              if (user && !Array.isArray(user) && user._id) {
                if (isAdmin && !branch.responsible.some((id: any) => id?.toString() === user._id?.toString())) {
                  branch.responsible.push(user._id);
                  branchUpdated = true;
                }

                if (isAgent && !branch.agents.some((id: any) => id?.toString() === user._id?.toString())) {
                  branch.agents.push(user._id);
                  branchUpdated = true;
                }

                if (isSeller && !branch.sellers.some((id: any) => id?.toString() === user._id?.toString())) {
                  branch.sellers.push(user._id);
                  branchUpdated = true;
                }
              } else {
                // This branch is admin-controlled but not in the submitted list, so we're removing the user
                console.log(`Removing user from branch ${branchId} (${branch.name})`);
                branchUpdated = true;
              }

              if (branchUpdated) {
                // Skip validation to avoid branch validation errors
                await branch.save({ validateBeforeSave: false });
                console.log(`Updated branch ${branch._id} (${branch.name})`);
              }
            }
          }

          // For branch admins, we also need to ensure the user has the proper role objects
          // This maintains consistency between branch arrays and user roles
          const rolesToAdd = [];

          // Get the roles that are being assigned
          const userRoles = await Role.find({ _id: { $in: body.roles } });
          const roleNames = userRoles.map(role => role.name);

          // Determine role associations
          const isAdmin = roleNames.includes('BranchesAdmin');
          const isAgent = roleNames.includes('BranchesAgent');
          const isSeller = roleNames.includes('Seller');

          if (isAdmin) {
            const adminRole = await Role.findOne({ name: 'BranchesAdmin' });
            if (adminRole) rolesToAdd.push(adminRole._id);
          }

          if (isAgent) {
            const agentRole = await Role.findOne({ name: 'BranchesAgent' });
            if (agentRole) rolesToAdd.push(agentRole._id);
          }

          if (isSeller) {
            const sellerRole = await Role.findOne({ name: 'Seller' });
            if (sellerRole) rolesToAdd.push(sellerRole._id);
          }

          // Only add branch-specific roles without touching other roles
          if (rolesToAdd.length > 0) {
            // Get current user roles
            const updatedUser = await User.findById(id);
            const currentRoleIds = updatedUser.roles.map((role: any) =>
              typeof role === 'string' ? role : role._id.toString()
            );

            // Add missing roles
            for (const roleId of rolesToAdd) {
              if (!currentRoleIds.includes(roleId.toString())) {
                updatedUser.roles.push(roleId);
              }
            }

            await updatedUser.save();
          }
        } else if (isSuperAdmin) {
          // Super admin code - original implementation
          // Get the roles that are being assigned
          const userRoles = await Role.find({ _id: { $in: body.roles } });
          const roleNames = userRoles.map(role => role.name);

          // Determine role associations
          const isAdmin = roleNames.includes('BranchesAdmin');
          const isAgent = roleNames.includes('BranchesAgent');
          const isSeller = roleNames.includes('Seller');

          console.log(`User roles: ${roleNames.join(', ')}`);
          console.log(`Role mappings - Admin: ${isAdmin}, Agent: ${isAgent}, Seller: ${isSeller}`);

          // Branch validation before updating branch assignments
          // Only apply this validation for SuperAdmin users
          if (isSuperAdmin && (isAdmin || isAgent || isSeller) &&
            (!uniqueBranchIds || uniqueBranchIds.length === 0)) {
            return NextResponse.json({
              error: 'Branch assignment required',
              details: 'At least one branch must be assigned for branch-related roles'
            }, { status: 400 });
          }

          // First, remove user from all branches they're currently in
          const allBranches = await Branch.find({
            $or: [
              { responsible: id },
              { agents: id },
              { sellers: id }
            ]
          });

          for (const branch of allBranches) {
            branch.responsible = branch.responsible.filter(
              (respId: Types.ObjectId) => respId.toString() !== id
            );
            branch.agents = branch.agents.filter(
              (agentId: Types.ObjectId) => agentId.toString() !== id
            );
            branch.sellers = branch.sellers.filter(
              (sellerId: Types.ObjectId) => sellerId.toString() !== id
            );

            // Skip validation to avoid branch validation errors
            await branch.save({ validateBeforeSave: false });
          }

          // Now add user to specified branches based on their roles
          if (isAdmin || isAgent || isSeller) {
            const branchesToUpdate = await Branch.find({
              _id: { $in: uniqueBranchIds }
            });

            console.log(`Found ${branchesToUpdate.length} branches to update`);

            for (const branch of branchesToUpdate) {
              let branchUpdated = false;

              if (isAdmin && !branch.responsible.some((id: any) => id?.toString() === user._id?.toString())) {
                branch.responsible.push(user._id);
                branchUpdated = true;
              }

              if (isAgent && !branch.agents.some((id: any) => id?.toString() === user._id?.toString())) {
                branch.agents.push(user._id);
                branchUpdated = true;
              }

              if (isSeller && !branch.sellers.some((id: any) => id?.toString() === user._id?.toString())) {
                branch.sellers.push(user._id);
                branchUpdated = true;
              }

              if (branchUpdated) {
                // Skip validation to avoid branch validation errors
                await branch.save({ validateBeforeSave: false });
                console.log(`Updated branch ${branch._id} (${branch.name})`);
              }
            }
          }
        }

        // After updating branch membership arrays, update the user's branchIds field
        await User.findByIdAndUpdate(id, { branchIds: uniqueBranchIds });
      } catch (error) {
        console.error('Error updating branch associations:', error);
      }
    }

    // Get updated user with branches info
    const branches = await Branch.find().lean();
    // Create a Map to deduplicate branches by ID
    const branchMap = new Map();

    for (const branch of branches) {
      // Check if user is responsible for this branch
      if (branch.responsible && branch.responsible.some((respId: any) => respId?.toString() === id)) {
        const branchId = String(branch._id);
        if (!branchMap.has(branchId)) {
          branchMap.set(branchId, {
            id: branchId,
            name: branch.name,
            roles: ['responsible']
          });
        } else {
          branchMap.get(branchId).roles.push('responsible');
        }
      }

      // Check if user is an agent for this branch
      if (branch.agents && Array.isArray(branch.agents) && branch.agents.some((agentId: any) => agentId?.toString() === id)) {
        const branchId = String(branch._id);
        if (!branchMap.has(branchId)) {
          branchMap.set(branchId, {
            id: branchId,
            name: branch.name,
            roles: ['agent']
          });
        } else {
          branchMap.get(branchId).roles.push('agent');
        }
      }

      // Check if user is a seller for this branch
      if (branch.sellers && Array.isArray(branch.sellers) && branch.sellers.some((sellerId: any) => sellerId?.toString() === id)) {
        const branchId = String(branch._id);
        if (!branchMap.has(branchId)) {
          branchMap.set(branchId, {
            id: branchId,
            name: branch.name,
            roles: ['seller']
          });
        } else {
          branchMap.get(branchId).roles.push('seller');
        }
      }
    }

    // Convert the branch map values to an array of branches with roles
    const userBranches = Array.from(branchMap.values()).map(branch => {
      // For backward compatibility, return in the expected format
      return branch.roles.map((role: string) => ({
        id: branch.id,
        name: branch.name,
        role: role
      }));
    }).flat();

    // Update the user with latest role information
    const updatedUser = await User.findById(id)
      .populate('roles', 'name')
      .populate('directPermissions', 'name')
      .select('-password');

    const userWithBranches = {
      ...updatedUser.toObject(),
      branches: userBranches
    };

    // Handle partner relationship updates if partnerId changed
    // Skip this for self-editing users who are not admins
    if (body.partnerId !== undefined && (isSuperAdmin || isBranchAdmin)) {
      try {
        // Get the current user's partnerId to compare
        const currentPartnerId = currentUser.partnerId?.toString();
        const newPartnerId = body.partnerId;

        // If partnerId changed, update partner relationships
        if (currentPartnerId !== newPartnerId) {
          // Remove user from old partner's owners array if they had one
          if (currentPartnerId) {
            await Partner.findByIdAndUpdate(
              currentPartnerId,
              {
                $pull: { owners: id },
                lastModified: new Date()
              }
            );
            console.log(`Removed user ${id} from partner ${currentPartnerId} owners`);
          }

          // Add user to new partner's owners array if new partnerId is provided
          if (newPartnerId) {
            await Partner.findByIdAndUpdate(
              newPartnerId,
              {
                $addToSet: { owners: id },
                lastModified: new Date()
              }
            );
            console.log(`Added user ${id} to partner ${newPartnerId} owners`);
          }
        }
      } catch (partnerError) {
        console.error('Failed to update partner relationship:', partnerError);
        // Don't fail user update if partner update fails
      }
    }

    return NextResponse.json(userWithBranches);
  } catch (error) {
    console.error('Update user error:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
      return NextResponse.json({
        error: 'Failed to update user',
        message: error.message
      }, { status: 500 });
    }
    // Fallback for non-Error objects or unexpected cases
    return NextResponse.json({ error: 'Failed to update user', details: typeof error === 'string' ? error : JSON.stringify(error) }, { status: 500 });
  }
}

export async function PATCH(request: Request, context: { params: { id: string } }) {
  const { id } = context.params;
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    if (session && !session?.user.roles) {
      session.user.roles = await getUserRoles(session);
    }
    if (!isValidObjectId(id)) {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }
    const body = await request.json();
    console.log('[PATCH] /api/users/dev/[id] body:', body);
    const user = await User.findById(id);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    // Only update provided fields
    for (const key of Object.keys(body)) {
      if (key === 'documents' && typeof body.documents === 'object') {
        user.documents = { ...user.documents, ...body.documents };
      } else {
        user[key] = body[key];
      }
    }
    await user.save();
    // Optionally, re-populate roles/branches if needed
    const updatedUser = await User.findById(id)
      .populate('roles', 'name')
      .populate('directPermissions', 'name')
      .select('-password');
    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('[PATCH] /api/users/dev/[id] error:', error);
    return NextResponse.json({ error: 'Failed to patch user' }, { status: 500 });
  }
}

