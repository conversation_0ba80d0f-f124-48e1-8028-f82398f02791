import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import User from '@/models/User';
import Branch from '@/models/Branch';
import Role from '@/models/Role';
import path from 'path';
import fs from 'fs/promises';

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const ref = url.searchParams.get('ref');
    if (!ref) {
      return NextResponse.json({ error: 'Missing file reference' }, { status: 400 });
    }

    // Only allow safe references (no directory traversal)
    if (ref.includes('..') || ref.startsWith('/')) {
      return NextResponse.json({ error: 'Invalid file reference' }, { status: 400 });
    }

    // Get session user
    const session = await getServerSession(authOptions);
    const sessionUserId = session?.user?.id;
    if (!sessionUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if session user is superadmin
    const sessionRoles = session.user.roles || [];
    const isSuperAdmin = sessionRoles.some((role: any) => {
      if (typeof role === 'string') return role === '67add3214badd3283e873329';
      return role._id === '67add3214badd3283e873329';
    });
    // Build the file path (uploads are private, not in public)
    const baseUploadDir = process.env.UPLOADS_DIR || 'uploads';
    const fileBase = path.isAbsolute(baseUploadDir)
      ? baseUploadDir
      : path.join(process.cwd(), baseUploadDir);
    const filePath = path.join(fileBase, ref);
    console.log('[DOWNLOAD] UPLOADS_DIR:', process.env.UPLOADS_DIR);
    console.log('[DOWNLOAD] Resolved filePath:', filePath);

    // Check if file exists
    try {
      await fs.access(filePath);
    } catch {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

    // If superadmin, allow download of any file
    if (isSuperAdmin) {
      const fileBuffer = await fs.readFile(filePath);
      const fileName = path.basename(ref);
      const ext = path.extname(fileName).toLowerCase();
      let contentType = 'application/octet-stream';
      if (ext === '.pdf') contentType = 'application/pdf';
      if ([ '.jpg', '.jpeg' ].includes(ext)) contentType = 'image/jpeg';
      if (ext === '.png') contentType = 'image/png';
      return new Response(fileBuffer, {
        status: 200,
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `inline; filename="${fileName}"`
        }
      });
    }

    // For non-superadmins, require the file to be referenced in a user document
    // Find the user who owns this file
    // Check both checkSpecimen and otherDocuments fields
    console.log('Download ref:', ref);
    const user = await User.findOne({
      $or: [
        { 'documents.checkSpecimen': ref },
        { 'documents.otherDocuments': ref },
      ]
    }).lean();
    console.log('User found for file:', !!user);
    if (!user) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

    // Check if session user is the owner
    const userId = (user as any)._id?.toString?.() ?? String((user as any)._id);
    const isOwner = userId === sessionUserId;

    // Check if session user is a branch admin for a branch the owner belongs to
    let isBranchAdmin = false;
    if (!isOwner) {
      // Find branches where session user is responsible (admin)
      const adminBranches = await Branch.find({ responsible: sessionUserId }).select('_id').lean();
      const adminBranchIds = adminBranches.map((b: any) => (b as any)._id?.toString?.() ?? String((b as any)._id));
      // Find branches where the file owner is a member
      const ownerBranches = await Branch.find({
        $or: [
          { responsible: (user as any)._id },
          { agents: (user as any)._id },
          { sellers: (user as any)._id }
        ]
      }).select('_id').lean();
      const ownerBranchIds = ownerBranches.map((b: any) => (b as any)._id?.toString?.() ?? String((b as any)._id));
      // If any overlap, allow
      isBranchAdmin = adminBranchIds.some(id => ownerBranchIds.includes(id));
    }

    if (!(isOwner || isBranchAdmin)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Stream the file
    const fileBuffer = await fs.readFile(filePath);
    const fileName = path.basename(ref);
    const ext = path.extname(fileName).toLowerCase();
    let contentType = 'application/octet-stream';
    if (ext === '.pdf') contentType = 'application/pdf';
    if ([ '.jpg', '.jpeg' ].includes(ext)) contentType = 'image/jpeg';
    if (ext === '.png') contentType = 'image/png';

    return new Response(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `inline; filename="${fileName}"`
      }
    });
  } catch (error) {
    console.error('Download error:', error);
    return NextResponse.json({ error: 'Failed to download file' }, { status: 500 });
  }
} 