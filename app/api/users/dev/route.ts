import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import bcrypt from 'bcryptjs';
import Role from '@/models/Role';
import Branch from '@/models/Branch';
import { Partner } from '@/models/Partner';
import { generateRandomPassword } from '@/lib/utils/password';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import EmailTemplate from '@/models/EmailTemplate';
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { getUserPermissions, getUserRoles } from '../../utils/server-permission-utils';
import { withApiLogging } from '../../utils/with-api-logging';
import { validateQSTRegistrationNumber } from '../../utils/qst-validation';
import { getFieldsForRoles } from '../../../users/dev/utils/UserFieldRegistry';
import TaxType from '@/models/TaxType';
import { userRolesRequireTaxInfo } from '../../utils/tax-validation';
import { createContractSigningToken, doesUserRequireContract } from '@/lib/utils/contract-token-utils';
import { isEventSupervisor } from '@/lib/utils/role-utils';

/**
 * Validates required fields for a user based on their roles
 * Returns an object with hasIssues flag and array of missing fields
 */
function validateUserRequiredFields(user: any): { hasIssues: boolean; missingFields: string[] } {
  const missingFields: string[] = [];

  // Get role names from user's roles
  const roleNames = user.roles?.map((role: any) =>
    typeof role === 'string' ? role : role.name
  ) || [];

  // Get required fields for user's roles
  const requiredFields = getFieldsForRoles(roleNames);

  // Check each required field
  for (const field of requiredFields) {
    if (!field.required) continue;

    const fieldValue = getNestedFieldValue(user, field.key);

    // Check if field is missing or empty
    if (isFieldEmpty(fieldValue)) {
      missingFields.push(field.key);
    }
  }

  return {
    hasIssues: missingFields.length > 0,
    missingFields
  };
}

/**
 * Helper function to get nested field values (e.g., 'taxInfo.isTaxable')
 */
function getNestedFieldValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Helper function to check if a field value is considered empty
 */
function isFieldEmpty(value: any): boolean {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string' && value.trim() === '') return true;
  if (Array.isArray(value) && value.length === 0) return true;
  return false;
}

export const GET = async (request: Request) => {
  await dbConnect();
  // Parse query parameters
  const url = new URL(request.url);
  const includeDeleted = url.searchParams.get('includeDeleted') === 'true';
  const page = parseInt(url.searchParams.get('page') || '1');
  const limit = parseInt(url.searchParams.get('limit') || '10');
  const branchId = url.searchParams.get('branchId');
  const roleId = url.searchParams.get('roleId');
  const searchTerm = url.searchParams.get('searchTerm');

  // Get current user session
  const session = await getServerSession(authOptions);
  if (session && !session?.user.permissions) {
    const user: any = await User.findById(session.user.id).select('roles').lean().populate('roles');
    session.user.roles = user?.roles || [];
  }
  if (session && !session.user.roles) {
    session.user.roles = await getUserRoles(session);
  }
  const userRoles = session?.user.roles || [];
  // Check if user is a SuperAdmin
  const isSuperAdmin = userRoles.some((role: any) =>
    role.name === 'SuperAdmin' || role._id?.toString() === '67add3214badd3283e873329'
  );
  // Check if user is a BranchesAdmin
  const isBranchAdmin = userRoles.some((role: any) =>
    role.name === 'BranchesAdmin' || role._id?.toString() === '67c032e90af5117479e27731'
  );
  // List of branch role IDs/names (hardcoded)
  // BranchesAdmin, BranchesAgent, Seller, PAP roles
  const branchRoleIds = [
    '67c032e90af5117479e27731', // BranchesAdmin
    '67c032fe0af5117479e27737', // BranchesAgent
    '67e0aad60f0a3bdeba18542c',  // Seller
    '67fbd1707839bdba5be4b02b' // PAP
  ];
  const branchRoleNames = ['BranchesAdmin', 'BranchesAgent', 'Seller', 'PAP'];

  // Get all roles to help with filtering
  const allRoles = await Role.find().lean();

  // Add role IDs for branchRoleNames
  allRoles.forEach(role => {
    const roleId = role._id?.toString();
    if (roleId) {
      if (branchRoleNames.includes(role.name) && !branchRoleIds.includes(roleId)) {
        branchRoleIds.push(roleId);
      }
    }
  });

  // Build query - exclude deleted users by default
  const query: any = includeDeleted ? {} : { deletedAt: null };

  // Apply search term if provided
  if (searchTerm) {
    query.$or = [
      { name: { $regex: searchTerm, $options: 'i' } },
      { email: { $regex: searchTerm, $options: 'i' } }
    ];
  }

  // If not a SuperAdmin, apply additional filters
  if (!isSuperAdmin && !isEventSupervisor(session?.user.roles) ) {
    if (isBranchAdmin) {
      // Branch admins can only see users in their assigned branches
      const adminBranches = await Branch.find({
        responsible: session?.user?.id
      }).select('_id').lean();

      const adminBranchIds = adminBranches.map(branch => branch._id?.toString());

      if (adminBranchIds.length === 0) {
        // Return empty response if admin has no branches
        return NextResponse.json({
          users: [],
          pagination: {
            total: 0,
            page,
            limit,
            totalPages: 0
          }
        });
      }

      // Branch admins can only see users with branch roles
      query.roles = { $in: branchRoleIds };
    } else {
      console.log('regular user');
      // Regular users shouldn't see any users - return empty
      return NextResponse.json({
        users: [],
        pagination: {
          total: 0,
          page,
          limit,
          totalPages: 0
        }
      });
    }
  }

  // Apply role filter if specified
  if (roleId) {
    // If role is not a branch role and user is not a super admin, don't show
    if (!branchRoleIds.includes(roleId) && !isSuperAdmin) {
      return NextResponse.json({
        users: [],
        pagination: {
          total: 0,
          page,
          limit,
          totalPages: 0
        }
      });
    }
    query.roles = roleId;
  }

  // Fetch base users without pagination for branch filtering and counting
  let allUsers = await User.find(query)
    .populate('roles')
    .populate('directPermissions')
    .select('-password')
    .lean();

  // Filter out users without branch roles for non-superadmins
  if (!isSuperAdmin) {
    allUsers = allUsers.filter(user => {
      const userRoleIds = user.roles.map((role: any) =>
        typeof role === 'string' ? role : role._id?.toString()
      );
      // Check if user has any branch roles
      return userRoleIds.some((roleId: string) => branchRoleIds.includes(roleId));
    });
  }

  // Get all branches
  const branches = await Branch.find().lean();

  // Map users to include their branch associations
  const usersWithBranches = allUsers.map((user) => {
    const userBranches = [];

    // Get user's role IDs
    const userRoleIds = user.roles.map((role: any) =>
      typeof role === 'string' ? role : role._id?.toString()
    );

    // Check if user has any branch role
    const hasBranchRole = userRoleIds.some((roleId: string) => branchRoleIds.includes(roleId));

    // If user has branch role, associate branches
    if (hasBranchRole) {
      for (const branch of branches) {
        if (branch.responsible && branch.responsible.some((id: any) => id?.toString() === user._id?.toString())) {
          userBranches.push({ id: String(branch._id || ''), name: branch.name || '', role: 'responsible' });
        }
        if (branch.agents && Array.isArray(branch.agents) && branch.agents.some((id: any) => id?.toString() === user._id?.toString())) {
          userBranches.push({ id: String(branch._id || ''), name: branch.name || '', role: 'agent' });
        }
        if (branch.sellers && Array.isArray(branch.sellers) && branch.sellers.some((id: any) => id?.toString() === user._id?.toString())) {
          userBranches.push({ id: String(branch._id || ''), name: branch.name || '', role: 'seller' });
        }
        if (branch.paps && Array.isArray(branch.paps) && branch.paps.some((id: any) => id?.toString() === user._id?.toString())) {
          userBranches.push({ id: String(branch._id || ''), name: branch.name || '', role: 'pap' });
        }
      }
    }

    return { ...user, branches: userBranches, roles: user.roles };
  });

  // Add branchIds with names for each user
  const usersWithBranchesAndNames = usersWithBranches.map(user => {
    // Get unique branch IDs from user.branches
    const branchIds = Array.from(new Set((user.branches || []).map(b => b.id)));
    // Map to branch objects with name
    const branchIdObjs = branchIds.map(id => {
      const branch = branches.find(b => String(b._id) === id);
      return { _id: id, name: branch?.name || id };
    });
    return { ...user, branchIds: branchIdObjs, roles: user.roles };
  });

  // For superadmins, skip all further branch/role filtering
  let filteredUsers = usersWithBranchesAndNames;
  if (isSuperAdmin) {
    if (branchId) {
      // Only apply branchId filter for users with branch roles
      filteredUsers = usersWithBranchesAndNames.filter(user => {
        const userRoleIds = user.roles.map((role: any) =>
          typeof role === 'string' ? role : role._id?.toString()
        );
        const hasBranchRole = userRoleIds.some((roleId: string) => branchRoleIds.includes(roleId));
        if (hasBranchRole) {
          // For branch roles, filter by branchId
          return user.branches.some(branch => branch.id === branchId);
        }
        // For non-branch roles, always include
        return true;
      });
    }
  } else {
    // Non-superadmins: existing filtering logic
    if (branchId) {
      filteredUsers = usersWithBranchesAndNames.filter(user =>
        user.branches.some(branch => branch.id === branchId)
      );
    }
    if (isBranchAdmin && !isSuperAdmin) {
      const adminBranches = await Branch.find({
        responsible: session?.user?.id
      }).select('_id').lean();
      const adminBranchIds = adminBranches.map(branch => branch._id?.toString());
      filteredUsers = filteredUsers.filter(user =>
        user.branches.some(branch => adminBranchIds.includes(branch.id))
      );
    }
  }

  // Calculate pagination
  const total = filteredUsers.length;
  const totalPages = Math.ceil(total / limit);
  const skip = (page - 1) * limit;

  // Apply pagination manually
  const paginatedUsers = filteredUsers.slice(skip, skip + limit);

  // Add required field validation to each user
  const usersWithValidation = paginatedUsers.map(user => {
    const validation = validateUserRequiredFields(user);
    return {
      ...user,
      hasRequiredFieldIssues: validation.hasIssues,
      missingRequiredFields: validation.missingFields
    };
  });

  return NextResponse.json({
    users: usersWithValidation,
    pagination: {
      total,
      page,
      limit,
      totalPages
    }
  });
};



export const POST = withApiLogging(async (request: Request) => {
  await dbConnect();

  // Get current user session for authorization checks
  const session = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if (session && !session?.user.permissions) {
    session.user.permissions = await getUserPermissions(session);
  }
  if (session && !session?.user.roles) {
    session.user.roles = await getUserRoles(session);
  }
  // Check if user is a SuperAdmin
  const userRoles = session.user.roles || [];
  const isCurrentUserSuperAdmin = userRoles.some((role: any) =>
    role.name === 'SuperAdmin' || role._id?.toString() === '67add3214badd3283e873329'
  );

  // Check if user is a BranchesAdmin
  const isCurrentUserBranchAdmin = userRoles.some((role: any) =>
    role.name === 'BranchesAdmin' || role._id?.toString() === '67c032e90af5117479e27731'
  );

  // If user is not a SuperAdmin or BranchesAdmin, they cannot create users
  if (!isCurrentUserSuperAdmin && !isCurrentUserBranchAdmin) {
    return NextResponse.json({
      error: 'You do not have permission to create users'
    }, { status: 403 });
  }
  const body = await request.json();
  // Debug log for NAS and taxInfo
  console.log('API received user body:', JSON.stringify(body, null, 2));

  // Prevent non-superadmins from creating a superadmin
  const SUPERADMIN_ROLE_ID = '67add3214badd3283e873329';
  if (!isCurrentUserSuperAdmin && Array.isArray(body.roles) && body.roles.some((roleId: string) => roleId === SUPERADMIN_ROLE_ID)) {
    return NextResponse.json({
      error: 'Only SuperAdmins can create a user with the SuperAdmin role.'
    }, { status: 403 });
  }

  console.log('body', body);
  console.log('socialAssurance in body:', body.socialAssurance);
  // Use provided password if present, otherwise generate a random one
  let plainPassword = body.password && body.password.trim() !== '' ? body.password : generateRandomPassword(10);
  const userData: any = {
    name: body.name,
    email: body.email ? body.email.toLowerCase() : undefined,
    roles: body.roles || [],
    directPermissions: body.directPermissions || [],
    isActive: body.isActive !== undefined ? body.isActive : true
  };
  // --- Additional user profile fields ---
  if (body.birthDate) userData.birthDate = body.birthDate;
  if (body.socialAssurance) userData.socialAssurance = body.socialAssurance;
  if (body.companyName) userData.companyName = body.companyName;
  if (body.address) userData.address = body.address;
  if (body.city) userData.city = body.city;
  if (body.postalCode) userData.postalCode = body.postalCode;
  if (body.emergencyContact) {
    // Format emergencyContact.phone as +1XXXXXXXXXX
    let phone = body.emergencyContact.phone || '';
    if (phone) {
      phone = phone.replace(/[^\d]/g, ''); // Remove non-digits
      if (phone.startsWith('1') && phone.length === 11) {
        phone = phone.substring(1);
      }
      if (phone.length === 10) {
        phone = '+1' + phone;
      } else if (!phone.startsWith('+1')) {
        // fallback: if not 10 digits, keep as is
        phone = body.emergencyContact.phone;
      }
    }
    userData.emergencyContact = {
      ...body.emergencyContact,
      phone,
    };
  }
  // --- Tax Info: Strict validation for tpstvq tax type ---
  if (body.taxInfo) {
    let taxtypeCode = '';

    // Check if user's roles require tax information
    const userRoles = body.roles || [];
    const rolesRequireTaxInfo = userRolesRequireTaxInfo(userRoles);

    // If user's roles don't require tax info OR user is not taxable, clear all tax-related data
    if (!rolesRequireTaxInfo || body.taxInfo.isTaxable === 'no') {
      userData.taxInfo = {
        isTaxable: 'no',
        taxtypeid: undefined,
        qstRegistrationNumber: '',
        tpsRegistrationNumber: '',
      };
      taxtypeCode = ''; // Ensure no validation is triggered
    } else {
      // Only process tax type if user is taxable
      if (body.taxInfo.taxtypeid) {
        const taxTypeDoc = await TaxType.findById(body.taxInfo.taxtypeid).lean();
        if (taxTypeDoc && !Array.isArray(taxTypeDoc)) {
          taxtypeCode = (taxTypeDoc as any).code || '';
        }
      }

      // Set up taxInfo with all provided fields
      userData.taxInfo = {
        ...body.taxInfo,
        qstRegistrationNumber: body.taxInfo.qstRegistrationNumber || '',
        tpsRegistrationNumber: body.taxInfo.tpsRegistrationNumber || '',
      };
    }

    // Strict validation for tpstvq tax type - only when roles require tax info AND isTaxable is 'yes'
    if (rolesRequireTaxInfo && taxtypeCode === 'tpstvq' && userData.taxInfo && userData.taxInfo.isTaxable === 'yes') {
      const { validateTPSTVQTaxInfo } = await import('../../utils/tax-validation');
      const validation = await validateTPSTVQTaxInfo(userData.taxInfo, taxtypeCode);

      if (!validation.isValid) {
        return NextResponse.json(
          {
            error: 'Tax registration validation failed for TPSTVQ tax type',
            details: validation.errors
          },
          { status: 400 }
        );
      }

      // Store the QST validation result
      if (validation.qstValidation) {
        userData.taxInfo.qstValidation = {
          isValid: validation.qstValidation.isValid,
          validatedAt: new Date(),
          status: validation.qstValidation.status,
          statusDescription: validation.qstValidation.statusDescription,
          apiResponse: validation.qstValidation.apiResponse,
          errorMessage: validation.qstValidation.errorMessage
        };
      }
    } else {
      // For non-tpstvq tax types, validate QST if provided (non-blocking)
      if (userData.taxInfo.qstRegistrationNumber) {
        try {
          const qstValidation = await validateQSTRegistrationNumber(userData.taxInfo.qstRegistrationNumber);
          userData.taxInfo.qstValidation = {
            isValid: qstValidation.isValid,
            validatedAt: new Date(),
            status: qstValidation.status,
            statusDescription: qstValidation.statusDescription,
            apiResponse: qstValidation.apiResponse,
            errorMessage: qstValidation.errorMessage
          };
        } catch (validationError) {
          console.error('QST validation failed:', validationError);
          userData.taxInfo.qstValidation = {
            isValid: false,
            validatedAt: new Date(),
            errorMessage: validationError instanceof Error ? validationError.message : 'Validation service unavailable'
          };
        }
      }
    }
  }
  // --- end additional user profile fields ---

  // If branch admin is creating a user, enforce branch access controls
  if (!isCurrentUserSuperAdmin && isCurrentUserBranchAdmin && body.branchIds && Array.isArray(body.branchIds)) {
    // Find branches where the requesting user is an admin
    const adminBranches = await Branch.find({
      responsible: session.user.id
    }).select('_id').lean();

    const adminBranchIds = adminBranches.map(branch => branch._id?.toString());

    // Check if user is trying to add to branches they don't have access to
    const unauthorizedBranches = body.branchIds.filter((branchId: string) =>
      !adminBranchIds.includes(branchId)
    );

    if (unauthorizedBranches.length > 0) {
      return NextResponse.json({
        error: 'You do not have permission to add users to these branches',
        unauthorizedBranches
      }, { status: 403 });
    }
  }

  if (body.phone) {
    userData.phone = body.phone;
  }
  if (body.metadata) {
    try {
      const metadataObj = JSON.parse(body.metadata);
      userData.metadata = new Map(Object.entries(metadataObj));
    } catch (error) {
      console.error('Error parsing metadata:', error);
    }
  }
  if (body.branchIds) userData.branchIds = body.branchIds;
  if (body.branchRoles) userData.branchRoles = body.branchRoles;
  if (body.partnerId) userData.partnerId = body.partnerId;
  if (body.fcmTokens) userData.fcmTokens = body.fcmTokens;
  // Hash the password (user-provided or random)
  userData.password = await bcrypt.hash(plainPassword, 10);
  if (!userData.name || !userData.email) {
    return NextResponse.json(
      { error: 'Name and email are required' },
      { status: 400 }
    );
  }
  if (userData.phone && (typeof userData.phone !== 'string' || !/^\d{10}$/.test(userData.phone))) {
    return NextResponse.json(
      { error: 'Phone number must be 10 digits (e.g. 5141234567)' },
      { status: 400 }
    );
  }
  userData.phone = "+1" + userData.phone;
  const requestingUserRoles = await Role.find({
    _id: { $in: body.requestingUserRoles || [] }
  });
  const restrictedRoles = ['SuperAdmin', 'BranchesAdmin'];
  const isBranchAdmin = requestingUserRoles.some(role => role.name === 'BranchesAdmin');
  if (isBranchAdmin) {
    const rolesToAssign = await Role.find({ _id: { $in: userData.roles } });
    const hasRestrictedRole = rolesToAssign.some(role => restrictedRoles.includes(role.name));
    if (hasRestrictedRole) {
      return NextResponse.json(
        { error: 'You do not have permission to assign these roles' },
        { status: 403 }
      );
    }
  }
  // Fix: Can't use $or with Date. Use $or at the top level.
  const existingUser = await User.findOne({
    email: userData.email,
    $or: [
      { deletedAt: { $exists: false } },
      { deletedAt: null }
    ]
  });
  if (existingUser) {
    return NextResponse.json(
      { error: 'Email already exists' },
      { status: 400 }
    );
  }
  // Branch validation before user creation
  const newUserRoles = await Role.find({ _id: { $in: userData.roles } });
  const newUserRoleNames = newUserRoles.map(role => role.name);
  const isAdmin = newUserRoleNames.includes('BranchesAdmin');
  const isAgent = newUserRoleNames.includes('BranchesAgent');
  const isSeller = newUserRoleNames.includes('Seller');
  const isPAP = newUserRoleNames.includes('PAP');
  if ((isAdmin || isAgent || isSeller || isPAP) && body.branchIds && Array.isArray(body.branchIds) && body.branchIds.length > 0) {
    try {
      const branches = await Branch.find({ _id: { $in: body.branchIds } });
      let branchAssociationErrors: string[] = [];
      for (const branch of branches) {
        let branchUpdated = false;
        if (isAdmin && !branch.responsible.includes('dummy')) branchUpdated = true;
        if (isAgent && !branch.agents.includes('dummy')) branchUpdated = true;
        if (isSeller && !branch.sellers.includes('dummy')) branchUpdated = true;
        if (isPAP && !branch.paps.includes('dummy')) branchUpdated = true;
        if (branchUpdated) {
          try {
            // Try to save branch to check for validation errors
            await branch.validate();
          } catch (branchError) {
            let msg = (branchError instanceof Error) ? branchError.message : String(branchError);
            branchAssociationErrors.push(`Branch ${branch._id}: ${msg}`);
          }
        }
      }
      if (branchAssociationErrors.length > 0) {
        return NextResponse.json({
          error: 'Branch is not valid',
          details: branchAssociationErrors
        }, { status: 400 });
      }
    } catch (err) {
      let msg = (err instanceof Error) ? err.message : String(err);
      return NextResponse.json({
        error: 'General branch association error',
        details: [msg]
      }, { status: 400 });
    }
  }

  // Clean up socialAssurance data structure before user creation
  if (userData.socialAssurance && typeof userData.socialAssurance === 'object') {
    // Fix any malformed socialAssurance data
    const cleanSocialAssurance: any = {};

    // Handle the NAS field
    if (userData.socialAssurance.nas) {
      cleanSocialAssurance.nas = userData.socialAssurance.nas;
    }

    // Handle expiry field - check for both 'expiry' and 'nas.expiration'
    if (userData.socialAssurance.expiry) {
      cleanSocialAssurance.expiry = userData.socialAssurance.expiry;
    } else if (userData.socialAssurance['nas.expiration']) {
      cleanSocialAssurance.expiry = userData.socialAssurance['nas.expiration'];
    }

    // Remove any malformed keys
    delete userData.socialAssurance['nas.expiration'];

    userData.socialAssurance = cleanSocialAssurance;
  }

  // If all branch validations pass, create user and update branches
  const user = await User.create(userData);
  const userWithoutPassword = await User.findById(user._id)
    .populate('roles')
    .populate('directPermissions')
    .select('-password')
    .lean();
  if (!userWithoutPassword) return NextResponse.json({ error: 'User not found' }, { status: 404 });
  // Ensure socialAssurance and emergencyContact are always present in the response
  if (!userWithoutPassword.socialAssurance) userWithoutPassword.socialAssurance = { nas: '', expiry: '' };
  if (!userWithoutPassword.emergencyContact) userWithoutPassword.emergencyContact = { name: '', phone: '', relationship: '' };
  if (!userWithoutPassword.taxInfo) userWithoutPassword.taxInfo = { isTaxable: 'no', taxtypeid: null, qstRegistrationNumber: '' };
  try {
    let template = await EmailTemplate.findOne({ type: 'account-created', disabled: { $ne: true } });
    let emailContent = '<p>Hello {{name}},</p><p>Your account has been created. Your password is: <b>{{password}}</b></p><p>Please log in and change your password as soon as possible.</p>';
    let emailSubject = 'Welcome to AMQ Partners';
    if (template) {
      emailContent = template.content;
      if (template.name) emailSubject = template.name;
    }
    await sendBrevoTemplatedEmail({
      to: [{ email: user.email, name: user.name }],
      subject: emailSubject,
      content: emailContent,
      variables: ([...(template?.variables || []), ...(template?.specialVariables || [])]).reduce((vars: Record<string, any>, key: string) => {
        if (key === 'password') {
          vars[key] = plainPassword;
        } else {
          vars[key] = user[key] ?? '';
        }
        return vars;
      }, {}),
    });
  } catch (emailError) {
    console.error('Failed to send welcome email:', emailError);
  }
  // Handle branch roles and assignments if branchIds are provided
  if (Array.isArray(userData.branchIds) && userData.branchIds.length > 0 && userData.branchRoles) {
    const { isAdmin, isAgent, isSeller, isPAP } = userData.branchRoles;

    // Only proceed if user has at least one branch-specific role
    if (isAdmin || isAgent || isSeller || isPAP) {
      // Get all branches to update
      const allBranches = await Branch.find().lean();

      // Process each branch
      for (const branch of allBranches) {
        const branchId = branch._id.toString();
        const shouldHave = userData.branchIds.includes(branchId);

        if (shouldHave) {
          const updates: any = {};
          let needsUpdate = false;

          // Update arrays based on roles
          if (isAdmin) {
            let responsible = branch.responsible || [];
            if (!Array.isArray(responsible)) responsible = [];
            if (!responsible.some((id: any) => id?.toString() === user._id.toString())) {
              responsible.push(user._id);
              updates.responsible = responsible;
              needsUpdate = true;
            }
          }

          if (isAgent) {
            let agents = branch.agents || [];
            if (!Array.isArray(agents)) agents = [];
            if (!agents.some((id: any) => id?.toString() === user._id.toString())) {
              agents.push(user._id);
              updates.agents = agents;
              needsUpdate = true;
            }
          }

          if (isSeller) {
            let sellers = branch.sellers || [];
            if (!Array.isArray(sellers)) sellers = [];
            if (!sellers.some((id: any) => id?.toString() === user._id.toString())) {
              sellers.push(user._id);
              updates.sellers = sellers;
              needsUpdate = true;
            }
          }

          if (isPAP) {
            let paps = branch.paps || [];
            if (!Array.isArray(paps)) paps = [];
            if (!paps.some((id: any) => id?.toString() === user._id.toString())) {
              paps.push(user._id);
              updates.paps = paps;
              needsUpdate = true;
            }
          }

          // Update branch if any changes were made
          if (needsUpdate) {
            await Branch.findByIdAndUpdate(branchId, updates);
          }
        }
      }
    }
  }

  // Handle partner relationship if partnerId is provided
  if (userData.partnerId) {
    try {
      // Add user to partner's owners array
      await Partner.findByIdAndUpdate(
        userData.partnerId,
        {
          $addToSet: { owners: user._id },
          lastModified: new Date()
        }
      );
      console.log(`Added user ${user._id} to partner ${userData.partnerId} owners`);
    } catch (partnerError) {
      console.error('Failed to update partner relationship:', partnerError);
      // Don't fail user creation if partner update fails
    }
  }

  // Create contract token for users who require contracts (PAP/Seller roles)
  if (doesUserRequireContract(userWithoutPassword.roles)) {
    try {
      const session = await getServerSession(authOptions);
      const adminId = session?.user?.id;

      if (adminId) {
        const contractResult = await createContractSigningToken(
          user._id,
          adminId,
          undefined, // No contract start date yet - admin will set this when signing
          false // Don't skip role validation
        );

        if (contractResult.success) {
          console.log(`Contract token created for user ${user._id}: ${contractResult.token}`);
        } else {
          console.error(`Failed to create contract token for user ${user._id}:`, contractResult.error);
          // Don't fail user creation if contract token creation fails
        }
      } else {
        console.error('No admin session found for contract token creation');
      }
    } catch (contractError) {
      console.error('Error creating contract token:', contractError);
      // Don't fail user creation if contract creation fails
    }
  }

  return NextResponse.json(userWithoutPassword, { status: 201 });
});