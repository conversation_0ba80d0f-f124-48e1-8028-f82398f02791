import { NextRequest, NextResponse } from 'next/server';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import type { BrevoSendEmailOptions } from '@/lib/brevo/brevoTypes';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    // Basic validation
    if (!body.to || !body.content || !body.subject) {
      return NextResponse.json({ success: false, error: 'Missing required fields: to, content, subject' }, { status: 400 });
    }
    const options: BrevoSendEmailOptions = {
      to: body.to,
      subject: body.subject,
      content: body.content,
      variables: body.variables,
      sender: body.sender,
      attachments: body.attachments
    };
    const success = await sendBrevoTemplatedEmail(options);
    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json({ success: false, error: 'Failed to send email' }, { status: 500 });
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('Brevo API error:', error);
    }
    return NextResponse.json({ success: false, error: 'Internal server error' }, { status: 500 });
  }
} 