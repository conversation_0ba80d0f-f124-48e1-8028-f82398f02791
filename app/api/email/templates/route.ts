import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import EmailTemplate from '@/models/EmailTemplate';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

const allowedRoleIds = ['67add3214badd3283e873329', '67c032e90af5117479e27731'];
const SPECIAL_TYPES = ['account-created', 'password-reset'];

async function checkAccess() {
  const session = await getServerSession(authOptions);
  if (!session?.user?.roles?.some((role: any) => allowedRoleIds.includes(role._id?.toString()))) {
    return false;
  }
  return true;
}

export async function GET() {
  if (!(await checkAccess())) {
    return NextResponse.json({ error: 'Access denied' }, { status: 403 });
  }
  await mongoose.connect(process.env.MONGODB_URI!);
  const templates = await EmailTemplate.find().sort({ createdAt: -1 });
  return NextResponse.json(templates);
}

export async function POST(req: NextRequest) {
  if (!(await checkAccess())) {
    return NextResponse.json({ error: 'Access denied' }, { status: 403 });
  }
  await mongoose.connect(process.env.MONGODB_URI!);
  const data = await req.json();
  if (data.type && SPECIAL_TYPES.includes(data.type)) {
    const exists = await EmailTemplate.findOne({ type: data.type });
    if (exists) {
      return NextResponse.json({ error: 'A template of this special type already exists. Only one is allowed.' }, { status: 400 });
    }
  }
  const tpl = await EmailTemplate.create(data);
  return NextResponse.json(tpl);
}

export async function PUT(req: NextRequest) {
  if (!(await checkAccess())) {
    return NextResponse.json({ error: 'Access denied' }, { status: 403 });
  }
  await mongoose.connect(process.env.MONGODB_URI!);
  const data = await req.json();
  if (!data._id) return NextResponse.json({ error: 'Missing template ID' }, { status: 400 });
  if (data.type && SPECIAL_TYPES.includes(data.type)) {
    const exists = await EmailTemplate.findOne({ type: data.type, _id: { $ne: data._id } });
    if (exists) {
      return NextResponse.json({ error: 'A template of this special type already exists. Only one is allowed.' }, { status: 400 });
    }
  }
  const tpl = await EmailTemplate.findByIdAndUpdate(data._id, data, { new: true });
  return NextResponse.json(tpl);
}

export async function DELETE(req: NextRequest) {
  if (!(await checkAccess())) {
    return NextResponse.json({ error: 'Access denied' }, { status: 403 });
  }
  await mongoose.connect(process.env.MONGODB_URI!);
  const { _id } = await req.json();
  if (!_id) return NextResponse.json({ error: 'Missing template ID' }, { status: 400 });
  await EmailTemplate.findByIdAndDelete(_id);
  return NextResponse.json({ success: true });
} 