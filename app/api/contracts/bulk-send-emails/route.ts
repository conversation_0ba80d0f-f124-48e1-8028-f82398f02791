import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import ContractSigningToken from '@/models/ContractSigningToken';
import ContractAuditLog from '@/models/ContractAuditLog';
import { sendContractSigningEmail } from '@/lib/utils/contract-email-service';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { userIds } = await request.json();

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { error: 'User IDs array is required' },
        { status: 400 }
      );
    }

    // Fetch users and their contract tokens
    const users = await User.find({ 
      _id: { $in: userIds },
      deletedAt: null 
    }).select('_id name email').lean();

    const results = {
      success: [],
      failed: [],
      skipped: []
    };

    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

    for (const user of users) {
      try {
        // Find contract token for this user
        const contractToken = await ContractSigningToken.findOne({ 
          userId: user._id 
        });

        if (!contractToken) {
          results.skipped.push({
            userId: user._id,
            userName: user.name,
            reason: 'No contract found for user'
          });
          continue;
        }

        // Check if admin has signed
        if (!contractToken.adminSignedAt) {
          results.skipped.push({
            userId: user._id,
            userName: user.name,
            reason: 'Contract not signed by admin yet'
          });
          continue;
        }

        // Check if user has already signed
        if (contractToken.userSignedAt) {
          results.skipped.push({
            userId: user._id,
            userName: user.name,
            reason: 'Contract already signed by user'
          });
          continue;
        }

        // Generate signing URL
        const signingUrl = `${baseUrl}/contract-signing/${contractToken.token}`;
        
        // Send email
        await sendContractSigningEmail({
          to: user.email,
          userName: user.name,
          signingUrl,
          contractStartDate: contractToken.contractStartDate
        });

        // Log audit event
        await ContractAuditLog.logAction(
          user._id,
          contractToken.token,
          'email_sent',
          session.user.id,
          undefined,
          request.headers.get('user-agent') || 'unknown'
        );

        results.success.push({
          userId: user._id,
          userName: user.name,
          email: user.email
        });

      } catch (error) {
        results.failed.push({
          userId: user._id,
          userName: user.name || 'Unknown',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({
      success: true,
      results,
      summary: {
        total: userIds.length,
        successful: results.success.length,
        failed: results.failed.length,
        skipped: results.skipped.length
      }
    });

  } catch (error) {
    console.error('Error in bulk email sending:', error);
    return NextResponse.json(
      { error: 'Failed to send emails' },
      { status: 500 }
    );
  }
}
