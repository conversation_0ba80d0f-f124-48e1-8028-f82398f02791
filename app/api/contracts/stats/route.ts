import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';

export async function GET(request: NextRequest) {
  await dbConnect();

  try {
    // Get all contracts
    const allContracts = await ContractSigningToken.find({}).lean();

    // Calculate stats
    const stats = {
      total: allContracts.length,
      completed: allContracts.filter(c => c.isCompleted).length,
      userSigned: allContracts.filter(c => c.userSignedAt && !c.isCompleted).length,
      adminSigned: allContracts.filter(c => c.adminSignedAt && !c.userSignedAt).length,
      draft: allContracts.filter(c => !c.adminSignedAt).length,
      pendingUser: allContracts.filter(c => c.adminSignedAt && !c.userSignedAt).length
    };

    // Calculate completion rate
    stats.completionRate = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;

    // Get recent activity (last 7 days)
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentContracts = allContracts.filter(c => 
      new Date(c.createdAt) >= weekAgo ||
      (c.adminSignedAt && new Date(c.adminSignedAt) >= weekAgo) ||
      (c.userSignedAt && new Date(c.userSignedAt) >= weekAgo)
    );

    stats.recentActivity = recentContracts.length;

    return NextResponse.json({
      success: true,
      stats
    });

  } catch (error: any) {
    console.error('Error fetching contract stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contract stats' },
      { status: 500 }
    );
  }
}
