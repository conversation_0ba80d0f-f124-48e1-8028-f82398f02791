import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { createContractSigningToken, doesUserRequireContract } from '@/lib/utils/contract-token-utils';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { 
      userIds, 
      contractStartDate, 
      adminSignatureData,
      signImmediately = false 
    } = await request.json();

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { error: 'User IDs array is required' },
        { status: 400 }
      );
    }

    if (signImmediately && (!contractStartDate || !adminSignatureData)) {
      return NextResponse.json(
        { error: 'Contract start date and admin signature are required for immediate signing' },
        { status: 400 }
      );
    }

    // Fetch users and validate they require contracts
    const users = await User.find({ 
      _id: { $in: userIds },
      deletedAt: null 
    }).populate('roles', 'name').lean();

    const results = {
      success: [],
      failed: [],
      skipped: []
    };

    for (const user of users) {
      try {
        // Check if user requires a contract
        if (!doesUserRequireContract(user.roles)) {
          results.skipped.push({
            userId: user._id,
            userName: user.name,
            reason: 'User does not require a contract (not PAP or Seller role)'
          });
          continue;
        }

        // Create contract token
        const contractResult = await createContractSigningToken(
          user._id,
          session.user.id,
          signImmediately ? new Date(contractStartDate) : undefined,
          false
        );

        if (!contractResult.success) {
          results.failed.push({
            userId: user._id,
            userName: user.name,
            error: contractResult.error
          });
          continue;
        }

        // If signing immediately, add admin signature directly
        if (signImmediately && contractResult.contractSigningToken) {
          try {
            const ContractSigningToken = (await import('@/models/ContractSigningToken')).default;
            const ContractAuditLog = (await import('@/models/ContractAuditLog')).default;
            const now = new Date();

            // Update the contract token with admin signature
            await ContractSigningToken.findByIdAndUpdate(
              contractResult.contractSigningToken._id,
              {
                adminSignedAt: now,
                adminSignatureData: adminSignatureData,
                contractStartDate: new Date(contractStartDate),
                lastModified: now
              }
            );

            // IMPORTANT: Also update the User model with signature data (needed for PDF generation)
            await User.findByIdAndUpdate(user._id, {
              contractSigningToken: contractResult.token,
              contractAdminSignedAt: now,
              contractStartDate: new Date(contractStartDate),
              contractAdminSignatureData: adminSignatureData
            });

            // Log the admin signing action
            await ContractAuditLog.logAction(
              user._id,
              contractResult.token,
              'admin_signed',
              session.user.id,
              undefined,
              request.headers.get('user-agent') || 'bulk-operation'
            );
          } catch (signError) {
            console.error('Error applying admin signature in bulk creation:', signError);
            results.failed.push({
              userId: user._id,
              userName: user.name,
              error: 'Failed to apply admin signature'
            });
            continue;
          }
        }

        results.success.push({
          userId: user._id,
          userName: user.name,
          token: contractResult.token,
          status: signImmediately ? 'admin_signed' : 'draft'
        });

      } catch (error) {
        results.failed.push({
          userId: user._id,
          userName: user.name || 'Unknown',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return NextResponse.json({
      success: true,
      results,
      summary: {
        total: userIds.length,
        successful: results.success.length,
        failed: results.failed.length,
        skipped: results.skipped.length
      }
    });

  } catch (error) {
    console.error('Error in bulk contract creation:', error);
    return NextResponse.json(
      { error: 'Failed to create contracts' },
      { status: 500 }
    );
  }
}
