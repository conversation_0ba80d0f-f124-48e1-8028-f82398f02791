import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { userIds } = await request.json();

    if (!Array.isArray(userIds)) {
      return NextResponse.json(
        { error: 'userIds must be an array' },
        { status: 400 }
      );
    }

    // Get contract tokens for all users
    const contractTokens = await ContractSigningToken.find({
      userId: { $in: userIds }
    }).lean();

    // Create status map
    const statuses: Record<string, any> = {};
    
    contractTokens.forEach(token => {
      statuses[token.userId.toString()] = {
        token: token.token,
        contractStartDate: token.contractStartDate,
        adminSignedAt: token.adminSignedAt,
        userSignedAt: token.userSignedAt,
        isCompleted: token.isCompleted,
        firstVisitedAt: token.firstVisitedAt
      };
    });

    return NextResponse.json({
      success: true,
      statuses
    });

  } catch (error: any) {
    console.error('Error fetching contract statuses:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contract statuses' },
      { status: 500 }
    );
  }
}
