import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import ContractAuditLog from '@/models/ContractAuditLog';
import User from '@/models/User';
import { randomBytes } from 'crypto';

// Utility function to extract client IP address
function getClientIP(request: NextRequest): string {
  // Check various headers for the real IP address
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip'); // Cloudflare

  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Fallback to unknown if no IP can be determined
  return 'unknown';
}

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    const { userId, signatureData, contractStartDate } = await request.json();
    
    // Get current user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Find or create contract signing token
    let contractToken = await ContractSigningToken.findOne({ userId });
    if (!contractToken) {
      contractToken = await ContractSigningToken.create({
        token: randomBytes(32).toString('hex'),
        userId,
        createdBy: session.user.id,
        createdAt: new Date()
      });
    }
    
    // Update with admin signature
    const now = new Date();
    const clientIP = getClientIP(request);
    
    contractToken.adminSignedAt = now;
    contractToken.adminSignatureData = signatureData;
    contractToken.adminSignedFromIP = clientIP;
    contractToken.contractStartDate = new Date(contractStartDate);
    await contractToken.save();
    
    // Update user record
    await User.findByIdAndUpdate(userId, {
      contractSigningToken: contractToken.token,
      contractAdminSignedAt: now,
      contractStartDate: new Date(contractStartDate),
      contractAdminSignatureData: signatureData
    });
    
    // Log audit event
    await ContractAuditLog.logAction(
      userId,
      contractToken.token,
      'admin_signed',
      session.user.id,
      clientIP,
      request.headers.get('user-agent') || 'unknown',
      { contractStartDate }
    );
    
    return NextResponse.json({
      success: true,
      token: contractToken.token,
      message: 'Contract signed by admin successfully'
    });
    
  } catch (error) {
    console.error('Error signing contract as admin:', error);
    return NextResponse.json(
      { error: 'Failed to sign contract' },
      { status: 500 }
    );
  }
}
