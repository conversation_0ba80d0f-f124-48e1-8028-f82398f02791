import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import User from '@/models/User';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    // Get current user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }
    
    // Get user data
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Get contract token if exists
    const contractToken = await ContractSigningToken.findOne({ userId });
    
    // Determine contract status
    let status = 'not_required';
    if (contractToken) {
      if (contractToken.isCompleted) {
        status = 'completed';
      } else if (contractToken.userSignedAt) {
        status = 'user_signed';
      } else if (contractToken.adminSignedAt) {
        status = 'admin_signed';
      } else {
        status = 'created';
      }
    }
    
    return NextResponse.json({
      success: true,
      status,
      contractToken,
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        roles: user.roles
      }
    });
    
  } catch (error) {
    console.error('Error getting contract status:', error);
    return NextResponse.json(
      { error: 'Failed to get contract status' },
      { status: 500 }
    );
  }
}
