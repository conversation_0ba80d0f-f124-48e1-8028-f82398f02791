import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import User from '@/models/User';

export async function POST(request: NextRequest) {
  await dbConnect();

  try {
    const { status, dateRange, search } = await request.json();

    // Build query
    let query: any = {};
    
    // Status filter
    if (status && status !== 'all') {
      switch (status) {
        case 'completed':
          query.isCompleted = true;
          break;
        case 'user_signed':
          query.userSignedAt = { $exists: true };
          query.isCompleted = false;
          break;
        case 'admin_signed':
          query.adminSignedAt = { $exists: true };
          query.userSignedAt = { $exists: false };
          break;
        case 'draft':
          query.adminSignedAt = { $exists: false };
          break;
      }
    }

    // Date range filter
    if (dateRange && dateRange !== 'all') {
      const now = new Date();
      let startDate: Date;
      
      switch (dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        default:
          startDate = new Date(0);
      }
      
      query.createdAt = { $gte: startDate };
    }

    // Get contracts with user data
    const contracts = await ContractSigningToken.find(query)
      .populate('userId', 'name email phone')
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 })
      .lean();

    // Filter by search if provided
    let filteredContracts = contracts;
    if (search && search.trim()) {
      const searchLower = search.toLowerCase();
      filteredContracts = contracts.filter(contract => 
        contract.userId?.name?.toLowerCase().includes(searchLower) ||
        contract.userId?.email?.toLowerCase().includes(searchLower)
      );
    }

    // Format contracts for response
    const formattedContracts = filteredContracts.map(contract => ({
      _id: contract._id,
      token: contract.token,
      userId: contract.userId?._id,
      userName: contract.userId?.name,
      userEmail: contract.userId?.email,
      userPhone: contract.userId?.phone,
      contractStartDate: contract.contractStartDate,
      adminSignedAt: contract.adminSignedAt,
      userSignedAt: contract.userSignedAt,
      isCompleted: contract.isCompleted,
      createdAt: contract.createdAt,
      createdBy: contract.createdBy?.name,
      firstVisitedAt: contract.firstVisitedAt
    }));

    return NextResponse.json({
      success: true,
      contracts: formattedContracts
    });

  } catch (error: any) {
    console.error('Error fetching contracts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contracts' },
      { status: 500 }
    );
  }
}
