import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import ContractAuditLog from '@/models/ContractAuditLog';
import User from '@/models/User';

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  await dbConnect();

  try {
    const { userId } = await params;

    // Get contract token
    const contractToken = await ContractSigningToken.findOne({ userId })
      .populate('createdBy', 'name email')
      .lean();

    if (!contractToken) {
      return NextResponse.json({
        success: true,
        details: null
      });
    }

    // Get audit log
    const auditLog = await ContractAuditLog.find({
      userId,
      token: contractToken.token
    }).sort({ timestamp: 1 }).lean();

    // Get user data
    const user = await User.findById(userId).select('name email').lean();

    const details = {
      ...contractToken,
      adminName: contractToken.createdBy?.name,
      userName: user?.name,
      userEmail: user?.email,
      auditLog
    };

    return NextResponse.json({
      success: true,
      details
    });

  } catch (error: any) {
    console.error('Error fetching contract details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch contract details' },
      { status: 500 }
    );
  }
}
