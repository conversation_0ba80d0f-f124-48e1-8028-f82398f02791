import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import ContractSigningToken from '@/models/ContractSigningToken';
import { doesUserRequireContract } from '@/lib/utils/contract-token-utils';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get all active users with PAP or Seller roles
    const users = await User.find({ 
      deletedAt: null,
      isActive: true,
      roles: { 
        $in: [
          '67e0aad60f0a3bdeba18542c', // Seller role ID
          '67fbd1707839bdba5be4b02b'  // PAP role ID
        ]
      }
    })
    .populate('roles', 'name')
    .select('_id name email roles')
    .lean();

    // Filter users who actually require contracts and get their contract status
    const eligibleUsers = [];
    const usersWithContracts = [];

    for (const user of users) {
      if (doesUserRequireContract(user.roles)) {
        // Check if user already has a contract
        const existingContract = await ContractSigningToken.findOne({ 
          userId: user._id 
        });

        const userInfo = {
          _id: user._id,
          name: user.name,
          email: user.email,
          roles: user.roles.map((role: any) => role.name).join(', ')
        };

        if (!existingContract) {
          eligibleUsers.push(userInfo);
        } else {
          usersWithContracts.push({
            ...userInfo,
            contractStatus: {
              hasContract: true,
              adminSigned: !!existingContract.adminSignedAt,
              userSigned: !!existingContract.userSignedAt,
              isCompleted: existingContract.isCompleted,
              token: existingContract.token
            }
          });
        }
      }
    }

    return NextResponse.json({
      success: true,
      eligibleUsers, // Users who need contracts but don't have them
      usersWithContracts, // Users who already have contracts
      summary: {
        totalEligible: eligibleUsers.length,
        totalWithContracts: usersWithContracts.length,
        totalUsers: users.length
      }
    });

  } catch (error) {
    console.error('Error fetching eligible users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch eligible users' },
      { status: 500 }
    );
  }
}
