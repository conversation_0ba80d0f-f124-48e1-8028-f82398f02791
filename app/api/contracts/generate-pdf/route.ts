import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { generateContractPDFDataURL } from '@/lib/utils/contract-pdf-utils';
import { mapUserWithExistingSignatures, validateContractData, userRequiresContract } from '@/lib/utils/contract-data-mapper';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { isValidObjectId } from 'mongoose';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { userId, includeSignatures = false, testData } = await request.json();

    // Handle test data for debugging (when userId is a dummy ID)
    if (testData && userId === '000000000000000000000000') {
      console.log('Using test data for contract PDF generation');

      // Validate test data has required role
      if (!userRequiresContract(testData.user)) {
        return NextResponse.json(
          { error: 'Test data must include PAP or Seller role for contract generation' },
          { status: 400 }
        );
      }

      // Use test data directly
      const contractData = testData;

      // If not including signatures, remove them from the data
      if (!includeSignatures) {
        contractData.adminSignature = undefined;
        contractData.userSignature = undefined;
      }

      // Validate contract data
      const validation = validateContractData(contractData);
      if (!validation.isValid) {
        return NextResponse.json(
          {
            error: 'Contract data validation failed',
            details: validation.errors
          },
          { status: 400 }
        );
      }

      // Generate PDF
      const pdfBase64 = await generateContractPDFDataURL(contractData);

      // Generate filename for test data
      const userName = contractData.user.name.replace(/[^a-z0-9_-]/gi, '_');
      const timestamp = new Date().toISOString().split('T')[0];

      let fileName = `Contract_${userName}_${timestamp}`;

      if (includeSignatures) {
        if (contractData.adminSignature && contractData.userSignature) {
          fileName += '_Completed';
        } else if (contractData.adminSignature) {
          fileName += '_AdminSigned';
        }
      } else {
        fileName += '_Draft';
      }

      fileName += '.pdf';

      return NextResponse.json({
        success: true,
        pdfBase64,
        fileName,
        contractStatus: {
          hasAdminSignature: !!contractData.adminSignature,
          hasUserSignature: !!contractData.userSignature,
          isCompleted: !!(contractData.adminSignature && contractData.userSignature)
        },
        warnings: validation.warnings
      });
    }

    // Validate required parameters for real user
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Validate ObjectId format
    if (!isValidObjectId(userId)) {
      return NextResponse.json({
        error: 'Invalid user ID format',
        details: 'User ID must be a valid 24-character MongoDB ObjectId'
      }, { status: 400 });
    }

    // Fetch user data with populated roles
    const user = await User.findById(userId).populate('roles').lean();
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user requires a contract
    if (!userRequiresContract(user)) {
      return NextResponse.json(
        { error: 'Contract generation is only available for PAP and Seller roles' },
        { status: 400 }
      );
    }

    // Map user data to contract format
    const contractData = mapUserWithExistingSignatures(user);

    // If not including signatures, remove them from the data
    if (!includeSignatures) {
      contractData.adminSignature = undefined;
      contractData.userSignature = undefined;
    }

    // Validate contract data
    const validation = validateContractData(contractData);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Contract data validation failed',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Generate PDF
    const pdfBase64 = await generateContractPDFDataURL(contractData);

    // Generate filename
    const userName = user.name.replace(/[^a-z0-9_-]/gi, '_');
    const timestamp = new Date().toISOString().split('T')[0];
    
    let fileName = `Contract_${userName}_${timestamp}`;
    
    if (includeSignatures) {
      if (contractData.adminSignature && contractData.userSignature) {
        fileName += '_Completed';
      } else if (contractData.adminSignature) {
        fileName += '_AdminSigned';
      }
    } else {
      fileName += '_Draft';
    }
    
    fileName += '.pdf';

    return NextResponse.json({
      success: true,
      pdfBase64,
      fileName,
      contractStatus: {
        hasAdminSignature: !!contractData.adminSignature,
        hasUserSignature: !!contractData.userSignature,
        isCompleted: !!(contractData.adminSignature && contractData.userSignature)
      },
      warnings: validation.warnings
    });

  } catch (error: any) {
    console.error('Error generating contract PDF:', error);
    
    // Handle specific error types
    if (error.message?.includes('Contract generation is only available')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to generate contract PDF',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const includeSignatures = searchParams.get('includeSignatures') === 'true';

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    await dbConnect();

    // Fetch user data with populated roles
    const user = await User.findById(userId).populate('roles').lean();
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user requires a contract
    if (!userRequiresContract(user)) {
      return NextResponse.json(
        { error: 'Contract generation is only available for PAP and Seller roles' },
        { status: 400 }
      );
    }

    // Map user data to contract format
    const contractData = mapUserWithExistingSignatures(user);

    // If not including signatures, remove them from the data
    if (!includeSignatures) {
      contractData.adminSignature = undefined;
      contractData.userSignature = undefined;
    }

    // Validate contract data
    const validation = validateContractData(contractData);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Contract data validation failed',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Generate PDF
    const pdfBase64 = await generateContractPDFDataURL(contractData);

    // Set appropriate headers for PDF download
    const pdfBuffer = Buffer.from(pdfBase64.split(',')[1], 'base64');
    
    const userName = user.name.replace(/[^a-z0-9_-]/gi, '_');
    const timestamp = new Date().toISOString().split('T')[0];
    let fileName = `Contract_${userName}_${timestamp}`;
    
    if (includeSignatures) {
      if (contractData.adminSignature && contractData.userSignature) {
        fileName += '_Completed';
      } else if (contractData.adminSignature) {
        fileName += '_AdminSigned';
      }
    } else {
      fileName += '_Draft';
    }
    
    fileName += '.pdf';

    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    });

  } catch (error: any) {
    console.error('Error generating contract PDF for download:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate contract PDF',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}
