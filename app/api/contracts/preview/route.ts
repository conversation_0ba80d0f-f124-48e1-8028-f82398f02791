import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import User from '@/models/User';
import { generateContractHTML } from '@/lib/utils/contract-html-template';
import { mapUserWithExistingSignatures, validateContractData, userRequiresContract, getContractStatus } from '@/lib/utils/contract-data-mapper';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { isValidObjectId } from 'mongoose';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    const { userId, includeSignatures = false, testData } = await request.json();

    // Handle test data for debugging (when userId is a dummy ID)
    if (testData && userId === '000000000000000000000000') {
      console.log('Using test data for contract preview');

      // Validate test data has required role
      if (!userRequiresContract(testData.user)) {
        return NextResponse.json(
          { error: 'Test data must include PAP or Seller role for contract preview' },
          { status: 400 }
        );
      }

      // Use test data directly
      const contractData = testData;

      // If not including signatures, remove them from the data
      if (!includeSignatures) {
        contractData.adminSignature = undefined;
        contractData.userSignature = undefined;
      }

      // Validate contract data
      const validation = validateContractData(contractData);
      if (!validation.isValid) {
        return NextResponse.json(
          {
            error: 'Contract data validation failed',
            details: validation.errors
          },
          { status: 400 }
        );
      }

      // Generate HTML preview
      const htmlContent = generateContractHTML(contractData);

      // Debug: Log HTML content hash for comparison with PDF
      if (process.env.NODE_ENV === 'development') {
        const crypto = require('crypto');
        const htmlHash = crypto.createHash('md5').update(htmlContent).digest('hex');
        console.log(`Preview HTML Content Hash: ${htmlHash}`);
      }

      const contractStatus = getContractStatus(contractData);

      return NextResponse.json({
        success: true,
        htmlContent,
        contractData: {
          user: {
            name: contractData.user.name,
            email: contractData.user.email,
            phone: contractData.user.phone,
            roles: contractData.user.roles?.map((role: any) => role.name).join(', ') || 'No roles'
          },
          contractStartDate: contractData.contractStartDate,
          hasAdminSignature: !!contractData.adminSignature,
          hasUserSignature: !!contractData.userSignature,
          status: contractStatus
        },
        validation: {
          isValid: validation.isValid,
          errors: validation.errors,
          warnings: validation.warnings
        }
      });
    }

    // Validate required parameters for real user
    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    // Validate ObjectId format
    if (!isValidObjectId(userId)) {
      return NextResponse.json({
        error: 'Invalid user ID format',
        details: 'User ID must be a valid 24-character MongoDB ObjectId'
      }, { status: 400 });
    }

    // Fetch user data with populated roles
    const user = await User.findById(userId).populate('roles').lean();
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user requires a contract
    if (!userRequiresContract(user)) {
      return NextResponse.json(
        { error: 'Contract preview is only available for PAP and Seller roles' },
        { status: 400 }
      );
    }

    // Map user data to contract format
    const contractData = mapUserWithExistingSignatures(user);

    // If not including signatures, remove them from the data
    if (!includeSignatures) {
      contractData.adminSignature = undefined;
      contractData.userSignature = undefined;
    }

    // Validate contract data
    const validation = validateContractData(contractData);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Contract data validation failed',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Generate HTML preview
    const htmlContent = generateContractHTML(contractData);

    // Get contract status
    const contractStatus = getContractStatus(contractData);

    return NextResponse.json({
      success: true,
      htmlContent,
      contractData: {
        user: {
          name: contractData.user.name,
          email: contractData.user.email,
          phone: contractData.user.phone,
          roles: contractData.user.roles?.map(role => role.name).join(', ') || 'No roles'
        },
        contractStartDate: contractData.contractStartDate,
        hasAdminSignature: !!contractData.adminSignature,
        hasUserSignature: !!contractData.userSignature,
        status: contractStatus
      },
      validation: {
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings
      }
    });

  } catch (error: any) {
    console.error('Error generating contract preview:', error);
    
    // Handle specific error types
    if (error.message?.includes('Contract generation is only available')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: 'Failed to generate contract preview',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const includeSignatures = searchParams.get('includeSignatures') === 'true';

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    await dbConnect();

    // Fetch user data with populated roles
    const user = await User.findById(userId).populate('roles').lean();
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user requires a contract
    if (!userRequiresContract(user)) {
      return NextResponse.json(
        { error: 'Contract preview is only available for PAP and Seller roles' },
        { status: 400 }
      );
    }

    // Map user data to contract format
    const contractData = mapUserWithExistingSignatures(user);

    // If not including signatures, remove them from the data
    if (!includeSignatures) {
      contractData.adminSignature = undefined;
      contractData.userSignature = undefined;
    }

    // Validate contract data
    const validation = validateContractData(contractData);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          error: 'Contract data validation failed',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Generate HTML preview
    const htmlContent = generateContractHTML(contractData);

    // Return HTML content directly for iframe preview
    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
      },
    });

  } catch (error: any) {
    console.error('Error generating contract HTML preview:', error);
    
    // Return error as HTML for iframe display
    const errorHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Contract Preview Error</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; color: #d32f2f; }
          .error { background: #ffebee; border: 1px solid #d32f2f; padding: 15px; border-radius: 4px; }
        </style>
      </head>
      <body>
        <div class="error">
          <h2>Error generating contract preview</h2>
          <p>${error.message || 'Unknown error occurred'}</p>
          ${process.env.NODE_ENV === 'development' ? `<pre>${error.stack}</pre>` : ''}
        </div>
      </body>
      </html>
    `;

    return new NextResponse(errorHtml, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
      },
      status: 500,
    });
  }
}
