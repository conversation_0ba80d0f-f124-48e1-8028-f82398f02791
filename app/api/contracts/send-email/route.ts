import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/route';
import dbConnect from '@/lib/db';
import ContractSigningToken from '@/models/ContractSigningToken';
import ContractAuditLog from '@/models/ContractAuditLog';
import User from '@/models/User';
import { sendContractSigningEmail } from '@/lib/utils/contract-email-service';

export async function POST(request: NextRequest) {
  try {
    await dbConnect();
    
    const { userId } = await request.json();
    
    // Get current user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get contract token
    const contractToken = await ContractSigningToken.findOne({ userId });
    if (!contractToken || !contractToken.adminSignedAt) {
      return NextResponse.json(
        { error: 'Contract must be signed by admin first' },
        { status: 400 }
      );
    }
    
    // Get user data
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    
    // Generate signing URL
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const signingUrl = `${baseUrl}/contract-signing/${contractToken.token}`;
    
    // Send email using contract signing template
    await sendContractSigningEmail({
      to: user.email,
      userName: user.name,
      signingUrl,
      contractStartDate: contractToken.contractStartDate
    });
    
    // Log audit event
    await ContractAuditLog.logAction(
      userId,
      contractToken.token,
      'email_sent',
      session.user.id,
      undefined,
      request.headers.get('user-agent') || 'unknown'
    );
    
    return NextResponse.json({
      success: true,
      message: 'Contract signing email sent successfully'
    });
    
  } catch (error) {
    console.error('Error sending contract email:', error);
    return NextResponse.json(
      { error: 'Failed to send contract email' },
      { status: 500 }
    );
  }
}
