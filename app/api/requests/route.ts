import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { RequestDocument } from '@/app/types/request';
import mongoose, { Document } from 'mongoose';
import { IReservation } from '@/models/Reservation';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import Branch from '@/models/Branch';
import { getUserPermissions, getUserRoles } from '../utils/server-permission-utils';
import * as RoleUtils from '@/lib/utils/role-utils';
// Define an interface for the reservation document that includes _id
interface ReservationDocument extends IReservation, Document {
  _id: mongoose.Types.ObjectId; // Explicitly include _id
}

// Define an interface for the aggregated result using ReservationDocument
interface AggregatedRequest extends Omit<RequestDocument, 'reservationId' | '_id' | 'createdAt'> {
  _id: mongoose.Types.ObjectId;
  createdAt: Date;
  reservationId?: mongoose.Types.ObjectId | null;
  reservationDetails?: ReservationDocument[];
}

export async function GET(request: Request) {
  try {
    const db = await connectToDatabase();
    const requestsCollection = db.collection<RequestDocument>('requests');

    // Parse branchId from query params
    const url = new URL(request.url);
    const branchId = url.searchParams.get('branchId');
    const showArchived = url.searchParams.get('showArchived') === 'true';

    // Auth check
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
   
    if(!session.user.roles) {
      session.user.roles = await getUserRoles(session);
    }
    if(!session.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }
    const userId = session.user.id;
    const isSuperAdmin = RoleUtils.isSuperAdmin(session.user.roles);

    // If not super admin, and branchId is provided, check membership
    if (!isSuperAdmin && branchId) {
      const branch = await Branch.findById(branchId).lean() as { responsible?: any[]; agents?: any[] } | null;
      console.log('branch responsible', branch?.responsible?.map((id: any) => id.toString()));
      console.log('user id', userId);
      if (!branch) {
        return NextResponse.json({ error: 'Branch not found' }, { status: 404 });
      }
      const isMember = (branch.responsible || []).some((id: any) => id.toString() === userId) ||
                      (branch.agents || []).some((id: any) => id.toString() === userId);
      if (!isMember) {
        return NextResponse.json({ error: 'Forbidden: not a member of this branch' }, { status: 403 });
      }
    }

    // Build match stage for aggregation
    let match: any = {};
    if (branchId) match.branchId = branchId;
    if (!showArchived) match.isArchived = { $ne: true };
    const matchStage = Object.keys(match).length > 0 ? { $match: match } : undefined;

    const pipeline = [
      ...(matchStage ? [matchStage] : []),
      { $sort: { createdAt: -1 } },
      {
        $lookup: {
          from: 'reservations',
          localField: 'reservationId',
          foreignField: '_id',
          as: 'reservationDetails'
        }
      }
    ];

    const requestsWithReservations = await requestsCollection.aggregate<AggregatedRequest>(pipeline).toArray();
    const sanitizedResults = requestsWithReservations.map(req => {
      const reservation = req.reservationDetails && req.reservationDetails.length > 0 ? req.reservationDetails[0] : null;
      
      // Now access _id safely from reservation
      const reservationData = reservation ? {
          _id: reservation._id.toString(), // Now this should be valid
          customerName: reservation.customerInfo?.client1Name, 
          visitDate: reservation.preferences?.visitDate,
          visitTime: reservation.preferences?.visitTime,
          status: reservation.status
      } : null;

      return {
        _id: req._id.toString(),
        messageSid: req.messageSid,
        from: req.from,
        body: req.body,
        processed: req.processed,
        createdAt: req.createdAt,
        status: req.status,
        assignedTo: req.assignedTo,
        notes: req.notes,
        reservationId: req.reservationId ? req.reservationId.toString() : null,
        reservation: reservationData
      };
    });

    return NextResponse.json(sanitizedResults);
  } catch (error) {
    console.error('Error fetching requests with reservations:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: 'Failed to fetch requests', details: errorMessage },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  // Bulk archive/unarchive endpoint: expects { status, isArchived, userId }
  try {
    const db = await connectToDatabase();
    const requestsCollection = db.collection<RequestDocument>('requests');
    const body = await request.json();
    const { status, isArchived, userId } = body;
    if (!['new', 'processing', 'processed'].includes(status)) {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 });
    }
    if (typeof isArchived !== 'boolean' || !userId) {
      return NextResponse.json({ error: 'Missing isArchived or userId' }, { status: 400 });
    }
    // Build update for archivedBy
    let update: any;
    if (isArchived) {
      update = { $set: { isArchived: true }, $addToSet: { archivedBy: userId } };
    } else {
      update = { $set: { isArchived: false }, $pull: { archivedBy: userId } };
    }
    // Update all requests with the given status
    const result = await requestsCollection.updateMany(
      { status, isArchived: { $ne: isArchived } },
      update
    );
    return NextResponse.json({ success: true, modified: result.modifiedCount });
  } catch (error) {
    console.error('Bulk archive error:', error);
    return NextResponse.json({ error: 'Bulk archive failed' }, { status: 500 });
  }
} 