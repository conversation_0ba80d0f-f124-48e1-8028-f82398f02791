import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import mongoose from 'mongoose';

interface RequestPatchData {
  status?: "new" | "processing" | "processed";
  notes?: string;
  assignedTo?: string;
  isArchived?: boolean;
  archivedBy?: string[];
}

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const p=await params;
    const id = p.id;
    
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid request ID' },
        { status: 400 }
      );
    }

    // Get update data from request body
    const data: RequestPatchData = await request.json();
    
    // Validate the status if provided
    if (data.status && !["new", "processing", "processed"].includes(data.status)) {
      return NextResponse.json(
        { error: 'Invalid status value' },
        { status: 400 }
      );
    }

    // Connect to DB
    const db = await connectToDatabase();
    const requestsCollection = db.collection('requests');

    // Build update object with only provided fields
    const updateData: { [key: string]: any } = {};
    if (data.status) updateData.status = data.status;
    if (data.notes) updateData.notes = data.notes;
    if (data.assignedTo) updateData.assignedTo = data.assignedTo;
    if (typeof data.isArchived === 'boolean') updateData.isArchived = data.isArchived;
    if (Array.isArray(data.archivedBy)) updateData.archivedBy = data.archivedBy;
    
    // Add updatedAt timestamp
    updateData.updatedAt = new Date();

    // Update the request
    const result = await requestsCollection.updateOne(
      { _id: new mongoose.Types.ObjectId(id) },
      { $set: updateData }
    );

    if (result.matchedCount === 0) {
      return NextResponse.json(
        { error: 'Request not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      updated: result.modifiedCount > 0,
      id
    });
  } catch (error) {
    console.error('Error updating request:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { error: 'Failed to update request', details: errorMessage },
      { status: 500 }
    );
  }
} 