import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { SETTINGS_PERMISSIONS } from '@/types/permission-codes';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';

// Define all available reservation columns
const AVAILABLE_RESERVATION_COLUMNS = [
  // Basic reservation info
  { key: '_id', label: 'ID', category: 'basic' },
  { key: 'type', label: 'Type', category: 'basic' },
  { key: 'status', label: 'Status', category: 'basic' },
  { key: 'source', label: 'Source', category: 'basic' },
  { key: 'confirmationType', label: 'Confirmation Type', category: 'basic' },
  { key: 'createdAt', label: 'Created At', category: 'basic' },
  { key: 'updatedAt', label: 'Updated At', category: 'basic' },
  
  // Customer information
  { key: 'customerInfo.client1Name', label: 'Client 1 Name', category: 'customer' },
  { key: 'customerInfo.client2Name', label: 'Client 2 Name', category: 'customer' },
  { key: 'customerInfo.hasCompanion', label: 'Has Companion', category: 'customer' },
  { key: 'customerInfo.city', label: 'City', category: 'customer' },
  { key: 'customerInfo.postalCode', label: 'Postal Code', category: 'customer' },
  { key: 'customerInfo.phone', label: 'Phone', category: 'customer' },
  { key: 'customerInfo.phone2', label: 'Phone 2', category: 'customer' },
  { key: 'customerInfo.email', label: 'Email', category: 'customer' },
  { key: 'customerInfo.isPostalCodeValid', label: 'Valid Postal Code', category: 'customer' },
  
  // Preferences
  { key: 'preferences.preferredLanguage', label: 'Preferred Language', category: 'preferences' },
  { key: 'preferences.allergies', label: 'Allergies', category: 'preferences' },
  { key: 'preferences.hasChildren', label: 'Has Children', category: 'preferences' },
  { key: 'preferences.childrenAges.age0to5', label: 'Children 0-5', category: 'preferences' },
  { key: 'preferences.childrenAges.age6to12', label: 'Children 6-12', category: 'preferences' },
  { key: 'preferences.childrenAges.age13to17', label: 'Children 13-17', category: 'preferences' },
  { key: 'preferences.branchId', label: 'Branch ID', category: 'preferences' },
  { key: 'preferences.visitDate', label: 'Visit Date', category: 'preferences' },
  { key: 'preferences.visitTime', label: 'Visit Time', category: 'preferences' },
  
  // Assignment and relationships
  { key: 'appointmentId', label: 'Appointment ID', category: 'relationships' },
  { key: 'partnerId', label: 'Partner ID', category: 'relationships' },
  { key: 'partner.name', label: 'Partner Name', category: 'relationships' },
  { key: 'partner.email', label: 'Partner Email', category: 'relationships' },
  { key: 'assigned_user_id', label: 'Assigned User ID', category: 'relationships' },
  
  // Status tracking
  { key: 'isDeleted', label: 'Is Deleted', category: 'status' },
  { key: 'deletedAt', label: 'Deleted At', category: 'status' },
  { key: 'presentAt', label: 'Present At', category: 'status' },
  { key: 'soldAt', label: 'Sold At', category: 'status' },
  { key: 'sellingAmount', label: 'Selling Amount', category: 'status' },
  
  // Communication
  { key: 'thankYouMessageSent', label: 'Thank You Message Sent', category: 'communication' },
  { key: 'grandJourSmsSent', label: 'Grand Jour SMS Sent', category: 'communication' },
  { key: 'stopMessage', label: 'Stop Message', category: 'communication' },
  
  // History
  { key: 'previousStatuses', label: 'Previous Statuses', category: 'history' },
];

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    if (!session.user.permissions?.includes(SETTINGS_PERMISSIONS.MANAGE_RESERVATION_SETTINGS)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Group columns by category for better organization
    const columnsByCategory = AVAILABLE_RESERVATION_COLUMNS.reduce((acc, column) => {
      if (!acc[column.category]) {
        acc[column.category] = [];
      }
      acc[column.category].push(column);
      return acc;
    }, {} as Record<string, typeof AVAILABLE_RESERVATION_COLUMNS>);

    return NextResponse.json({
      columns: AVAILABLE_RESERVATION_COLUMNS,
      columnsByCategory,
      categories: [
        { key: 'basic', label: 'Basic Information' },
        { key: 'customer', label: 'Customer Information' },
        { key: 'preferences', label: 'Preferences' },
        { key: 'relationships', label: 'Relationships' },
        { key: 'status', label: 'Status Tracking' },
        { key: 'communication', label: 'Communication' },
        { key: 'history', label: 'History' },
      ]
    });
  } catch (error) {
    console.error('GET reservation columns error:', error);
    return NextResponse.json({ error: 'Failed to fetch reservation columns' }, { status: 500 });
  }
}
