import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Reservation from '@/models/Reservation';
import Appointment from '@/models/Appointment';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import mongoose from 'mongoose';

interface RouteParams {
  params: Promise<{ id: string }>;
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    await dbConnect();

    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user permissions
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    const { id } = await params;
    const body = await request.json();
    const { appointmentId } = body;

    // Validate required fields
    if (!appointmentId) {
      return NextResponse.json(
        { error: 'appointmentId is required' },
        { status: 400 }
      );
    }

    // Validate reservation ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid reservation ID format' },
        { status: 400 }
      );
    }

    // Validate appointment ID format
    if (!mongoose.Types.ObjectId.isValid(appointmentId)) {
      return NextResponse.json(
        { error: 'Invalid appointment ID format' },
        { status: 400 }
      );
    }

    // Check if reservation exists
    const existingReservation = await Reservation.findById(id);
    if (!existingReservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }

    // Fetch the new appointment details to update preferences
    let newAppointment;
    try {
      newAppointment = await Appointment.findById(appointmentId);
      if (!newAppointment) {
        return NextResponse.json(
          { error: 'Appointment not found' },
          { status: 404 }
        );
      }
    } catch (appointmentError) {
      console.warn('Could not fetch appointment details:', appointmentError);
      return NextResponse.json(
        { error: 'Failed to fetch appointment details' },
        { status: 500 }
      );
    }

    // Prepare the update data with new appointment details
    const updateData: any = {
      appointmentId: appointmentId,
      updatedAt: new Date()
    };

    // Update preferences with new appointment date and time
    if (newAppointment) {
      // Format the date as ISO string for consistency with existing data
      const visitDate = `${newAppointment.date}T04:00:00.000Z`;
      const visitTime = `${newAppointment.startHour}-${newAppointment.endHour}`;

      updateData['preferences.visitDate'] = visitDate;
      updateData['preferences.visitTime'] = visitTime;
    }

    // Update the reservation's appointmentId and preferences
    const updatedReservation = await Reservation.findByIdAndUpdate(
      id,
      updateData,
      {
        new: true, // Return the updated document
        runValidators: true // Run schema validators
      }
    );

    if (!updatedReservation) {
      return NextResponse.json(
        { error: 'Failed to update reservation' },
        { status: 500 }
      );
    }

    // Log the update for audit purposes
    console.log(`Reservation ${id} appointment updated from ${existingReservation.appointmentId} to ${appointmentId} by user ${session.user.id}`);

    return NextResponse.json({
      success: true,
      message: 'Reservation appointment and preferences updated successfully',
      data: {
        reservationId: updatedReservation._id,
        previousAppointmentId: existingReservation.appointmentId,
        newAppointmentId: updatedReservation.appointmentId,
        updatedPreferences: {
          visitDate: updatedReservation.preferences?.visitDate,
          visitTime: updatedReservation.preferences?.visitTime
        },
        appointmentDetails: newAppointment ? {
          id: newAppointment._id.toString(),
          date: newAppointment.date,
          startHour: newAppointment.startHour,
          endHour: newAppointment.endHour,
          capacity: newAppointment.capacity,
          branchId: newAppointment.branchId?.toString()
        } : null,
        updatedAt: updatedReservation.updatedAt
      }
    });

  } catch (error) {
    console.error('Error updating reservation appointment:', error);

    // Provide more detailed error information for debugging
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorDetails = {
      error: 'Failed to update reservation appointment',
      message: errorMessage,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(errorDetails, { status: 500 });
  }
}

// Optional: Add GET method to retrieve a specific reservation with appointment details
export async function GET(_request: NextRequest, { params }: RouteParams) {
  try {
    await dbConnect();

    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    // Validate reservation ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: 'Invalid reservation ID format' },
        { status: 400 }
      );
    }

    // Find reservation and try to populate appointment data
    let reservation;
    try {
      reservation = await Reservation.findById(id)
        .populate('appointmentId', 'date startHour capacity branchId')
        .lean();
    } catch (populateError) {
      console.warn('Failed to populate appointment data:', populateError);
      // Fallback without population
      reservation = await Reservation.findById(id).lean();
    }

    if (!reservation) {
      return NextResponse.json(
        { error: 'Reservation not found' },
        { status: 404 }
      );
    }

    // Transform appointment data if populated
    const reservationData = reservation as any;
    if (reservationData.appointmentId && typeof reservationData.appointmentId === 'object') {
      const appointment = reservationData.appointmentId;
      reservationData.appointment = {
        id: appointment._id?.toString() || null,
        date: appointment.date ? appointment.date.split('T')[0] : null,
        startHour: appointment.startHour || null,
        capacity: appointment.capacity || null,
        branchId: appointment.branchId?.toString() || null
      };
      reservationData.appointmentId = appointment._id?.toString() || null;
    } else {
      reservationData.appointment = {
        id: reservationData.appointmentId?.toString() || null,
        date: null,
        startHour: null,
        capacity: null,
        branchId: null
      };
    }

    return NextResponse.json({
      success: true,
      data: reservation
    });

  } catch (error) {
    console.error('Error fetching reservation:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorDetails = {
      error: 'Failed to fetch reservation',
      message: errorMessage,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(errorDetails, { status: 500 });
  }
}
