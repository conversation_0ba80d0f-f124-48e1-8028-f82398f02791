import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import Reservation from '@/models/Reservation';
import Branch from '@/models/Branch';
import User from '@/models/User';
import Appointment from '@/models/Appointment';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getUserPermissions } from '@/app/api/utils/server-permission-utils';
import {
  getUserEffectiveFilters,
  getAvailableStatuses,
  getAvailableBranches,
  getAvailableUsers,
  applyAccessControlToQuery,
  applyBranchAccessControl,
  getSelectFields,
  transformReservationData,
  getColumnDefinitions,
  getSearchableFields,
  getSortableFields,
  mapFrontendFieldToDb
} from '@/app/api/utils/reservation-access-utils';
import {
  type MRT_ColumnFiltersState,
  type MRT_SortingState,
} from 'mantine-react-table';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    // Ensure Appointment model is registered
    if (!Appointment) {
      throw new Error('Appointment model not available');
    }

    // Authentication check
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user permissions
    if (session && !session?.user.permissions) {
      session.user.permissions = await getUserPermissions(session);
    }

    const { searchParams } = new URL(request.url);
    const start = parseInt(searchParams.get('start') || '0');
    const size = parseInt(searchParams.get('size') || '10');
    const filters = searchParams.get('filters') || '[]';
    const sorting = searchParams.get('sorting') || '[]';
    const globalFilter = searchParams.get('globalFilter') || '';

    const parsedColumnFilters = JSON.parse(filters) as MRT_ColumnFiltersState;
    const parsedSorting = JSON.parse(sorting) as MRT_SortingState;

    // Get user's effective filters for access control
    const effectiveFilters = await getUserEffectiveFilters(session.user.id);

    // Get user's branch IDs once for reuse
    let userBranchIds: string[] = [];
    if (effectiveFilters.branchAccess === 'assigned') {
      const user = await User.findById(session.user.id).select('branchIds').lean() as any;
      userBranchIds = user?.branchIds?.map((id: any) => id.toString()) || [];
    }

    // Build MongoDB query
    let query: any = { isDeleted: { $ne: true } }; // Exclude deleted reservations

    // Apply access control filters (excluding branch filtering)
    query = await applyAccessControlToQuery(query, effectiveFilters, session.user.id);

    // Apply column filters using dynamic field mapping
    if (parsedColumnFilters?.length) {
      parsedColumnFilters.forEach((filter) => {
        const { id: columnId, value: filterValue } = filter;
        if (filterValue) {
          // Map frontend field to database field
          const dbField = mapFrontendFieldToDb(columnId);
          if (dbField) {
            // Special handling for combined fields like clientNames
            if (columnId === 'clientNames') {
              // Search in both client1Name and client2Name
              query.$or = query.$or || [];
              query.$or.push(
                { 'customerInfo.client1Name': { $regex: filterValue, $options: 'i' } },
                { 'customerInfo.client2Name': { $regex: filterValue, $options: 'i' } }
              );
            } else if (columnId === 'branchName') {
              // Special handling for branch filter - filterValue is already a branch ID
              // Note: Branch access validation will be applied later to override this if needed
              query['preferences.branchId'] = filterValue;
            } else if (columnId === 'assignedUserName') {
              // Special handling for assigned user filter - filterValue is a user ID
              query['assigned_user_id'] = filterValue;
            } else if (columnId === 'partnerName') {
              // Special handling for partner filter - filterValue is a user ID
              query['partnerId'] = filterValue;
            } else if (columnId === 'visitDate') {
              // Special handling for visit date filter - compare dates without time
              // Extract just the date part from the ISO string (YYYY-MM-DD)
              const filterDateStr = (filterValue as string).split('T')[0]; // Get "2025-05-01" from "2025-05-01T04:00:00.000Z"

              // Create a regex to match the date part in stored values
              // This will match dates that start with the same YYYY-MM-DD pattern
              query['preferences.visitDate'] = {
                $regex: `^${filterDateStr}`,
                $options: 'i'
              };
            } else if (columnId === 'visitTime') {
              // Special handling for visit time filter - exact match on time value
              query['preferences.visitTime'] = filterValue;
            } else {
              // Standard field filtering
              query[dbField] = { $regex: filterValue, $options: 'i' };
            }
          }
        }
      });
    }

    // Apply global filter using dynamic searchable fields
    if (globalFilter) {
      const searchableFields = getSearchableFields(effectiveFilters.availableColumns || []);
      if (searchableFields.length > 0) {
        query.$or = searchableFields.map(field => ({
          [field]: { $regex: globalFilter, $options: 'i' }
        }));
      }
    }

    // Build sort options using dynamic field mapping
    let sortOptions: any = { createdAt: -1 }; // Default sort by creation date
    if (parsedSorting?.length) {
      const sort = parsedSorting[0];
      const { id, desc } = sort;
      sortOptions = {};

      // Get sortable fields mapping
      const sortableFields = getSortableFields(effectiveFilters.availableColumns || []);
      const dbField = sortableFields[id];

      if (dbField) {
        // Special handling for combined fields like clientNames
        if (id === 'clientNames') {
          // Sort by client1Name for combined client names
          sortOptions['customerInfo.client1Name'] = desc ? -1 : 1;
        } else {
          // Standard field sorting
          sortOptions[dbField] = desc ? -1 : 1;
        }
      } else {
        // Fallback to creation date
        sortOptions.createdAt = desc ? -1 : 1;
      }
    }

    // Apply branch access control AFTER all other filters to override any unauthorized attempts
    query = applyBranchAccessControl(query, effectiveFilters, userBranchIds);

    // Get total count for pagination
    const totalCount = await Reservation.countDocuments(query);

    // Get select fields based on available columns
    const selectFields = getSelectFields(effectiveFilters);

    // Fetch reservations with pagination and field selection
    let reservationQuery = Reservation.find(query)
      .sort(sortOptions)
      .skip(start)
      .limit(size);

    if (selectFields) {
      reservationQuery = reservationQuery.select(selectFields);
    }

    // Fetch reservations with populated appointment data and partner data for invitation sources
    const reservations = await reservationQuery
      .populate('appointmentId', 'date startHour capacity branchId')
      .populate('partnerId', 'name email')
      .lean();

    // Use the appointment data as returned by the API (nested appointmentId object)

    // Get unique branch IDs to fetch branch data
    const branchIds = Array.from(new Set(reservations.map((r: any) => r.preferences?.branchId).filter(Boolean)));
    const branches = await Branch.find({ _id: { $in: branchIds } }).select('_id name').lean();
    const branchMap = new Map(branches.map((b: any) => [b._id.toString(), b.name]));

    // Get metadata for the response
    const [availableStatuses, availableBranches, availableUsers] = await Promise.all([
      getAvailableStatuses(),
      getAvailableBranches(effectiveFilters.branchAccess, userBranchIds),
      getAvailableUsers()
    ]);

    // Create user map for quick lookups
    const userMap = new Map(availableUsers.map(user => [user.id, user.name]));

    // Transform data dynamically based on available columns
    const transformedData = transformReservationData(
      reservations,
      effectiveFilters.availableColumns || [],
      branchMap,
      userMap
    );

    // Get column definitions for frontend
    const { mainColumns, detailColumns } = getColumnDefinitions(effectiveFilters.availableColumns || []);

    return NextResponse.json({
      data: transformedData,
      meta: {
        totalRowCount: totalCount,
        availableColumns: effectiveFilters.availableColumns || [],
        availableStatuses,
        availableBranches,
        availableUsers,
        mainColumns,
        detailColumns
      },
    });

  } catch (error) {
    console.error('Error fetching reservations:', error);

    // Provide more detailed error information for debugging
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorDetails = {
      error: 'Failed to fetch reservations',
      message: errorMessage,
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(errorDetails, { status: 500 });
  }
}
