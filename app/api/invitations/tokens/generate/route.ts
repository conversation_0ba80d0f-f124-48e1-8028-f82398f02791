import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongodb';
import { generateTokenBatch, getTokenPoolStats, cleanupOldTokens, getComprehensiveTokenStats } from '@/app/invitations/utils/token-pregeneration';
import mongoose from 'mongoose';
import dbConnect from '@/lib/db';

// In-memory storage for batch progress tracking
const batchProgress = new Map<string, any>();

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to generate tokens' },
        { status: 401 }
      );
    }

  await dbConnect();

    const body = await req.json();

    // Validate required fields
    if (!body.count || body.count <= 0) {
      return NextResponse.json(
        { error: 'Count must be a positive number' },
        { status: 400 }
      );
    }

    if (body.count > 1000000) {
      return NextResponse.json(
        { error: 'Count cannot exceed 1,000,000 tokens per batch' },
        { status: 400 }
      );
    }

    const { count, groupId = null, batchSize = 1000 } = body;
    
    // Validate groupId if provided
    if (groupId && !mongoose.Types.ObjectId.isValid(groupId)) {
      return NextResponse.json(
        { error: 'Invalid group ID format' },
        { status: 400 }
      );
    }

    // Generate batch ID for tracking
    const batchId = new mongoose.Types.ObjectId().toString();

    // For large batches (>10000), process asynchronously
    if (count > 10000) {
      // Initialize progress tracking
      batchProgress.set(batchId, {
        batchId,
        total: count,
        generated: 0,
        failed: 0,
        status: 'running',
        startTime: new Date(),
        groupId
      });

      // Start background processing
      setImmediate(async () => {
        try {
          const result = await generateTokenBatch(count, groupId, batchSize);
          
          // Update progress with final result
          batchProgress.set(batchId, {
            ...batchProgress.get(batchId),
            ...result,
            status: result.status
          });
        } catch (error: any) {
          batchProgress.set(batchId, {
            ...batchProgress.get(batchId),
            status: 'error',
            error: error.message || 'Unknown error'
          });
        }
      });

      return NextResponse.json({
        success: true,
        batchId,
        message: 'Token generation started',
        total: count,
        groupId
      }, { status: 202 }); // 202 Accepted
    }

    // For smaller batches, process synchronously
    const result = await generateTokenBatch(count, groupId, batchSize);

    return NextResponse.json({
      success: true,
      batchId,
      ...result
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error generating token batch:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to generate token batch' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to view token statistics' },
        { status: 401 }
      );
    }

  await dbConnect();

    const { searchParams } = new URL(req.url);
    const batchId = searchParams.get('batchId');
    const groupId = searchParams.get('groupId');
    const action = searchParams.get('action');

    // Handle batch progress tracking
    if (batchId) {
      const progress = batchProgress.get(batchId);
      
      if (!progress) {
        return NextResponse.json(
          { error: 'Batch not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(progress);
    }

    // Handle token pool statistics
    if (action === 'stats') {
      if (groupId) {
        // For specific group, return simple stats
        const stats = await getTokenPoolStats(groupId);
        return NextResponse.json(stats);
      } else {
        // For global stats, return comprehensive breakdown
        const stats = await getComprehensiveTokenStats();
        return NextResponse.json(stats);
      }
    }

    // Handle cleanup action
    if (action === 'cleanup') {
      const daysOld = parseInt(searchParams.get('daysOld') || '30');
      const cleanedCount = await cleanupOldTokens(daysOld, groupId);
      
      return NextResponse.json({
        success: true,
        cleanedCount,
        daysOld,
        groupId
      });
    }

    // Default: return general statistics
    if (groupId) {
      const stats = await getTokenPoolStats(groupId);
      return NextResponse.json(stats);
    } else {
      const stats = await getComprehensiveTokenStats();
      return NextResponse.json(stats);
    }

  } catch (error: any) {
    console.error('Error fetching token information:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch token information' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to cleanup tokens' },
        { status: 401 }
      );
    }

  await dbConnect();

    const { searchParams } = new URL(req.url);
    const groupId = searchParams.get('groupId');
    const daysOld = parseInt(searchParams.get('daysOld') || '30');

    const cleanedCount = await cleanupOldTokens(daysOld, groupId);

    return NextResponse.json({
      success: true,
      cleanedCount,
      daysOld,
      groupId
    });

  } catch (error: any) {
    console.error('Error cleaning up tokens:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to cleanup tokens' },
      { status: 500 }
    );
  }
}
