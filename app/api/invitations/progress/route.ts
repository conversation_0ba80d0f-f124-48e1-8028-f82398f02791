import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Import the batchProgress map from the parent module
import { batchProgress, logBatchProgressState } from '../batch-progress';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to check invitation generation progress' },
        { status: 401 }
      );
    }
    
    // Get batchId from query parameter
    const searchParams = req.nextUrl.searchParams;
    const batchId = searchParams.get('batchId');
    
    if (!batchId) {
      return NextResponse.json(
        { error: 'Batch ID is required' },
        { status: 400 }
      );
    }
    
    // Log the current state of batch progress for debugging
    logBatchProgressState();
    
    // Check if batch exists
    if (!batchProgress.has(batchId)) {
      console.log(`Batch not found: ${batchId}`);
      
      return NextResponse.json(
        { error: 'Batch not found' },
        { status: 404 }
      );
    }
    
    // Get progress
    const progress = batchProgress.get(batchId)!;
    
    return NextResponse.json({
      batchId,
      ...progress,
      percentage: Math.round((progress.completed / progress.total) * 100)
    });
  } catch (error: any) {
    console.error('Error checking invitation generation progress:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to check invitation generation progress' },
      { status: 500 }
    );
  }
} 