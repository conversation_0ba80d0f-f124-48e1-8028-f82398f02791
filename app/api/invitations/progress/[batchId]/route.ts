import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// Import the batchProgress map from the parent module
// This is a reference to the same Map object
import { batchProgress, logBatchProgressState } from '../../batch-progress';

export async function GET(
  req: NextRequest,
  { params }: { params: { batchId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to check invitation generation progress' },
        { status: 401 }
      );
    }
    
    const { batchId } = params;
    
    
    // Check if batch exists
    if (!batchProgress.has(batchId)) {
      return NextResponse.json(
        { error: 'Batch not found' },
        { status: 404 }
      );
    }
    
    // Get progress
    const progress = batchProgress.get(batchId)!;
    
    return NextResponse.json({
      batchId,
      ...progress,
      percentage: Math.round((progress.completed / progress.total) * 100)
    });
  } catch (error: any) {
    console.error('Error checking invitation generation progress:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to check invitation generation progress' },
      { status: 500 }
    );
  }
} 