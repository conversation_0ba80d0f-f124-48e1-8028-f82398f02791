import { NextRequest, NextResponse } from 'next/server';
import Invitation from '@/models/Invitation';
import InvitationGroup from '@/models/InvitationGroup';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
import { connectToDatabase } from '@/lib/mongodb';
import { Partner } from '@/models/Partner';
import { consumePreGeneratedToken, generateTokensWithDocuments } from '@/app/invitations/utils/token-pregeneration';
import { batchProgress } from './batch-progress';
import dbConnect from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to view invitations' },
        { status: 401 }
      );
    }

  await dbConnect();

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const partner_id = searchParams.get('partner_id');
    const groupId = searchParams.get('groupId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const search = searchParams.get('search');

    // Build query object based on filters
    const query: any = {};

    if (partner_id && partner_id !== 'all') {
      query.partner_id = new mongoose.Types.ObjectId(partner_id);
    }

    if (groupId && groupId !== 'all') {
      query.groupId = new mongoose.Types.ObjectId(groupId);
    }

    // Date range filter
    if (startDate || endDate) {
      query.created_at = {};
      if (startDate) {
        query.created_at.$gte = new Date(startDate);
      }
      if (endDate) {
        query.created_at.$lte = new Date(endDate);
      }
    }

    // Search functionality using aggregation
    let invitations;
    let total;

    // Calculate pagination
    const skip = (page - 1) * limit;

    if (search) {
      // Use aggregation pipeline for search
      const pipeline = [
        // Look up partner details
        {
          $lookup: {
            from: 'users',
            localField: 'partner_id',
            foreignField: '_id',
            as: 'partner'
          }
        },
        // Unwind partner array (from lookup)
        {
          $unwind: {
            path: '$partner',
            preserveNullAndEmptyArrays: true
          }
        },
        // Match based on query conditions and search term
        {
          $match: {
            $and: [
              query, // Include our existing query filters
              {
                $or: [
                  { 'partner.name': { $regex: search, $options: 'i' } },
                  { 'partner.email': { $regex: search, $options: 'i' } }
                ]
              }
            ]
          }
        },
        // Count total results for pagination
        {
          $count: 'total'
        }
      ];

      const countResult = await Invitation.aggregate(pipeline);
      total = countResult.length > 0 ? countResult[0].total : 0;

      // Now get the actual results with pagination
      invitations = await Invitation.aggregate([
        // Look up partner details
        {
          $lookup: {
            from: 'users',
            localField: 'partner_id',
            foreignField: '_id',
            as: 'partner'
          }
        },
        // Unwind partner array
        {
          $unwind: {
            path: '$partner',
            preserveNullAndEmptyArrays: true
          }
        },
        // Match based on query conditions and search term
        {
          $match: {
            $and: [
              query,
              {
                $or: [
                  { 'partner.name': { $regex: search, $options: 'i' } },
                  { 'partner.email': { $regex: search, $options: 'i' } }
                ]
              }
            ]
          }
        },
        // Sort by created_at descending
        {
          $sort: { created_at: -1 }
        },
        // Apply pagination
        {
          $skip: skip
        },
        {
          $limit: limit
        },
        // Look up group information
        {
          $lookup: {
            from: 'invitationgroups',
            localField: 'groupId',
            foreignField: '_id',
            as: 'group'
          }
        },
        // Unwind group array (optional since groupId can be null)
        {
          $unwind: {
            path: '$group',
            preserveNullAndEmptyArrays: true
          }
        },
        // Restructure the output to match our normal format
        {
          $project: {
            _id: 1,
            partner_id: '$partner',
            reservation_id: 1,
            groupId: {
              $cond: {
                if: { $ne: ['$group', null] },
                then: {
                  _id: '$group._id',
                  name: '$group.name'
                },
                else: '$groupId'
              }
            },
            token: 1,
            created_at: 1,
            updated_at: 1
          }
        }
      ]);

      // Enhance with partner company information
      invitations = await Promise.all(invitations.map(async (invitation) => {
        if (invitation.partner_id && invitation.partner_id._id) {
          // Find partner where this user is an owner
          const partnerCompany = await Partner.findOne({
            owners: { $in: [invitation.partner_id._id] }
          }).select('name');

          // Add partner company name to the response
          invitation.partner_id = {
            ...invitation.partner_id,
            partnerCompany: partnerCompany ? partnerCompany.name : null
          };
        }
        return invitation;
      }));
    } else {
      // If no search term, use regular query with populate
      total = await Invitation.countDocuments(query);

      // Get invitations with basic populated fields
      invitations = await Invitation.find(query)
        .sort({ created_at: -1 })
        .skip(skip)
        .limit(limit)
        .populate('partner_id', 'name email')
        .populate('groupId', 'name')
        .lean();

      // Enhance with partner company information
      invitations = await Promise.all(invitations.map(async (invitation) => {
        if (invitation.partner_id && invitation.partner_id._id) {
          // Find partner where this user is an owner
          const partnerCompany = await Partner.findOne({
            owners: { $in: [invitation.partner_id._id] }
          }).select('name');
          // Add partner company name to the response
          invitation.partner_id = {
            ...invitation.partner_id,
            partnerCompany: partnerCompany ? partnerCompany.name : null
          };
        }
        return invitation;
      }));
    }

    return NextResponse.json({
      invitations,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    console.error('Error fetching invitations:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch invitations' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'You must be signed in to create an invitation' },
        { status: 401 }
      );
    }

  await dbConnect();

    const body = await req.json();

    // Validate required fields
    if (!body.partner_id) {
      return NextResponse.json(
        { error: 'Partner ID is required' },
        { status: 400 }
      );
    }

    // Validate that group name is provided (now required)
    if (!body.groupName || typeof body.groupName !== 'string' || !body.groupName.trim()) {
      return NextResponse.json(
        { error: 'Group name is required for all invitations' },
        { status: 400 }
      );
    }

    // Handle group creation (now always required)
    let groupId = null;
    let createdGroup = null;
    try {
      createdGroup = new InvitationGroup({
        name: body.groupName.trim(),
        partnerId: body.partner_id,
        invitationIds: [] // Will be populated after invitations are created
      });
      await createdGroup.save();
      groupId = createdGroup._id;
    } catch (error: any) {
      return NextResponse.json(
        { error: `Failed to create group: ${error.message}` },
        { status: 400 }
      );
    }

    // Handle mass invitation creation
    if (body.count && typeof body.count === 'number') {
      // Validate count limits
      if (body.count < 1) {
        return NextResponse.json(
          { error: 'Count must be at least 1' },
          { status: 400 }
        );
      }

      if (body.count > 1000000) {
        return NextResponse.json(
          { error: 'Count cannot exceed 1,000,000 invitations' },
          { status: 400 }
        );
      }

      // For large batches, create a progress tracker
      const batchId = `batch-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

      if (body.count > 100) {
        // Initialize progress tracker
        batchProgress.set(batchId, {
          total: body.count,
          completed: 0,
          status: 'pending'
        });


        // Start processing in the background
        setTimeout(async () => {
          try {
            // Update status to processing
            batchProgress.set(batchId, {
              ...batchProgress.get(batchId)!,
              status: 'processing'
            });

            // For large batches, process in chunks to avoid memory issues
            const BATCH_SIZE = 1000; // Process 1000 invitations at a time
            let totalCreated = 0;
            const allCreatedIds: any[] = []; // Track all created invitation IDs for group update

            // Calculate number of batches
            const totalBatches = Math.ceil(body.count / BATCH_SIZE);

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
              // Calculate how many invitations to create in this batch
              const currentBatchSize = Math.min(BATCH_SIZE, body.count - totalCreated);

              if (currentBatchSize <= 0) break;

              // Generate invitation IDs first
              const invitationIds = Array.from({ length: currentBatchSize }, () =>
                new mongoose.Types.ObjectId().toString()
              );

              // Generate tokens and ensure InvitationToken documents are created
              const uniqueTokens = await generateTokensWithDocuments(
                currentBatchSize,
                groupId?.toString() || null,
                invitationIds
              );

              // Create batch of invitations with unique tokens and pre-generated IDs
              const invitationBatch = uniqueTokens.map((token, index) => ({
                _id: new mongoose.Types.ObjectId(invitationIds[index]),
                partner_id: body.partner_id,
                reservation_id: null,
                groupId: groupId,
                token: token
              }));

              // Insert batch with error handling for potential duplicates
              let createdBatch;
              try {
                createdBatch = await Invitation.insertMany(invitationBatch, {
                  ordered: false, // Continue on duplicate key errors
                  rawResult: false
                });
              } catch (error: any) {
                // Handle duplicate key errors gracefully
                if (error.code === 11000) {
                  // Some tokens were duplicates, retry with new tokens for failed ones
                  const failedCount = currentBatchSize - (error.insertedDocs?.length || 0);
                  if (failedCount > 0) {
                    // Generate new invitation IDs for retry
                    const retryInvitationIds = Array.from({ length: failedCount }, () =>
                      new mongoose.Types.ObjectId().toString()
                    );

                    // Generate tokens with InvitationToken documents for retry
                    const retryTokens = await generateTokensWithDocuments(
                      failedCount,
                      groupId?.toString() || null,
                      retryInvitationIds
                    );

                    const retryBatch = retryTokens.map((token, index) => ({
                      _id: new mongoose.Types.ObjectId(retryInvitationIds[index]),
                      partner_id: body.partner_id,
                      reservation_id: null,
                      groupId: groupId,
                      token: token
                    }));
                    const retryResult = await Invitation.insertMany(retryBatch, {
                      ordered: false,
                      rawResult: false
                    });
                    createdBatch = [...(error.insertedDocs || []), ...retryResult];
                  } else {
                    createdBatch = error.insertedDocs || [];
                  }
                } else {
                  throw error;
                }
              }

              // Track created IDs for group update
              allCreatedIds.push(...createdBatch.map((inv:any) => inv._id));

              // Update totals
              totalCreated += createdBatch.length;

              // Update progress
              batchProgress.set(batchId, {
                ...batchProgress.get(batchId)!,
                completed: totalCreated
              });
            }

            // Update group with all invitation IDs if group was created
            if (createdGroup && allCreatedIds.length > 0) {
              await InvitationGroup.findByIdAndUpdate(
                createdGroup._id,
                { $push: { invitationIds: { $each: allCreatedIds } } }
              );
            }

            // Mark as completed
            batchProgress.set(batchId, {
              ...batchProgress.get(batchId)!,
              status: 'completed',
              completed: totalCreated
            });
          } catch (error: any) {
            // Mark as error
            batchProgress.set(batchId, {
              ...batchProgress.get(batchId)!,
              status: 'error',
              error: error.message || 'Unknown error'
            });
            console.error('Error in background invitation generation:', error);
          }
        }, 0);

        // Return immediately with batch ID for tracking
        return NextResponse.json({
          success: true,
          batchId,
          message: 'Invitation generation started',
          total: body.count
        }, { status: 202 }); // 202 Accepted
      }

      // For smaller batches, process synchronously
      // Use enhanced token consumption that handles fallback automatically
      const tokenResults: Array<{ id: mongoose.Types.ObjectId; token: string }> = [];

      for (let i = 0; i < body.count; i++) {
        const tempId = new mongoose.Types.ObjectId();
        const tokenResult = await consumePreGeneratedToken(
          groupId?.toString() || null,
          tempId.toString()
        );

        if (tokenResult.success && tokenResult.token) {
          tokenResults.push({ id: tempId, token: tokenResult.token });
        } else {
          throw new Error(`Failed to get token for invitation ${i + 1}: ${tokenResult.error}`);
        }
      }

      // Create array of invitations with tokens
      const invitationData = tokenResults.map(({ id, token }) => ({
        _id: id,
        partner_id: body.partner_id,
        reservation_id: null,
        groupId: groupId,
        token: token
      }));

      // Insert many invitations at once with error handling
      let createdInvitations;
      try {
        createdInvitations = await Invitation.insertMany(invitationData, {
          ordered: false // Continue on duplicate key errors
        });
      } catch (error: any) {
        // Handle duplicate key errors gracefully
        if (error.code === 11000) {
          // Some tokens were duplicates, use the successfully inserted ones
          createdInvitations = error.insertedDocs || [];

          // If we need more invitations, generate additional ones
          const failedCount = body.count - createdInvitations.length;
          if (failedCount > 0) {
            // Generate new invitation IDs for retry
            const retryInvitationIds = Array.from({ length: failedCount }, () =>
              new mongoose.Types.ObjectId().toString()
            );

            // Generate tokens with InvitationToken documents for retry
            const retryTokens = await generateTokensWithDocuments(
              failedCount,
              groupId?.toString() || null,
              retryInvitationIds
            );

            const retryData = retryTokens.map((token, index) => ({
              _id: new mongoose.Types.ObjectId(retryInvitationIds[index]),
              partner_id: body.partner_id,
              reservation_id: null,
              groupId: groupId,
              token: token
            }));
            const retryResult = await Invitation.insertMany(retryData, {
              ordered: false
            });
            createdInvitations = [...createdInvitations, ...retryResult];
          }
        } else {
          throw error;
        }
      }

      // Update group with invitation IDs if group was created
      if (createdGroup && createdInvitations.length > 0) {
        const invitationIds = createdInvitations.map((inv:any) => inv._id);
        await InvitationGroup.findByIdAndUpdate(
          createdGroup._id,
          { $push: { invitationIds: { $each: invitationIds } } }
        );
      }

      return NextResponse.json({
        success: true,
        count: createdInvitations.length,
        invitations: createdInvitations,
        group: createdGroup ? {
          _id: createdGroup._id,
          name: createdGroup.name,
          partnerId: createdGroup.partnerId,
          createdAt: createdGroup.createdAt
        } : null
      }, { status: 201 });
    } else {
      // Create a single invitation (legacy mode)
      // Use the enhanced token consumption that handles fallback automatically
      const tempInvitationId = new mongoose.Types.ObjectId();
      const tokenResult = await consumePreGeneratedToken(
        groupId?.toString() || null,
        tempInvitationId.toString()
      );

      if (!tokenResult.success || !tokenResult.token) {
        throw new Error(`Failed to get token: ${tokenResult.error}`);
      }

      const newInvitation = new Invitation({
        _id: tempInvitationId,
        partner_id: body.partner_id,
        reservation_id: body.reservation_id || null,
        groupId: groupId,
        token: tokenResult.token
      });

      await newInvitation.save();

      // Update group with invitation ID if group was created
      if (createdGroup) {
        await InvitationGroup.findByIdAndUpdate(
          createdGroup._id,
          { $push: { invitationIds: newInvitation._id } }
        );
      }

      return NextResponse.json({
        invitation: newInvitation,
        group: createdGroup ? {
          _id: createdGroup._id,
          name: createdGroup.name,
          partnerId: createdGroup.partnerId,
          createdAt: createdGroup.createdAt
        } : null
      }, { status: 201 });
    }
  } catch (error: any) {
    console.error('Error creating invitation(s):', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create invitation(s)' },
      { status: 500 }
    );
  }
}