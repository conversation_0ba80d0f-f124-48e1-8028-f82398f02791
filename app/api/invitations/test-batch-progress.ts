// Test script to verify batch-progress.ts functionality
import { batchProgress, logBatchProgressState } from './batch-progress';

// Generate a test batch ID
const testBatchId = `test-batch-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

console.log(`Creating test batch with ID: ${testBatchId}`);

// Add a test batch
batchProgress.set(testBatchId, {
  total: 100,
  completed: 0,
  status: 'pending'
});

// Log the state
console.log('After adding test batch:');
logBatchProgressState();

// Update the batch
batchProgress.set(testBatchId, {
  ...batchProgress.get(testBatchId)!,
  completed: 50,
  status: 'processing'
});

// Log the state again
console.log('After updating test batch:');
logBatchProgressState();

// Check if a batch exists
console.log(`Batch exists: ${batchProgress.has(testBatchId)}`);
console.log(`Non-existent batch exists: ${batchProgress.has('non-existent-batch')}`);

// Complete the batch
batchProgress.set(testBatchId, {
  ...batchProgress.get(testBatchId)!,
  completed: 100,
  status: 'completed'
});

// Log the final state
console.log('After completing test batch:');
logBatchProgressState();

// Export the test batch ID for use in other tests
export const exportedTestBatchId = testBatchId; 