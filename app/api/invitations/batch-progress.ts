// Store progress information for batch operations
export const batchProgress = new Map<string, {
  total: number;
  completed: number;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
}>();

// Debug utility to log the current state of the batch progress map
export function logBatchProgressState() {
  console.log('Current batch progress state:');
  console.log(`Total batches: ${batchProgress.size}`);
  
  if (batchProgress.size > 0) {
    console.log('Batch IDs:');
    Array.from(batchProgress.entries()).forEach(([key, value]) => {
      console.log(`- ${key}: ${value.status} (${value.completed}/${value.total})`);
    });
  } else {
    console.log('No active batches');
  }
}

// Clean up old progress entries periodically
setInterval(() => {
  const now = Date.now();
  // Use Array.from to avoid iterator issues
  Array.from(batchProgress.entries()).forEach(([key, value]) => {
    // Remove completed or error entries after 1 hour
    if ((value.status === 'completed' || value.status === 'error') && 
        value.completed === value.total && 
        now - parseInt(key.split('-')[1]) > 3600000) {
      batchProgress.delete(key);
    }
  });
}, 3600000); // Run every hour 