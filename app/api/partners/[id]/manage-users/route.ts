import { NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import { Partner } from '@/app/models/Partner';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

/**
 * GET endpoint to retrieve current owners and agents for a partner
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    // Authorize request
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();

    // Get partner with populated owners and agents
    const partner = await Partner.findById(params.id)
      .populate('owners', 'name email')
      .populate('agents', 'name email')
      .lean();

    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 });
    }

    return NextResponse.json({
      currentUsers: {
        owners: partner.owners || [],
        agents: partner.agents || []
      }
    });
  } catch (error) {
    console.error('Error fetching partner users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch partner users' },
      { status: 500 }
    );
  }
}

/**
 * PUT endpoint to update only owners and agents for a partner
 */
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  console.log('PUT /api/partners/[id]/manage-users started', params.id);
  
  try {
    // Authorize request
    const session = await getServerSession(authOptions);
    if (!session) {
      console.log('Unauthorized request to manage users');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await dbConnect();
    console.log('Database connected');

    let data;
    try {
      data = await request.json();
      console.log('Request data:', data);
    } catch (parseError) {
      console.error('Error parsing request JSON:', parseError);
      return NextResponse.json({ 
        error: 'Invalid JSON in request body' 
      }, { status: 400 });
    }
    
    const { owners, agents } = data;

    // Validate input
    if (!Array.isArray(owners) || !Array.isArray(agents)) {
      console.log('Invalid data format, owners or agents not arrays:', { owners, agents });
      return NextResponse.json({ 
        error: 'Invalid data format. Owners and agents must be arrays' 
      }, { status: 400 });
    }

    try {
      // Convert string IDs to ObjectIds
      const ownerIds = owners.map(id => new Types.ObjectId(id));
      const agentIds = agents.map(id => new Types.ObjectId(id));
      console.log('Converted IDs to ObjectIds');

      // Update ONLY the owners and agents fields
      console.log('Updating partner:', params.id);
      const updatedPartner = await Partner.findByIdAndUpdate(
        params.id,
        { 
          $set: { 
            owners: ownerIds,
            agents: agentIds,
            lastModified: new Date()
          } 
        },
        { new: true }
      )
      .populate('owners', 'name email')
      .populate('agents', 'name email')
      .lean();

      if (!updatedPartner) {
        console.log('Partner not found:', params.id);
        return NextResponse.json({ error: 'Partner not found' }, { status: 404 });
      }

      console.log('Partner updated successfully');
      return NextResponse.json({ 
        success: true,
        owners: updatedPartner.owners || [],
        agents: updatedPartner.agents || [] 
      });
    } catch (error: any) {
      console.error('MongoDB operation error:', error);
      
      // Handle invalid ObjectId errors
      if (error.name === 'CastError' && error.kind === 'ObjectId') {
        return NextResponse.json({ 
          error: `Invalid ID format: ${error.value}` 
        }, { status: 400 });
      }
      
      throw error; // Let the outer catch handle other errors
    }
  } catch (error: any) {
    console.error('Error updating partner users:', error);
    
    // Handle MongoDB validation errors
    if (error.name === 'ValidationError') {
      return NextResponse.json({ 
        error: 'Invalid user data provided' 
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      error: `Failed to update partner users: ${error.message || 'Unknown error'}` 
    }, { status: 500 });
  }
} 