import { NextResponse } from 'next/server';
import { isValidObjectId } from 'mongoose';
import dbConnect from '@/lib/db';
import Partner from '@/app/models/Partner';
import User from '@/app/models/User';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    
    if (!isValidObjectId(params.id)) {
      return NextResponse.json(
        { error: 'Invalid partner ID' },
        { status: 400 }
      );
    }

    const partner = await Partner.findById(params.id)
      .populate('agents', 'name email')
      .lean();

    if (!partner) {
      return NextResponse.json(
        { error: 'Partner not found' },
        { status: 404 }
      );
    }

    // Get eligible agents
    const agents = await User.find({ 
      roles: { $in: ['PartnerAgent'] } 
    })
      .select('name email')
      .lean();

    return NextResponse.json({
      users: agents,
      currentUsers: {
        agents: (partner as any).agents || []
      }
    });
  } catch (error) {
    console.error('GET partner agents error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch partner agents' },
      { status: 500 }
    );
  }
}


export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();
    
    if (!isValidObjectId(params.id)) {
      return NextResponse.json(
        { error: 'Invalid partner ID' },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Backward compatibility: support both { agents: [...] } and { agentId, phone }
    if (Array.isArray(body.agents)) {
      // Old format: { agents: [...] }
      const { agents } = body;

      // Validate that all user IDs exist
      const validUsers = await User.find({
        _id: { $in: agents },
        roles: { $in: ['PartnerAgent'] }
      }).select('_id').lean();

      if (validUsers.length !== agents.length) {
        return NextResponse.json(
          { error: 'One or more invalid agent IDs' },
          { status: 400 }
        );
      }

      const partner = await Partner.findByIdAndUpdate(
        params.id,
        { 
          agents,
          updatedAt: new Date()
        },
        { new: true }
      )
        .populate('agents', 'name email')
        .lean();

      if (!partner) {
        return NextResponse.json(
          { error: 'Partner not found' },
          { status: 404 }
        );
      }

      return NextResponse.json(partner);
    } else if (body.agentId) {
      // New format: { agentId, phone }
      const { agentId, phone } = body;
      if (!isValidObjectId(agentId)) {
        return NextResponse.json(
          { error: 'Invalid agentId' },
          { status: 400 }
        );
      }
      // Add agentId to the partner's agents array if not already present
      const partner = await Partner.findById(params.id);
      if (!partner) {
        return NextResponse.json(
          { error: 'Partner not found' },
          { status: 404 }
        );
      }
      // Only add if not already present
      if (Array.isArray(partner)) {
        return NextResponse.json(
          { error: 'Unexpected: partner is an array' },
          { status: 500 }
        );
      }
      const partnerDoc = partner as any;
      const agentIds = Array.isArray(partnerDoc.agents)
        ? partnerDoc.agents.map((a: any) => a.toString())
        : [];
      if (!agentIds.includes(agentId)) {
        partnerDoc.agents.push(agentId);
      }
      // Optionally update phone if needed (not specified in schema, but you can add logic here)
      partnerDoc.updatedAt = new Date();
      await partnerDoc.save();
      // Optionally update the agent's phone if provided
      if (phone) {
        await User.findByIdAndUpdate(agentId, { phone });
      }
      const updatedPartner = await Partner.findById(params.id)
        .populate('agents', 'name email phone')
        .lean();
      return NextResponse.json(updatedPartner);
    } else {
      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Update partner agents error:', error);
    return NextResponse.json(
      { error: 'Failed to update partner agents' },
      { status: 500 }
    );
  }
} 