import { NextResponse } from 'next/server';
import { Types } from 'mongoose';
import dbConnect from '@/lib/db';
import { Partner } from '@/app/models/Partner';
import User from '@/app/models/User';
import Role from '@/app/models/Role';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    // Get roles first
    const [partnerRole, agentRole] = await Promise.all([
      Role.findOne({ name: 'Partner' }).select('_id'),
      Role.findOne({ name: 'AgentPartner' }).select('_id')
    ]);

    if (!partnerRole || !agentRole) {
      return NextResponse.json({ error: 'Required roles not found' }, { status: 500 });
    }

    // Get all users with respective roles
    const [owners, agents] = await Promise.all([
      User.find({ roles: partnerRole._id, isActive: true }).select('name email'),
      User.find({ roles: agentRole._id, isActive: true }).select('name email')
    ]);

    // Get current partner assignments
    const currentPartner = await Partner.findById(params.id)
      .populate('owners agents', 'name email');

    if (!currentPartner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 });
    }

    return NextResponse.json({
      owners,
      agents,
      currentUsers: {
        owners: currentPartner.owners || [],
        agents: currentPartner.agents || []
      }
    });

  } catch (error) {
    console.error('GET partner users error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch partner users' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const body = await request.json();
    const { owners, agents } = body;

    if (!Array.isArray(owners) || !Array.isArray(agents)) {
      return NextResponse.json(
        { error: 'Invalid request format' },
        { status: 400 }
      );
    }

    // Validate that all user IDs exist and have correct roles
    const [validOwners, validAgents] = await Promise.all([
      User.find({
        _id: { $in: owners },
        roles: new Types.ObjectId("67cec8568f77ab38a0c94e61") // Partner role
      }).select('_id').lean(),
      User.find({
        _id: { $in: agents },
        roles: new Types.ObjectId("67cec8ce8f77ab38a0c94e6e") // AgentPartner role
      }).select('_id').lean()
    ]);

    if (validOwners.length !== owners.length || validAgents.length !== agents.length) {
      return NextResponse.json(
        { error: 'One or more invalid user IDs' },
        { status: 400 }
      );
    }

    const partner = await Partner.findByIdAndUpdate(
      params.id,
      { 
        $set: {
          owners,
          agents,
          updatedAt: new Date()
        }
      },
      { new: true }
    )
      .populate('owners', 'name email')
      .populate('agents', 'name email');

    if (!partner) {
      return NextResponse.json(
        { error: 'Partner not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(partner);
  } catch (error) {
    console.error('Update partner users error:', error);
    return NextResponse.json(
      { error: 'Failed to update partner users' },
      { status: 500 }
    );
  }
} 