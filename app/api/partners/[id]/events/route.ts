import { NextResponse } from 'next/server';
import dbConnect from '@/lib/db';
import { Event } from '@/models/Event';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';
// Import EventType model to make sure it's registered
import '@/models/EventType';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  await Promise.resolve(); // Ensure params are handled asynchronously
  
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const sortField = searchParams.get('sortField') || 'startDate';
    const sortDirection = searchParams.get('sortDirection') || 'desc';
    const skip = (page - 1) * limit;

    if (!mongoose.isValidObjectId(params.id)) {
      return NextResponse.json(
        { error: 'Invalid partner ID' },
        { status: 400 }
      );
    }

    // Build the filter object
    const filter: any = { partnerId: new mongoose.Types.ObjectId(params.id) };
    
    // Build sort object
    const sortObj: any = {};
    sortObj[sortField] = sortDirection === 'asc' ? 1 : -1;

    await dbConnect();
    
    // Set timeout for the query
    const queryTimeoutMs = 30000; // 30 seconds
    const query = Event.find(filter)
      .populate('branchId', 'name')
      .populate('eventTypeId', 'name')
      .sort(sortObj)
      .lean()
      .maxTimeMS(queryTimeoutMs)
      .skip(skip)
      .limit(limit);

    const [events, total] = await Promise.all([
      query,
      Event.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: events,
      total,
      page,
      totalPages,
      limit
    });

  } catch (error: any) {
    console.error('GET partner events error:', error);
    
    // Handle specific error types
    if (error.name === 'MongooseError' && error.message.includes('buffering timed out')) {
      return NextResponse.json(
        { error: 'Database connection timed out. Please try again.' },
        { status: 503 }
      );
    }

    if (error.name === 'MongoServerError') {
      return NextResponse.json(
        { error: 'Database server error. Please try again.' },
        { status: 503 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch partner events' },
      { status: 500 }
    );
  }
} 