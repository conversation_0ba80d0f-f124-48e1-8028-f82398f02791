import { NextRequest, NextResponse } from 'next/server';
import { InvoiceAutomationService } from '@/lib/services/invoice-automation';

/**
 * Development-only route to trigger Friday transition
 * GET /api/dev/trigger-friday-transition
 * 
 * This route is only available in development environment
 * and does not require authentication for easier testing.
 */

export async function GET(request: NextRequest) {
  // Only allow in development environment
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { 
        error: 'This endpoint is only available in development environment',
        environment: process.env.NODE_ENV 
      }, 
      { status: 403 }
    );
  }

  try {
    console.log('🔄 Development trigger: Friday transition started...');
    
    // Execute the Friday transition
    const result = await InvoiceAutomationService.fridayTransition();
    
    console.log('✅ Development trigger: Friday transition completed');
    console.log(`📊 Result: ${result.updatedCount}/${result.totalFound} invoices updated`);

    return NextResponse.json({
      success: true,
      message: `Friday transition completed successfully`,
      result: {
        action: 'friday',
        updatedCount: result.updatedCount,
        totalFound: result.totalFound,
        timestamp: new Date().toISOString(),
        environment: 'development',
        triggeredBy: 'dev-endpoint'
      },
      details: {
        description: 'Transitioned invoices from Processing (en_traitement) to Paid (paye)',
        condition: 'Only invoices in processing since before last Thursday 12pm',
        automatic: true
      }
    });

  } catch (error) {
    console.error('❌ Development trigger: Friday transition failed:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Friday transition failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      environment: 'development'
    }, { status: 500 });
  }
}

// Provide helpful information for other HTTP methods
export async function POST() {
  return NextResponse.json({
    error: 'Method not allowed. Use GET to trigger Friday transition.',
    availableMethods: ['GET'],
    usage: 'GET /api/dev/trigger-friday-transition'
  }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({
    error: 'Method not allowed. Use GET to trigger Friday transition.',
    availableMethods: ['GET'],
    usage: 'GET /api/dev/trigger-friday-transition'
  }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({
    error: 'Method not allowed. Use GET to trigger Friday transition.',
    availableMethods: ['GET'],
    usage: 'GET /api/dev/trigger-friday-transition'
  }, { status: 405 });
}
