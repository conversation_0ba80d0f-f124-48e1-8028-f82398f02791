'use client';

import React from 'react';
import { Toolt<PERSON>, TooltipContent, Toolt<PERSON><PERSON>rovider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/lib/contexts/language-context';

interface ReservationStats {
  totalCount: number;
  salesCount: number;
  presenceCount: number;
  confirmedCount: number;
  closingRate: number;
}

interface ReservationStatsTooltipProps {
  children: React.ReactNode;
  stats: ReservationStats | null;
  isLoading?: boolean;
}

export function ReservationStatsTooltip({ 
  children, 
  stats, 
  isLoading = false 
}: ReservationStatsTooltipProps) {
  const { t } = useLanguage();

  if (isLoading || !stats) {
    return <>{children}</>;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {children}
        </TooltipTrigger>
        <TooltipContent className="p-3 max-w-xs">
          <div className="space-y-2">
            <div className="font-semibold text-sm border-b pb-1">
              {t('reservations.statsTooltip.title', 'Statistiques détaillées')}
            </div>
            <div className="space-y-1.5 text-xs">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">
                  {t('reservations.statsTooltip.totalSales', 'Total ventes')}:
                </span>
                <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-green-100 text-green-800">
                  {stats.salesCount}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">
                  {t('reservations.statsTooltip.rendezvousAssis', 'Rendez-vous assis')}:
                </span>
                <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-blue-100 text-blue-800">
                  {stats.presenceCount}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">
                  {t('reservations.statsTooltip.totalConfirmed', 'Total confirmé')}:
                </span>
                <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-purple-100 text-purple-800">
                  {stats.confirmedCount}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">
                  {t('reservations.statsTooltip.closingRate', 'Taux de closing')}:
                </span>
                <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-orange-100 text-orange-800">
                  {stats.closingRate.toFixed(1)}%
                </Badge>
              </div>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Component for responsive badges that show/hide based on available space
interface ResponsiveStatsBadgesProps {
  stats: ReservationStats | null;
  isLoading?: boolean;
  className?: string;
}

export function ResponsiveStatsBadges({ 
  stats, 
  isLoading = false, 
  className = "" 
}: ResponsiveStatsBadgesProps) {
  const { t } = useLanguage();

  if (isLoading || !stats) {
    return null;
  }

  return (
    <TooltipProvider>
      <div className={`flex items-center gap-1 flex-shrink ${className}`}>
        {/* Sales badge - highest priority */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="secondary"
              className="text-xs px-1.5 py-0.5 bg-green-100 text-green-800 hidden sm:inline-flex cursor-help whitespace-nowrap flex-shrink-0"
            >
              {t('reservations.statsTooltip.salesBadge', 'Ventes')}: {stats.salesCount}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-sm">
              {t('reservations.statsTooltip.salesExplanation', 'Réservations avec statuts de vente')}
            </p>
          </TooltipContent>
        </Tooltip>

        {/* Presence badge - medium priority */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="secondary"
              className="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-800 hidden md:inline-flex cursor-help whitespace-nowrap flex-shrink-0"
            >
              {t('reservations.statsTooltip.presenceBadge', 'RDV Assis')}: {stats.presenceCount}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-sm">
              {t('reservations.statsTooltip.presenceExplanation', 'Réservations avec statuts de présence')}
            </p>
          </TooltipContent>
        </Tooltip>

        {/* Confirmed badge - lower priority */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="secondary"
              className="text-xs px-1.5 py-0.5 bg-purple-100 text-purple-800 hidden lg:inline-flex cursor-help whitespace-nowrap flex-shrink-0"
            >
              {t('reservations.statsTooltip.confirmedBadge', 'Confirmé')}: {stats.confirmedCount}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-sm">
              {t('reservations.statsTooltip.confirmedExplanation', 'Réservations avec statuts de présence + présent + absent')}
            </p>
          </TooltipContent>
        </Tooltip>

        {/* Closing rate badge - lowest priority */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="secondary"
              className="text-xs px-1.5 py-0.5 bg-orange-100 text-orange-800 hidden xl:inline-flex cursor-help whitespace-nowrap flex-shrink-0"
            >
              {t('reservations.statsTooltip.closingRateBadge', 'Closing')}: {stats.closingRate.toFixed(1)}%
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p className="text-sm">
              {t('reservations.statsTooltip.closingRateExplanation', 'Taux de vente par rapport aux RDV assis (Ventes ÷ RDV Assis)')}
            </p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
}
