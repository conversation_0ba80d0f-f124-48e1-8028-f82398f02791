'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Bug, Database, Activity, Mail, Settings, ExternalLink } from 'lucide-react';
import BranchStatsQuickTest from '@/components/debug/BranchStatsQuickTest';
import Link from 'next/link';

export default function DebugDashboard() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Bug className="h-8 w-8" />
            Debug Dashboard
          </h1>
          <p className="text-gray-600 mt-1">Development and testing tools for the AMQ Partners system</p>
        </div>
        <Badge variant="outline" className="text-sm">
          <Settings className="h-3 w-3 mr-1" />
          Development Mode
        </Badge>
      </div>

      <Tabs defaultValue="branch-stats" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="branch-stats">Branch Stats</TabsTrigger>
          <TabsTrigger value="email-system">Email System</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="api-tools">API Tools</TabsTrigger>
        </TabsList>

        <TabsContent value="branch-stats" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Quick Test Panel */}
            <div className="lg:col-span-1">
              <BranchStatsQuickTest />
            </div>

            {/* Navigation and Info */}
            <div className="lg:col-span-2 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Branch Stats Testing Tools
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Link href="/admin-dashboard/debug/branch-stats">
                      <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                        <Database className="h-6 w-6 mb-2" />
                        <span>Full Debug Console</span>
                        <ExternalLink className="h-3 w-3 mt-1" />
                      </Button>
                    </Link>

                    <Button 
                      variant="outline" 
                      className="w-full h-20 flex flex-col items-center justify-center"
                      onClick={() => window.open('/api/admin-dashboard/debug/branch-stats-model', '_blank')}
                    >
                      <Database className="h-6 w-6 mb-2" />
                      <span>Model API</span>
                      <ExternalLink className="h-3 w-3 mt-1" />
                    </Button>

                    <Button 
                      variant="outline" 
                      className="w-full h-20 flex flex-col items-center justify-center"
                      onClick={() => window.open('/api/admin-dashboard/debug/branch-stats-aggregation', '_blank')}
                    >
                      <Activity className="h-6 w-6 mb-2" />
                      <span>Aggregation API</span>
                      <ExternalLink className="h-3 w-3 mt-1" />
                    </Button>

                    <Link href="/admin-dashboard/stats-email-settings">
                      <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center">
                        <Mail className="h-6 w-6 mb-2" />
                        <span>Email Settings</span>
                        <ExternalLink className="h-3 w-3 mt-1" />
                      </Button>
                    </Link>
                  </div>

                  <div className="border-t pt-4">
                    <h4 className="font-semibold mb-2">Branch Stats Feature Status</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between items-center">
                        <span>Task 1: Database Schema & Models</span>
                        <Badge variant="default">✅ Completed</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Task 2: Aggregation Service</span>
                        <Badge variant="default">✅ Completed</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Task 3: Email Templates</span>
                        <Badge variant="default">✅ Completed</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Task 4: API Endpoints</span>
                        <Badge variant="default">✅ Completed</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Task 5: Frontend Interface</span>
                        <Badge variant="secondary">⏳ Pending</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Task 6: Email Service Integration</span>
                        <Badge variant="secondary">⏳ Pending</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Task 7: Cron Job Scheduler</span>
                        <Badge variant="secondary">⏳ Pending</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>API Endpoints</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <h5 className="font-semibold text-sm mb-2">Debug Endpoints</h5>
                      <div className="space-y-1 text-sm font-mono">
                        <div className="flex justify-between">
                          <span className="text-blue-600">GET</span>
                          <span>/api/admin-dashboard/debug/branch-stats-model</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-600">POST</span>
                          <span>/api/admin-dashboard/debug/branch-stats-aggregation</span>
                        </div>
                      </div>
                    </div>

                    <div className="border-t pt-3">
                      <h5 className="font-semibold text-sm mb-2">Production Endpoints (Task 4) ✅</h5>
                      <div className="space-y-1 text-sm font-mono">
                        <div className="flex justify-between">
                          <span className="text-blue-600">GET</span>
                          <span>/api/admin-dashboard/available-branches</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-600">POST</span>
                          <span>/api/admin-dashboard/branch-stats-data</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-600">POST</span>
                          <span>/api/admin-dashboard/branch-stats-preview</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-600">POST</span>
                          <span>/api/admin-dashboard/branch-stats-test-email</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-orange-600">PUT</span>
                          <span>/api/admin-dashboard/global-email-config</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-green-600">POST</span>
                          <span>/api/admin-dashboard/stats-email-settings</span>
                        </div>
                      </div>
                      <div className="mt-2 text-xs text-green-600">
                        ✅ All endpoints created and debug tools updated
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="email-system" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email System Debug Tools
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Link href="/admin-dashboard/stats-email-settings">
                  <Button variant="outline" className="w-full h-16 flex flex-col items-center justify-center">
                    <Settings className="h-5 w-5 mb-1" />
                    <span>Email Settings</span>
                  </Button>
                </Link>

                <Link href="/admin-dashboard/global-email-config">
                  <Button variant="outline" className="w-full h-16 flex flex-col items-center justify-center">
                    <Settings className="h-5 w-5 mb-1" />
                    <span>Global Config</span>
                  </Button>
                </Link>

                <Button 
                  variant="outline" 
                  className="w-full h-16 flex flex-col items-center justify-center"
                  onClick={() => window.open('/api/admin-dashboard/debug/stats-email-service/logs', '_blank')}
                >
                  <Activity className="h-5 w-5 mb-1" />
                  <span>Service Logs</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="database" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Debug Tools
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button 
                  variant="outline" 
                  className="w-full h-16 flex flex-col items-center justify-center"
                  onClick={() => {
                    const confirmed = confirm('This will run the branch stats migration. Continue?');
                    if (confirmed) {
                      // You could add a migration runner here
                      alert('Migration functionality would be implemented here');
                    }
                  }}
                >
                  <Database className="h-5 w-5 mb-1" />
                  <span>Run Migration</span>
                </Button>

                <Button 
                  variant="outline" 
                  className="w-full h-16 flex flex-col items-center justify-center"
                  onClick={() => window.open('/api/admin-dashboard/debug/branch-stats-model', '_blank')}
                >
                  <Activity className="h-5 w-5 mb-1" />
                  <span>Test Models</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api-tools" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                API Testing Tools
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => window.open('/api/branches', '_blank')}
                  >
                    Test Branches API
                  </Button>

                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => window.open('/api/admin-dashboard/global-email-config', '_blank')}
                  >
                    Test Global Config API
                  </Button>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-semibold mb-2">Quick API Tests</h4>
                  <div className="text-sm text-gray-600">
                    Use the browser developer tools to inspect API responses and test authentication.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
