'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, CheckCircle, Clock, Database, Activity } from 'lucide-react';

interface TestResult {
  success: boolean;
  data?: any;
  error?: string;
  timestamp: string;
  executionTime?: number;
}

interface Branch {
  value: string;
  label: string;
  name: string;
  city: string;
  province: string;
}

export default function BranchStatsDebugPage() {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedBranches, setSelectedBranches] = useState<string[]>([]);
  const [targetDate, setTargetDate] = useState(new Date().toISOString().split('T')[0]);
  const [sendingHour, setSendingHour] = useState(14);
  const [selectedStats, setSelectedStats] = useState<string[]>(['reservationCount', 'adultCount', 'childCount', 'allergies', 'serviceTypes']);
  const [testResults, setTestResults] = useState<{ [key: string]: TestResult }>({});
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});
  const [testEmailAddress, setTestEmailAddress] = useState('');

  // Load branches on component mount
  React.useEffect(() => {
    loadBranches();
  }, []);

  const loadBranches = async () => {
    try {
      const response = await fetch('/api/admin-dashboard/available-branches');
      if (response.ok) {
        const data = await response.json();
        setBranches(data.branches || []);
        // Select first 3 branches by default
        if (data.branches?.length > 0) {
          setSelectedBranches(data.branches.slice(0, 3).map((b: any) => b.value));
        }
      }
    } catch (error) {
      console.error('Failed to load branches:', error);
    }
  };

  const runHtmlPreviewTest = async () => {
    if (selectedBranches.length === 0) {
      alert('Please select at least one branch first');
      return;
    }

    setLoading(prev => ({ ...prev, 'email-preview-html': true }));
    const startTime = Date.now();

    try {
      const response = await fetch('/api/admin-dashboard/branch-stats-preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          branchIds: selectedBranches,
          targetDate,
          selectedStats,
          sendingHour
        })
      });

      const data = await response.json();
      const executionTime = Date.now() - startTime;

      if (response.ok && data.success) {
        // Store in test results for consistency
        setTestResults(prev => ({
          ...prev,
          'email-preview-html': {
            success: true,
            data: data,
            timestamp: new Date().toISOString(),
            executionTime
          }
        }));

        // Open HTML in new window
        const newWindow = window.open('', '_blank');
        if (newWindow) {
          newWindow.document.body.innerHTML = data.preview.html;
          newWindow.document.title = 'Branch Stats Email Preview';
        }
      } else {
        setTestResults(prev => ({
          ...prev,
          'email-preview-html': {
            success: false,
            error: data.error || 'Failed to generate preview',
            timestamp: new Date().toISOString(),
            executionTime
          }
        }));
      }
    } catch (error) {
      const executionTime = Date.now() - startTime;
      setTestResults(prev => ({
        ...prev,
        'email-preview-html': {
          success: false,
          error: error instanceof Error ? error.message : 'Network error',
          timestamp: new Date().toISOString(),
          executionTime
        }
      }));
    } finally {
      setLoading(prev => ({ ...prev, 'email-preview-html': false }));
    }
  };

  const runTest = async (testType: string, endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', payload?: any) => {
    setLoading(prev => ({ ...prev, [testType]: true }));
    const startTime = Date.now();

    try {
      const url = endpoint.includes('?') ? endpoint : `${endpoint}?timestamp=${Date.now()}`;
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (method === 'POST' && payload) {
        options.body = JSON.stringify(payload);
      }

      const response = await fetch(url, options);
      const data = await response.json();
      const executionTime = Date.now() - startTime;

      setTestResults(prev => ({
        ...prev,
        [testType]: {
          success: response.ok,
          data: response.ok ? data : undefined,
          error: response.ok ? undefined : data.error || 'Unknown error',
          timestamp: new Date().toISOString(),
          executionTime
        }
      }));
    } catch (error) {
      const executionTime = Date.now() - startTime;
      setTestResults(prev => ({
        ...prev,
        [testType]: {
          success: false,
          error: error instanceof Error ? error.message : 'Network error',
          timestamp: new Date().toISOString(),
          executionTime
        }
      }));
    } finally {
      setLoading(prev => ({ ...prev, [testType]: false }));
    }
  };

  const handleStatsChange = (stat: string, checked: boolean) => {
    if (checked) {
      setSelectedStats(prev => [...prev, stat]);
    } else {
      setSelectedStats(prev => prev.filter(s => s !== stat));
    }
  };

  const handleBranchChange = (branchId: string, checked: boolean) => {
    if (checked) {
      setSelectedBranches(prev => [...prev, branchId]);
    } else {
      setSelectedBranches(prev => prev.filter(id => id !== branchId));
    }
  };

  const formatJson = (obj: any) => {
    return JSON.stringify(obj, null, 2);
  };

  const getStatusIcon = (result: TestResult) => {
    if (result.success) {
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    } else {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Branch Stats Debug Console</h1>
        <Badge variant="outline" className="text-sm">
          <Activity className="h-3 w-3 mr-1" />
          Debug Mode
        </Badge>
      </div>

      <Tabs defaultValue="api" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="api">API Endpoints</TabsTrigger>
          <TabsTrigger value="model">Model Tests</TabsTrigger>
          <TabsTrigger value="aggregation">Aggregation Tests</TabsTrigger>
          <TabsTrigger value="email">Email Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="api" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                API Endpoints Testing
              </CardTitle>
              <div className="text-sm text-green-600">
                ✅ Updated to use actual production endpoints (not debug endpoints)
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Available Branches API */}
              <div className="border rounded-lg p-4 space-y-4">
                <h4 className="font-semibold">Available Branches API</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    onClick={() => runTest('available-branches', '/api/admin-dashboard/available-branches')}
                    disabled={loading['available-branches']}
                    className="w-full"
                  >
                    {loading['available-branches'] ? 'Testing...' : 'Test Available Branches'}
                  </Button>
                  <div className="text-sm text-gray-600">
                    Tests the branch selection API for frontend dropdowns
                  </div>
                </div>
              </div>

              {/* Branch Stats Data API */}
              <div className="border rounded-lg p-4 space-y-4">
                <h4 className="font-semibold">Branch Stats Data API</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    onClick={() => runTest('branch-stats-data', '/api/admin-dashboard/branch-stats-data', 'POST', {
                      branchIds: selectedBranches,
                      targetDate,
                      selectedStats
                    })}
                    disabled={loading['branch-stats-data'] || selectedBranches.length === 0}
                    className="w-full"
                  >
                    {loading['branch-stats-data'] ? 'Testing...' : 'Test Branch Stats Data'}
                  </Button>
                  <div className="text-sm text-gray-600">
                    Tests data aggregation for selected branches and date
                  </div>
                </div>
              </div>

              {/* Branch Stats Preview API */}
              <div className="border rounded-lg p-4 space-y-4">
                <h4 className="font-semibold">Branch Stats Preview API</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    onClick={() => runTest('branch-stats-preview', '/api/admin-dashboard/branch-stats-preview', 'POST', {
                      branchIds: selectedBranches,
                      targetDate,
                      selectedStats,
                      sendingHour
                    })}
                    disabled={loading['branch-stats-preview'] || selectedBranches.length === 0}
                    className="w-full"
                  >
                    {loading['branch-stats-preview'] ? 'Testing...' : 'Test Email Preview'}
                  </Button>
                  <div className="text-sm text-gray-600">
                    Tests email template generation and preview
                  </div>
                </div>
              </div>

              {/* Test Email API */}
              <div className="border rounded-lg p-4 space-y-4">
                <h4 className="font-semibold">Test Email API</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="test-email-address">Test Email Address</Label>
                    <Input
                      id="test-email-address"
                      type="email"
                      placeholder="<EMAIL>"
                      value={testEmailAddress}
                      onChange={(e) => setTestEmailAddress(e.target.value)}
                    />
                  </div>
                  <Button
                    onClick={() => {
                      if (!testEmailAddress) {
                        alert('Please enter an email address');
                        return;
                      }
                      runTest('branch-stats-test-email', '/api/admin-dashboard/branch-stats-test-email', 'POST', {
                        email: testEmailAddress,
                        branchIds: selectedBranches,
                        targetDate,
                        selectedStats,
                        sendingHour
                      });
                    }}
                    disabled={loading['branch-stats-test-email'] || selectedBranches.length === 0}
                    className="w-full mt-6"
                  >
                    {loading['branch-stats-test-email'] ? 'Sending...' : 'Send Test Email'}
                  </Button>
                </div>
                <div className="text-sm text-yellow-600">
                  ⚠️ This will send an actual email to the specified address
                </div>
              </div>

              {/* Global Config API */}
              <div className="border rounded-lg p-4 space-y-4">
                <h4 className="font-semibold">Global Configuration API</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    onClick={() => runTest('global-config-get', '/api/admin-dashboard/global-email-config')}
                    disabled={loading['global-config-get']}
                    variant="outline"
                    className="w-full"
                  >
                    {loading['global-config-get'] ? 'Testing...' : 'Get Config'}
                  </Button>
                  <Button
                    onClick={() => runTest('global-config-update', '/api/admin-dashboard/global-email-config', 'PUT', {
                      branchStatsEnabled: true,
                      branchStatsHour: sendingHour,
                      branchStatsMinute: 0,
                      branchStatsTimezone: 'America/Toronto'
                    })}
                    disabled={loading['global-config-update']}
                    variant="outline"
                    className="w-full"
                  >
                    {loading['global-config-update'] ? 'Testing...' : 'Update Branch Stats Config'}
                  </Button>
                  <div className="text-sm text-gray-600">
                    Tests configuration management for branch stats
                  </div>
                </div>
              </div>

              {/* API Test Summary */}
              {Object.keys(testResults).some(key => key.includes('branch-stats') || key.includes('available-branches') || key.includes('global-config')) && (
                <div className="border rounded-lg p-4 space-y-4 bg-gray-50">
                  <h4 className="font-semibold">API Test Results Summary</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {['available-branches', 'branch-stats-data', 'branch-stats-preview', 'branch-stats-test-email', 'global-config-get', 'global-config-update'].map(testKey => {
                      const result = testResults[testKey];
                      if (!result) return null;

                      return (
                        <div key={testKey} className="bg-white border rounded p-3">
                          <div className="flex items-center gap-2 mb-2">
                            {result.success ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <AlertCircle className="h-4 w-4 text-red-500" />
                            )}
                            <span className="font-medium text-sm">{testKey.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                          </div>
                          <div className="text-xs text-gray-600">
                            {result.executionTime && `${result.executionTime}ms`}
                            {result.success && result.data?.count && ` • ${result.data.count} items`}
                            {result.success && result.data?.branchCount && ` • ${result.data.branchCount} branches`}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="model" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Branch Stats Model Tests
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button
                  onClick={() => runTest('schema-fields', '/api/admin-dashboard/debug/branch-stats-model')}
                  disabled={loading['schema-fields']}
                  className="w-full"
                >
                  {loading['schema-fields'] ? 'Testing...' : 'Test Schema Fields'}
                </Button>

                <Button
                  onClick={() => runTest('global-config', '/api/admin-dashboard/debug/branch-stats-model')}
                  disabled={loading['global-config']}
                  variant="outline"
                  className="w-full"
                >
                  {loading['global-config'] ? 'Testing...' : 'Test Global Config'}
                </Button>

                <Button
                  onClick={() => runTest('instance-methods', '/api/admin-dashboard/debug/branch-stats-model')}
                  disabled={loading['instance-methods']}
                  variant="outline"
                  className="w-full"
                >
                  {loading['instance-methods'] ? 'Testing...' : 'Test Methods'}
                </Button>

                <Button
                  onClick={() => runTest('static-methods', '/api/admin-dashboard/debug/branch-stats-model')}
                  disabled={loading['static-methods']}
                  variant="outline"
                  className="w-full"
                >
                  {loading['static-methods'] ? 'Testing...' : 'Test Queries'}
                </Button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="test-email">Test Email Address</Label>
                    <Input
                      id="test-email"
                      placeholder="<EMAIL>"
                      defaultValue="<EMAIL>"
                    />
                  </div>
                  <div className="flex items-end">
                    <Button
                      onClick={() => {
                        const email = (document.getElementById('test-email') as HTMLInputElement)?.value;
                        if (email) {
                          runTest('create-test-setting', '/api/admin-dashboard/debug/branch-stats-model', 'POST', {
                            email,
                            branchStatsEnabled: true,
                            selectedBranches: selectedBranches.slice(0, 2),
                            branchStatsSelectedStats: {
                              reservationCount: true,
                              adultCount: true,
                              childCount: true,
                              allergies: true,
                              serviceTypes: true
                            },
                            regularStatsEnabled: true
                          });
                        }
                      }}
                      disabled={loading['create-test-setting']}
                      className="w-full"
                    >
                      {loading['create-test-setting'] ? 'Creating...' : 'Create Test Setting'}
                    </Button>
                  </div>
                </div>

                <Button
                  onClick={() => runTest('cleanup-test-data', '/api/admin-dashboard/debug/branch-stats-model', 'DELETE')}
                  disabled={loading['cleanup-test-data']}
                  variant="destructive"
                  className="w-full"
                >
                  {loading['cleanup-test-data'] ? 'Cleaning...' : 'Cleanup Test Data'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="aggregation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Branch Stats Aggregation Tests
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Configuration Section */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="target-date">Target Date</Label>
                  <Input
                    id="target-date"
                    type="date"
                    value={targetDate}
                    onChange={(e) => setTargetDate(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="sending-hour">Sending Hour (0-23)</Label>
                  <Input
                    id="sending-hour"
                    type="number"
                    min="0"
                    max="23"
                    value={sendingHour}
                    onChange={(e) => setSendingHour(parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <Label>Calculated Target Date</Label>
                  <div className="p-2 bg-gray-100 rounded text-sm">
                    {sendingHour < 12 ? 'Current day' : 'Next day'} logic
                  </div>
                </div>
              </div>

              {/* Branch Selection */}
              <div>
                <Label>Selected Branches ({selectedBranches.length})</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2 max-h-40 overflow-y-auto">
                  {branches.map((branch) => (
                    <div key={branch.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={`branch-${branch.value}`}
                        checked={selectedBranches.includes(branch.value)}
                        onCheckedChange={(checked) => handleBranchChange(branch.value, checked as boolean)}
                      />
                      <Label htmlFor={`branch-${branch.value}`} className="text-sm truncate">
                        {branch.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Stats Selection */}
              <div>
                <Label>Selected Statistics</Label>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-2 mt-2">
                  {['reservationCount', 'adultCount', 'childCount', 'allergies', 'serviceTypes'].map((stat) => (
                    <div key={stat} className="flex items-center space-x-2">
                      <Checkbox
                        id={`stat-${stat}`}
                        checked={selectedStats.includes(stat)}
                        onCheckedChange={(checked) => handleStatsChange(stat, checked as boolean)}
                      />
                      <Label htmlFor={`stat-${stat}`} className="text-sm">
                        {stat}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Test Buttons */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <Button
                  onClick={() => runTest('date-calculation', `/api/admin-dashboard/debug/branch-stats-aggregation?testType=dateCalculation&sendingHour=${sendingHour}`)}
                  disabled={loading['date-calculation']}
                  variant="outline"
                >
                  {loading['date-calculation'] ? 'Testing...' : 'Date Calculation'}
                </Button>

                <Button
                  onClick={() => {
                    if (selectedBranches.length === 0) {
                      alert('Please select at least one branch');
                      return;
                    }
                    runTest('demographics', `/api/admin-dashboard/debug/branch-stats-aggregation?testType=demographics&branchId=${selectedBranches[0]}&targetDate=${targetDate}`);
                  }}
                  disabled={loading['demographics'] || selectedBranches.length === 0}
                  variant="outline"
                >
                  {loading['demographics'] ? 'Testing...' : 'Demographics'}
                </Button>

                <Button
                  onClick={() => {
                    if (selectedBranches.length === 0) {
                      alert('Please select at least one branch');
                      return;
                    }
                    runTest('allergies', `/api/admin-dashboard/debug/branch-stats-aggregation?testType=allergies&branchId=${selectedBranches[0]}&targetDate=${targetDate}`);
                  }}
                  disabled={loading['allergies'] || selectedBranches.length === 0}
                  variant="outline"
                >
                  {loading['allergies'] ? 'Testing...' : 'Allergies'}
                </Button>

                <Button
                  onClick={() => {
                    if (selectedBranches.length === 0) {
                      alert('Please select at least one branch');
                      return;
                    }
                    runTest('service-types', `/api/admin-dashboard/debug/branch-stats-aggregation?testType=serviceTypes&branchId=${selectedBranches[0]}&targetDate=${targetDate}`);
                  }}
                  disabled={loading['service-types'] || selectedBranches.length === 0}
                  variant="outline"
                >
                  {loading['service-types'] ? 'Testing...' : 'Service Types'}
                </Button>

                <Button
                  onClick={() => {
                    if (selectedBranches.length === 0) {
                      alert('Please select at least one branch');
                      return;
                    }
                    runTest('full-aggregation', `/api/admin-dashboard/debug/branch-stats-aggregation?testType=fullAggregation&branchId=${selectedBranches[0]}&targetDate=${targetDate}`);
                  }}
                  disabled={loading['full-aggregation'] || selectedBranches.length === 0}
                >
                  {loading['full-aggregation'] ? 'Testing...' : 'Full Aggregation'}
                </Button>

                <Button
                  onClick={() => {
                    runTest('performance', `/api/admin-dashboard/debug/branch-stats-aggregation?testType=performance&targetDate=${targetDate}`);
                  }}
                  disabled={loading['performance']}
                  variant="secondary"
                >
                  {loading['performance'] ? 'Testing...' : 'Performance Test'}
                </Button>
              </div>

              {/* Custom Test */}
              <div className="border-t pt-4">
                <Label>Custom Aggregation Test</Label>
                <Button
                  onClick={() => {
                    if (selectedBranches.length === 0) {
                      alert('Please select at least one branch');
                      return;
                    }
                    runTest('custom-aggregation', '/api/admin-dashboard/debug/branch-stats-aggregation', 'POST', {
                      branchIds: selectedBranches,
                      targetDate,
                      sendingHour,
                      selectedStats,
                      testType: 'fullAggregation'
                    });
                  }}
                  disabled={loading['custom-aggregation'] || selectedBranches.length === 0}
                  className="w-full mt-2"
                >
                  {loading['custom-aggregation'] ? 'Running...' : 'Run Custom Test'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="email" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📧 Email Template Tests
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">

                {/* Template Options */}
                <div className="space-y-4">
                  <h4 className="font-medium">Template Options</h4>

                  <div className="space-y-2">
                    <Label className="flex items-center space-x-2">
                      <Checkbox
                        checked={true}
                        disabled
                      />
                      <span>Include Detailed Reservations</span>
                    </Label>

                    <Label className="flex items-center space-x-2">
                      <Checkbox
                        checked={true}
                        disabled
                      />
                      <span>Include Allergies Breakdown</span>
                    </Label>

                    <Label className="flex items-center space-x-2">
                      <Checkbox
                        checked={true}
                        disabled
                      />
                      <span>Include Service Types</span>
                    </Label>

                    <Label className="flex items-center space-x-2">
                      <Checkbox
                        checked={false}
                        disabled
                      />
                      <span>Compact Mode</span>
                    </Label>
                  </div>
                </div>

                {/* Test Actions */}
                <div className="space-y-4">
                  <h4 className="font-medium">Template Tests</h4>

                  <div className="space-y-2">
                    <Button
                      onClick={() => {
                        if (selectedBranches.length === 0) {
                          alert('Please select at least one branch first');
                          return;
                        }
                        runTest('email-preview-sample', '/api/admin-dashboard/branch-stats-preview', 'POST', {
                          branchIds: selectedBranches,
                          targetDate,
                          selectedStats,
                          sendingHour
                        });
                      }}
                      disabled={loading['email-preview-sample'] || selectedBranches.length === 0}
                      className="w-full"
                    >
                      {loading['email-preview-sample'] ? 'Generating...' : 'Generate Sample Email'}
                    </Button>

                    <Button
                      onClick={runHtmlPreviewTest}
                      disabled={loading['email-preview-html'] || selectedBranches.length === 0}
                      variant="outline"
                      className="w-full"
                    >
                      {loading['email-preview-html'] ? 'Generating...' : 'Preview HTML Email (Opens in New Window)'}
                    </Button>

                    <Button
                      onClick={() => runTest('email-configurations', '/api/admin-dashboard/debug/branch-stats-email-template', 'PUT', { testType: 'all-configurations' })}
                      disabled={loading['email-configurations']}
                      variant="outline"
                      className="w-full"
                    >
                      {loading['email-configurations'] ? 'Testing...' : 'Test All Configurations'}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">Email Template Features</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                  <Badge variant="outline">📱 Mobile Responsive</Badge>
                  <Badge variant="outline">🖨️ Print Friendly</Badge>
                  <Badge variant="outline">🎨 Company Branding</Badge>
                  <Badge variant="outline">📊 Export Links</Badge>
                  <Badge variant="outline">🚫 Allergies Highlighting</Badge>
                  <Badge variant="outline">🍽️ Service Types</Badge>
                  <Badge variant="outline">👥 Demographics</Badge>
                  <Badge variant="outline">📋 Detailed Tables</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Results Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Test Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          {Object.keys(testResults).length === 0 ? (
            <p className="text-gray-500 text-center py-8">No tests run yet. Click a test button above to get started.</p>
          ) : (
            <div className="space-y-4">
              {Object.entries(testResults).map(([testType, result]) => (
                <div key={testType} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result)}
                      <h3 className="font-semibold capitalize">{testType.replace('-', ' ')}</h3>
                      <Badge variant={result.success ? 'default' : 'destructive'}>
                        {result.success ? 'Success' : 'Failed'}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-500">
                      {result.executionTime}ms • {new Date(result.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                  
                  {result.error && (
                    <div className="bg-red-50 border border-red-200 rounded p-3 mb-3">
                      <p className="text-red-700 text-sm">{result.error}</p>
                    </div>
                  )}
                  
                  {result.data && (
                    <Textarea
                      value={formatJson(result.data)}
                      readOnly
                      className="font-mono text-xs"
                      rows={Math.min(20, formatJson(result.data).split('\n').length)}
                    />
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
