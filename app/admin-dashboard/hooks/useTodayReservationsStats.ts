import useSWR from 'swr';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

export interface TodayReservationsStats {
  todayReservations: number;
  loading: boolean;
  error: string | null;
  refresh: () => void;
}

// Helper function to build API URL for branch parameters
const buildApiUrl = (branchId: string | string[] | null, baseUrl: string) => {
  if (!branchId) return null;

  if (Array.isArray(branchId)) {
    if (branchId.length === 0) return null;
    return `${baseUrl}?branchIds=${branchId.join(',')}`;
  }

  return `${baseUrl}?branchId=${branchId}`;
};

export function useTodayReservationsStats(
  branchId: string | string[] | null
): TodayReservationsStats {
  const url = buildApiUrl(branchId, '/api/admin-dashboard/today-reservations');
  const { data, error, isValidating, mutate } = useSWR(url, dashboardFetcher, { revalidateOnFocus: false });

  return {
    todayReservations: data?.todayReservations ?? 0,
    loading: !data && !error,
    error: error ? error.message : null,
    refresh: mutate,
  };
}