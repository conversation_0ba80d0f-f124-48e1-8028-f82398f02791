import useSWR from 'swr';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

export interface SalesByTimeslotStat {
  dayIndex: number;
  dayName: string;
  dayNameFr: string;
  timeslots: {
    [timeSlot: string]: number | { [branchId: string]: number };
  };
}

export interface SalesByTimeslotTotals {
  totalSales: number;
  totalAmount: number;
}

interface SalesByTimeslotResponse {
  stats: SalesByTimeslotStat[];
  totals: SalesByTimeslotTotals;
  debug?: any;
}

function buildApiUrl(branchId: string | string[] | null, baseUrl: string): string | null {
  if (!branchId) return null;
  
  if (branchId === 'all') {
    return `${baseUrl}?allBranches=true`;
  }
  
  if (Array.isArray(branchId)) {
    if (branchId.length === 0) return null;
    // For multiple branches, we need allBranches=true to get per-branch data
    return `${baseUrl}?branchIds=${branchId.join(',')}&allBranches=true`;
  }
  
  return `${baseUrl}?branchId=${branchId}`;
}

export function useSalesByTimeslot(
  branchId: string | string[] | null,
  startDate?: string,
  endDate?: string
) {
  const baseUrl = buildApiUrl(branchId, '/api/admin-dashboard/sales-by-timeslot');
  
  let url = baseUrl;
  if (url && startDate && endDate) {
    const separator = url.includes('?') ? '&' : '?';
    url = `${url}${separator}startDate=${startDate}&endDate=${endDate}`;
  }

  const { data, error, isValidating, mutate } = useSWR<SalesByTimeslotResponse>(
    url,
    dashboardFetcher,
    {
      revalidateOnFocus: false,
      shouldRetryOnError: false,
    }
  );

  return {
    stats: data?.stats || [],
    totals: data?.totals || { totalSales: 0, totalAmount: 0 },
    loading: !error && !data,
    error: error?.message || null,
    refresh: mutate,
    debug: data?.debug
  };
}