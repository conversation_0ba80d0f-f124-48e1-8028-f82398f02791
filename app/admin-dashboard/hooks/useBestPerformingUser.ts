import useSWR from 'swr';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

export interface UserInfo {
  _id: string;
  name: string;
}

interface Result {
  user: UserInfo | null;
  count: number;
  loading: boolean;
  error: string | null;
  refresh: () => void;
}



// Helper function to build API URL for branch parameters
const buildApiUrl = (branchId: string | string[] | null, baseUrl: string) => {
  if (!branchId) return null;

  if (Array.isArray(branchId)) {
    if (branchId.length === 0) return null;
    return `${baseUrl}?branchIds=${branchId.join(',')}`;
  }

  if (branchId === 'all') {
    return `${baseUrl}?allBranches=true`;
  }

  return `${baseUrl}?branchId=${branchId}`;
};

export function useBestPerformingUser(branchId: string | string[] | null, start: string, end: string): Result {
  const shouldFetch = branchId !== null && start && end;
  const baseUrl = buildApiUrl(branchId, '/api/admin-dashboard/user-performance-stats');
  const url = shouldFetch && baseUrl
    ? `${baseUrl}&startDate=${start}&endDate=${end}&page=1&pageSize=1000`
    : null;

  const { data, error, isValidating, mutate } = useSWR(url, dashboardFetcher, {
    revalidateOnFocus: false,
    shouldRetryOnError: false,
  });

  let user: UserInfo | null = null;
  let count = 0;
  if (data && data.stats && data.users) {
    // Aggregate by userId
    const counts: Record<string, number> = {};
    data.stats.forEach((s: any) => {
      if (!s.userId) return;
      counts[s.userId] = (counts[s.userId] || 0) + s.count;
    });
    const bestUserId = Object.entries(counts).sort((a, b) => b[1] - a[1])[0]?.[0];
    count = bestUserId ? counts[bestUserId] : 0;
    user = data.users.find((u: any) => u._id === bestUserId) || null;
  }

  return {
    user,
    count,
    loading: isValidating && !data,
    error: error ? (error as Error).message : null,
    refresh: mutate,
  };
} 