import useSWR from 'swr';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

export interface DailyCreationStat {
  _id: string; // date string
  count: number;
}

export interface CreationStatsTotals {
  totalReservations: number;
  totalVisitReservations: number;
}

interface ApiResponse {
  stats: DailyCreationStat[];
  visitDateStats: DailyCreationStat[];
  totals: CreationStatsTotals;
  branchMap: Record<string, string>;
}

// Helper function to build API URL for branch parameters
const buildApiUrl = (branchId: string | string[] | null, baseUrl: string) => {
  if (!branchId) return null;

  if (Array.isArray(branchId)) {
    if (branchId.length === 0) return null;
    return `${baseUrl}?branchIds=${branchId.join(',')}`;
  }

  if (branchId === 'all') {
    return `${baseUrl}?allBranches=true`;
  }

  return `${baseUrl}?branchId=${branchId}`;
};

export function useDailyCreationStats(
  branchId: string | string[] | null,
  startDate?: string,
  endDate?: string,
  groupBy: 'day' | 'week' | 'month' = 'day'
) {
  const shouldFetch = branchId !== null;
  let url = shouldFetch
    ? buildApiUrl(branchId, '/api/admin-dashboard/daily-creation-stats')
    : null;

  if (url) {
    url += `&groupBy=${groupBy}`;
    // Add date range if provided
    if (startDate && endDate) {
      url += `&startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`;
    }
  }

  const { data, error, isValidating, mutate } = useSWR<ApiResponse>(url, dashboardFetcher, {
    revalidateOnFocus: false,
    shouldRetryOnError: false,
  });

  return {
    stats: data?.stats || [],
    visitDateStats: data?.visitDateStats || [],
    totals: data?.totals || {
      totalReservations: 0,
      totalVisitReservations: 0
    },
    branchMap: data?.branchMap || {},
    loading: isValidating && !data,
    error: error ? (error as Error).message : null,
    refresh: mutate,
    debug: process.env.NODE_ENV === 'development' ? data : undefined,
  };
} 