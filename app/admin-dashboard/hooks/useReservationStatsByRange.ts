import useSWR from 'swr';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

export interface ReservationDailyStat {
  date: string;
  count: number;
  branchId?: string;
}

interface ApiResponse {
  stats: Array<{ _id?: { date: string, branchId?: string }, date?: string, count: number, branchId?: string }>;
}

// Helper function to build API URL for branch parameters
const buildApiUrl = (branchId: string | string[] | null, baseUrl: string) => {
  if (!branchId) return null;

  if (Array.isArray(branchId)) {
    if (branchId.length === 0) return null;
    return `${baseUrl}?branchIds=${branchId.join(',')}`;
  }

  return `${baseUrl}?branchId=${branchId}`;
};

export function useReservationStatsByRange(
  branchId: string | string[] | null,
  start: string,
  end: string
) {
  const shouldFetch = !!branchId && !!start && !!end;
  const baseUrl = buildApiUrl(branchId, '/api/admin-dashboard/reservation-stats');
  const url = shouldFetch && baseUrl
    ? `${baseUrl}&startDate=${start}&endDate=${end}`
    : null;

  const { data, error, isValidating, mutate } = useSWR<ApiResponse>(url, dashboardFetcher, {
    revalidateOnFocus: false,
    shouldRetryOnError: false,
  });

  // If branchId is 'all', return per-branch stats
  const stats: ReservationDailyStat[] = data?.stats
    ? data.stats.map(item => ({
        date: item._id?.date || item.date || '',
        count: item.count,
        branchId: item._id?.branchId || item.branchId,
      })).sort((a, b) => a.date.localeCompare(b.date) || (a.branchId || '').localeCompare(b.branchId || ''))
    : [];

  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.debug('ReservationStatsByRange API data:', data);
  }

  return {
    stats,
    loading: isValidating && !data,
    error: error ? (error as Error).message : null,
    refresh: mutate,
  };
} 