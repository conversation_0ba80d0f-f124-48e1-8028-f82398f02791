import useSWR from 'swr';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

export interface AssignedUserPerformanceStat {
  assignedUserId: string;
  date: string;
  count: number;
  branchId?: string;
  branchName?: string;
}

export interface AssignedUserInfo {
  _id: string;
  name: string;
}

interface ApiResponse {
  stats: AssignedUserPerformanceStat[];
  totals: { assignedUserId: string; total: number; salesCount: number; presenceCount: number }[];
  totalCount: number;
  totalPages: number;
  users: AssignedUserInfo[];
  branchMap: Record<string, string>;
}

export function useAssignedUserPerformanceStatsByRange(
  branchId: string | string[] | null,
  start: string,
  end: string,
  page: number,
  pageSize: number
) {
  const shouldFetch = (branchId !== null) && !!start && !!end;

  const buildUrl = () => {
    if (!shouldFetch) return null;

    const params = new URLSearchParams({
      startDate: start,
      endDate: end,
      page: page.toString(),
      pageSize: pageSize.toString()
    });

    if (Array.isArray(branchId)) {
      if (branchId.length === 0) return null;
      params.append('branchIds', branchId.join(','));
    } else if (branchId === 'all') {
      params.append('allBranches', 'true');
    } else if (branchId) {
      params.append('branchId', branchId);
    }

    return `/api/admin-dashboard/assigned-user-performance-stats?${params.toString()}`;
  };

  const url = buildUrl();

  const { data, error, isValidating, mutate } = useSWR<ApiResponse>(url, dashboardFetcher, {
    revalidateOnFocus: false,
    shouldRetryOnError: false,
  });

  return {
    stats: data?.stats || [],
    users: data?.users || [],
    totals: data?.totals || [],
    totalPages: data?.totalPages || 1,
    totalCount: data?.totalCount || 0,
    loading: isValidating && !data,
    error: error ? (error as Error).message : null,
    refresh: mutate,
    branchMap: data?.branchMap || {},
    isAllBranches: branchId === 'all' || (Array.isArray(branchId) && branchId.length === 0),
  };
} 