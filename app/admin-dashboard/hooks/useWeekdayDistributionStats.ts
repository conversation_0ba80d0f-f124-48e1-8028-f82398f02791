import useSWR from 'swr';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

export interface WeekdayDistributionStat {
  dayIndex: number;
  dayName: string;
  dayNameFr: string;
  count: number;
  sales: number;
  presence: number;
}

// Helper function to build API URL for branch parameters
const buildApiUrl = (branchId: string | string[] | null, baseUrl: string) => {
  if (!branchId) return null;

  if (Array.isArray(branchId)) {
    if (branchId.length === 0) return null;
    return `${baseUrl}?branchIds=${branchId.join(',')}`;
  }

  if (branchId === 'all') {
    return `${baseUrl}?allBranches=true`;
  }

  return `${baseUrl}?branchId=${branchId}`;
};

export function useWeekdayDistributionStats(
  branchId: string | string[] | null,
  startDate?: string,
  endDate?: string
) {
  const shouldFetch = branchId !== null;
  let url = shouldFetch
    ? buildApiUrl(branchId, '/api/admin-dashboard/weekday-distribution-stats')
    : null;

  // Add date range if provided
  if (url && startDate && endDate) {
    url += `&startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`;
  }

  const { data, error, isValidating, mutate } = useSWR(url, dashboardFetcher, {
    revalidateOnFocus: false,
    shouldRetryOnError: false,
  });

  return {
    stats: data?.stats || [],
    loading: isValidating && !data,
    error: error ? (error as Error).message : null,
    refresh: mutate,
    debug: process.env.NODE_ENV === 'development' ? data : undefined,
  };
} 