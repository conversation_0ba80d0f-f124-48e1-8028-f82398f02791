import useSWRInfinite from 'swr/infinite';
import useS<PERSON> from 'swr';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

export interface ReservationStat {
  _id: { date: string; time: string; branchId?: string };
  count: number;
  salesCount?: number;
  presenceCount?: number;
}

interface UseReservationStatsResult {
  stats: ReservationStat[];
  loading: boolean;
  error: string | null;
  refresh: () => void;
  loadMore: () => void;
  hasMore: boolean;
  isLoadingMore: boolean;
}

const PAGE_SIZE = 10;

// Helper function to build API URL for branch parameters
const buildApiUrl = (branchId: string | string[] | null, baseUrl: string) => {
  if (!branchId) return null;

  if (Array.isArray(branchId)) {
    if (branchId.length === 0) return null;
    return `${baseUrl}?branchIds=${branchId.join(',')}`;
  }

  return `${baseUrl}?branchId=${branchId}`;
};

export function useReservationStats(branchId: string | string[] | null, pageSize = PAGE_SIZE): UseReservationStatsResult {
  const getKey = (pageIndex: number, previousPageData: any) => {
    if (!branchId) return null;
    if (previousPageData && !previousPageData.stats.length) return null; // reached end
    const baseUrl = buildApiUrl(branchId, '/api/admin-dashboard/reservation-stats');
    return baseUrl ? `${baseUrl}&page=${pageIndex + 1}&pageSize=${pageSize}` : null;
  };

  const {
    data,
    error,
    size,
    setSize,
    isValidating,
    mutate,
  } = useSWRInfinite(getKey, dashboardFetcher, { revalidateOnFocus: false });

  const stats: ReservationStat[] = data ? data.flatMap((page) => page.stats) : [];
  const totalPages = data && data.length > 0 ? data[0].totalPages : 1;
  const hasMore = size < totalPages;
  const loading = !data && !error;
  const isLoadingMore = isValidating && size > 1;

  const loadMore = () => {
    if (hasMore && !isLoadingMore) setSize(size + 1);
  };

  const refresh = () => {
    mutate();
  };

  return {
    stats,
    loading,
    error: error ? error.message : null,
    refresh,
    loadMore,
    hasMore,
    isLoadingMore,
  };
}

// New: Fetch all reservation stats for a branch (no pagination)
export function useReservationStatsAll(branchId: string | string[] | null) {
  const baseUrl = buildApiUrl(branchId, '/api/admin-dashboard/reservation-stats');
  const url = baseUrl ? `${baseUrl}&page=1&pageSize=10000` : null;
  const { data, error, isValidating, mutate } = useSWR(url, dashboardFetcher, { revalidateOnFocus: false });

  return {
    stats: data?.stats ?? [],
    loading: !data && !error,
    error: error ? error.message : null,
    refresh: mutate,
  };
} 