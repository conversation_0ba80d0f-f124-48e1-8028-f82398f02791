import useSWR from 'swr';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

export interface ReservationSummaryStats {
  totalReservations: number;
  totalSales: number;
  cancelledSales: number;
  averageSale: number;
  loading: boolean;
  error: string | null;
  refresh: () => void;
}

export interface ReservationSummaryStatsOptions {
  startDate?: string;
  endDate?: string;
}

// Helper function to build API URL for branch parameters
const buildApiUrl = (branchId: string | string[] | null, baseUrl: string) => {
  if (!branchId) return null;

  if (Array.isArray(branchId)) {
    if (branchId.length === 0) return null;
    return `${baseUrl}?branchIds=${branchId.join(',')}`;
  }

  return `${baseUrl}?branchId=${branchId}`;
};

export function useReservationSummaryStats(
  branchId: string | string[] | null,
  options?: ReservationSummaryStatsOptions
): ReservationSummaryStats {
  let url = buildApiUrl(branchId, '/api/admin-dashboard/reservation-stats');
  if (url && options?.startDate && options?.endDate) {
    url += `&startDate=${encodeURIComponent(options.startDate)}&endDate=${encodeURIComponent(options.endDate)}`;
  }
  const { data, error, isValidating, mutate } = useSWR(url, dashboardFetcher, { revalidateOnFocus: false });

  return {
    totalReservations: data?.totalReservations ?? 0,
    totalSales: data?.totalSales ?? 0,
    cancelledSales: data?.cancelledSales ?? 0,
    averageSale: data?.averageSale ?? 0,
    loading: !data && !error,
    error: error ? error.message : null,
    refresh: mutate,
  };
} 