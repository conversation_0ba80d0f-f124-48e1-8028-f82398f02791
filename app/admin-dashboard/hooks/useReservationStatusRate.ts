import useSWR from 'swr';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

// Helper function to build API URL for branch parameters
const buildApiUrl = (branchId: string | string[] | null, baseUrl: string) => {
  if (!branchId) return null;

  if (Array.isArray(branchId)) {
    if (branchId.length === 0) return null;
    return `${baseUrl}?branchIds=${branchId.join(',')}`;
  }

  if (branchId === 'all') {
    return `${baseUrl}?allBranches=true`;
  }

  return `${baseUrl}?branchId=${branchId}`;
};

export function useReservationStatusRate(
  branchId: string | string[] | null,
  type: 'presence' | 'sales',
  startDate?: string,
  endDate?: string
) {
  const shouldFetch = branchId !== null;
  let url = shouldFetch
    ? buildApiUrl(branchId, '/api/admin-dashboard/reservation-status-rate')
    : null;

  if (url) {
    url += `&type=${type}`;
    if (startDate && endDate) {
      url += `&startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`;
    }
  }

  const { data, error, isValidating, mutate } = useSWR(url, dashboardFetcher, {
    revalidateOnFocus: false,
    shouldRetryOnError: false,
  });

  const total = data?.total ?? 0;
  const count = data?.count ?? 0;
  const rate = total > 0 ? Math.round((count / total) * 100) : 0;

  return {
    rate,
    count,
    total,
    loading: isValidating && !data,
    error: error ? (error as Error).message : null,
    refresh: mutate,
    debug: process.env.NODE_ENV === 'development' ? data : undefined,
  };
} 