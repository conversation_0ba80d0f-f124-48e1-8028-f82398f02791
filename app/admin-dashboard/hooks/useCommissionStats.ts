import { useCallback, useEffect, useState } from 'react';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

export interface CommissionStats {
  totalCommissions: number;
  totalAmount: number;
  topUser: { _id: string; name: string; total: number } | null;
}

export interface BranchCommissionStats extends CommissionStats {
  branchId: string;
  branchName: string;
}

export function useCommissionStats(start: string, end: string, branchId?: string | string[] | null) {
  const [global, setGlobal] = useState<CommissionStats | null>(null);
  const [branches, setBranches] = useState<BranchCommissionStats[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        startDate: start,
        endDate: end
      });

      if (Array.isArray(branchId)) {
        if (branchId.length > 0) {
          params.append('branchIds', branchId.join(','));
        }
      } else if (branchId === 'all') {
        params.append('allBranches', 'true');
      } else if (branchId) {
        params.append('branchId', branchId);
      }

      const json = await dashboardFetcher(`/api/admin-dashboard/commissions?${params.toString()}`);
      setGlobal(json.global || null);
      setBranches(json.branches || []);
    } catch (e: any) {
      setError(e.message || 'Unknown error');
      setGlobal(null);
      setBranches([]);
    } finally {
      setLoading(false);
    }
  }, [start, end, branchId]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const refresh = fetchStats;

  return { global, branches, loading, error, refresh };
} 