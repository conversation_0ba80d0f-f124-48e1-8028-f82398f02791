## Admin Dashboard Components & Hooks

### `<AssignedUserPerformanceStatsCard branchId: string | null />`
- **Purpose:** Displays a paginated, sortable table of assigned user performance stats (reservations per assigned user per day) for a selected date range and branch. Uses a date range picker, handles loading, error, and empty states, and supports translations. Debug panel in development.
- **Props:**
  - `branchId: string | null` — The branch to filter stats by, or all branches if null.

### `useAssignedUserPerformanceStatsByRange(branchId: string | null, start: string, end: string, page: number, pageSize: number): { stats: { assignedUserId: string, date: string, count: number }[], users: { _id: string, name: string }[], loading: boolean, error: string | null, refresh: () => void, totalPages: number, totalCount: number, totals: { assignedUserId: string, total: number, salesCount: number }[] }`
- **Purpose:** Fetches assigned user performance stats (number of reservations per assigned user per day) for a given branch and date range, paginated and sorted by top assigned user. Also fetches user names for display.
- **Returns:**
  - `stats: { assignedUserId: string, date: string, count: number }[]` — Array of assigned user stats by day
  - `users: { _id: string, name: string }[]` — List of users for name resolution
  - `loading: boolean` — Loading state
  - `error: string | null` — Error message if fetch fails
  - `refresh: () => void` — Function to refetch the data
  - `totalPages: number` — Total number of pages
  - `totalCount: number` — Total number of assigned users in the result
  - `totals: { assignedUserId: string, total: number, salesCount: number }[]` — Array of totals per assigned user, including total reservations and number of reservations with 'sales' status

### `useReservationSummaryStats(branchId: string | null): ReservationSummaryStats`
- **Purpose:** Fetches total reservations, total sales, and average sale for the admin dashboard. Handles loading, error, and refresh.
- **Returns:**
  - `totalReservations: number`
  - `totalSales: number`
  - `averageSale: number`
  - `loading: boolean`
  - `error: string | null`
  - `refresh: () => void`

### `<ReservationSummaryCards branchId: string | null />`
- **Purpose:** Displays three cards for total reservations, total sales, and average sale. Handles loading, error, and debug states. Uses translations and is responsive.
- **Props:**
  - `branchId: string | null` — The branch to filter stats by, or all branches if null. 

### `useReservationStatsByRange(branchId: string | null, start: string, end: string)`
- **Purpose:** Fetches daily reservation counts for a given branch and date range, for use in charts or reports.
- **Returns:**
  - `stats: { date: string, count: number }[]` — Array of daily stats sorted by date
  - `loading: boolean` — Loading state
  - `error: string | null` — Error message if fetch fails
  - `refresh: () => void` — Function to refetch the data

### <ReservationRangeChart branchId: string | null />
- **Purpose:** Displays a bar chart of daily reservations for a selected date range, with a modern date range picker.
- **Features:**
  - User selects a date range (with a calendar picker)
  - Chart updates to show only the selected range
  - Handles loading, empty, and error states
  - Locale-aware date formatting for axis and tooltips
  - Debug panel in development
  - **Layout:** On desktop, this card takes up half the dashboard width (left side), with the right half intentionally left empty for future cards. On mobile, it is full width.
- **Props:**
  - `branchId: string | null` — The branch to filter stats by, or all branches if null.

### <ReservationStatsCard branchId: string | null />
- **Purpose:** Displays a table of reservations by day and time slot, with totals and color-coded cells. Handles loading, error, and empty states. Debug panel in development.
- **Props:**
  - `branchId: string | null` — The branch to filter stats by, or all branches if null.
- **Layout:** This card is always full width and appears below the ReservationRangeChart row.

### `<DateRangePicker value, onChange />`
- **Purpose:** Reusable, locale-aware date range picker for dashboard use.
- **Props:**
  - `value: { from: Date | undefined, to: Date | undefined }`
  - `onChange: (range: { from: Date | undefined, to: Date | undefined }) => void`

### `useUserPerformanceStatsByRange(branchId: string | null, start: string, end: string, page: number, pageSize: number): { stats: { userId: string, date: string, count: number }[], users: { _id: string, name: string }[], loading: boolean, error: string | null, refresh: () => void, totalPages: number, totalCount: number, totals: { userId: string, total: number, salesCount: number }[] }`
- **Purpose:** Fetches user performance stats (number of reservations per user per day) for a given branch and date range, paginated and sorted by top user. Also fetches user names for display.
- **Returns:**
  - `stats: { userId: string, date: string, count: number }[]` — Array of user stats by day
  - `users: { _id: string, name: string }[]` — List of users for name resolution
  - `loading: boolean` — Loading state
  - `error: string | null` — Error message if fetch fails
  - `refresh: () => void` — Function to refetch the data
  - `totalPages: number` — Total number of pages
  - `totalCount: number` — Total number of users in the result
  - `totals: { userId: string, total: number, salesCount: number }[]` — Array of totals per user, including total reservations and number of reservations with 'sales' status

### `<UserPerformanceStatsCard branchId: string | null />`
- **Purpose:** Displays a paginated, sortable table of user performance stats (reservations per user per day) for a selected date range and branch. Uses a date range picker, handles loading, error, and empty states, and supports translations. Debug panel in development.
- **Props:**
  - `branchId: string | null` — The branch to filter stats by, or all branches if null.

### API: `/api/admin-dashboard/user-performance-stats`
- **Purpose:** Returns reservation counts grouped by user (partnerId) and date, for a given branch and date range. Supports pagination and sorting by top user.
- **Returns:**
  - `stats: { userId: string, date: string, count: number }[]`
  - `totals: { userId: string, total: number }[]`
  - `totalCount: number`
  - `totalPages: number`
  - `users: { _id: string, name: string }[]` — All users for name resolution

## Dashboard Card Style Guide

All dashboard cards use a consistent, colorful, and modern style for visual clarity and user engagement. **Each card uses a distinct color scheme:**

- **ReservationSummaryCards:**
  - Total Reservations: Blue gradient
  - Total Sales: Green gradient
  - Average Sale: Yellow gradient
- **ReservationRangeChart:** Blue gradient
- **ReservationStatsCard:** Teal gradient
- **UserPerformanceStatsCard:** Purple gradient

Use the following guidelines:

- **Base Card:**
  - Use the `Card`, `CardHeader`, `CardTitle`, and `CardContent` components from `@/components/ui/card`.
  - Apply a gradient background and text color for each card type:
    - Blue: `bg-gradient-to-br from-blue-100 to-blue-300 dark:from-blue-900 dark:to-blue-700 text-blue-900 dark:text-blue-100`
    - Green: `bg-gradient-to-br from-green-100 to-green-300 dark:from-green-900 dark:to-green-700 text-green-900 dark:text-green-100`
    - Yellow: `bg-gradient-to-br from-yellow-100 to-yellow-300 dark:from-yellow-900 dark:to-yellow-700 text-yellow-900 dark:text-yellow-100`
    - Teal: `bg-gradient-to-br from-teal-100 to-teal-300 dark:from-teal-900 dark:to-teal-700 text-teal-900 dark:text-teal-100`
    - Purple: `bg-gradient-to-br from-purple-100 to-purple-300 dark:from-purple-900 dark:to-purple-700 text-purple-900 dark:text-purple-100`
  - Add `shadow-lg border-0 relative overflow-hidden` for depth and modern look.
- **Icons:**
  - Use a relevant SVG icon in the card header, sized `w-7 h-7` or `w-8 h-8`, with `currentColor` for stroke/fill.
  - Place the icon to the left of the card title.
- **Content:**
  - Use large, bold numbers for main stats (`text-4xl font-extrabold`).
  - Use `CardTitle` for the label/title.
- **Accessibility & Dark Mode:**
  - Always use color classes that support both light and dark mode.
  - Ensure text contrast is sufficient for readability.
- **Layout:**
  - Place cards in a responsive grid: `grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6`.

**Example usage:**
```tsx
<Card className="bg-gradient-to-br from-blue-100 to-blue-300 dark:from-blue-900 dark:to-blue-700 text-blue-900 dark:text-blue-100 shadow-lg border-0 relative overflow-hidden">
  <CardHeader className="flex flex-row items-center justify-between pb-2">
    <CardTitle className="text-lg font-semibold flex items-center gap-2">
      <svg className="w-7 h-7" ... />
      <span>Card Title</span>
    </CardTitle>
  </CardHeader>
  <CardContent>
    <span className="text-4xl font-extrabold block mt-2 mb-1">123</span>
  </CardContent>
</Card>
```

**Follow this style for all future dashboard cards for a cohesive and professional UI.**

### 🟦 Color Coding for Multi-Branch Data (IMPORTANT)

**When "all" branches are selected and it makes sense for the data (e.g., per-branch breakdown is possible), always color code the card's data by branch.**

- Use a distinct color for each branch (see `BRANCH_COLORS` in existing cards for palette inspiration).
- Add a legend mapping color to branch name.
- For charts: show multiple bars/segments per day if multiple branches have data.
- For tables: show color-coded chips or cells for each branch in each relevant slot.
- This applies to all future dashboard cards where per-branch breakdown is meaningful.

**This ensures consistency, clarity, and a professional look for multi-branch admin dashboards.**

## Components & Hooks

### ReservationRangeChart
`ReservationRangeChart: React.FC<{ branchId: string | null }>`
- Displays a bar chart of reservations grouped by day for a selected branch and date range.
- Supports panning left/right to shift the date range window.
- Chart updates automatically when the date range or branch changes.
- Shows loading, error, and empty states. Includes a debug panel in development.

### useReservationStatsByRange
`useReservationStatsByRange(branchId: string | null, start: string, end: string): { stats: { date: string, count: number }[], loading: boolean, error: string | null, refresh: () => void }`
- Fetches reservation stats grouped by day for a branch and date range.
- Handles both old and new API response formats (grouped by preferences.visitDate or appointment.date).
- Logs API data in development for debugging.

### Translation keys
- `common.panLeft`: Pan left (Défiler à gauche)
- `common.panRight`: Pan right (Défiler à droite)

## Chart Utilities

### `formatDateWithCount(date: string, count?: number): string`
- **Purpose:** Formats a date as 'MMM dd' and appends the count in parentheses if provided, e.g., 'Apr 10 (12)'. Used for X-axis labels and tooltips in reservation charts.

### `CustomTooltip: React.FC<{ active?: boolean; payload?: Array<{ value: number }>; label?: string; }>`
- **Purpose:** Reusable tooltip component for recharts bar charts. Shows the weekday, formatted date, and reservation count. Uses translations for the label. 

### UI Update
- In both `<UserPerformanceStatsCard />` and `<AssignedUserPerformanceStatsCard />`, a green badge is now shown beside each user's name (if applicable) indicating the number of reservations with 'sales' status for that user in the selected range. The badge uses the translation key `dashboard.salesRate` for its label/tooltip. 

### `useTablePan(ref: React.RefObject<HTMLElement>): { cursor: string, onMouseDown: ..., onMouseUp: ..., onMouseMove: ..., onTouchStart: ..., onTouchEnd: ..., onTouchMove: ... }`
- **Purpose:** Adds drag-to-scroll (pan) support for scrollable containers, allowing users to click/touch and drag to scroll both horizontally and vertically. Handles both mouse and touch events, and provides a `cursor` value for UI feedback.
- **Params:**
  - `ref: React.RefObject<HTMLElement>` — Ref to the scrollable container element.
- **Returns:**
  - `cursor: string` — The cursor style to use (`'grab'` or `'grabbing'`).
  - `onMouseDown`, `onMouseUp`, `onMouseMove`, `onTouchStart`, `onTouchEnd`, `onTouchMove`: Event handlers to spread onto the scrollable element.
- **Usage:**
  - Attach the returned handlers and `ref` to the scrollable div (the one with both `overflow-x-auto` and/or `overflow-y-auto`). Spread the handlers and set the `style={{ cursor }}` for best UX. 

### `<BestPerformingUserCard branchId: string | null />`
- **Purpose:** Displays the user (partnerId) with the most reservations for a selected timeframe (today, this month, this year) and branch. Handles loading, error, and empty states. Uses translations and shadcn UI.
- **Props:**
  - `branchId: string | null` — The branch to filter by, or all branches if null.

### `<BestPerformingAssignedUserCard branchId: string | null />`
- **Purpose:** Displays the assigned user (assigned_user_id) with the most reservations for a selected timeframe (today, this month, this year) and branch. Handles loading, error, and empty states. Uses translations and shadcn UI.
- **Props:**
  - `branchId: string | null` — The branch to filter by, or all branches if null.

### `<TimeframeToggle value, onChange />`
- **Purpose:** Reusable toggle for selecting a timeframe: today, this month, or this year.
- **Props:**
  - `value: 'today' | 'month' | 'year'`
  - `onChange: (val: 'today' | 'month' | 'year') => void`

### `useBestPerformingUser(branchId: string | null, timeframe: 'today' | 'month' | 'year'): { user: { _id: string, name: string } | null, count: number, loading: boolean, error: string | null, refresh: () => void }`
- **Purpose:** Fetches the user (partnerId) with the most reservations for the given branch and timeframe.

### `useBestPerformingAssignedUser(branchId: string | null, timeframe: 'today' | 'month' | 'year'): { user: { _id: string, name: string } | null, count: number, loading: boolean, error: string | null, refresh: () => void }`
- **Purpose:** Fetches the assigned user (assigned_user_id) with the most reservations for the given branch and timeframe. 

## Newly Added Components & Hooks

### useReservationStatsAll
Signature: (branchId: string | null) => { stats: ReservationStat[], loading: boolean, error: string | null, refresh: () => void }
Purpose: Fetches all reservation stats for a branch in one request (no pagination), used for accurate slot totals in the sticky footer of the reservation stats card.

### allSlotTotals (in reservation-stats-card.tsx)
Signature: const allSlotTotals: Record<string, number>
Purpose: Memoized calculation of slot totals for all reservation data, used in the sticky footer. Uses useReservationStatsAll for data source.

**This ensures consistency, clarity, and a professional look for multi-branch admin dashboards.**

### <CommissionStatsCards />
- **Purpose:** Displays three horizontally stacked cards for commission stats: total commissions distributed, total dollar amount distributed, and the top user by commission amount, for a selected timeframe (today, this month, this year). Handles loading, error, and debug states. Uses translations and shadcn UI.
- **Props:**
  - None (timeframe is internal, branchId support can be added in future)

### useCommissionStats
Signature: (timeframe: 'today' | 'month' | 'year') => { data: CommissionStats | null, loading: boolean, error: string | null, refresh: () => void }
Purpose: Fetches commission stats (total commissions, total amount, top user) for a given timeframe. Handles loading, error, and refresh. 

## Fullscreen Support for Dashboard Cards

### `withFullscreenSupport<P>(CardComponent: React.ComponentType<P>): React.FC<P>`
- **Purpose:** Higher-order component (HOC) that adds fullscreen capability to any card component.
- **Usage:**
  ```tsx
  const FullscreenCard = withFullscreenSupport(RegularCard);
  ```
- **Features:**
  - Adds a fullscreen toggle button to the top-right corner of the card
  - Expands the card to fill the viewport (with small margins) when toggled
  - Creates a semi-transparent overlay behind the card in fullscreen mode
  - Supports Escape key to exit fullscreen
  - Handles all animations and transitions

### `useFullscreenCard(): { isFullscreen: boolean, toggleFullscreen: () => void, fullscreenClass: string }`
- **Purpose:** Hook that provides fullscreen functionality with state management.
- **Returns:**
  - `isFullscreen: boolean` — Current fullscreen state
  - `toggleFullscreen: () => void` — Function to toggle fullscreen state
  - `fullscreenClass: string` — CSS classes for fullscreen styling

### `<FullscreenButton isFullscreen, toggleFullscreen, className? />`
- **Purpose:** Reusable button component for toggling fullscreen mode.
- **Props:**
  - `isFullscreen: boolean` — Current fullscreen state
  - `toggleFullscreen: () => void` — Function to toggle fullscreen
  - `className?: string` — Optional additional CSS classes

### `<FullscreenOverlay isFullscreen />`
- **Purpose:** Overlay component that dims the background in fullscreen mode.
- **Props:**
  - `isFullscreen: boolean` — Whether to show the overlay

### Enhanced Versions of Existing Components
- `<FullscreenUserPerformanceStatsCard branchId />`
- `<FullscreenAssignedUserPerformanceStatsCard branchId />`

### Translation Keys
- `common.enterFullscreen`: "Enter fullscreen" / "Plein écran"
- `common.exitFullscreen`: "Exit fullscreen" / "Quitter le plein écran"

This fullscreen functionality can be applied to any card component when needed. Simply wrap the component with the `withFullscreenSupport` HOC. 

## Components & Hooks (Dashboard Date Range Integration)

### PresenceRateCard
`PresenceRateCard: React.FC<{ branchId: string | null; dateRange?: DateRange }>`
- Displays the presence rate for a branch and date range. Now accepts a `dateRange` prop to filter by date.

### SalesRateCard
`SalesRateCard: React.FC<{ branchId: string | null; dateRange?: DateRange }>`
- Displays the sales rate for a branch and date range. Now accepts a `dateRange` prop to filter by date.

### ReservationSummaryCards
`ReservationSummaryCards: React.FC<{ branchId: string | null; dateRange: DateRange }>`
- Displays summary cards for reservations, sales, and average sale. Now accepts a `dateRange` prop to filter by date. The date picker is now controlled by the parent.

### useReservationStatusRate
`useReservationStatusRate(branchId: string | null, type: 'presence' | 'sales', startDate?: string, endDate?: string)`
- Custom hook to fetch presence or sales rate for a branch and date range. Now supports `startDate` and `endDate` params.

### AdminDashboard (date range lifting)
- The date range state and picker are now managed at the AdminDashboard level and passed to ReservationSummaryCards, PresenceRateCard, and SalesRateCard for unified filtering.