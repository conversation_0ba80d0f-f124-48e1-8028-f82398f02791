/**
 * Shared fetcher utility for admin dashboard API calls
 * Automatically includes dashboard token if available in sessionStorage
 */
export const dashboardFetcher = async (url: string, retries = 3) => {
  // Wait for token to be available if we're in a browser environment
  let dashboardToken: string | null = null;

  if (typeof window !== 'undefined') {
    // Try to get token with retries for timing issues
    for (let i = 0; i < 5; i++) {
      dashboardToken = sessionStorage.getItem('dashboardToken');
      if (dashboardToken) break;

      // Wait a bit and try again
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add token to headers if available
  if (dashboardToken) {
    headers['x-dashboard-token'] = dashboardToken;
  }

  for (let attempt = 0; attempt < retries; attempt++) {
    try {
      const response = await fetch(url, { headers });

      if (!response.ok) {
        // If it's a 401 and we have retries left, try again
        if (response.status === 401 && attempt < retries - 1) {
          console.warn(`Dashboard fetcher: 401 error on attempt ${attempt + 1}, retrying...`);
          await new Promise(resolve => setTimeout(resolve, 100 * (attempt + 1)));
          continue;
        }

        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText || 'Failed to fetch'}`);
      }

      return await response.json();
    } catch (error) {
      if (attempt === retries - 1) {
        console.error('Dashboard fetcher error:', {
          url,
          hasToken: !!dashboardToken,
          attempt: attempt + 1,
          error: error instanceof Error ? error.message : error
        });
        throw error;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, 100 * (attempt + 1)));
    }
  }
};
