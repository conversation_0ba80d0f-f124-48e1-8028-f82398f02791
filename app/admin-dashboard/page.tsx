"use client";

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { AdminDashboard } from './components/admin-dashboard';
import { Card } from '@/components/ui/card';
import { useLanguage } from '@/lib/contexts/language-context';
import { usePermissions } from '@/hooks/use-permissions';
import { useAppSelector } from '@/lib/redux/hooks';
import * as RoleUtils from '@/lib/utils/role-utils';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { BarChart3, Grid3X3 } from 'lucide-react';
import { NewDashboardView } from '@/components/shared/stats/new-dashboard-view';

export default function AdminDashboardPage() {
  const { t } = useLanguage();
  const router = useRouter();
  const { isLoading: permissionsLoading, error: permissionsError } = usePermissions();
  const roles = useAppSelector((state: any) => state.permissions.roles);
  const [checked, setChecked] = useState(false);
  const [dashboardView, setDashboardView] = useState<'current' | 'new'>('current');

  useEffect(() => {
    if (permissionsLoading) return;
    const isSuperAdmin = RoleUtils.isSuperAdmin(roles);
    const isBranchesAdmin = RoleUtils.isBranchesAdmin(roles);

    setChecked(true);
  }, [permissionsLoading, roles, router]);

  if (permissionsLoading || !checked) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex items-center justify-center h-[calc(100vh-200px)]">
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-center text-gray-600">
              {t('common.loading')}
            </h2>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      {/* Dashboard View Toggle */}
      <div className="flex justify-center mb-6">
        <ToggleGroup
          type="single"
          value={dashboardView}
          onValueChange={(value) => value && setDashboardView(value as 'current' | 'new')}
          className="border rounded-lg p-1"
        >
          <ToggleGroupItem value="current" className="flex items-center gap-2 px-4 py-2">
            <BarChart3 className="h-4 w-4" />
            <span>Current Dashboard</span>
          </ToggleGroupItem>
          <ToggleGroupItem value="new" className="flex items-center gap-2 px-4 py-2">
            <Grid3X3 className="h-4 w-4" />
            <span>New Dashboard</span>
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      {/* Dashboard Content */}
      {dashboardView === 'current' ? (
        <AdminDashboard />
      ) : (
        <NewDashboardView />
      )}
    </div>
  );
}