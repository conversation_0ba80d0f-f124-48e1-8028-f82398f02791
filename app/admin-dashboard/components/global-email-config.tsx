'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Clock, Save, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface GlobalEmailConfig {
  _id: string;
  sendingHour: number;
  sendingMinute: number;
  timezone: string;
  isEnabled: boolean;
  // Weekly fields
  weeklyEnabled: boolean;
  weeklyDay: number;
  weeklyHour: number;
  weeklyMinute: number;
  weeklyTimezone: string;
  // Branch stats fields
  branchStatsEnabled: boolean;
  branchStatsHour: number;
  branchStatsMinute: number;
  branchStatsTimezone: string;
  lastUpdated: string;
  updatedBy: string;
}

interface GlobalEmailConfigProps {
  className?: string;
}

export const GlobalEmailConfig: React.FC<GlobalEmailConfigProps> = ({ className }) => {
  const { toast } = useToast();
  
  const [config, setConfig] = useState<GlobalEmailConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form states - Daily
  const [sendingHour, setSendingHour] = useState<number>(9);
  const [sendingMinute, setSendingMinute] = useState<number>(0);
  const [timezone, setTimezone] = useState<string>('America/Toronto');
  const [isEnabled, setIsEnabled] = useState<boolean>(true);

  // Form states - Weekly
  const [weeklyEnabled, setWeeklyEnabled] = useState<boolean>(false);
  const [weeklyDay, setWeeklyDay] = useState<number>(5); // Friday
  const [weeklyHour, setWeeklyHour] = useState<number>(9);
  const [weeklyMinute, setWeeklyMinute] = useState<number>(0);
  const [weeklyTimezone, setWeeklyTimezone] = useState<string>('America/Toronto');

  // Form states - Branch Stats
  const [branchStatsEnabled, setBranchStatsEnabled] = useState<boolean>(false);
  const [branchStatsHour, setBranchStatsHour] = useState<number>(10);
  const [branchStatsMinute, setBranchStatsMinute] = useState<number>(0);
  const [branchStatsTimezone, setBranchStatsTimezone] = useState<string>('America/Toronto');

  // Load configuration on component mount
  useEffect(() => {
    loadConfig();
  }, []);

  // Calculate target date logic for display
  const getDateLogicText = (hour: number) => {
    return hour < 12
      ? 'Données du jour actuel (envoi matinal)'
      : 'Données du jour suivant (envoi après-midi)';
  };

  const loadConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin-dashboard/global-email-config');
      
      if (!response.ok) {
        throw new Error('Failed to load global email configuration');
      }
      
      const data = await response.json();
      setConfig(data.config);
      setSendingHour(data.config.sendingHour);
      setSendingMinute(data.config.sendingMinute);
      setTimezone(data.config.timezone);
      setIsEnabled(data.config.isEnabled);

      // Set weekly fields with defaults if not present
      setWeeklyEnabled(data.config.weeklyEnabled || false);
      setWeeklyDay(data.config.weeklyDay || 5);
      setWeeklyHour(data.config.weeklyHour || 9);
      setWeeklyMinute(data.config.weeklyMinute || 0);
      setWeeklyTimezone(data.config.weeklyTimezone || 'America/Toronto');

      // Set branch stats fields with defaults if not present
      setBranchStatsEnabled(data.config.branchStatsEnabled || false);
      setBranchStatsHour(data.config.branchStatsHour || 10);
      setBranchStatsMinute(data.config.branchStatsMinute || 0);
      setBranchStatsTimezone(data.config.branchStatsTimezone || 'America/Toronto');

      setError(null);
    } catch (err) {
      console.error('Error loading global email config:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      toast({
        title: 'Erreur',
        description: 'Impossible de charger la configuration globale',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/admin-dashboard/global-email-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sendingHour,
          sendingMinute,
          timezone,
          isEnabled,
          weeklyEnabled,
          weeklyDay,
          weeklyHour,
          weeklyMinute,
          weeklyTimezone,
          branchStatsEnabled,
          branchStatsHour,
          branchStatsMinute,
          branchStatsTimezone
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save configuration');
      }

      const data = await response.json();
      setConfig(data.config);
      
      toast({
        title: 'Succès',
        description: data.cronRestarted 
          ? 'Configuration sauvegardée et planificateur redémarré avec succès'
          : 'Configuration sauvegardée avec succès'
      });
    } catch (err) {
      console.error('Error saving global email config:', err);
      toast({
        title: 'Erreur',
        description: err instanceof Error ? err.message : 'Erreur lors de la sauvegarde',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const formatTime = (hour: number, minute: number): string => {
    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  };

  const formatLastUpdated = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('fr-CA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Generate hour options (0-23)
  const hourOptions = Array.from({ length: 24 }, (_, i) => ({
    value: i,
    label: `${i.toString().padStart(2, '0')}:00`
  }));

  // Generate minute options (0, 15, 30, 45)
  const minuteOptions = [
    { value: 0, label: '00' },
    { value: 15, label: '15' },
    { value: 30, label: '30' },
    { value: 45, label: '45' }
  ];

  const timezoneOptions = [
    { value: 'America/Toronto', label: 'America/Toronto (EST/EDT)' },
    { value: 'America/Montreal', label: 'America/Montreal (EST/EDT)' },
    { value: 'America/New_York', label: 'America/New_York (EST/EDT)' },
    { value: 'UTC', label: 'UTC' }
  ];

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Configuration Globale des Emails
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Configuration Globale des Emails
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={loadConfig} variant="outline">
              Réessayer
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Configuration Globale des Emails
            </CardTitle>
            <CardDescription>
              Configurez l'heure d'envoi et les paramètres globaux pour les emails de statistiques quotidiennes et hebdomadaires
            </CardDescription>
          </div>
          <Badge variant={isEnabled ? "default" : "secondary"}>
            {isEnabled ? 'Activé' : 'Désactivé'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Enable/Disable Toggle */}
        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium">Envoi automatique des emails</label>
            <p className="text-sm text-gray-500">Activer ou désactiver l'envoi automatique quotidien</p>
          </div>
          <Switch
            checked={isEnabled}
            onCheckedChange={setIsEnabled}
          />
        </div>

        {/* Sending Time Configuration */}
        <div className="space-y-4">
          <label className="text-sm font-medium">Heure d'envoi</label>

          <div className="grid grid-cols-2 gap-4">
            {/* Hour Selection */}
            <div className="space-y-2">
              <label className="text-xs text-gray-600">Heure</label>
              <Select value={sendingHour.toString()} onValueChange={(value) => setSendingHour(parseInt(value))}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Heure" />
                </SelectTrigger>
                <SelectContent>
                  {hourOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.value.toString().padStart(2, '0')}h
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Minute Selection */}
            <div className="space-y-2">
              <label className="text-xs text-gray-600">Minutes</label>
              <Select value={sendingMinute.toString()} onValueChange={(value) => setSendingMinute(parseInt(value))}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Minutes" />
                </SelectTrigger>
                <SelectContent>
                  {minuteOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <p className="text-sm text-gray-500">
            Les emails seront envoyés quotidiennement à {formatTime(sendingHour, sendingMinute)}
          </p>
        </div>

        {/* Timezone Configuration */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Fuseau horaire</label>
          <Select value={timezone} onValueChange={setTimezone}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Sélectionnez le fuseau horaire" />
            </SelectTrigger>
            <SelectContent>
              {timezoneOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Weekly Email Configuration */}
        <div className="border-t pt-6 space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">Emails hebdomadaires</label>
              <p className="text-sm text-gray-500">Activer l'envoi automatique d'emails hebdomadaires</p>
            </div>
            <Switch
              checked={weeklyEnabled}
              onCheckedChange={setWeeklyEnabled}
            />
          </div>

          {weeklyEnabled && (
            <>
              {/* Weekly Day Selection */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Jour d'envoi</label>
                <Select value={weeklyDay.toString()} onValueChange={(value) => setWeeklyDay(parseInt(value))}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Sélectionnez le jour" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Dimanche</SelectItem>
                    <SelectItem value="1">Lundi</SelectItem>
                    <SelectItem value="2">Mardi</SelectItem>
                    <SelectItem value="3">Mercredi</SelectItem>
                    <SelectItem value="4">Jeudi</SelectItem>
                    <SelectItem value="5">Vendredi</SelectItem>
                    <SelectItem value="6">Samedi</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Weekly Time Configuration */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Heure d'envoi hebdomadaire</label>
                <div className="flex gap-2">
                  <div className="flex-1">
                    <Select value={weeklyHour.toString()} onValueChange={(value) => setWeeklyHour(parseInt(value))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Heure" />
                      </SelectTrigger>
                      <SelectContent>
                        {Array.from({ length: 24 }, (_, i) => (
                          <SelectItem key={i} value={i.toString()}>
                            {i.toString().padStart(2, '0')}h
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex-1">
                    <Select value={weeklyMinute.toString()} onValueChange={(value) => setWeeklyMinute(parseInt(value))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Minutes" />
                      </SelectTrigger>
                      <SelectContent>
                        {[0, 15, 30, 45].map((minute) => (
                          <SelectItem key={minute} value={minute.toString()}>
                            {minute.toString().padStart(2, '0')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <p className="text-sm text-gray-500">
                  Les emails hebdomadaires seront envoyés le {['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'][weeklyDay]} à {formatTime(weeklyHour, weeklyMinute)}
                </p>
              </div>

              {/* Weekly Timezone Configuration */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Fuseau horaire hebdomadaire</label>
                <Select value={weeklyTimezone} onValueChange={setWeeklyTimezone}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Sélectionnez le fuseau horaire" />
                  </SelectTrigger>
                  <SelectContent>
                    {timezoneOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </div>

        {/* Branch Stats Configuration */}
        <div className="border-t pt-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-medium">Statistiques de Succursale</h3>
              <p className="text-sm text-gray-600">
                Configuration pour les emails de statistiques par succursale
              </p>
            </div>
            <Switch
              checked={branchStatsEnabled}
              onCheckedChange={setBranchStatsEnabled}
            />
          </div>

          {branchStatsEnabled && (
            <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Heure d'envoi
                  </label>
                  <Select value={branchStatsHour.toString()} onValueChange={(value) => setBranchStatsHour(parseInt(value))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 24 }, (_, i) => (
                        <SelectItem key={i} value={i.toString()}>
                          {i.toString().padStart(2, '0')}:00
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minutes
                  </label>
                  <Select value={branchStatsMinute.toString()} onValueChange={(value) => setBranchStatsMinute(parseInt(value))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">:00</SelectItem>
                      <SelectItem value="15">:15</SelectItem>
                      <SelectItem value="30">:30</SelectItem>
                      <SelectItem value="45">:45</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fuseau horaire
                  </label>
                  <Select value={branchStatsTimezone} onValueChange={setBranchStatsTimezone}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="America/Toronto">America/Toronto</SelectItem>
                      <SelectItem value="America/Montreal">America/Montreal</SelectItem>
                      <SelectItem value="America/Vancouver">America/Vancouver</SelectItem>
                      <SelectItem value="UTC">UTC</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded p-3">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium">Logique de calcul des données:</p>
                    <p>{getDateLogicText(branchStatsHour)}</p>
                    <p className="mt-1 text-xs">
                      Heure configurée: {branchStatsHour.toString().padStart(2, '0')}:{branchStatsMinute.toString().padStart(2, '0')} ({branchStatsTimezone})
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Current Configuration Display */}
        {config && (
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <h4 className="font-medium text-sm">Configuration actuelle</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Emails quotidiens:</span>
                <span className="ml-2 font-medium">
                  {config.isEnabled ? `${formatTime(config.sendingHour, config.sendingMinute)} ${config.timezone}` : 'Désactivés'}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Emails hebdomadaires:</span>
                <span className="ml-2 font-medium">
                  {config.weeklyEnabled ?
                    `${['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'][config.weeklyDay]} ${formatTime(config.weeklyHour, config.weeklyMinute)} ${config.weeklyTimezone}` :
                    'Désactivés'
                  }
                </span>
              </div>
              <div>
                <span className="text-gray-600">Stats de succursale:</span>
                <span className="ml-2 font-medium">
                  {config.branchStatsEnabled ?
                    `${formatTime(config.branchStatsHour, config.branchStatsMinute)} ${config.branchStatsTimezone}` :
                    'Désactivés'
                  }
                </span>
              </div>
              <div>
                <span className="text-gray-600">Dernière modification:</span>
                <span className="ml-2">{formatLastUpdated(config.lastUpdated)}</span>
              </div>
            </div>
          </div>
        )}

        {/* Save Button */}
        <div className="flex justify-end">
          <Button 
            onClick={saveConfig} 
            disabled={saving}
            className="flex items-center gap-2"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            {saving ? 'Sauvegarde...' : 'Sauvegarder'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
