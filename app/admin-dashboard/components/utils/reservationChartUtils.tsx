import React from 'react';
import { useLanguage } from '@/lib/contexts/language-context';

export function formatDateWithCount(date: string, count?: number): string {
  try {
    const d = new Date(date);
    const base = d.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
    return count !== undefined ? `${base} (${count})` : base;
  } catch {
    return count !== undefined ? `${date} (${count})` : date;
  }
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{ value: number }>;
  label?: string;
}

export const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload, label }) => {
  const { t } = useLanguage();
  if (active && payload && payload.length && label) {
    const d = new Date(label);
    const weekday = d.toLocaleDateString(undefined, { weekday: 'short' });
    const count = payload[0].value;
    return (
      <div className="bg-white dark:bg-gray-800 p-2 border border-gray-200 dark:border-gray-700 rounded shadow">
        <p className="font-semibold">{weekday} {formatDateWithCount(label, count)}</p>
        <p>
          {t('dashboard.reservations')}: {count}
        </p>
      </div>
    );
  }
  return null;
};

export function getDateRangeForTimeframe(timeframe: 'today' | 'month' | 'year'): { start: string, end: string } {
  const now = new Date();
  let start: Date, end: Date;
  if (timeframe === 'today') {
    start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    end = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  } else if (timeframe === 'month') {
    start = new Date(now.getFullYear(), now.getMonth(), 1);
    end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  } else {
    start = new Date(now.getFullYear(), 0, 1);
    end = new Date(now.getFullYear(), 11, 31);
  }
  // Format as yyyy-mm-dd
  const pad = (n: number) => n.toString().padStart(2, '0');
  const format = (d: Date) => `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`;
  return { start: format(start), end: format(end) };
}

// Returns the start (Monday) and end (Sunday) of the current week as Date objects
export function getDateRangeForThisWeek(): { from: Date; to: Date } {
  const now = new Date();
  const day = now.getDay();
  // getDay: 0 (Sunday) to 6 (Saturday)
  // Monday = 1
  const diffToMonday = day === 0 ? -6 : 1 - day;
  const monday = new Date(now);
  monday.setDate(now.getDate() + diffToMonday);
  monday.setHours(0, 0, 0, 0);
  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  sunday.setHours(23, 59, 59, 999);
  return { from: monday, to: sunday };
} 