'use client';

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/lib/contexts/language-context';
import { Copy, Eye, EyeOff, Trash2, Plus, ExternalLink } from 'lucide-react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface DashboardToken {
  id: string;
  token: string;
  description: string;
  createdBy: {
    name: string;
    email: string;
  };
  createdAt: string;
  expiresAt: string | null;
  isActive: boolean;
  lastUsedAt: string | null;
}

export function DashboardTokenManagement() {
  const { t } = useLanguage();
  const { toast } = useToast();
  const [tokens, setTokens] = useState<DashboardToken[]>([]);
  const [loading, setLoading] = useState(true);
  const [showTokens, setShowTokens] = useState<Record<string, boolean>>({});
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newTokenDescription, setNewTokenDescription] = useState('');
  const [newTokenExpiry, setNewTokenExpiry] = useState('');
  const [creating, setCreating] = useState(false);

  const fetchTokens = async () => {
    try {
      const response = await fetch('/api/admin-dashboard/generate-token');
      if (response.ok) {
        const data = await response.json();
        setTokens(data.tokens || []);
      } else {
        throw new Error('Failed to fetch tokens');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch dashboard tokens',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTokens();
  }, []);

  const createToken = async () => {
    setCreating(true);
    try {
      const response = await fetch('/api/admin-dashboard/generate-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          description: newTokenDescription,
          expiresAt: newTokenExpiry || null,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: 'Success',
          description: 'Dashboard access token created successfully',
        });
        setIsCreateDialogOpen(false);
        setNewTokenDescription('');
        setNewTokenExpiry('');
        fetchTokens();
      } else {
        throw new Error('Failed to create token');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create dashboard token',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  const revokeToken = async (tokenId: string) => {
    try {
      const response = await fetch(`/api/admin-dashboard/generate-token?tokenId=${tokenId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({
          title: 'Success',
          description: 'Token revoked successfully',
        });
        fetchTokens();
      } else {
        throw new Error('Failed to revoke token');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to revoke token',
        variant: 'destructive',
      });
    }
  };

  const copyToken = (token: string) => {
    navigator.clipboard.writeText(token);
    toast({
      title: 'Copied',
      description: 'Token copied to clipboard',
    });
  };

  const copyDashboardUrl = (token: string) => {
    const url = `${window.location.origin}/dashboard-view/${token}`;
    navigator.clipboard.writeText(url);
    toast({
      title: 'Copied',
      description: 'Dashboard URL copied to clipboard',
    });
  };

  const openDashboard = (token: string) => {
    const url = `${window.location.origin}/dashboard-view/${token}`;
    window.open(url, '_blank');
  };

  const toggleTokenVisibility = (tokenId: string) => {
    setShowTokens(prev => ({
      ...prev,
      [tokenId]: !prev[tokenId]
    }));
  };

  const maskToken = (token: string) => {
    return token.substring(0, 8) + '•'.repeat(token.length - 8);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Dashboard Access Tokens</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Dashboard Access Tokens</CardTitle>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Token
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Dashboard Access Token</DialogTitle>
              <DialogDescription>
                Create a new token for unauthenticated dashboard access. This token will allow viewing all branch statistics.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="e.g., External monitoring dashboard"
                  value={newTokenDescription}
                  onChange={(e) => setNewTokenDescription(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="expiry">Expiry Date (optional)</Label>
                <Input
                  id="expiry"
                  type="datetime-local"
                  value={newTokenExpiry}
                  onChange={(e) => setNewTokenExpiry(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={createToken} disabled={creating}>
                {creating ? 'Creating...' : 'Create Token'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        {tokens.length === 0 ? (
          <p className="text-muted-foreground text-center py-8">
            No dashboard access tokens created yet.
          </p>
        ) : (
          <div className="space-y-4">
            {tokens.map((token) => (
              <div key={token.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{token.description || 'Untitled Token'}</h4>
                    <p className="text-sm text-muted-foreground">
                      Created by {token.createdBy.name} on {format(new Date(token.createdAt), 'PPP')}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      token.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {token.isActive ? 'Active' : 'Revoked'}
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <code className="flex-1 bg-muted p-2 rounded text-sm font-mono">
                    {showTokens[token.id] ? token.token : maskToken(token.token)}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleTokenVisibility(token.id)}
                  >
                    {showTokens[token.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToken(token.token)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyDashboardUrl(token.token)}
                    >
                      Copy URL
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openDashboard(token.token)}
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      Open
                    </Button>
                  </div>
                  
                  {token.isActive && (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="destructive" size="sm">
                          <Trash2 className="h-4 w-4 mr-1" />
                          Revoke
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Revoke Token</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to revoke this token? This action cannot be undone and will immediately disable access.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => revokeToken(token.id)}>
                            Revoke Token
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  )}
                </div>

                {token.expiresAt && (
                  <p className="text-sm text-muted-foreground">
                    Expires: {format(new Date(token.expiresAt), 'PPP')}
                  </p>
                )}
                
                {token.lastUsedAt && (
                  <p className="text-sm text-muted-foreground">
                    Last used: {format(new Date(token.lastUsedAt), 'PPP')}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
