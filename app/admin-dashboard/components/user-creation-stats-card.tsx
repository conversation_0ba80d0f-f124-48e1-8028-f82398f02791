import React, { useMemo, useEffect, useRef } from 'react';
import { Card, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useUserCreationStatsByRange } from '../hooks/useUserCreationStatsByRange';
import { useLanguage } from '@/lib/contexts/language-context';
import { DateRange } from 'react-day-picker';
import { useTablePan } from './utils/useTablePan';

// Utility function for date formatting (using UTC to avoid timezone issues)
function formatDateLocal(date: Date | undefined): string {
  if (!date) return '';
  // Use UTC methods to avoid timezone conversion issues
  const year = date.getUTCFullYear();
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

function getStartOfWeek(date: Date) {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is sunday
  d.setDate(diff);
  d.setHours(0, 0, 0, 0);
  return d;
}
function getEndOfWeek(date: Date) {
  const d = getStartOfWeek(date);
  d.setDate(d.getDate() + 6);
  d.setHours(23, 59, 59, 999);
  return d;
}

interface UserCreationStatsCardProps {
  branchId: string | string[] | null;
  dateRange?: DateRange;
}

// Fixed color palette for branches
const BRANCH_COLORS = [
  { bg: 'bg-blue-100 dark:bg-blue-800', text: 'text-blue-700 dark:text-blue-200' },
  { bg: 'bg-green-100 dark:bg-green-800', text: 'text-green-700 dark:text-green-200' },
  { bg: 'bg-orange-100 dark:bg-orange-800', text: 'text-orange-700 dark:text-orange-200' },
  { bg: 'bg-pink-100 dark:bg-pink-800', text: 'text-pink-700 dark:text-pink-200' },
  { bg: 'bg-purple-100 dark:bg-purple-800', text: 'text-purple-700 dark:text-purple-200' },
  { bg: 'bg-indigo-100 dark:bg-indigo-800', text: 'text-indigo-700 dark:text-indigo-200' },
  { bg: 'bg-teal-100 dark:bg-teal-800', text: 'text-teal-700 dark:text-teal-200' },
  { bg: 'bg-yellow-100 dark:bg-yellow-800', text: 'text-yellow-700 dark:text-yellow-200' },
  { bg: 'bg-red-100 dark:bg-red-800', text: 'text-red-700 dark:text-red-200' },
  { bg: 'bg-gray-100 dark:bg-gray-800', text: 'text-gray-700 dark:text-gray-200' },
];

export const UserCreationStatsCard: React.FC<UserCreationStatsCardProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();

  // Use provided dateRange or default to current week (Monday to Sunday)
  const today = new Date();
  const defaultStart = getStartOfWeek(today);
  const defaultEnd = getEndOfWeek(today);
  const range = dateRange || { from: defaultStart, to: defaultEnd };

  const start = range.from ? formatDateLocal(range.from) : '';
  const end = range.to ? formatDateLocal(range.to) : '';

  const { 
    stats, 
    users, 
    totals, 
    loading, 
    error, 
    refresh, 
    branchMap,
    isAllBranches 
  } = useUserCreationStatsByRange(
    branchId,
    start,
    end,
    1,
    1000 // fetch all users for the range
  );

  // Add effect to refresh data when branchId changes
  useEffect(() => {
    refresh();
  }, [branchId, refresh]);

  // Build date columns
  const dateColumns = useMemo(() => {
    if (!range.from || !range.to) return [];
    const dates: string[] = [];
    const d = new Date(range.from);
    while (d <= range.to) {
      dates.push(formatDateLocal(new Date(d)));
      d.setDate(d.getDate() + 1);
    }
    return dates;
  }, [range]);

  // Map userId to name
  const userMap = useMemo(() => {
    const map: Record<string, string> = {};
    users.forEach(u => { map[u._id] = u.name; });
    return map;
  }, [users]);

  // Group stats by userId
  const userRows = useMemo(() => {
    const rows: Record<string, Record<string, number>> = {};
    stats.forEach(s => {
      if (!rows[s.userId]) rows[s.userId] = {};
      rows[s.userId][s.date] = s.count;
    });
    return rows;
  }, [stats]);

  // Get totals for current users
  const pageTotals = useMemo(() => {
    const map: Record<string, number> = {};
    totals.forEach(t => { map[t.userId] = t.total; });
    return map;
  }, [totals]);

  // Filter dateColumns to only include dates with at least one non-zero reservation
  const filteredDateColumns = useMemo(() => {
    return dateColumns.filter(date =>
      totals.some(({ userId }) => userRows[userId]?.[date] && userRows[userId][date] > 0)
    );
  }, [dateColumns, totals, userRows]);

  // Create a map of branchId to color
  const branchColorMap = useMemo(() => {
    const shouldShowBranches = isAllBranches || (Array.isArray(branchId) && branchId.length > 1);
    if (!shouldShowBranches) return {};
    const uniqueBranchIds = Array.from(
      new Set(stats.map(s => s.branchId).filter(Boolean) as string[])
    );
    return Object.fromEntries(
      uniqueBranchIds.map((bId, index) => [
        bId,
        BRANCH_COLORS[index % BRANCH_COLORS.length]
      ])
    );
  }, [isAllBranches, branchId, stats]);

  // Group stats by userId, date, and branchId for cell display
  const userDateBranchRows = useMemo(() => {
    const shouldShowBranches = isAllBranches || (Array.isArray(branchId) && branchId.length > 1);
    if (!shouldShowBranches) return {}; // Only relevant when showing multiple branches

    const rows: Record<string, Record<string, Record<string, number>>> = {};
    stats.forEach(s => {
      if (!s.branchId) return;

      if (!rows[s.userId]) rows[s.userId] = {};
      if (!rows[s.userId][s.date]) rows[s.userId][s.date] = {};

      rows[s.userId][s.date][s.branchId] = s.count;
    });
    return rows;
  }, [stats, isAllBranches, branchId]);

  // Create the URL that would be used for fetching to display in debug panel
  const debugUrl = useMemo(() => {
    if (!branchId || !start || !end) return 'No URL (missing parameters)';

    const params = new URLSearchParams({
      startDate: start,
      endDate: end,
      page: '1',
      pageSize: '1000'
    });

    if (Array.isArray(branchId)) {
      if (branchId.length > 0) {
        params.append('branchIds', branchId.join(','));
      }
    } else if (branchId === 'all') {
      params.append('allBranches', 'true');
    } else {
      params.append('branchId', branchId);
    }

    return `/api/admin-dashboard/user-creation-stats?${params.toString()}`;
  }, [branchId, start, end]);

  // Pan logic
  const panRef = useRef<HTMLDivElement>(null);
  const { cursor, onMouseDown, onMouseUp, onMouseMove, onTouchStart, onTouchEnd, onTouchMove } = useTablePan(panRef);

  return (
    <Card className="bg-gradient-to-br from-teal-100 to-teal-300 dark:from-teal-900 dark:to-teal-700 text-teal-900 dark:text-teal-100 shadow-lg border-0 relative overflow-hidden min-h-[120px]">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
          </svg>
          <span>{t('dashboard.userCreationStats') || 'User Creation Stats'}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-40">
            <span className="text-gray-500 dark:text-gray-300 animate-pulse">{t('common.loading')}</span>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-40">
            <span className="text-red-500 dark:text-red-200">{error}</span>
            <Button variant="outline" size="sm" className="mt-2" onClick={() => refresh()}>
              {t('common.retry')}
            </Button>
          </div>
        ) : totals.length === 0 ? (
          <div className="flex items-center justify-center h-40">
            <span className="text-gray-400 dark:text-gray-200">{t('common.noData')}</span>
          </div>
        ) : (
          <div
            className="overflow-x-auto rounded-lg bg-white/70 dark:bg-white/10 p-2"
          >
            {(isAllBranches || (Array.isArray(branchId) && branchId.length > 1)) && Object.keys(branchMap).length > 0 && (
              <div className="mb-3 flex flex-wrap gap-2 justify-start">
                {Object.entries(branchMap).map(([bId, bName], index) => (
                  <div 
                    key={bId} 
                    className={`flex items-center gap-1 rounded px-2 py-1 text-xs ${BRANCH_COLORS[index % BRANCH_COLORS.length].bg} ${BRANCH_COLORS[index % BRANCH_COLORS.length].text}`}
                  >
                    <span className="w-3 h-3 rounded-full inline-block" style={{ backgroundColor: 'currentColor' }}></span>
                    <span>{bName}</span>
                  </div>
                ))}
              </div>
            )}
            <div
              className="max-h-[420px] overflow-auto"
              ref={panRef}
              style={{ cursor }}
              onMouseDown={onMouseDown}
              onMouseUp={onMouseUp}
              onMouseLeave={onMouseUp}
              onMouseMove={onMouseMove}
              onTouchStart={onTouchStart}
              onTouchEnd={onTouchEnd}
              onTouchMove={onTouchMove}
            >
              <table className="min-w-max w-full border-collapse text-center text-xs sm:text-sm">
                <thead className="sticky top-0 z-10 bg-white/80 dark:bg-gray-900/80">
                  <tr>
                    <th className="border px-1 sm:px-2 py-1 sticky left-0 z-20 bg-white/80 dark:bg-gray-900/80 text-xs sm:text-sm min-w-[120px]">{t('dashboard.user')}</th>
                    {filteredDateColumns.map(date => (
                      <th key={date} className="border px-1 sm:px-2 py-1 text-xs sm:text-sm min-w-[60px]">{date}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {totals.map(({ userId, salesCount, presenceCount }) => (
                    <tr key={userId}>
                      <td className="border px-1 sm:px-2 py-1 font-semibold text-left sticky left-0 z-10 bg-white/70 dark:bg-white/10 min-w-[120px]">
                        <div className="space-y-1">
                          <span className="block truncate text-xs sm:text-sm">{userMap[userId] || t('dashboard.unassigned')}</span>
                          <div className="flex flex-wrap gap-1">
                            <span className="text-xs text-blue-700 dark:text-blue-200 font-bold bg-blue-100 dark:bg-blue-900 rounded px-1 py-0.5">{pageTotals[userId] || 0}</span>
                            {salesCount > 0 && (
                              <span
                                className="text-xs font-bold bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-200 rounded px-1 py-0.5"
                                title={t('dashboard.sales') || t('dashboard.salesRate') || 'Sales'}
                              >
                                S:{salesCount}
                              </span>
                            )}
                            {presenceCount > 0 && (
                              <span
                                className="text-xs font-bold bg-orange-100 dark:bg-orange-800 text-orange-700 dark:text-orange-200 rounded px-1 py-0.5"
                                title={t('dashboard.presence') || t('dashboard.presenceCount') || 'Presence'}
                              >
                                P:{presenceCount}
                              </span>
                            )}
                          </div>
                        </div>
                      </td>
                      {filteredDateColumns.map(date => {
                        // Single branch mode - simple display
                        const shouldShowBranches = isAllBranches || (Array.isArray(branchId) && branchId.length > 1);
                        if (!shouldShowBranches) {
                          return (
                            <td key={date} className="border px-1 sm:px-2 py-1 text-xs sm:text-sm min-w-[60px] text-center">
                              {userRows[userId]?.[date] || ''}
                            </td>
                          );
                        }
                        
                        // All branches mode - show branch-colored values
                        const dateBranchCounts = userDateBranchRows[userId]?.[date] || {};
                        const hasData = Object.keys(dateBranchCounts).length > 0;
                        
                        if (!hasData) {
                          return <td key={date} className="border px-2 py-1"></td>;
                        }
                        
                        return (
                          <td key={date} className="border px-2 py-1">
                            <div className="flex flex-wrap justify-center gap-1">
                              {Object.entries(dateBranchCounts).map(([bId, count]) => {
                                const color = branchColorMap[bId] || BRANCH_COLORS[0];
                                return (
                                  <span 
                                    key={bId} 
                                    className={`inline-flex items-center justify-center text-xs min-w-[24px] h-6 rounded-full px-1 ${color.bg} ${color.text}`}
                                  >
                                    {count}
                                  </span>
                                );
                              })}
                            </div>
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className="sticky bottom-0 bg-white dark:bg-gray-900 z-10">
                    <td className="border px-2 py-1 font-bold text-left sticky left-0 z-20 bg-white dark:bg-gray-900">Total</td>
                    {filteredDateColumns.map(date => {
                      // Regular total for single branch view
                      const shouldShowBranches = isAllBranches || (Array.isArray(branchId) && branchId.length > 1);
                      if (!shouldShowBranches) {
                        const singleTotal = totals.length > 0 ? totals.reduce((sum, { userId }) => sum + (userRows[userId]?.[date] || 0), 0) : 0;
                        return (
                          <td key={date} className="border px-2 py-1 font-bold">{singleTotal > 0 ? singleTotal : ''}</td>
                        );
                      }
                      
                      // For all branches, show totals per branch with colors
                      const branchTotals: Record<string, number> = {};
                      
                      // Collect totals by branch
                      stats.forEach(s => {
                        if (s.date === date && s.branchId) {
                          branchTotals[s.branchId] = (branchTotals[s.branchId] || 0) + s.count;
                        }
                      });
                      
                      if (Object.keys(branchTotals).length === 0) {
                        return <td key={date} className="border px-2 py-1 font-bold"></td>;
                      }
                      
                      return (
                        <td key={date} className="border px-2 py-1 font-bold">
                          <div className="flex flex-wrap justify-center gap-1">
                            {Object.entries(branchTotals).map(([bId, count]) => {
                              const color = branchColorMap[bId] || BRANCH_COLORS[0];
                              return (
                                <span 
                                  key={bId} 
                                  className={`inline-flex items-center justify-center text-xs min-w-[24px] h-6 rounded-full px-1 ${color.bg} ${color.text}`}
                                >
                                  {count}
                                </span>
                              );
                            })}
                          </div>
                        </td>
                      );
                    })}
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        )}
        {process.env.NODE_ENV === 'development' && (
          <div className="p-2 bg-blue-50 dark:bg-blue-900 text-xs rounded mt-2 border border-blue-200 dark:border-blue-700">
            <strong>DEBUG UserCreationStats:</strong>
            <ul className="list-disc ml-4 space-y-1">
              <li>Date range: {start} → {end}</li>
              <li>branchId: {String(branchId)}</li>
              <li>isAllBranches: {String(isAllBranches)}</li>
              <li>stats.length: {stats.length}</li>
              <li>users.length: {users.length}</li>
              <li>branches: {Object.keys(branchMap).length}</li>
              <li>loading: {String(loading)}</li>
              <li>error: {String(error)}</li>
              <li>API URL: <span className="font-mono text-xs break-all">{debugUrl}</span></li>
              <li>
                <strong>Totals (userId, total, salesCount):</strong>
                <ul className="ml-4">
                  {totals.map(t => (
                    <li key={t.userId}>
                      {t.userId + ': total=' + t.total + ', salesCount=' + t.salesCount}
                    </li>
                  ))}
                </ul>
              </li>
              <li>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-1" 
                  onClick={() => {
                    console.log('Creation Stats Debug:', {
                      branchId,
                      isAllBranches,
                      start,
                      end,
                      stats,
                      users,
                      totals,
                      branchMap
                    });
                    refresh();
                  }}
                >
                  Log Data & Refresh
                </Button>
              </li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
};