import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useLanguage } from '@/lib/contexts/language-context';
import { BranchSelector } from './branch-selector';
import { ReservationStatsCard } from './reservation-stats-card';
import { ReservationSummaryCards } from './reservation-summary-cards';
import { ReservationRangeChart } from './reservation-range-chart';
import { UserPerformanceStatsCard } from './user-performance-stats-card';
import { UserCreationStatsCard } from './user-creation-stats-card';
import { AssignedUserPerformanceStatsCard } from './assigned-user-performance-stats-card';
import { PresenceRateCard, SalesRateCard, SalesToPresenceRateCard } from './presence-sales-rate-cards';
import { BestPerformingUserCard } from './best-performing-user-card';
import { BestPerformingAssignedUserCard } from './best-performing-assigned-user-card';
import { CommissionStatsCards } from './commission-stats-cards';
import { withFullscreenSupport } from '@/lib/fullscreen-ui/withFullscreenSupport';
import { DateRange } from 'react-day-picker';
import { startOfMonth, endOfMonth } from 'date-fns';
import { DateRangePicker } from './date-range-picker';
import { WeekdayDistributionChart } from './weekday-distribution-chart';
import { DailyCreationStatsChart } from './daily-creation-stats-chart';
import { SalesByTimeslotChart } from './sales-by-timeslot-chart';
import { SalesByTimeslotHorizontalChart } from './sales-by-timeslot-horizontal-chart';
import { DashboardTokenManagement } from './dashboard-token-management';
import StatsEmailModelDebug from './debug/stats-email-model-debug';
import StatsEmailApiDebug from './debug/stats-email-api-debug';
import StatsEmailTemplateDebug from './debug/stats-email-template-debug';
import StatsAggregationDebug from './debug/stats-aggregation-debug';
import StatsEmailServiceDebug from './debug/stats-email-service-debug';
import { CronJobDebug } from './debug/cron-job-debug';
import StatsEmailConfigDebug from './debug/stats-email-config-debug';
import StatsExportDebug from './debug/stats-export-debug';
import BranchStatsDebug from './debug/branch-stats-debug';
import { StatsEmailSettings } from './stats-email-settings';
import { useAppSelector } from '@/lib/redux/hooks';
import { isSuperAdmin } from '@/lib/utils/role-utils';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { BarChart3, TrendingUp, Users, Settings } from 'lucide-react';

// Apply fullscreen support directly to the components
const FullscreenUserPerformanceStatsCard = withFullscreenSupport(UserPerformanceStatsCard);
const FullscreenUserCreationStatsCard = withFullscreenSupport(UserCreationStatsCard);
const FullscreenAssignedUserPerformanceStatsCard = withFullscreenSupport(AssignedUserPerformanceStatsCard);
const FullscreenWeekdayDistributionChart = withFullscreenSupport(WeekdayDistributionChart);
const FullscreenDailyCreationStatsChart = withFullscreenSupport(DailyCreationStatsChart);
const FullscreenSalesByTimeslotChart = withFullscreenSupport(SalesByTimeslotChart);
const FullscreenSalesByTimeslotHorizontalChart = withFullscreenSupport(SalesByTimeslotHorizontalChart);

export function AdminDashboard() {
  const { data: session } = useSession();
  const roles = useAppSelector((state: any) => state.permissions.roles);
  const isSuperAdminUser = isSuperAdmin(roles);

  // --- LocalStorage keys ---
  const FILTERS_KEY = 'amq_admin_dashboard_filters';

  // --- Load initial state from localStorage ---
  function getInitialFilters() {
    if (typeof window === 'undefined') return { branchId: null, dateRange: null };
    try {
      const raw = localStorage.getItem(FILTERS_KEY);
      if (!raw) return { branchId: null, dateRange: null };
      const parsed = JSON.parse(raw);
      let dateRange = null;
      if (parsed.dateRange && parsed.dateRange.from && parsed.dateRange.to) {
        dateRange = {
          from: parsed.dateRange.from ? new Date(parsed.dateRange.from) : undefined,
          to: parsed.dateRange.to ? new Date(parsed.dateRange.to) : undefined,
        };
      }
      return {
        branchId: parsed.branchId ?? null,
        dateRange,
      };
    } catch {
      return { branchId: null, dateRange: null };
    }
  }

  const initialFilters = typeof window !== 'undefined' ? getInitialFilters() : { branchId: null, dateRange: null };

  const [selectedBranchId, setSelectedBranchId] = useState<string | string[] | null>(initialFilters.branchId);
  const { t } = useLanguage();



  // Date range state lifted up
  const [dateRange, setDateRange] = useState<DateRange>(
    initialFilters.dateRange || {
      from: startOfMonth(new Date()),
      to: endOfMonth(new Date()),
    }
  );

  // --- Save filters to localStorage on change ---
  useEffect(() => {
    if (typeof window === 'undefined') return;
    localStorage.setItem(
      FILTERS_KEY,
      JSON.stringify({
        branchId: selectedBranchId,
        dateRange: {
          from: dateRange?.from ? dateRange.from.toISOString() : null,
          to: dateRange?.to ? dateRange.to.toISOString() : null,
        },
      })
    );
  }, [selectedBranchId, dateRange]);

  return (
    <div className="container mx-auto py-10">
      <div className="p-6 space-y-6">
        {/* Branch selector and date picker - responsive layout */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-4">
          {/* Branch selector */}
          <div className="flex-shrink-0 w-full lg:w-auto">
            <BranchSelector
              onChange={setSelectedBranchId}
              currentBranchId={selectedBranchId}
              multiselect={true}
            />
          </div>

          {/* Date picker */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-end lg:justify-start gap-2 w-full lg:w-auto lg:ml-auto">
            <span className="text-sm text-gray-500 sm:whitespace-nowrap">{t('dashboard.dateRange')}:</span>
            <DateRangePicker
              value={dateRange}
              onChange={setDateRange}
              className="w-full sm:w-auto"
            />
          </div>
        </div>

        {/* Only render dashboard content if a branch is selected */}
        {selectedBranchId ? (
          <>
            {/* Summary cards always visible at top */}
            <ReservationSummaryCards branchId={selectedBranchId} dateRange={dateRange} />

            {/* Tabbed dashboard content */}
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className={`grid w-full mb-6 ${isSuperAdminUser ? 'grid-cols-4' : 'grid-cols-3'}`}>
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  <span className="hidden sm:inline">{t('dashboard.tabs.overview') || 'Overview'}</span>
                  <span className="sm:hidden">Overview</span>
                </TabsTrigger>
                <TabsTrigger value="analytics" className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  <span className="hidden sm:inline">{t('dashboard.tabs.analytics') || 'Analytics'}</span>
                  <span className="sm:hidden">Analytics</span>
                </TabsTrigger>
                <TabsTrigger value="performance" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span className="hidden sm:inline">{t('dashboard.tabs.performance') || 'Performance'}</span>
                  <span className="sm:hidden">Performance</span>
                </TabsTrigger>
                {isSuperAdminUser && (
                  <TabsTrigger value="management" className="flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    <span className="hidden sm:inline">{t('dashboard.tabs.management') || 'Management'}</span>
                    <span className="sm:hidden">Management</span>
                  </TabsTrigger>
                )}
              </TabsList>

              {/* Overview Tab - Main metrics and key charts */}
              <TabsContent value="overview" className="space-y-6">
                {/* Main dashboard layout */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left column */}
                  <ReservationRangeChart branchId={selectedBranchId} dateRange={dateRange} />

                  {/* Right column: stacked rate cards */}
                  <div className="flex flex-col gap-4">
                    <PresenceRateCard branchId={selectedBranchId} dateRange={dateRange} />
                    <SalesRateCard branchId={selectedBranchId} dateRange={dateRange} />
                    <SalesToPresenceRateCard branchId={selectedBranchId} dateRange={dateRange} />
                  </div>
                </div>

                <ReservationStatsCard branchId={selectedBranchId} />
              </TabsContent>

              {/* Analytics Tab - Detailed charts and distributions */}
              <TabsContent value="analytics" className="space-y-6">
                {/* Chart components - Weekday distribution and Daily creation stats */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FullscreenWeekdayDistributionChart branchId={selectedBranchId} dateRange={dateRange} />
                  <FullscreenDailyCreationStatsChart branchId={selectedBranchId} dateRange={dateRange} />
                </div>

                {/* Sales by timeslot charts - Full width */}
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                  <FullscreenSalesByTimeslotChart branchId={selectedBranchId} dateRange={dateRange} />
                  <FullscreenSalesByTimeslotHorizontalChart branchId={selectedBranchId} dateRange={dateRange} />
                </div>
              </TabsContent>

              {/* Performance Tab - User performance and best performers */}
              <TabsContent value="performance" className="space-y-6">
                {/* Best performing cards row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <BestPerformingUserCard branchId={selectedBranchId} dateRange={dateRange} />
                  <BestPerformingAssignedUserCard branchId={selectedBranchId} dateRange={dateRange} />
                </div>

                {/* Commission stats cards */}
                <CommissionStatsCards branchId={selectedBranchId} dateRange={dateRange} />

                {/* Performance stats cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FullscreenUserPerformanceStatsCard branchId={selectedBranchId} dateRange={dateRange} />
                  <FullscreenUserCreationStatsCard branchId={selectedBranchId} dateRange={dateRange} />
                </div>

                {/* Additional performance stats cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FullscreenAssignedUserPerformanceStatsCard branchId={selectedBranchId} dateRange={dateRange} />
                </div>
              </TabsContent>

              {/* Management Tab - Administrative tools (SuperAdmin only) */}
              {isSuperAdminUser && (
                <TabsContent value="management" className="space-y-6">
                  <DashboardTokenManagement />
                  <StatsEmailSettings />

                  {/* Debug Tools Section - Development Only */}
                  {process.env.NODE_ENV === 'development' && (
                    <div className="border-t pt-6">
                      <h2 className="text-2xl font-bold mb-4">Debug Tools</h2>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                        <p className="text-sm text-yellow-800">
                          <strong>Development Mode:</strong> Debug tools are only available in development environment.
                        </p>
                      </div>
                      <div className="space-y-6">
                        <BranchStatsDebug />
                        <StatsEmailModelDebug />
                        <StatsEmailApiDebug />
                        <StatsEmailTemplateDebug />
                        <StatsAggregationDebug />
                        <StatsEmailServiceDebug />
                        <StatsEmailConfigDebug />
                        <CronJobDebug />
                        <StatsExportDebug />
                      </div>
                    </div>
                  )}
                </TabsContent>
              )}
            </Tabs>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="w-1/2 max-w-md">
              <div className="animate-pulse h-8 bg-gray-200 rounded mb-4" />
              <div className="animate-pulse h-40 bg-gray-200 rounded mb-4" />
              <div className="animate-pulse h-40 bg-gray-200 rounded mb-4" />
            </div>
            <span className="text-gray-400 mt-4">{t('dashboard.selectBranchPrompt') || 'Please select a branch to view dashboard data.'}</span>
          </div>
        )}

        <div className="text-center text-gray-400 mt-20 text-lg">
          {/* Placeholder for future admin dashboard content */}
          {t('common.comingSoon') || 'Dashboard content coming soon...'}
        </div>
      </div>
    </div>
  );
} 