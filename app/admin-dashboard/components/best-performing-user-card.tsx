import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useBestPerformingUser } from '../hooks/useBestPerformingUser';
import { useLanguage } from '@/lib/contexts/language-context';
import { DateRange } from 'react-day-picker';
import { dashboardFetcher } from '../utils/dashboard-fetcher';

interface BestPerformingUserCardProps {
  branchId: string | string[] | null;
  dateRange?: DateRange;
}

// Fixed color palette for branches (same as user-performance-stats-card)
const BRANCH_COLORS = [
  { bg: 'bg-blue-100 dark:bg-blue-800', text: 'text-blue-700 dark:text-blue-200' },
  { bg: 'bg-green-100 dark:bg-green-800', text: 'text-green-700 dark:text-green-200' },
  { bg: 'bg-orange-100 dark:bg-orange-800', text: 'text-orange-700 dark:text-orange-200' },
  { bg: 'bg-pink-100 dark:bg-pink-800', text: 'text-pink-700 dark:text-pink-200' },
  { bg: 'bg-purple-100 dark:bg-purple-800', text: 'text-purple-700 dark:text-purple-200' },
  { bg: 'bg-indigo-100 dark:bg-indigo-800', text: 'text-indigo-700 dark:text-indigo-200' },
  { bg: 'bg-teal-100 dark:bg-teal-800', text: 'text-teal-700 dark:text-teal-200' },
  { bg: 'bg-yellow-100 dark:bg-yellow-800', text: 'text-yellow-700 dark:text-yellow-200' },
  { bg: 'bg-red-100 dark:bg-red-800', text: 'text-red-700 dark:text-red-200' },
  { bg: 'bg-gray-100 dark:bg-gray-800', text: 'text-gray-700 dark:text-gray-200' },
];

// Utility function for date formatting (using UTC to avoid timezone issues)
function formatDateLocal(date: Date | undefined): string {
  if (!date) return '';
  // Use UTC methods to avoid timezone conversion issues
  const year = date.getUTCFullYear();
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export const BestPerformingUserCard: React.FC<BestPerformingUserCardProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();

  // Use provided dateRange or default to today
  const today = new Date();
  const defaultStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const defaultEnd = new Date(defaultStart);
  const range = dateRange || { from: defaultStart, to: defaultEnd };

  const start = range.from ? formatDateLocal(range.from) : '';
  const end = range.to ? formatDateLocal(range.to) : '';

  const result = useBestPerformingUser(branchId, start, end);
  const { user, count, loading, error, refresh } = result;

  // Branch color/legend logic for multiple branches
  const [branchMap, setBranchMap] = useState<Record<string, string>>({});
  useEffect(() => {
    const shouldFetchBranches = branchId === 'all' || (Array.isArray(branchId) && branchId.length > 1);
    if (shouldFetchBranches) {
      dashboardFetcher('/api/branches')
        .then(data => {
          const map: Record<string, string> = {};
          data.forEach((b: { _id: string; name: string }) => {
            map[b._id] = b.name;
          });
          setBranchMap(map);
        })
        .catch(err => {
          console.error('Error fetching branches for best performing user:', err);
        });
    } else {
      setBranchMap({});
    }
  }, [branchId]);

  // Defensive: Some user objects may not have branchId (API may not return it)
  // Try to infer branchId from user if possible, else fallback to null
  const userBranchId = user && (user as any).branchId ? (user as any).branchId : null;
  const branchIds = Object.keys(branchMap);
  const branchColorIndex = userBranchId ? branchIds.indexOf(userBranchId) : -1;
  const branchColor = branchColorIndex >= 0 ? BRANCH_COLORS[branchColorIndex % BRANCH_COLORS.length] : null;
  const debugData: string | undefined = (process.env.NODE_ENV === 'development' && (result as any).debug)
    ? JSON.stringify((result as any).debug, null, 2)
    : undefined;

  return (
    <Card className="bg-gradient-to-br from-purple-100 to-purple-300 dark:from-purple-900 dark:to-purple-700 text-purple-900 dark:text-purple-100 shadow-lg border-0 relative overflow-hidden min-h-[120px] flex flex-col">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
          </svg>
          <span>{t('dashboard.bestPerformingUser')}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col items-center justify-center w-full">
        {loading ? (
          <span className="text-gray-500 dark:text-gray-300 animate-pulse">{t('common.loading')}</span>
        ) : error ? (
          <div className="flex flex-col items-center">
            <span className="text-red-500 dark:text-red-200">{error}</span>
            <Button variant="outline" size="sm" className="mt-2" onClick={refresh}>
              {t('common.retry')}
            </Button>
          </div>
        ) : !user ? (
          <span className="text-gray-400 dark:text-gray-200">{t('common.noData')}</span>
        ) : (
          <div className="flex flex-col items-center gap-1 w-full">
            <span className="text-4xl font-extrabold flex items-center gap-2">
              {user.name}
              {(branchId === 'all' || (Array.isArray(branchId) && branchId.length > 1)) && userBranchId && branchMap[userBranchId] && (
                <span className={`inline-flex items-center gap-1 rounded px-2 py-1 text-xs ${branchColor?.bg || ''} ${branchColor?.text || ''}`}>
                  <span className="w-3 h-3 rounded-full inline-block" style={{ backgroundColor: 'currentColor' }}></span>
                  <span>{branchMap[userBranchId]}</span>
                </span>
              )}
            </span>
            {/* TODO: Integrate salesCount and presenceCount from API */}
            <div className="flex gap-2 mt-1">
              {/* Replace 0 with salesCount when available */}
              {0 > 0 && (
                <span className="text-xs font-bold bg-green-100 dark:bg-green-800 text-green-700 dark:text-green-200 rounded px-2 py-0.5 align-middle" title={t('dashboard.sales') || t('dashboard.salesRate') || 'Sales'}>
                  {0} {t('dashboard.sales') || t('dashboard.salesRate') || 'Sales'}
                </span>
              )}
              {/* Replace 0 with presenceCount when available */}
              {0 > 0 && (
                <span className="text-xs font-bold bg-orange-100 dark:bg-orange-800 text-orange-700 dark:text-orange-200 rounded px-2 py-0.5 align-middle" title={t('dashboard.presence') || t('dashboard.presenceCount') || 'Presence'}>
                  {0} {t('dashboard.presence') || t('dashboard.presenceCount') || 'Presence'}
                </span>
              )}
            </div>
            <span className="text-lg font-semibold">{t('dashboard.reservations')}: {count}</span>
          </div>
        )}
        {/* Branch legend for all branches */}
        {(branchId === 'all' || (Array.isArray(branchId) && branchId.length > 1)) && branchIds.length > 0 && branchMap && typeof branchMap === 'object' && (
          <div className="mt-3 flex flex-wrap gap-2 justify-center w-full">
            <span className="font-medium text-xs text-gray-500 dark:text-gray-300 w-full text-center">{t('dashboard.branchLegend')}</span>
            {branchIds.map((bId, idx) => (
              <span
                key={bId}
                className={`inline-flex items-center gap-1 rounded px-2 py-1 text-xs ${BRANCH_COLORS[idx % BRANCH_COLORS.length].bg} ${BRANCH_COLORS[idx % BRANCH_COLORS.length].text}`}
              >
                <span className="w-3 h-3 rounded-full inline-block" style={{ backgroundColor: 'currentColor' }}></span>
                <span>{(branchMap as Record<string, string>)[bId]}</span>
              </span>
            ))}
          </div>
        )}
        {/* Debug panel in development */}
        {process.env.NODE_ENV === 'development' && debugData && (
          <div className="p-2 bg-purple-50 dark:bg-purple-900 text-xs rounded mt-2 border border-purple-200 dark:border-purple-700 w-full">
            <strong>{t('dashboard.debug')}:</strong>
            <pre className="whitespace-pre-wrap break-all">{debugData}</pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 