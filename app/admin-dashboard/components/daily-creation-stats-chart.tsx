import React, { useState, useMemo } from 'react';
import { Card, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/lib/contexts/language-context';
import { DateRange } from 'react-day-picker';
import { useDailyCreationStats } from '../hooks/useDailyCreationStats';
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Area, AreaChart } from 'recharts';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Utility function for date formatting (using UTC to avoid timezone issues)
function formatDateLocal(date: Date | undefined): string {
  if (!date) return '';
  // Use UTC methods to avoid timezone conversion issues
  const year = date.getUTCFullYear();
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

interface DailyCreationStatsChartProps {
  branchId: string | string[] | null;
  dateRange?: DateRange;
}

export const DailyCreationStatsChart: React.FC<DailyCreationStatsChartProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();
  const [groupBy, setGroupBy] = useState<'day' | 'week' | 'month'>('day');
  const [dataType, setDataType] = useState<'creation' | 'visit'>('creation');
  
  const startDate = dateRange?.from ? formatDateLocal(dateRange.from) : undefined;
  const endDate = dateRange?.to ? formatDateLocal(dateRange.to) : undefined;

  const { stats, visitDateStats, totals, loading, error, refresh } = useDailyCreationStats(
    branchId,
    startDate,
    endDate,
    groupBy
  );

  // Prepare formatted data for the chart
  const chartData = useMemo(() => {
    const sourceData = dataType === 'creation' ? stats : visitDateStats;
    return sourceData.map(day => ({
      date: day._id,
      count: day.count
    }));
  }, [stats, visitDateStats, dataType]);

  // Function to handle retry button click
  const handleRetry = () => {
    refresh();
  };

  // Custom tooltip component for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 rounded-md shadow border border-gray-200 dark:border-gray-700">
          <p className="font-medium mb-1">{label}</p>
          <div className="space-y-1">
            {payload.map((entry: any, index: number) => (
              <div key={`${entry.name}-${index}`} className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: entry.color }}
                />
                <span style={{ color: entry.color }}>
                  {entry.name === 'count' ? t('dashboard.totalCreations') || 'Total Reservations' : entry.name}
                </span>
                <span className="font-semibold">{entry.value}</span>
              </div>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="bg-gradient-to-br from-cyan-100 to-cyan-300 dark:from-cyan-900 dark:to-cyan-700 text-cyan-900 dark:text-cyan-100 shadow-lg border-0 relative overflow-hidden">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2 mb-3">
          <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <span>{t('dashboard.dailyCreationStats') || 'Statistiques de création quotidienne'}</span>
        </CardTitle>
        <div className="flex flex-col sm:flex-row items-center gap-3 w-full">
          <Select
            value={dataType}
            onValueChange={(value) => setDataType(value as 'creation' | 'visit')}
          >
            <SelectTrigger className="w-full sm:w-[150px]">
              <SelectValue placeholder={t('dashboard.dataType') || 'Type de données'} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="creation">{t('dashboard.creationDate') || 'Date de création'}</SelectItem>
              <SelectItem value="visit">{t('dashboard.visitDate') || 'Date de visite'}</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={groupBy}
            onValueChange={(value) => setGroupBy(value as 'day' | 'week' | 'month')}
          >
            <SelectTrigger className="w-full sm:w-[120px]">
              <SelectValue placeholder={t('dashboard.groupBy') || 'Grouper par'} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">{t('dashboard.groupByDay') || 'Jour'}</SelectItem>
              <SelectItem value="week">{t('dashboard.groupByWeek') || 'Semaine'}</SelectItem>
              <SelectItem value="month">{t('dashboard.groupByMonth') || 'Mois'}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-[350px]">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyan-500"></div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-[350px]">
            <span className="text-red-600 dark:text-red-400 mb-2">{error}</span>
            <Button variant="outline" size="sm" onClick={handleRetry}>
              {t('common.retry')}
            </Button>
          </div>
        ) : chartData.length === 0 ? (
          <div className="flex items-center justify-center h-[350px]">
            <span className="text-gray-500 dark:text-gray-400">{t('common.noData')}</span>
          </div>
        ) : (
          <>
            <div className="h-[350px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={chartData}
                  margin={{
                    top: 20,
                    right: 20,
                    bottom: 20,
                    left: 20,
                  }}
                >
                  <CartesianGrid stroke="#d1d5db" strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date"
                    tick={{ fill: 'currentColor', fontSize: 12 }}
                    tickMargin={10}
                  />
                  <YAxis 
                    tick={{ fill: 'currentColor', fontSize: 12 }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Area 
                    type="monotone" 
                    dataKey="count" 
                    fill="#3b82f6" 
                    stroke="#3b82f6"
                    name={dataType === 'creation' ? 
                      (t('dashboard.creationsWithCreationDate') || 'Réservations par date de création') : 
                      (t('dashboard.creationsWithVisitDate') || 'Réservations par date de visite')}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
            <div className="flex flex-col sm:flex-row justify-around mt-4 gap-2">
              <div className="flex flex-col items-center p-2 rounded-lg bg-white/30 dark:bg-black/20">
                <span className="text-sm">{t('dashboard.creationDateTotal') || 'Total (date de création)'}</span>
                <span className="text-2xl font-bold">{totals.totalReservations}</span>
              </div>
              <div className="flex flex-col items-center p-2 rounded-lg bg-blue-200/50 dark:bg-blue-700/30">
                <span className="text-sm">{t('dashboard.visitDateTotal') || 'Total (date de visite)'}</span>
                <span className="text-2xl font-bold">{totals.totalVisitReservations}</span>
                <span className="text-xs mt-1">
                  {t('dashboard.matchesSummaryCards') || 'Correspond aux cartes sommaires'}
                </span>
              </div>
            </div>
          </>
        )}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 p-2 bg-cyan-50 dark:bg-cyan-950 text-xs rounded border border-cyan-200 dark:border-cyan-800">
            <strong>DEBUG:</strong> 
            <br />
            <div>Date range: {startDate || 'not set'} to {endDate || 'not set'}</div>
            <div>Branch ID: {branchId}</div>
            <div>Group by: {groupBy}</div>
            <div>Data type: {dataType}</div>
            <div>Creation data points: {stats.length}</div>
            <div>Visit data points: {visitDateStats.length}</div>
            <div>Totals: Creation={totals.totalReservations}, Visit={totals.totalVisitReservations}</div>
            <pre className="mt-1 overflow-auto max-h-[100px]">
              {JSON.stringify(dataType === 'creation' ? stats.slice(0, 3) : visitDateStats.slice(0, 3), null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 