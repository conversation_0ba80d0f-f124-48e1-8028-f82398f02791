import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { useReservationStatsByRange } from '../hooks/useReservationStatsByRange';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { useLanguage } from '@/lib/contexts/language-context';
import { Calendar } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { formatDateWithCount, CustomTooltip } from './utils/reservationChartUtils';
import { dashboardFetcher } from '../utils/dashboard-fetcher';
import { Button } from '@/components/ui/button';

interface ReservationRangeChartProps {
  branchId: string | string[] | null;
  dateRange?: DateRange;
}

// Color palette for branches
const BRANCH_COLORS = [
  '#60a5fa', // blue
  '#34d399', // green
  '#fbbf24', // yellow
  '#f472b6', // pink
  '#a78bfa', // purple
  '#38bdf8', // sky
  '#f87171', // red
  '#facc15', // amber
  '#4ade80', // emerald
  '#818cf8', // indigo
];

// Utility function for date formatting (using UTC to avoid timezone issues)
function formatDateLocal(date: Date | undefined): string {
  if (!date) return '';
  // Use UTC methods to avoid timezone conversion issues
  const year = date.getUTCFullYear();
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export const ReservationRangeChart: React.FC<ReservationRangeChartProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();

  // Use provided dateRange or default to last 14 days
  const today = new Date();
  const defaultEnd = today;
  const defaultStart = new Date(today);
  defaultStart.setDate(today.getDate() - 13);
  const range = dateRange || { from: defaultStart, to: defaultEnd };

  const start = range.from ? formatDateLocal(range.from) : '';
  const end = range.to ? formatDateLocal(range.to) : '';

  const { stats, loading, error, refresh } = useReservationStatsByRange(
    branchId,
    start,
    end
  );

  // Fetch branch names for legend if all branches
  const [branchMap, setBranchMap] = useState<Record<string, string>>({});
  useEffect(() => {
    if (branchId === 'all') {
      dashboardFetcher('/api/branches')
        .then(data => {
          const map: Record<string, string> = {};
          data.forEach((b: { _id: string; name: string }) => {
            map[b._id] = b.name;
          });
          setBranchMap(map);
        })
        .catch(err => {
          console.error('Error fetching branches for reservation range chart:', err);
        });
    }
  }, [branchId]);

  // Prepare chart data - aggregate by date only (sum all time slots per day)
  type ChartDataItem = { date: string; [branchId: string]: string | number };
  const chartData = useMemo<ChartDataItem[]>(() => {
    if (branchId !== 'all') {
      // For single branch, aggregate by date (sum all time slots)
      const grouped: Record<string, number> = {};
      stats.forEach((s) => {
        if (!grouped[s.date]) grouped[s.date] = 0;
        grouped[s.date] += s.count;
      });
      return Object.entries(grouped).map(([date, count]) => ({
        date,
        count,
      }));
    }

    // For all branches, group by date, then by branch (sum all time slots per day per branch)
    const grouped: Record<string, Record<string, number>> = {};
    (stats as Array<{ date: string; count: number; branchId?: string }>).forEach((s) => {
      if (!grouped[s.date]) grouped[s.date] = {};
      if (s.branchId) {
        if (!grouped[s.date][s.branchId]) grouped[s.date][s.branchId] = 0;
        grouped[s.date][s.branchId] += s.count;
      }
    });

    // Build array for recharts
    return Object.entries(grouped).map(([date, branches]) => {
      return {
        date,
        ...(branches as Record<string, number>),
      };
    });
  }, [stats, branchId]);

  // Get all branchIds present in the data (for color assignment)
  const branchIds = useMemo(() => {
    if (branchId !== 'all') return [];
    const ids = new Set<string>();
    stats.forEach(s => { if (s.branchId) ids.add(s.branchId); });
    return Array.from(ids);
  }, [stats, branchId]);





  return (
    <Card className="bg-gradient-to-br from-blue-100 to-blue-300 dark:from-blue-900 dark:to-blue-700 text-blue-900 dark:text-blue-100 shadow-lg border-0 relative overflow-hidden">
      <CardHeader className="pb-2 sticky top-0 z-20 bg-gradient-to-br from-blue-100 to-blue-300 dark:from-blue-900 dark:to-blue-700 bg-opacity-90 backdrop-blur-md">
        <CardTitle className="text-lg font-semibold flex items-center gap-2 mb-3">
          <Calendar className="w-7 h-7" />
          <span>{t('dashboard.dailyReservationsChart')}</span>
        </CardTitle>

      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="flex flex-col items-center gap-2">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span>{t('common.loading')}</span>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-red-500 dark:text-red-400 flex flex-col items-center gap-2">
              <span className="text-xl">⚠️</span>
              <span>{error}</span>
              <Button variant="outline" size="sm" className="mt-2" onClick={() => refresh()}>
                {t('common.retry')}
              </Button>
            </div>
          </div>
        ) : stats.length === 0 ? (
          <div className="flex items-center justify-center h-64">
            <span className="text-gray-500 dark:text-gray-300">{t('common.noData')}</span>
          </div>
        ) : (
          <div className="relative w-full">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 40 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis
                  dataKey="date"
                  tickFormatter={(date: any, idx: number) => {
                    // Get the count for this date from chartData
                    const day = chartData[idx];
                    if (!day) return '';

                    if (branchId === 'all') {
                      // Sum counts for all branches for this day
                      const total = branchIds.reduce((sum, bId) => {
                        const val = day[bId];
                        return sum + (typeof val === 'number' ? val : Number(val) || 0);
                      }, 0);
                      return formatDateWithCount(date, total) || '';
                    } else {
                      // Single branch - use the count directly
                      const count = typeof day.count === 'number' ? day.count : Number(day.count) || 0;
                      return formatDateWithCount(date, count) || '';
                    }
                  }}
                  padding={{ left: 10, right: 10 }}
                  tick={{ fill: 'currentColor', fontSize: 12 }}
                />
                <YAxis
                  width={40}
                  tick={{ fill: 'currentColor', fontSize: 12 }}
                />
                <Tooltip content={<CustomTooltip />} />
                {branchId === 'all'
                  ? branchIds.map((bId, idx) => (
                      <Bar
                        key={bId}
                        dataKey={bId}
                        stackId={undefined}
                        fill={BRANCH_COLORS[idx % BRANCH_COLORS.length]}
                        radius={[4, 4, 0, 0]}
                        animationDuration={300}
                        name={branchMap[bId] || bId}
                      />
                    ))
                  : (
                      <Bar
                        dataKey="count"
                        fill="#60a5fa"
                        radius={[4, 4, 0, 0]}
                        animationDuration={300}
                      />
                    )}
                {branchId === 'all' && (
                  <Legend
                    verticalAlign="top"
                    align="right"
                    wrapperStyle={{ top: 0, right: 0 }}
                    payload={branchIds.map((bId, idx) => ({
                      value: branchMap[bId] || bId,
                      type: 'rect',
                      color: BRANCH_COLORS[idx % BRANCH_COLORS.length],
                    }))}
                  />
                )}
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}
        {/* Debug panel */}
        {process.env.NODE_ENV === 'development' && (
          <div className="p-2 bg-blue-50 dark:bg-blue-900 text-xs rounded mt-2 border border-blue-200 dark:border-blue-700">
            <strong>DEBUG Range Chart:</strong>
            <ul className="list-disc ml-4 space-y-1">
              <li>Date range: {start} → {end}</li>
              <li>Raw stats.length: {stats.length}</li>
              <li>Aggregated chartData.length: {chartData.length}</li>
              <li>loading: {String(loading)}</li>
              <li>error: {String(error)}</li>
              <li>branchIds: {branchIds.join(', ')}</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 