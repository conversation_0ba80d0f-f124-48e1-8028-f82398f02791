import React, { useMemo, useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLanguage } from '@/lib/contexts/language-context';
import { useSalesByTimeslot, SalesByTimeslotStat } from '../hooks/useSalesByTimeslot';
import { DateRange } from 'react-day-picker';
import { dashboardFetcher } from '../utils/dashboard-fetcher';
import { BarChart3, GitCompare, Building2 } from 'lucide-react';

// Utility function for date formatting (using UTC to avoid timezone issues)
function formatDateLocal(date: Date | undefined): string {
  if (!date) return '';
  // Use UTC methods to avoid timezone conversion issues
  const year = date.getUTCFullYear();
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

interface SalesByTimeslotHorizontalChartProps {
  branchId: string | string[] | null;
  dateRange?: DateRange;
}

// Color palette for time slots
const TIMESLOT_COLORS = [
  '#3b82f6', // blue - 11:00
  '#10b981', // green - 13:00
  '#f59e0b', // amber - 15:00
  '#ef4444', // red - 17:00
  '#8b5cf6', // violet - 19:00
];

// Color palette for branches (when showing multiple branches)
const BRANCH_COLORS = [
  '#60a5fa', // blue
  '#34d399', // green
  '#fbbf24', // yellow
  '#f472b6', // pink
  '#a78bfa', // purple
  '#38bdf8', // sky
  '#f87171', // red
  '#facc15', // amber
  '#4ade80', // emerald
  '#818cf8', // indigo
];

type ViewMode = 'combined' | 'comparison' | 'individual';

interface TimeslotSegment {
  timeslot: string;
  value: number;
  percentage: number;
  color: string;
  branchId?: string;
  branchName?: string;
}

interface DayBarData {
  dayName: string;
  dayIndex: number;
  segments: TimeslotSegment[];
  totalSales: number;
}

export const SalesByTimeslotHorizontalChart: React.FC<SalesByTimeslotHorizontalChartProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();
  
  const startDate = dateRange?.from ? formatDateLocal(dateRange.from) : undefined;
  const endDate = dateRange?.to ? formatDateLocal(dateRange.to) : undefined;

  const { stats, totals, loading, error, refresh } = useSalesByTimeslot(
    branchId,
    startDate,
    endDate
  );

  // View mode and branch selection state
  const [viewMode, setViewMode] = useState<ViewMode>('combined');
  const [selectedBranchForIndividual, setSelectedBranchForIndividual] = useState<string>('');

  // Fetch branch names and data
  const [branchMap, setBranchMap] = useState<Record<string, string>>({});
  const [availableBranches, setAvailableBranches] = useState<Array<{ _id: string; name: string }>>([]);
  const isMultipleBranches = branchId === 'all' || (Array.isArray(branchId) && branchId.length > 1);
  
  useEffect(() => {
    dashboardFetcher('/api/branches')
      .then(data => {
        const map: Record<string, string> = {};
        data.forEach((b: { _id: string; name: string }) => {
          map[b._id] = b.name;
        });
        setBranchMap(map);
        setAvailableBranches(data);
      })
      .catch(err => {
        console.error('Error fetching branches for sales by timeslot horizontal chart:', err);
      });
  }, []);

  // Get available branches from the current data
  const branchesInData = useMemo(() => {
    const branchIds = new Set<string>();
    stats.forEach(day => {
      Object.values(day.timeslots).forEach(slotData => {
        if (typeof slotData === 'object' && slotData !== null) {
          Object.keys(slotData).forEach(branchId => branchIds.add(branchId));
        }
      });
    });
    return Array.from(branchIds).filter(id => branchMap[id]);
  }, [stats, branchMap]);

  // Get branches available for individual selection
  const availableBranchesForIndividual = useMemo(() => {
    if (branchId === 'all') {
      return availableBranches.map(b => b._id);
    } else if (Array.isArray(branchId)) {
      return branchId;
    } else if (branchId) {
      return [branchId];
    }
    return [];
  }, [branchId, availableBranches]);

  // Set default selected branch for individual view
  useEffect(() => {
    if (availableBranchesForIndividual.length > 0 && !selectedBranchForIndividual) {
      setSelectedBranchForIndividual(availableBranchesForIndividual[0]);
    }
  }, [availableBranchesForIndividual, selectedBranchForIndividual]);

  // Auto-set view mode based on data availability
  useEffect(() => {
    if (!isMultipleBranches) {
      setViewMode('combined');
    }
  }, [isMultipleBranches]);

  // Function to handle retry button click
  const handleRetry = () => {
    refresh();
  };

  // Prepare horizontal bar data based on view mode
  const horizontalBarData = useMemo((): DayBarData[] => {
    const timeSlots = ['11:00', '13:00', '15:00', '17:00', '19:00'];
    
    return stats.map((day: SalesByTimeslotStat) => {
      const segments: TimeslotSegment[] = [];
      let totalSales = 0;

      // Calculate total sales for the day first
      timeSlots.forEach(slot => {
        const slotData = day.timeslots[slot];
        
        if (viewMode === 'individual' && selectedBranchForIndividual) {
          if (typeof slotData === 'object' && slotData !== null) {
            totalSales += (slotData as Record<string, number>)[selectedBranchForIndividual] || 0;
          } else if (typeof slotData === 'number') {
            totalSales += slotData;
          }
        } else if (viewMode === 'comparison' && isMultipleBranches) {
          if (typeof slotData === 'object' && slotData !== null) {
            Object.values(slotData as Record<string, number>).forEach(val => totalSales += val);
          }
        } else {
          // Combined view
          if (typeof slotData === 'object' && slotData !== null) {
            totalSales += Object.values(slotData as Record<string, number>).reduce((sum, val) => sum + val, 0);
          } else {
            totalSales += typeof slotData === 'number' ? slotData : 0;
          }
        }
      });

      // Create segments based on view mode
      timeSlots.forEach((slot, slotIndex) => {
        const slotData = day.timeslots[slot];
        
        if (viewMode === 'individual' && selectedBranchForIndividual) {
          let value = 0;
          if (typeof slotData === 'object' && slotData !== null) {
            value = (slotData as Record<string, number>)[selectedBranchForIndividual] || 0;
          } else if (typeof slotData === 'number') {
            value = slotData;
          }
          
          if (value > 0) {
            segments.push({
              timeslot: slot,
              value,
              percentage: totalSales > 0 ? (value / totalSales) * 100 : 0,
              color: TIMESLOT_COLORS[slotIndex],
            });
          }
        } else if (viewMode === 'comparison' && isMultipleBranches) {
          if (typeof slotData === 'object' && slotData !== null) {
            branchesInData.forEach((branchId, branchIndex) => {
              const value = (slotData as Record<string, number>)[branchId] || 0;
              if (value > 0) {
                const branchName = branchMap[branchId] || branchId;
                // Create a variation of the timeslot color for each branch
                const baseColor = TIMESLOT_COLORS[slotIndex];
                const opacity = 0.6 + (branchIndex * 0.15);
                segments.push({
                  timeslot: slot,
                  value,
                  percentage: totalSales > 0 ? (value / totalSales) * 100 : 0,
                  color: baseColor,
                  branchId,
                  branchName,
                });
              }
            });
          }
        } else {
          // Combined view
          let value = 0;
          if (typeof slotData === 'object' && slotData !== null) {
            value = Object.values(slotData as Record<string, number>).reduce((sum, val) => sum + val, 0);
          } else {
            value = typeof slotData === 'number' ? slotData : 0;
          }
          
          if (value > 0) {
            segments.push({
              timeslot: slot,
              value,
              percentage: totalSales > 0 ? (value / totalSales) * 100 : 0,
              color: TIMESLOT_COLORS[slotIndex],
            });
          }
        }
      });

      return {
        dayName: t(`common.days.${day.dayName.toLowerCase()}`) || day.dayNameFr,
        dayIndex: day.dayIndex,
        segments,
        totalSales,
      };
    });
  }, [stats, t, isMultipleBranches, viewMode, selectedBranchForIndividual, branchesInData, branchMap]);

  // Calculate totals for individual branch view
  const individualBranchTotals = useMemo(() => {
    if (viewMode !== 'individual' || !selectedBranchForIndividual) {
      return { totalSales: 0, totalAmount: 0 };
    }

    let totalSales = 0;
    stats.forEach(day => {
      Object.values(day.timeslots).forEach(slotData => {
        if (typeof slotData === 'object' && slotData !== null) {
          totalSales += (slotData as Record<string, number>)[selectedBranchForIndividual] || 0;
        } else if (typeof slotData === 'number') {
          totalSales += slotData;
        }
      });
    });

    return { totalSales, totalAmount: 0 }; // Amount calculation would need API enhancement
  }, [stats, viewMode, selectedBranchForIndividual]);

  const displayTotals = viewMode === 'individual' ? individualBranchTotals : totals;

  // Render horizontal bar for a day
  const renderHorizontalBar = (dayData: DayBarData) => {
    if (dayData.totalSales === 0) {
      return (
        <div className="flex items-center h-12 bg-gray-200 dark:bg-gray-700 rounded-lg">
          <div className="w-full text-center text-sm text-gray-500 dark:text-gray-400">
            No sales
          </div>
        </div>
      );
    }

    let cumulativePercentage = 0;

    return (
      <div className="flex items-center h-12 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
        {dayData.segments.map((segment, index) => {
          const segmentStyle = {
            width: `${segment.percentage}%`,
            backgroundColor: segment.color,
            opacity: viewMode === 'comparison' && segment.branchId ? 0.8 : 1,
          };

          cumulativePercentage += segment.percentage;

          return (
            <div
              key={`${segment.timeslot}-${segment.branchId || 'default'}-${index}`}
              className="h-full flex items-center justify-center text-white text-xs font-medium relative group cursor-pointer transition-all duration-200 hover:brightness-110"
              style={segmentStyle}
              title={`${segment.timeslot}${segment.branchName ? ` - ${segment.branchName}` : ''}: ${segment.value} sales (${segment.percentage.toFixed(1)}%)`}
            >
              {segment.percentage > 8 && (
                <span className="text-shadow">
                  {segment.value}
                </span>
              )}
              
              {/* Tooltip on hover */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                {segment.timeslot}
                {segment.branchName && ` - ${segment.branchName}`}
                <br />
                {segment.value} sales ({segment.percentage.toFixed(1)}%)
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <Card className="bg-gradient-to-br from-blue-100 to-blue-300 dark:from-blue-900 dark:to-blue-700 text-blue-900 dark:text-blue-100 shadow-lg border-0 relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" 
            />
            <path strokeLinecap="round" strokeLinejoin="round" d="M8 15l4-4 4 4" />
          </svg>
          <span>Sales Distribution by Day</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* View Mode Controls - Only show if multiple branches */}
        {isMultipleBranches && (
          <div className="mb-4 space-y-3">
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as ViewMode)} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="combined" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  <span className="hidden sm:inline">Combined</span>
                </TabsTrigger>
                <TabsTrigger value="comparison" className="flex items-center gap-2">
                  <GitCompare className="h-4 w-4" />
                  <span className="hidden sm:inline">Compare</span>
                </TabsTrigger>
                <TabsTrigger value="individual" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  <span className="hidden sm:inline">Individual</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Branch selector for individual view */}
            {viewMode === 'individual' && (
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Branch:</span>
                <Select value={selectedBranchForIndividual} onValueChange={setSelectedBranchForIndividual}>
                  <SelectTrigger className="w-[200px] bg-white/20 border-white/30">
                    <SelectValue placeholder="Select branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableBranchesForIndividual.map((branchId) => (
                      <SelectItem key={branchId} value={branchId}>
                        {branchMap[branchId] || branchId}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Branch legend for comparison view */}
            {viewMode === 'comparison' && branchesInData.length > 0 && (
              <div className="flex flex-wrap gap-2">
                <span className="text-sm font-medium">Branches:</span>
                {branchesInData.map((branchId, index) => (
                  <Badge 
                    key={branchId} 
                    variant="secondary" 
                    className="text-xs"
                    style={{ 
                      backgroundColor: `${BRANCH_COLORS[index % BRANCH_COLORS.length]}20`,
                      borderColor: BRANCH_COLORS[index % BRANCH_COLORS.length],
                      color: BRANCH_COLORS[index % BRANCH_COLORS.length]
                    }}
                  >
                    {branchMap[branchId] || branchId}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Time slot legend */}
        <div className="mb-4 flex flex-wrap gap-2">
          <span className="text-sm font-medium">Time Slots:</span>
          {['11:00', '13:00', '15:00', '17:00', '19:00'].map((slot, index) => (
            <Badge 
              key={slot} 
              variant="secondary" 
              className="text-xs"
              style={{ 
                backgroundColor: `${TIMESLOT_COLORS[index]}20`,
                borderColor: TIMESLOT_COLORS[index],
                color: TIMESLOT_COLORS[index]
              }}
            >
              {slot}
            </Badge>
          ))}
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-[400px]">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-[400px]">
            <span className="text-red-600 dark:text-red-400 mb-2">{error}</span>
            <Button variant="outline" size="sm" onClick={handleRetry}>
              Retry
            </Button>
          </div>
        ) : horizontalBarData.length === 0 ? (
          <div className="flex items-center justify-center h-[400px]">
            <span className="text-gray-500 dark:text-gray-400">No data available</span>
          </div>
        ) : (
          <>
            {/* Horizontal bars for each day */}
            <div className="space-y-4 min-h-[400px]">
              {horizontalBarData.map((dayData) => (
                <div key={dayData.dayIndex} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-medium">{dayData.dayName}</h3>
                    <span className="text-xs text-gray-600 dark:text-gray-400">
                      Total: {dayData.totalSales} sales
                    </span>
                  </div>
                  {renderHorizontalBar(dayData)}
                </div>
              ))}
            </div>
            
            {/* Summary stats */}
            <div className="flex flex-col sm:flex-row justify-around mt-6 gap-2">
              <div className="flex flex-col items-center p-2 rounded-lg bg-white/30 dark:bg-black/20">
                <span className="text-sm font-medium">
                  {viewMode === 'individual' && selectedBranchForIndividual 
                    ? `${branchMap[selectedBranchForIndividual]} - Total Sales`
                    : 'Total Sales'
                  }
                </span>
                <span className="text-xl font-bold">{displayTotals.totalSales}</span>
              </div>
              {viewMode !== 'individual' && (
                <div className="flex flex-col items-center p-2 rounded-lg bg-white/30 dark:bg-black/20">
                  <span className="text-sm font-medium">Total Amount</span>
                  <span className="text-xl font-bold">${displayTotals.totalAmount.toLocaleString()}</span>
                </div>
              )}
            </div>
          </>
        )}
        
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 p-2 bg-blue-50 dark:bg-blue-950 text-xs rounded border border-blue-200 dark:border-blue-800">
            <strong>DEBUG:</strong> 
            <br />
            <div>Date range: {startDate || 'not set'} to {endDate || 'not set'}</div>
            <div>Branch ID: {Array.isArray(branchId) ? branchId.join(', ') : branchId}</div>
            <div>View Mode: {viewMode}</div>
            <div>Selected Branch: {selectedBranchForIndividual}</div>
            <div>Available Branches for Individual: {availableBranchesForIndividual.join(', ')}</div>
            <div>Branches in Data: {branchesInData.join(', ')}</div>
            <div>Is Multiple Branches: {isMultipleBranches.toString()}</div>
            <div>Data points: {stats.length}</div>
            <div>Total sales: {displayTotals.totalSales}, Total amount: ${displayTotals.totalAmount}</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};