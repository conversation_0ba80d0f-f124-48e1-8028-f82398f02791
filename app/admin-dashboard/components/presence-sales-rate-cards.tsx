import React from 'react';
import { <PERSON>, CardHeader, <PERSON>T<PERSON>le, CardContent } from '@/components/ui/card';
import { useReservationStatusRate } from '../hooks/useReservationStatusRate';
import { useLanguage } from '@/lib/contexts/language-context';
import { DateRange } from 'react-day-picker';

interface RateCardProps {
  branchId: string | string[] | null;
  dateRange?: DateRange;
}

// Utility function for date formatting (using UTC to avoid timezone issues)
function formatDateLocal(date: Date | undefined): string | undefined {
  if (!date) return undefined;
  // Use UTC methods to avoid timezone conversion issues
  const year = date.getUTCFullYear();
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export const PresenceRateCard: React.FC<RateCardProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();
  const { rate, count, total, loading, error, refresh, debug } = useReservationStatusRate(
    branchId,
    'presence',
    formatDateLocal(dateRange?.from),
    formatDateLocal(dateRange?.to)
  );

  return (
    <Card className="bg-gradient-to-br from-blue-100 to-blue-300 dark:from-blue-900 dark:to-blue-700 text-blue-900 dark:text-blue-100 shadow-lg border-0 relative overflow-hidden min-h-[120px]">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none" />
            <path d="M8 12l2 2 4-4" stroke="currentColor" strokeWidth="2" fill="none" />
          </svg>
          <span>{t('dashboard.presenceRate') || 'Presence Rate'}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-20">
            <span className="text-gray-500 dark:text-gray-300 animate-pulse">{t('common.loading')}</span>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-20">
            <span className="text-red-500 dark:text-red-200">{error}</span>
          </div>
        ) : total === 0 ? (
          <div className="flex items-center justify-center h-20">
            <span className="text-gray-400 dark:text-gray-200">{t('common.noData')}</span>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-20">
            <span className="text-4xl font-extrabold">{rate}%</span>
            <span className="text-sm mt-1">{count} / {total} {t('dashboard.reservations')}</span>
          </div>
        )}
        {process.env.NODE_ENV === 'development' && debug && (
          <div className="p-2 bg-blue-50 dark:bg-blue-900 text-xs rounded mt-2 border border-blue-200 dark:border-blue-700">
            <strong>DEBUG PresenceRate:</strong>
            <pre className="whitespace-pre-wrap break-all">{JSON.stringify(debug, null, 2)}</pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export const SalesRateCard: React.FC<RateCardProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();
  const { rate, count, total, loading, error, refresh, debug } = useReservationStatusRate(
    branchId,
    'sales',
    formatDateLocal(dateRange?.from),
    formatDateLocal(dateRange?.to)
  );

  return (
    <Card className="bg-gradient-to-br from-green-100 to-green-300 dark:from-green-900 dark:to-green-700 text-green-900 dark:text-green-100 shadow-lg border-0 relative overflow-hidden min-h-[120px]">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none" />
            <path d="M8 12l2 2 4-4" stroke="currentColor" strokeWidth="2" fill="none" />
          </svg>
          <span>{t('dashboard.salesRate') || 'Sales Rate'}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-20">
            <span className="text-gray-500 dark:text-gray-300 animate-pulse">{t('common.loading')}</span>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-20">
            <span className="text-red-500 dark:text-red-200">{error}</span>
          </div>
        ) : total === 0 ? (
          <div className="flex items-center justify-center h-20">
            <span className="text-gray-400 dark:text-gray-200">{t('common.noData')}</span>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-20">
            <span className="text-4xl font-extrabold">{rate}%</span>
            <span className="text-sm mt-1">{count} / {total} {t('dashboard.reservations')}</span>
          </div>
        )}
        {process.env.NODE_ENV === 'development' && debug && (
          <div className="p-2 bg-green-50 dark:bg-green-900 text-xs rounded mt-2 border border-green-200 dark:border-green-700">
            <strong>DEBUG SalesRate:</strong>
            <pre className="whitespace-pre-wrap break-all">{JSON.stringify(debug, null, 2)}</pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export const SalesToPresenceRateCard: React.FC<RateCardProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();
  const { count: salesCount, loading: salesLoading, error: salesError } = useReservationStatusRate(
    branchId,
    'sales',
    formatDateLocal(dateRange?.from),
    formatDateLocal(dateRange?.to)
  );

  const { count: presenceCount, loading: presenceLoading, error: presenceError } = useReservationStatusRate(
    branchId,
    'presence',
    formatDateLocal(dateRange?.from),
    formatDateLocal(dateRange?.to)
  );

  const loading = salesLoading || presenceLoading;
  const error = salesError || presenceError;
  const rate = presenceCount > 0 ? Math.round((salesCount / presenceCount) * 100) : 0;

  return (
    <Card className="bg-gradient-to-br from-purple-100 to-purple-300 dark:from-purple-900 dark:to-purple-700 text-purple-900 dark:text-purple-100 shadow-lg border-0 relative overflow-hidden min-h-[120px]">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none" />
            <path d="M12 6v6l3 3" stroke="currentColor" strokeWidth="2" fill="none" />
          </svg>
          <span>{t('dashboard.salesToPresenceRate') || 'Sales to Presence Rate'}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-20">
            <span className="text-gray-500 dark:text-gray-300 animate-pulse">{t('common.loading')}</span>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-20">
            <span className="text-red-500 dark:text-red-200">{error}</span>
          </div>
        ) : presenceCount === 0 ? (
          <div className="flex items-center justify-center h-20">
            <span className="text-gray-400 dark:text-gray-200">{t('common.noData')}</span>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-20">
            <span className="text-4xl font-extrabold">{rate}%</span>
            <span className="text-sm mt-1">{salesCount} / {presenceCount} {t('dashboard.conversions')}</span>
          </div>
        )}
        {process.env.NODE_ENV === 'development' && (
          <div className="p-2 bg-purple-50 dark:bg-purple-900 text-xs rounded mt-2 border border-purple-200 dark:border-purple-700">
            <strong>DEBUG SalesToPresenceRate:</strong>
            <pre className="whitespace-pre-wrap break-all">
              {JSON.stringify({ salesCount, presenceCount, rate }, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 