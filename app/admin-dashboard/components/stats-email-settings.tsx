'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '@/lib/contexts/language-context';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Trash2, Plus, Mail, Settings, Eye, Send } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { GlobalEmailConfig } from './global-email-config';
import { MultiSelectBranches } from './multi-select-branches';

// Helper function to migrate old settings format
function migrateSettingsFormat(settings: StatsEmailSetting[]): StatsEmailSetting[] {
  return settings.map(setting => {
    // Check if setting has old totalReservations field
    if ('totalReservations' in setting.selectedStats) {
      const oldValue = (setting.selectedStats as any).totalReservations;
      const { totalReservations, ...otherStats } = setting.selectedStats as any;

      return {
        ...setting,
        selectedStats: {
          ...otherStats,
          totalReservationsScheduled: oldValue,
          totalReservationsCreated: oldValue
        }
      };
    }

    // Ensure new fields exist with defaults if missing
    if (!('totalReservationsScheduled' in setting.selectedStats)) {
      return {
        ...setting,
        selectedStats: {
          ...setting.selectedStats,
          totalReservationsScheduled: true,
          totalReservationsCreated: true,
          reservationsCreatedByBranch: true
        }
      };
    }

    // Ensure reservationsCreatedByBranch field exists
    if (!('reservationsCreatedByBranch' in setting.selectedStats)) {
      return {
        ...setting,
        selectedStats: {
          ...setting.selectedStats,
          reservationsCreatedByBranch: true
        }
      };
    }

    // Ensure weekly fields exist with defaults if missing
    const updatedSetting = { ...setting };
    if (!('weeklyEnabled' in setting)) {
      updatedSetting.weeklyEnabled = false;
      updatedSetting.weeklySelectedStats = {
        totalSales: true,
        totalReservationsScheduled: true,
        totalReservationsCreated: true,
        totalPresence: true,
        salesByBranch: true,
        reservationsByBranch: true,
        reservationsCreatedByBranch: true,
        presenceByBranch: true,
        topSellers: true,
        topPaps: true
      };
      updatedSetting.lastWeeklyEmailSent = null;
      updatedSetting.weeklyEmailSendCount = 0;
    }

    return updatedSetting;
  });
}

interface StatsEmailSetting {
  _id: string;
  email: string;
  isActive: boolean;
  selectedStats: {
    totalSales: boolean;
    totalReservationsScheduled: boolean; // Reservations scheduled for this date
    totalReservationsCreated: boolean;   // Reservations created on this date
    totalPresence: boolean;
    salesByBranch: boolean;
    reservationsByBranch: boolean;
    reservationsCreatedByBranch: boolean; // Reservations created by branch (using creation date)
    presenceByBranch: boolean;
    topSellers: boolean;
    topPaps: boolean;
  };
  // Weekly fields
  weeklyEnabled: boolean;
  weeklySelectedStats: {
    totalSales: boolean;
    totalReservationsScheduled: boolean;
    totalReservationsCreated: boolean;
    totalPresence: boolean;
    salesByBranch: boolean;
    reservationsByBranch: boolean;
    reservationsCreatedByBranch: boolean;
    presenceByBranch: boolean;
    topSellers: boolean;
    topPaps: boolean;
  };
  // Branch stats fields
  regularStatsEnabled: boolean;
  branchStatsEnabled: boolean;
  selectedBranches: string[];
  branchStatsSelectedStats: {
    reservationCount: boolean;
    adultCount: boolean;
    childCount: boolean;
    allergies: boolean;
    serviceTypes: boolean;
  };
  lastWeeklyEmailSent: string | null;
  weeklyEmailSendCount: number;
  lastEmailSent: string | null;
  emailSendCount: number;
  createdAt: string;
}

interface Branch {
  value: string;
  label: string;
  name: string;
  city: string;
  province: string;
}

interface StatsEmailSettingsProps {
  className?: string;
}

export const StatsEmailSettings: React.FC<StatsEmailSettingsProps> = ({ className }) => {
  const { t } = useLanguage();
  const { toast } = useToast();
  
  const [settings, setSettings] = useState<StatsEmailSetting[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Form states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isPreviewDialogOpen, setIsPreviewDialogOpen] = useState(false);
  const [currentSetting, setCurrentSetting] = useState<StatsEmailSetting | null>(null);
  const [newEmail, setNewEmail] = useState('');
  const [newSelectedStats, setNewSelectedStats] = useState({
    totalSales: true,
    totalReservationsScheduled: true,
    totalReservationsCreated: true,
    totalPresence: true,
    salesByBranch: true,
    reservationsByBranch: true,
    reservationsCreatedByBranch: true,
    presenceByBranch: true,
    topSellers: true,
    topPaps: true
  });

  // Weekly form states
  const [newWeeklyEnabled, setNewWeeklyEnabled] = useState(false);
  const [newWeeklySelectedStats, setNewWeeklySelectedStats] = useState({
    totalSales: true,
    totalReservationsScheduled: true,
    totalReservationsCreated: true,
    totalPresence: true,
    salesByBranch: true,
    reservationsByBranch: true,
    reservationsCreatedByBranch: true,
    presenceByBranch: true,
    topSellers: true,
    topPaps: true
  });

  // Branch stats state
  const [availableBranches, setAvailableBranches] = useState<Branch[]>([]);
  const [loadingBranches, setLoadingBranches] = useState(false);

  // New email form state - branch stats
  const [newRegularStatsEnabled, setNewRegularStatsEnabled] = useState(true);
  const [newBranchStatsEnabled, setNewBranchStatsEnabled] = useState(false);
  const [newSelectedBranches, setNewSelectedBranches] = useState<string[]>([]);
  const [newBranchStatsSelectedStats, setNewBranchStatsSelectedStats] = useState({
    reservationCount: true,
    adultCount: true,
    childCount: true,
    allergies: true,
    serviceTypes: true
  });

  // Load available branches
  const loadAvailableBranches = async () => {
    try {
      setLoadingBranches(true);
      const response = await fetch('/api/admin-dashboard/available-branches');

      if (!response.ok) {
        throw new Error('Failed to load branches');
      }

      const data = await response.json();
      setAvailableBranches(data.branches);
    } catch (error) {
      console.error('Error loading branches:', error);
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les succursales',
        variant: 'destructive'
      });
    } finally {
      setLoadingBranches(false);
    }
  };

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
    loadAvailableBranches();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin-dashboard/stats-email-settings');

      if (!response.ok) {
        throw new Error('Failed to load email settings');
      }

      const data = await response.json();
      const migratedSettings = migrateSettingsFormat(data.settings || []);
      setSettings(migratedSettings);
      setError(null);
    } catch (err) {
      console.error('Error loading email settings:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      toast({
        title: 'Erreur',
        description: 'Impossible de charger les paramètres email',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddEmail = async () => {
    if (!newEmail || !isValidEmail(newEmail)) {
      toast({
        title: 'Erreur',
        description: 'Veuillez entrer une adresse email valide',
        variant: 'destructive'
      });
      return;
    }

    // Validate branch selection if branch stats enabled
    if (newBranchStatsEnabled && newSelectedBranches.length === 0) {
      toast({
        title: 'Erreur',
        description: 'Veuillez sélectionner au moins une succursale pour les statistiques de succursale',
        variant: 'destructive'
      });
      return;
    }

    // Validate that at least one type of stats is enabled
    if (!newRegularStatsEnabled && !newBranchStatsEnabled) {
      toast({
        title: 'Erreur',
        description: 'Veuillez activer au moins un type de statistiques (régulières ou succursales)',
        variant: 'destructive'
      });
      return;
    }

    try {
      const response = await fetch('/api/admin-dashboard/stats-email-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: newEmail,
          selectedStats: newSelectedStats,
          weeklyEnabled: newWeeklyEnabled,
          weeklySelectedStats: newWeeklySelectedStats,
          regularStatsEnabled: newRegularStatsEnabled,
          branchStatsEnabled: newBranchStatsEnabled,
          selectedBranches: newSelectedBranches,
          branchStatsSelectedStats: newBranchStatsSelectedStats
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to add email');
      }

      const data = await response.json();
      const migratedSetting = migrateSettingsFormat([data.setting])[0];
      setSettings(prev => [...prev, migratedSetting]);
      setNewEmail('');
      setNewSelectedStats({
        totalSales: true,
        totalReservationsScheduled: true,
        totalReservationsCreated: true,
        totalPresence: true,
        salesByBranch: true,
        reservationsByBranch: true,
        reservationsCreatedByBranch: true,
        presenceByBranch: true,
        topSellers: true,
        topPaps: true
      });
      setNewWeeklyEnabled(false);
      setNewWeeklySelectedStats({
        totalSales: true,
        totalReservationsScheduled: true,
        totalReservationsCreated: true,
        totalPresence: true,
        salesByBranch: true,
        reservationsByBranch: true,
        reservationsCreatedByBranch: true,
        presenceByBranch: true,
        topSellers: true,
        topPaps: true
      });
      setNewRegularStatsEnabled(true);
      setNewBranchStatsEnabled(false);
      setNewSelectedBranches([]);
      setNewBranchStatsSelectedStats({
        reservationCount: true,
        adultCount: true,
        childCount: true,
        allergies: true,
        serviceTypes: true
      });
      setIsAddDialogOpen(false);
      
      toast({
        title: 'Succès',
        description: 'Adresse email ajoutée avec succès'
      });
    } catch (err) {
      console.error('Error adding email:', err);
      toast({
        title: 'Erreur',
        description: err instanceof Error ? err.message : 'Erreur lors de l\'ajout',
        variant: 'destructive'
      });
    }
  };

  const handleUpdateEmail = async (id: string, updates: Partial<StatsEmailSetting>) => {
    try {
      const response = await fetch(`/api/admin-dashboard/stats-email-settings/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update email');
      }

      const data = await response.json();
      const migratedSetting = migrateSettingsFormat([data.setting])[0];
      setSettings(prev => prev.map(setting =>
        setting._id === id ? migratedSetting : setting
      ));
      
      toast({
        title: 'Succès',
        description: 'Paramètres mis à jour avec succès'
      });
    } catch (err) {
      console.error('Error updating email:', err);
      toast({
        title: 'Erreur',
        description: err instanceof Error ? err.message : 'Erreur lors de la mise à jour',
        variant: 'destructive'
      });
    }
  };

  const handleDeleteEmail = async (id: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette adresse email ?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin-dashboard/stats-email-settings/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete email');
      }

      setSettings(prev => prev.filter(setting => setting._id !== id));
      
      toast({
        title: 'Succès',
        description: 'Adresse email supprimée avec succès'
      });
    } catch (err) {
      console.error('Error deleting email:', err);
      toast({
        title: 'Erreur',
        description: err instanceof Error ? err.message : 'Erreur lors de la suppression',
        variant: 'destructive'
      });
    }
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'Jamais';
    return new Date(dateString).toLocaleDateString('fr-CA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSelectedStatsCount = (selectedStats: any): number => {
    return Object.values(selectedStats).filter(Boolean).length;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Paramètres Email des Statistiques
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Paramètres Email des Statistiques
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={loadSettings} variant="outline">
              Réessayer
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Global Email Configuration */}
      <GlobalEmailConfig />

      {/* Individual Email Settings */}
      <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Paramètres Email des Statistiques
            </CardTitle>
            <CardDescription>
              Gérez les adresses email qui recevront les statistiques quotidiennes
            </CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Ajouter Email
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Ajouter une adresse email</DialogTitle>
                <DialogDescription>
                  Configurez les types de statistiques à envoyer à cette adresse email
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                <div>
                  <label className="text-sm font-medium">Adresse email</label>
                  <Input
                    type="email"
                    value={newEmail}
                    onChange={(e) => setNewEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="mt-1"
                  />
                </div>

                {/* Regular Daily Stats Section */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-medium">Statistiques quotidiennes régulières</h4>
                      <p className="text-sm text-gray-600">Statistiques générales de l'entreprise</p>
                    </div>
                    <Switch
                      checked={newRegularStatsEnabled}
                      onCheckedChange={setNewRegularStatsEnabled}
                    />
                  </div>

                  {newRegularStatsEnabled && (
                    <div className="space-y-2 bg-gray-50 p-3 rounded">
                      {Object.entries(newSelectedStats).map(([key, value]) => (
                        <div key={key} className="flex items-center space-x-2">
                          <Checkbox
                            id={key}
                            checked={value}
                            onCheckedChange={(checked) =>
                              setNewSelectedStats(prev => ({ ...prev, [key]: !!checked }))
                            }
                          />
                          <label htmlFor={key} className="text-sm">
                            {getStatLabel(key)}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Branch Stats Section */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-medium">Statistiques de succursale</h4>
                      <p className="text-sm text-gray-600">Données opérationnelles par succursale</p>
                    </div>
                    <Switch
                      checked={newBranchStatsEnabled}
                      onCheckedChange={setNewBranchStatsEnabled}
                    />
                  </div>

                  {newBranchStatsEnabled && (
                    <div className="space-y-4 bg-gray-50 p-3 rounded">
                      {/* Branch Selection */}
                      <div>
                        <label className="text-sm font-medium mb-2 block">
                          Succursales sélectionnées
                        </label>
                        <MultiSelectBranches
                          options={availableBranches}
                          value={newSelectedBranches}
                          onChange={setNewSelectedBranches}
                          placeholder="Sélectionner les succursales..."
                          loading={loadingBranches}
                        />
                      </div>

                      {/* Branch Stats Selection */}
                      <div>
                        <label className="text-sm font-medium mb-2 block">
                          Statistiques à inclure
                        </label>
                        <div className="space-y-2">
                          {Object.entries(newBranchStatsSelectedStats).map(([key, value]) => (
                            <div key={key} className="flex items-center space-x-2">
                              <Checkbox
                                id={`branch-${key}`}
                                checked={value}
                                onCheckedChange={(checked) =>
                                  setNewBranchStatsSelectedStats(prev => ({ ...prev, [key]: !!checked }))
                                }
                              />
                              <label htmlFor={`branch-${key}`} className="text-sm">
                                {getBranchStatLabel(key)}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Weekly Email Configuration */}
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between mb-3">
                    <label className="text-sm font-medium">Emails hebdomadaires</label>
                    <Switch
                      checked={newWeeklyEnabled}
                      onCheckedChange={setNewWeeklyEnabled}
                    />
                  </div>

                  {newWeeklyEnabled && (
                    <div>
                      <label className="text-sm font-medium mb-3 block">Statistiques hebdomadaires à inclure</label>
                      <div className="space-y-2">
                        {Object.entries(newWeeklySelectedStats).map(([key, value]) => (
                          <div key={`weekly-${key}`} className="flex items-center space-x-2">
                            <Checkbox
                              id={`weekly-${key}`}
                              checked={value}
                              onCheckedChange={(checked) =>
                                setNewWeeklySelectedStats(prev => ({ ...prev, [key]: !!checked }))
                              }
                            />
                            <label htmlFor={`weekly-${key}`} className="text-sm">
                              {getStatLabel(key)}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                    Annuler
                  </Button>
                  <Button onClick={handleAddEmail}>
                    Ajouter
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {settings.length === 0 ? (
          <div className="text-center py-8">
            <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">Aucune adresse email configurée</p>
            <p className="text-sm text-gray-500">
              Ajoutez des adresses email pour commencer à envoyer les statistiques quotidiennes
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {settings.map((setting) => (
              <div key={setting._id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div>
                      <p className="font-medium">{setting.email}</p>
                      <p className="text-sm text-gray-500">
                        {(setting.regularStatsEnabled ?? true) && `Quotidien: ${getSelectedStatsCount(setting.selectedStats)} statistiques`}
                        {setting.weeklyEnabled && ` • Hebdomadaire: ${getSelectedStatsCount(setting.weeklySelectedStats)} statistiques`}
                        {(setting.branchStatsEnabled ?? false) && ` • Succursales: ${(setting.selectedBranches ?? []).length} succursale${(setting.selectedBranches ?? []).length > 1 ? 's' : ''}`}
                      </p>
                    </div>
                    <Badge variant={setting.isActive ? "default" : "secondary"}>
                      {setting.isActive ? 'Actif' : 'Inactif'}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={setting.isActive}
                      onCheckedChange={(checked) =>
                        handleUpdateEmail(setting._id, { isActive: checked })
                      }
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setCurrentSetting(setting);
                        setIsEditDialogOpen(true);
                      }}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setCurrentSetting(setting);
                        setIsPreviewDialogOpen(true);
                      }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteEmail(setting._id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="text-sm text-gray-500 space-y-1">
                  <p>Dernier envoi quotidien: {formatDate(setting.lastEmailSent)}</p>
                  <p>Emails quotidiens envoyés: {setting.emailSendCount}</p>
                  {setting.weeklyEnabled && (
                    <>
                      <p>Dernier envoi hebdomadaire: {formatDate(setting.lastWeeklyEmailSent)}</p>
                      <p>Emails hebdomadaires envoyés: {setting.weeklyEmailSendCount}</p>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Edit Dialog */}
      {currentSetting && (
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Modifier les paramètres - {currentSetting.email}</DialogTitle>
              <DialogDescription>
                Modifiez les types de statistiques à envoyer à cette adresse email
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              {/* Regular Daily Stats Section */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium">Statistiques quotidiennes régulières</h4>
                    <p className="text-sm text-gray-600">Statistiques générales de l'entreprise</p>
                  </div>
                  <Switch
                    checked={currentSetting.regularStatsEnabled ?? true}
                    onCheckedChange={(checked) => {
                      setCurrentSetting({
                        ...currentSetting,
                        regularStatsEnabled: checked
                      });
                    }}
                  />
                </div>

                {(currentSetting.regularStatsEnabled ?? true) && (
                  <div className="space-y-2 bg-gray-50 p-3 rounded">
                    {Object.entries(currentSetting.selectedStats).map(([key, value]) => (
                      <div key={key} className="flex items-center space-x-2">
                        <Checkbox
                          id={`edit-${key}`}
                          checked={value}
                          onCheckedChange={(checked) => {
                            const updatedStats = {
                              ...currentSetting.selectedStats,
                              [key]: !!checked
                            };
                            setCurrentSetting({
                              ...currentSetting,
                              selectedStats: updatedStats
                            });
                          }}
                        />
                        <label htmlFor={`edit-${key}`} className="text-sm">
                          {getStatLabel(key)}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Branch Stats Section */}
              <div className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium">Statistiques de succursale</h4>
                    <p className="text-sm text-gray-600">Données opérationnelles par succursale</p>
                  </div>
                  <Switch
                    checked={currentSetting.branchStatsEnabled ?? false}
                    onCheckedChange={(checked) => {
                      setCurrentSetting({
                        ...currentSetting,
                        branchStatsEnabled: checked
                      });
                    }}
                  />
                </div>

                {(currentSetting.branchStatsEnabled ?? false) && (
                  <div className="space-y-4 bg-gray-50 p-3 rounded">
                    {/* Branch Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">
                        Succursales sélectionnées
                      </label>
                      <MultiSelectBranches
                        options={availableBranches}
                        value={currentSetting.selectedBranches ?? []}
                        onChange={(branches) => {
                          setCurrentSetting({
                            ...currentSetting,
                            selectedBranches: branches
                          });
                        }}
                        placeholder="Sélectionner les succursales..."
                        loading={loadingBranches}
                      />
                    </div>

                    {/* Branch Stats Selection */}
                    <div>
                      <label className="text-sm font-medium mb-2 block">
                        Statistiques à inclure
                      </label>
                      <div className="space-y-2">
                        {Object.entries(currentSetting.branchStatsSelectedStats ?? {
                          reservationCount: true,
                          adultCount: true,
                          childCount: true,
                          allergies: true,
                          serviceTypes: true
                        }).map(([key, value]) => (
                          <div key={key} className="flex items-center space-x-2">
                            <Checkbox
                              id={`edit-branch-${key}`}
                              checked={value}
                              onCheckedChange={(checked) => {
                                const updatedBranchStats = {
                                  ...(currentSetting.branchStatsSelectedStats ?? {}),
                                  [key]: !!checked
                                };
                                setCurrentSetting({
                                  ...currentSetting,
                                  branchStatsSelectedStats: updatedBranchStats
                                });
                              }}
                            />
                            <label htmlFor={`edit-branch-${key}`} className="text-sm">
                              {getBranchStatLabel(key)}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Weekly Email Configuration */}
              <div className="border-t pt-4">
                <div className="flex items-center justify-between mb-3">
                  <label className="text-sm font-medium">Emails hebdomadaires</label>
                  <Switch
                    checked={currentSetting.weeklyEnabled}
                    onCheckedChange={(checked) => {
                      setCurrentSetting({
                        ...currentSetting,
                        weeklyEnabled: checked
                      });
                    }}
                  />
                </div>

                {currentSetting.weeklyEnabled && (
                  <div>
                    <label className="text-sm font-medium mb-3 block">Statistiques hebdomadaires à inclure</label>
                    <div className="space-y-2">
                      {Object.entries(currentSetting.weeklySelectedStats).map(([key, value]) => (
                        <div key={`edit-weekly-${key}`} className="flex items-center space-x-2">
                          <Checkbox
                            id={`edit-weekly-${key}`}
                            checked={value}
                            onCheckedChange={(checked) => {
                              const updatedWeeklyStats = {
                                ...currentSetting.weeklySelectedStats,
                                [key]: !!checked
                              };
                              setCurrentSetting({
                                ...currentSetting,
                                weeklySelectedStats: updatedWeeklyStats
                              });
                            }}
                          />
                          <label htmlFor={`edit-weekly-${key}`} className="text-sm">
                            {getStatLabel(key)}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Annuler
                </Button>
                <Button onClick={() => {
                  handleUpdateEmail(currentSetting._id, {
                    selectedStats: currentSetting.selectedStats,
                    weeklyEnabled: currentSetting.weeklyEnabled,
                    weeklySelectedStats: currentSetting.weeklySelectedStats,
                    regularStatsEnabled: currentSetting.regularStatsEnabled ?? true,
                    branchStatsEnabled: currentSetting.branchStatsEnabled ?? false,
                    selectedBranches: currentSetting.selectedBranches ?? [],
                    branchStatsSelectedStats: currentSetting.branchStatsSelectedStats ?? {
                      reservationCount: true,
                      adultCount: true,
                      childCount: true,
                      allergies: true,
                      serviceTypes: true
                    }
                  });
                  setIsEditDialogOpen(false);
                }}>
                  Sauvegarder
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Preview Dialog */}
      {currentSetting && (
        <EmailPreview
          emailSetting={currentSetting}
          isOpen={isPreviewDialogOpen}
          onClose={() => setIsPreviewDialogOpen(false)}
        />
      )}
    </Card>
    </div>
  );
};

// Email Preview Component
interface EmailPreviewProps {
  emailSetting: StatsEmailSetting;
  isOpen: boolean;
  onClose: () => void;
}

const EmailPreview: React.FC<EmailPreviewProps> = ({ emailSetting, isOpen, onClose }) => {
  const { toast } = useToast();
  const [previewHtml, setPreviewHtml] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop');

  // Ensure the email setting is migrated before use
  const migratedEmailSetting = migrateSettingsFormat([emailSetting])[0];

  const generatePreview = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin-dashboard/stats-email-preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          emailSettingId: migratedEmailSetting._id,
          useSampleData: true
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate preview');
      }

      const data = await response.json();
      setPreviewHtml(data.html);
    } catch (err) {
      console.error('Error generating preview:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const sendTestEmail = async () => {
    try {
      const response = await fetch('/api/admin-dashboard/stats-email-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          emailAddress: migratedEmailSetting.email,
          emailSettingId: migratedEmailSetting._id
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send test email');
      }

      toast({
        title: 'Succès',
        description: 'Email de test envoyé avec succès'
      });
    } catch (err) {
      console.error('Error sending test email:', err);
      toast({
        title: 'Erreur',
        description: 'Erreur lors de l\'envoi de l\'email de test',
        variant: 'destructive'
      });
    }
  };

  // Generate preview when dialog opens
  useEffect(() => {
    if (isOpen) {
      generatePreview();
    }
  }, [isOpen, emailSetting._id]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle>Aperçu Email - {migratedEmailSetting.email}</DialogTitle>
              <DialogDescription>
                Prévisualisation de l'email avec des données d'exemple
              </DialogDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPreviewMode(previewMode === 'desktop' ? 'mobile' : 'desktop')}
              >
                {previewMode === 'desktop' ? 'Mobile' : 'Desktop'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={sendTestEmail}
              >
                <Send className="h-4 w-4 mr-2" />
                Test
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={generatePreview} variant="outline">
                Réessayer
              </Button>
            </div>
          ) : (
            <div className={`border rounded-lg overflow-hidden ${
              previewMode === 'mobile' ? 'max-w-sm mx-auto' : 'w-full'
            }`}>
              <iframe
                srcDoc={previewHtml}
                className="w-full h-96"
                title="Email Preview"
                sandbox="allow-same-origin"
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Helper function to get stat labels
function getStatLabel(key: string): string {
  const labels: Record<string, string> = {
    totalSales: 'Total des ventes',
    totalReservations: 'Réservations (ancien format)', // For backward compatibility
    totalReservationsScheduled: 'Réservations programmées',
    totalReservationsCreated: 'Réservations créées',
    totalPresence: 'Total des présences',
    salesByBranch: 'Ventes par succursale',
    reservationsByBranch: 'Réservations par succursale',
    reservationsCreatedByBranch: 'Réservations créées par succursale',
    presenceByBranch: 'Présences par succursale',
    topSellers: 'Top 5 vendeurs (assigned_user_id)',
    topPaps: 'Top 5 PAPs (partnerId)'
  };
  return labels[key] || key;
}

// Helper function to get branch stat labels
function getBranchStatLabel(key: string): string {
  const labels: Record<string, string> = {
    reservationCount: 'Nombre de réservations',
    adultCount: 'Nombre d\'adultes',
    childCount: 'Nombre d\'enfants',
    allergies: 'Allergies',
    serviceTypes: 'Types de service (préparation alimentaire)'
  };
  return labels[key] || key;
}
