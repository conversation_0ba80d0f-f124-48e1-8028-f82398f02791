'use client';

import { useLanguage } from '@/lib/contexts/language-context';

interface DateRange {
  from: string;
  to: string;
}

interface CustomRangePickerProps {
  show: boolean;
  onClose: () => void;
  dateRange: DateRange;
  onApply: (dateRange: DateRange) => void;
}

export function CustomRangePicker({
  show,
  onClose,
  dateRange,
  onApply
}: CustomRangePickerProps) {
  const { t } = useLanguage();

  if (!show) return null;

  const handleFromDateChange = (value: string) => {
    onApply({
      ...dateRange,
      from: value
    });
  };

  const handleToDateChange = (value: string) => {
    onApply({
      ...dateRange,
      to: value
    });
  };

  const handleApply = () => {
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className="text-lg font-medium mb-4">{t('newDashboard.selectCustomDateRange')}</h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('newDashboard.fromDate')}
            </label>
            <input
              type="date"
              className="w-full p-2 border rounded"
              value={dateRange.from}
              onChange={(e) => handleFromDateChange(e.target.value)}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('newDashboard.toDate')}
            </label>
            <input
              type="date"
              className="w-full p-2 border rounded"
              value={dateRange.to}
              onChange={(e) => handleToDateChange(e.target.value)}
            />
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border rounded hover:bg-gray-50"
          >
            {t('common.cancel')}
          </button>
          <button
            onClick={handleApply}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            {t('newDashboard.apply')}
          </button>
        </div>
      </div>
    </div>
  );
}
