import React from 'react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/lib/contexts/language-context';

export type Timeframe = 'today' | 'month' | 'year';

interface TimeframeToggleProps {
  value: Timeframe;
  onChange: (val: Timeframe) => void;
}

export const TimeframeToggle: React.FC<TimeframeToggleProps> = ({ value, onChange }) => {
  const { t } = useLanguage();
  return (
    <div className="flex flex-wrap justify-center gap-1">
      <Button
        variant={value === 'today' ? 'default' : 'outline'}
        size="sm"
        onClick={() => onChange('today')}
        aria-pressed={value === 'today'}
        className="min-w-[4.5rem] px-2 whitespace-nowrap"
      >
        {t('common.today')}
      </Button>
      <Button
        variant={value === 'month' ? 'default' : 'outline'}
        size="sm"
        onClick={() => onChange('month')}
        aria-pressed={value === 'month'}
        className="min-w-[4.5rem] px-2 whitespace-nowrap"
      >
        {t('common.thisMonth')}
      </Button>
      <Button
        variant={value === 'year' ? 'default' : 'outline'}
        size="sm"
        onClick={() => onChange('year')}
        aria-pressed={value === 'year'}
        className="min-w-[4.5rem] px-2 whitespace-nowrap"
      >
        {t('common.thisYear')}
      </Button>
    </div>
  );
}; 