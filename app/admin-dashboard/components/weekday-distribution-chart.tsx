import React, { useMemo } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/lib/contexts/language-context';
import { ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Legend, Tooltip } from 'recharts';
import { useWeekdayDistributionStats, WeekdayDistributionStat } from '../hooks/useWeekdayDistributionStats';
import { DateRange } from 'react-day-picker';

// Utility function for local date formatting
function formatDateLocal(date: Date | undefined): string {
  if (!date) return '';
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

interface WeekdayDistributionChartProps {
  branchId: string | string[] | null;
  dateRange?: DateRange;
}

export const WeekdayDistributionChart: React.FC<WeekdayDistributionChartProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();
  
  const startDate = dateRange?.from ? formatDateLocal(dateRange.from) : undefined;
  const endDate = dateRange?.to ? formatDateLocal(dateRange.to) : undefined;

  const { stats, loading, error, refresh } = useWeekdayDistributionStats(
    branchId,
    startDate,
    endDate
  );

  // Function to handle retry button click
  const handleRetry = () => {
    refresh();
  };

  // Prepare formatted data for the radar chart
  const chartData = useMemo(() => {
    return stats.map((day: WeekdayDistributionStat) => ({
      ...day,
      dayLabel: t(`common.days.${day.dayName.toLowerCase()}`) || day.dayNameFr,
      // Convert number values to a more appropriate scale if needed
      // and add custom metrics for the radar chart
      reservations: day.count,
      sales: day.sales,
      presence: day.presence,
      // Calculate conversion rate (sales/presence) if presence > 0
      conversionRate: day.presence > 0 
        ? Math.round((day.sales / day.presence) * 100)
        : 0
    }));
  }, [stats, t]);

  // Custom tooltip for the radar chart
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow border border-gray-200 dark:border-gray-700 text-sm">
          <p className="font-semibold mb-1">{data.dayLabel}</p>
          <div className="grid grid-cols-2 gap-x-2 gap-y-1">
            <span className="text-blue-600 dark:text-blue-400">{t('dashboard.reservations')}:</span>
            <span className="font-medium">{data.reservations}</span>
            
            <span className="text-green-600 dark:text-green-400">{t('dashboard.sales')}:</span>
            <span className="font-medium">{data.sales}</span>
            
            <span className="text-orange-600 dark:text-orange-400">{t('dashboard.presence')}:</span>
            <span className="font-medium">{data.presence}</span>
            
            <span className="text-purple-600 dark:text-purple-400">{t('dashboard.conversionRate')}:</span>
            <span className="font-medium">{data.conversionRate}%</span>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="bg-gradient-to-br from-indigo-100 to-indigo-300 dark:from-indigo-900 dark:to-indigo-700 text-indigo-900 dark:text-indigo-100 shadow-lg border-0 relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z" 
            />
          </svg>
          <span>{t('dashboard.weekdayDistribution') || 'Répartition par jour de la semaine'}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-[350px]">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-[350px]">
            <span className="text-red-600 dark:text-red-400 mb-2">{error}</span>
            <Button variant="outline" size="sm" onClick={handleRetry}>
              {t('common.retry')}
            </Button>
          </div>
        ) : chartData.length === 0 ? (
          <div className="flex items-center justify-center h-[350px]">
            <span className="text-gray-500 dark:text-gray-400">{t('common.noData')}</span>
          </div>
        ) : (
          <div className="h-[350px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <RadarChart
                cx="50%"
                cy="50%"
                outerRadius="80%"
                data={chartData}
              >
                <PolarGrid stroke="#9ca3af" strokeDasharray="3 3" strokeWidth={1.5} />
                <PolarAngleAxis 
                  dataKey="dayLabel" 
                  tick={{ fill: 'currentColor', fontSize: 12 }}
                />
                <PolarRadiusAxis 
                  angle={90} 
                  domain={[0, 'auto']} 
                  tick={{ fill: 'currentColor', fontSize: 10 }}
                />
                <Radar
                  name={t('dashboard.reservations') || 'Réservations'}
                  dataKey="reservations"
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.5}
                />
                <Radar
                  name={t('dashboard.sales') || 'Ventes'}
                  dataKey="sales"
                  stroke="#10b981"
                  fill="#10b981"
                  fillOpacity={0.5}
                />
                <Radar
                  name={t('dashboard.presence') || 'Présence'}
                  dataKey="presence"
                  stroke="#f97316"
                  fill="#f97316"
                  fillOpacity={0.5}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
              </RadarChart>
            </ResponsiveContainer>
          </div>
        )}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-2 p-2 bg-indigo-50 dark:bg-indigo-950 text-xs rounded border border-indigo-200 dark:border-indigo-800">
            <strong>DEBUG:</strong> 
            <br />
            <div>Date range: {startDate || 'not set'} to {endDate || 'not set'}</div>
            <div>Branch ID: {branchId}</div>
            <div>Data points: {stats.length}</div>
            <pre className="mt-1 overflow-auto max-h-[100px]">
              {JSON.stringify(stats, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 