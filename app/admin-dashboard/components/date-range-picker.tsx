'use client';

import * as React from 'react';
import { useState } from 'react';
import { DateRange } from 'react-day-picker';
import { useLanguage } from '@/lib/contexts/language-context';
import { cn } from '@/lib/utils';
import { getDateRange, getActiveQuickSelect } from '@/components/shared/stats/date-utils';
import { CustomRangePicker } from './custom-range-picker';

interface DateRangePickerProps {
  value: DateRange;
  onChange: (range: DateRange) => void;
  className?: string;
}

export const DateRangePicker: React.FC<DateRangePickerProps> = ({ value, onChange, className }) => {
  const { t } = useLanguage();
  const [showCustomRangePicker, setShowCustomRangePicker] = useState(false);
  const [lastClickedQuickSelect, setLastClickedQuickSelect] = useState<string | undefined>();

  // Convert DateRange to string format for the new picker
  const dateRangeString = React.useMemo(() => {
    const fromStr = value?.from ? value.from.toISOString().split('T')[0] : '';
    const toStr = value?.to ? value.to.toISOString().split('T')[0] : '';
    return { from: fromStr, to: toStr };
  }, [value]);



  const handleQuickDateSelect = (type: string) => {
    const range = getDateRange(type);
    const fromDate = range.from ? new Date(range.from) : undefined;
    const toDate = range.to ? new Date(range.to) : undefined;
    setLastClickedQuickSelect(type);
    onChange({ from: fromDate, to: toDate });
  };

  const handleCustomRangeApply = (customRange: { from: string; to: string }) => {
    const fromDate = customRange.from ? new Date(customRange.from) : undefined;
    const toDate = customRange.to ? new Date(customRange.to) : undefined;

    // Clear the last clicked quick select since this is a custom range
    setLastClickedQuickSelect(undefined);
    onChange({ from: fromDate, to: toDate });
  };

  // Get the active quick select option
  const activeQuickSelect = getActiveQuickSelect(dateRangeString, lastClickedQuickSelect);

  // Format display text for date range
  const displayText = value?.from && value?.to
    ? `${value.from.toISOString().split('T')[0]} - ${value.to.toISOString().split('T')[0]}`
    : t('newDashboard.selectDateRange') || 'Select date range';

  return (
    <div className={cn("flex flex-col sm:flex-row sm:items-center gap-3", className)}>
      {/* Date Range Display Field */}
      <button
        onClick={() => setShowCustomRangePicker(true)}
        className="py-2 px-3 border rounded cursor-pointer bg-white hover:bg-gray-50 min-w-[200px] w-full sm:w-auto text-left text-sm"
      >
        {displayText}
      </button>

      {/* Quick Select Buttons */}
      <div className="flex flex-wrap gap-1.5">
        {[
          { key: 'today', label: t('newDashboard.today') || 'Today' },
          { key: 'yesterday', label: t('newDashboard.yesterday') || 'Yesterday' },
          { key: 'tomorrow', label: t('newDashboard.tomorrow') || 'Tomorrow' },
          { key: 'thisweek', label: t('newDashboard.thisWeek') || 'This Week' },
          { key: 'last7days', label: t('newDashboard.last7Days') || 'Last 7 Days' },
          { key: 'last30days', label: t('newDashboard.last30Days') || 'Last 30 Days' },
          { key: 'thismonth', label: t('newDashboard.thisMonth') || 'This Month' },
          { key: 'lastmonth', label: t('newDashboard.lastMonth') || 'Last Month' },
        ].map(({ key, label }) => (
          <button
            key={key}
            onClick={() => handleQuickDateSelect(key)}
            className={`px-0.5 sm:px-1.5 py-2 text-xs sm:text-sm rounded transition-colors whitespace-nowrap ${
              activeQuickSelect === key
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {label}
          </button>
        ))}
      </div>

      {/* Custom Range Picker Modal */}
      <CustomRangePicker
        show={showCustomRangePicker}
        onClose={() => setShowCustomRangePicker(false)}
        dateRange={dateRangeString}
        onApply={handleCustomRangeApply}
      />
    </div>
  );
};
