'use client';

import { useEffect, useState } from 'react';
import { useLanguage } from '@/lib/contexts/language-context';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';
import { usePermissions } from '@/hooks/use-permissions';
import { useAppSelector } from '@/lib/redux/hooks';
import * as RoleUtils from '@/lib/utils/role-utils';
import { dashboardFetcher } from '../utils/dashboard-fetcher';
import { ChevronDown, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Branch {
  _id: string;
  name: string;
  phone: string;
  responsible: string[];
  agents: string[];
  sellers: string[];
}

interface BranchSelectorProps {
  onChange: (branchId: string | string[]) => void;
  currentBranchId: string | string[] | null;
  multiselect?: boolean;
}

export function BranchSelector({ onChange, currentBranchId, multiselect = false }: BranchSelectorProps) {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const { t } = useLanguage();
  const { isLoading: permissionsLoading } = usePermissions();
  const roles = useAppSelector((state: any) => state.permissions.roles);
  const userId = useAppSelector((state: any) => state.permissions.userId) || '';

  // Check if user is SuperAdmin
  const isSuperAdmin = RoleUtils.isSuperAdmin(roles);

  // Helper functions for multiselect
  const getSelectedBranches = (): string[] => {
    if (!currentBranchId) return [];
    if (Array.isArray(currentBranchId)) return currentBranchId;
    return currentBranchId === 'all' ? [] : [currentBranchId];
  };

  const handleBranchToggle = (branchId: string) => {
    if (!multiselect) {
      onChange(branchId);
      return;
    }

    const selected = getSelectedBranches();
    if (branchId === 'all') {
      // Toggle all branches
      if (selected.length === branches.length) {
        onChange([]);
      } else {
        onChange(branches.map(b => b._id));
      }
    } else {
      // Toggle individual branch
      if (selected.includes(branchId)) {
        onChange(selected.filter(id => id !== branchId));
      } else {
        onChange([...selected, branchId]);
      }
    }
  };

  const removeBranch = (branchId: string) => {
    if (!multiselect) return;
    const selected = getSelectedBranches();
    onChange(selected.filter(id => id !== branchId));
  };

  useEffect(() => {
    async function fetchBranches() {
      try {
        setLoading(true);
        // Fetch branches
        const data = await dashboardFetcher('/api/branches');
        setBranches(data);
        // If we have branches but no currentBranchId, set the first branch as default
        if (data.length > 0 && !currentBranchId) {
          // For regular users, find their branch
          if (!isSuperAdmin) {
            const userBranches = data.filter((branch: Branch) => {
              return branch.responsible?.some((user: any) => user._id === userId);
            });
            if (userBranches.length > 0) {
              // Set to 'all' by default for branch admins with multiple branches
              if (userBranches.length > 1) {
                onChange(multiselect ? userBranches.map((b: Branch) => b._id) : 'all');
              } else {
                onChange(multiselect ? [userBranches[0]._id] : userBranches[0]._id);
              }
            } else if (data[0]) {
              // Fallback to first branch if user's branch not found
              onChange(multiselect ? [data[0]._id] : data[0]._id);
            }
          } else {
            // For SuperAdmin, set 'all' as default
            onChange(multiselect ? data.map((b: Branch) => b._id) : 'all');
          }
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
        setError(error instanceof Error ? error.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    }
    fetchBranches();
  }, [onChange, currentBranchId, isSuperAdmin, userId, multiselect]);

  if (permissionsLoading || loading) {
    return <Skeleton className="h-10 w-[200px]" />;
  }

  if (error) {
    return <div className="text-red-500 text-sm">{error}</div>;
  }

  // Get branches the user is responsible for
  const userBranches = branches.filter((branch) =>
    branch.responsible?.some((user: any) => user._id === userId)
  ).sort((a, b) => {
    // Sort branches with names starting with "*" to the bottom
    const aStartsWithStar = a.name.startsWith('*');
    const bStartsWithStar = b.name.startsWith('*');

    if (aStartsWithStar && !bStartsWithStar) return 1;
    if (!aStartsWithStar && bStartsWithStar) return -1;

    // If both start with "*" or both don't, sort alphabetically
    return a.name.localeCompare(b.name);
  });

  // If user is not SuperAdmin, show appropriate selector based on branch responsibility
  if (!isSuperAdmin) {
    // If user is responsible for only one branch, show it as static text
    if (userBranches.length === 1) {
      return (
        <div className="text-sm text-gray-500 flex items-center gap-2">
          <span>{t('common.branch')}:</span>
          <span className="font-medium">{userBranches[0].name}</span>
        </div>
      );
    }
    // If user is responsible for multiple branches, show selector with ALL option
    else if (userBranches.length > 1) {
      if (multiselect) {
        const selectedBranches = getSelectedBranches();
        const selectedBranchNames = selectedBranches.map(id =>
          userBranches.find(b => b._id === id)?.name || id
        );

        return (
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 w-full sm:w-auto">
            <span className="text-sm text-gray-500 flex-shrink-0">{t('common.branch')}:</span>
            <Popover open={isOpen} onOpenChange={setIsOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={isOpen}
                  className="w-full sm:w-[300px] min-w-[200px] justify-between"
                >
                  <div className="flex flex-wrap gap-1 flex-1 min-w-0">
                    {selectedBranches.length === 0 ? (
                      <span className="text-muted-foreground">{t('conversations.selectBranch')}</span>
                    ) : selectedBranches.length === userBranches.length ? (
                      <span>{t('common.all')}</span>
                    ) : (
                      selectedBranchNames.slice(0, 2).map((name, index) => (
                        <Badge key={index} variant="secondary" className="flex items-center gap-1">
                          {name}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={(e) => {
                              e.stopPropagation();
                              const branchId = selectedBranches[index];
                              removeBranch(branchId);
                            }}
                          />
                        </Badge>
                      ))
                    )}
                    {selectedBranches.length > 2 && (
                      <Badge variant="secondary">+{selectedBranches.length - 2}</Badge>
                    )}
                  </div>
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] sm:w-[300px] p-0">
                <div className="p-2 space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      checked={selectedBranches.length === userBranches.length}
                      onCheckedChange={() => handleBranchToggle('all')}
                    />
                    <span className="text-sm font-medium">{t('common.all')}</span>
                  </div>
                  {userBranches.map((branch) => (
                    <div key={branch._id} className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedBranches.includes(branch._id)}
                        onCheckedChange={() => handleBranchToggle(branch._id)}
                      />
                      <span className="text-sm">{branch.name}</span>
                    </div>
                  ))}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        );
      } else {
        return (
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 w-full sm:w-auto">
            <span className="text-sm text-gray-500 flex-shrink-0">{t('common.branch')}:</span>
            <Select
              value={typeof currentBranchId === 'string' ? currentBranchId : undefined}
              onValueChange={(value) => onChange(value)}
            >
              <SelectTrigger className="w-full sm:w-[200px] min-w-[160px]">
                <SelectValue placeholder={t('conversations.selectBranch')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('common.all')}</SelectItem>
                {userBranches.map((branch) => (
                  <SelectItem key={branch._id} value={branch._id}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        );
      }
    }
    // If user is not responsible for any branch, show nothing
    return null;
  }

  // SuperAdmin case
  if (multiselect) {
    const selectedBranches = getSelectedBranches();
    const selectedBranchNames = selectedBranches.map(id =>
      branches.find(b => b._id === id)?.name || id
    );

    return (
      <div className="flex flex-col sm:flex-row sm:items-center gap-2 w-full sm:w-auto">
        <span className="text-sm text-gray-500 flex-shrink-0">{t('common.branch')}:</span>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={isOpen}
              className="w-full sm:w-[300px] min-w-[200px] justify-between"
            >
              <div className="flex flex-wrap gap-1 flex-1 min-w-0">
                {selectedBranches.length === 0 ? (
                  <span className="text-muted-foreground">{t('conversations.selectBranch')}</span>
                ) : selectedBranches.length === branches.length ? (
                  <span>{t('common.all')}</span>
                ) : (
                  selectedBranchNames.slice(0, 2).map((name, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {name}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          const branchId = selectedBranches[index];
                          removeBranch(branchId);
                        }}
                      />
                    </Badge>
                  ))
                )}
                {selectedBranches.length > 2 && (
                  <Badge variant="secondary">+{selectedBranches.length - 2}</Badge>
                )}
              </div>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] sm:w-[300px] p-0">
            <div className="p-2 space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={selectedBranches.length === branches.length}
                  onCheckedChange={() => handleBranchToggle('all')}
                />
                <span className="text-sm font-medium">{t('common.all')}</span>
              </div>
              {branches.sort((a, b) => {
                // Sort branches with names starting with "*" to the bottom
                const aStartsWithStar = a.name.startsWith('*');
                const bStartsWithStar = b.name.startsWith('*');

                if (aStartsWithStar && !bStartsWithStar) return 1;
                if (!aStartsWithStar && bStartsWithStar) return -1;

                // If both start with "*" or both don't, sort alphabetically
                return a.name.localeCompare(b.name);
              }).map((branch) => (
                <div key={branch._id} className="flex items-center space-x-2">
                  <Checkbox
                    checked={selectedBranches.includes(branch._id)}
                    onCheckedChange={() => handleBranchToggle(branch._id)}
                  />
                  <span className="text-sm">{branch.name}</span>
                </div>
              ))}
            </div>
          </PopoverContent>
        </Popover>
      </div>
    );
  }

  return (
    <div className="flex flex-col sm:flex-row sm:items-center gap-2 w-full sm:w-auto">
      <span className="text-sm text-gray-500 flex-shrink-0">{t('common.branch')}:</span>
      <Select
        value={typeof currentBranchId === 'string' ? currentBranchId : undefined}
        onValueChange={(value) => onChange(value)}
      >
        <SelectTrigger className="w-full sm:w-[200px] min-w-[160px]">
          <SelectValue placeholder={t('conversations.selectBranch')} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t('common.all')}</SelectItem>
          {branches.sort((a, b) => {
            // Sort branches with names starting with "*" to the bottom
            const aStartsWithStar = a.name.startsWith('*');
            const bStartsWithStar = b.name.startsWith('*');

            if (aStartsWithStar && !bStartsWithStar) return 1;
            if (!aStartsWithStar && bStartsWithStar) return -1;

            // If both start with "*" or both don't, sort alphabetically
            return a.name.localeCompare(b.name);
          }).map((branch) => (
            <SelectItem key={branch._id} value={branch._id}>
              {branch.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}