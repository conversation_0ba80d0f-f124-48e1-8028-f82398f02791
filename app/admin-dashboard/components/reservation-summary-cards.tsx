import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { useReservationSummaryStats } from '../hooks/useReservationSummaryStats';
import { useTodayReservationsStats } from '../hooks/useTodayReservationsStats';
import { useLanguage } from '@/lib/contexts/language-context';
import { DateRange } from 'react-day-picker';

interface ReservationSummaryCardsProps {
  branchId: string | string[] | null;
  dateRange: DateRange;
}

const cardStyles = [
  'bg-gradient-to-br from-blue-100 to-blue-300 dark:from-blue-900 dark:to-blue-700 text-blue-900 dark:text-blue-100',
  'bg-gradient-to-br from-green-100 to-green-300 dark:from-green-900 dark:to-green-700 text-green-900 dark:text-green-100',
  'bg-gradient-to-br from-red-100 to-red-300 dark:from-red-900 dark:to-red-700 text-red-900 dark:text-red-100',
  'bg-gradient-to-br from-yellow-100 to-yellow-300 dark:from-yellow-900 dark:to-yellow-700 text-yellow-900 dark:text-yellow-100',
  'bg-gradient-to-br from-purple-100 to-purple-300 dark:from-purple-900 dark:to-purple-700 text-purple-900 dark:text-purple-100',
];

const icons = [
  (
    <svg key="users" className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87m9-7a4 4 0 11-8 0 4 4 0 018 0zm6 8v2a2 2 0 01-2 2h-4a2 2 0 01-2-2v-2a2 2 0 012-2h4a2 2 0 012 2zM7 20v-2a2 2 0 012-2h4a2 2 0 012 2v2" />
    </svg>
  ),
  (
    <svg key="sales" className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 1.343-3 3 0 1.657 1.343 3 3 3s3-1.343 3-3c0-1.657-1.343-3-3-3zm0 0V4m0 7v7m0 0h4m-4 0H8" />
    </svg>
  ),
  (
    <svg key="cancelled" className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  ),
  (
    <svg key="avg" className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" d="M3 17a4 4 0 004 4h10a4 4 0 004-4V7a4 4 0 00-4-4H7a4 4 0 00-4 4v10zm4-4h10" />
    </svg>
  ),
  (
    <svg key="today" className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
  ),
];

// Utility function for date formatting (using UTC to avoid timezone issues)
function formatDateLocal(date: Date | undefined): string | undefined {
  if (!date) return undefined;
  // Use UTC methods to avoid timezone conversion issues
  const year = date.getUTCFullYear();
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export const ReservationSummaryCards: React.FC<ReservationSummaryCardsProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();

  const stats = useReservationSummaryStats(branchId, {
    startDate: formatDateLocal(dateRange.from),
    endDate: formatDateLocal(dateRange.to),
  });

  const todayStats = useTodayReservationsStats(branchId);

  if (stats.loading || todayStats.loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        {[1, 2, 3, 4, 5].map(i => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <CardTitle className="h-6 bg-gray-200 rounded w-2/3 mb-2" />
            </CardHeader>
            <CardContent>
              <div className="h-10 bg-gray-100 rounded w-1/2" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (stats.error || todayStats.error) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <Card className="col-span-5 border-red-400">
          <CardHeader>
            <CardTitle className="text-red-600">{t('common.error')}</CardTitle>
          </CardHeader>
          <CardContent>
            <span className="text-red-500">{stats.error || todayStats.error}</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  const cardData = [
    {
      title: t('dashboard.totalReservations'),
      value: stats.totalReservations,
      icon: icons[0],
      style: cardStyles[0],
    },
    {
      title: t('dashboard.totalSales'),
      value: stats.totalSales.toLocaleString(undefined, { style: 'currency', currency: 'CAD' }),
      icon: icons[1],
      style: cardStyles[1],
    },
    {
      title: t('dashboard.cancelledSales'),
      value: stats.cancelledSales.toLocaleString(undefined, { style: 'currency', currency: 'CAD' }),
      icon: icons[2],
      style: cardStyles[2],
    },
    {
      title: t('dashboard.averageSale'),
      value: stats.averageSale.toLocaleString(undefined, { style: 'currency', currency: 'CAD' }),
      icon: icons[3],
      style: cardStyles[3],
    },
    {
      title: t('dashboard.todayReservations') || 'Réservations créées aujourd\'hui',
      value: todayStats.todayReservations,
      icon: icons[4],
      style: cardStyles[4],
    },
  ];

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        {cardData.map((card, idx) => (
          <Card key={card.title} className={`${card.style} shadow-lg border-0 relative overflow-hidden`}>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                {card.icon}
                <span>{card.title}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <span className="text-4xl font-extrabold block mt-2 mb-1">{card.value}</span>
            </CardContent>
          </Card>
        ))}
      </div>
      {process.env.NODE_ENV === 'development' && (
        <Card className="mb-6 border-dashed border-2 border-blue-300">
          <CardHeader>
            <CardTitle>DEBUG: ReservationSummaryStats</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs text-gray-600 whitespace-pre-wrap">{JSON.stringify(stats, null, 2)}</pre>
          </CardContent>
        </Card>
      )}
    </>
  );
}; 