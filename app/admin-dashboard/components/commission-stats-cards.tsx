import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent } from '@/components/ui/card';
import { useCommissionStats } from '../hooks/useCommissionStats';
import { DateRange } from 'react-day-picker';
import { useLanguage } from '@/lib/contexts/language-context';

interface CommissionStatsCardsProps {
  branchId?: string | string[] | null;
  dateRange?: DateRange;
}

const cardStyles = [
  'bg-gradient-to-br from-purple-100 to-purple-300 dark:from-purple-900 dark:to-purple-700 text-purple-900 dark:text-purple-100',
  'bg-gradient-to-br from-green-100 to-green-300 dark:from-green-900 dark:to-green-700 text-green-900 dark:text-green-100',
  'bg-gradient-to-br from-yellow-100 to-yellow-300 dark:from-yellow-900 dark:to-yellow-700 text-yellow-900 dark:text-yellow-100',
];

const icons = [
  // Total commissions
  <svg key="commissions" className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 1.343-3 3 0 1.657 1.343 3 3 3s3-1.343 3-3c0-1.657-1.343-3-3-3zm0 0V4m0 7v7m0 0h4m-4 0H8" />
  </svg>,
  // Total amount
  <svg key="amount" className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" d="M17 9V7a5 5 0 00-10 0v2a5 5 0 0010 0zm-2 7a2 2 0 11-4 0" />
  </svg>,
  // Top user
  <svg key="topuser" className="w-8 h-8" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 14l9-5-9-5-9 5 9 5zm0 7v-6m0 0l-9-5m9 5l9-5" />
  </svg>,
];

// Color palette for branches (repeat if more branches)
const BRANCH_COLORS = [
  'bg-blue-500',
  'bg-green-500',
  'bg-pink-500',
  'bg-yellow-500',
  'bg-purple-500',
  'bg-teal-500',
  'bg-orange-500',
  'bg-red-500',
  'bg-indigo-500',
  'bg-cyan-500',
];

// Utility function for date formatting (using UTC to avoid timezone issues)
function formatDateLocal(date: Date | undefined): string {
  if (!date) return '';
  // Use UTC methods to avoid timezone conversion issues
  const year = date.getUTCFullYear();
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export const CommissionStatsCards: React.FC<CommissionStatsCardsProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();

  // Use provided dateRange or default to today
  const today = new Date();
  const defaultStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const defaultEnd = new Date(defaultStart);
  const range = dateRange || { from: defaultStart, to: defaultEnd };

  const start = range.from ? formatDateLocal(range.from) : '';
  const end = range.to ? formatDateLocal(range.to) : '';

  const { global, branches, loading, error } = useCommissionStats(start, end, branchId);

  // Map branchId to color
  const branchColorMap = branches.reduce((acc, b, idx) => {
    acc[b.branchId] = BRANCH_COLORS[idx % BRANCH_COLORS.length];
    return acc;
  }, {} as Record<string, string>);

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
        {[1, 2, 3].map(i => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <CardTitle className="h-6 bg-gray-200 rounded w-2/3 mb-2" />
            </CardHeader>
            <CardContent>
              <div className="h-10 bg-gray-100 rounded w-1/2" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
        <Card className="col-span-3 border-red-400">
          <CardHeader>
            <CardTitle className="text-red-600">{t('common.error')}</CardTitle>
          </CardHeader>
          <CardContent>
            <span className="text-red-500">{error}</span>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If all branches, show global + per-branch breakdown
  if (!branchId || branchId === 'all') {
    const cardData = [
      {
        title: t('dashboard.totalCommissions'),
        value: global?.totalCommissions ?? 0,
        icon: icons[0],
        style: cardStyles[0],
        breakdown: branches.map(b => ({
          label: b.branchName,
          value: b.totalCommissions,
          color: branchColorMap[b.branchId],
        })),
      },
      {
        title: t('dashboard.totalCommissionAmount'),
        value: (global?.totalAmount ?? 0).toLocaleString(undefined, { style: 'currency', currency: 'CAD' }),
        icon: icons[1],
        style: cardStyles[1],
        breakdown: branches.map(b => ({
          label: b.branchName,
          value: b.totalAmount.toLocaleString(undefined, { style: 'currency', currency: 'CAD' }),
          color: branchColorMap[b.branchId],
        })),
      },
      {
        title: t('dashboard.topCommissionUser'),
        value: global?.topUser ? `${global.topUser.name} (${global.topUser.total.toLocaleString(undefined, { style: 'currency', currency: 'CAD' })})` : t('dashboard.noData'),
        icon: icons[2],
        style: cardStyles[2],
        breakdown: branches.map(b => ({
          label: b.branchName,
          value: b.topUser ? `${b.topUser.name} (${b.topUser.total.toLocaleString(undefined, { style: 'currency', currency: 'CAD' })})` : t('dashboard.noData'),
          color: branchColorMap[b.branchId],
        })),
      },
    ];

    return (
      <>
        <div className="mb-2">
          <h2 className="text-xl font-bold">{t('dashboard.commissionStats')}</h2>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
          {cardData.map((card, _idx) => (
            <Card key={card.title} className={`${card.style} shadow-lg border-0 relative overflow-hidden`}>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  {card.icon}
                  <span>{card.title}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <span className="text-4xl font-extrabold block mt-2 mb-1">{card.value}</span>
                {/* Per-branch breakdown */}
                <div className="mt-3 flex flex-col gap-1">
                  {card.breakdown.map((b, _i) => (
                    <div key={b.label} className="flex items-center gap-2 text-sm">
                      <span className={`inline-block w-3 h-3 rounded-full ${b.color}`} />
                      <span className="truncate max-w-[8rem]" title={b.label}>{b.label}</span>
                      <span className="ml-auto font-semibold">{b.value}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        {/* Branch legend */}
        {branches.length > 0 && (
          <div className="flex flex-wrap gap-3 mb-4 items-center">
            <span className="text-xs text-gray-500">{t('dashboard.branchLegend') || 'Branch Legend'}:</span>
            {branches.map(b => (
              <span key={b.branchId} className="flex items-center gap-1 text-xs">
                <span className={`inline-block w-3 h-3 rounded-full ${branchColorMap[b.branchId]}`} />
                <span>{b.branchName}</span>
              </span>
            ))}
          </div>
        )}
        {process.env.NODE_ENV === 'development' && (
          <Card className="mb-6 border-dashed border-2 border-purple-300">
            <CardHeader>
              <CardTitle>DEBUG: CommissionStats</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-xs text-gray-600 whitespace-pre-wrap">{JSON.stringify({ global, branches }, null, 2)}</pre>
            </CardContent>
          </Card>
        )}
      </>
    );
  }

  // Single branch: show only that branch's stats
  const branch = branches[0] || global;
  const cardData = [
    {
      title: t('dashboard.totalCommissions'),
      value: branch?.totalCommissions ?? 0,
      icon: icons[0],
      style: cardStyles[0],
    },
    {
      title: t('dashboard.totalCommissionAmount'),
      value: (branch?.totalAmount ?? 0).toLocaleString(undefined, { style: 'currency', currency: 'CAD' }),
      icon: icons[1],
      style: cardStyles[1],
    },
    {
      title: t('dashboard.topCommissionUser'),
      value: branch?.topUser ? `${branch.topUser.name} (${branch.topUser.total.toLocaleString(undefined, { style: 'currency', currency: 'CAD' })})` : t('dashboard.noData'),
      icon: icons[2],
      style: cardStyles[2],
    },
  ];

  return (
    <>
      <div className="mb-2">
        <h2 className="text-xl font-bold">{t('dashboard.commissionStats')}</h2>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
        {cardData.map((card, _idx) => (
          <Card key={card.title} className={`${card.style} shadow-lg border-0 relative overflow-hidden`}>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                {card.icon}
                <span>{card.title}</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <span className="text-4xl font-extrabold block mt-2 mb-1">{card.value}</span>
            </CardContent>
          </Card>
        ))}
      </div>
      {process.env.NODE_ENV === 'development' && (
        <Card className="mb-6 border-dashed border-2 border-purple-300">
          <CardHeader>
            <CardTitle>DEBUG: CommissionStats</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs text-gray-600 whitespace-pre-wrap">{JSON.stringify({ global, branches }, null, 2)}</pre>
          </CardContent>
        </Card>
      )}
    </>
  );
}; 