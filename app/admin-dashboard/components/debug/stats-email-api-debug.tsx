'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, CheckCircle, Clock, Zap } from 'lucide-react';

interface EndpointTestResult {
  endpoint: string;
  method: string;
  status: number;
  responseTime: number;
  success: boolean;
  error?: string;
  data?: any;
}

interface PerformanceMetrics {
  averageResponseTime: number;
  totalRequests: number;
  successRate: number;
  errorCount: number;
}

interface TestResults {
  endpoints: EndpointTestResult[];
  performance: PerformanceMetrics;
  errors: string[];
}

export default function StatsEmailApiDebug() {
  const [testResults, setTestResults] = useState<TestResults | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testDate, setTestDate] = useState(() => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    // Use local timezone to avoid date shifts
    const year = yesterday.getFullYear();
    const month = String(yesterday.getMonth() + 1).padStart(2, '0');
    const day = String(yesterday.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  });

  const [useCurrentMonth, setUseCurrentMonth] = useState(true);
  const [selectedStats, setSelectedStats] = useState({
    totalSales: true,
    totalReservations: true,
    totalPresence: true,
    salesByBranch: true,
    reservationsByBranch: true,
    presenceByBranch: true,
    topSellers: true,
    topPaps: true
  });

  const runApiComparison = async () => {
    setIsLoading(true);
    const results: EndpointTestResult[] = [];
    const errors: string[] = [];

    try {
      // Test dashboard reservation-stats API (what the cards use)
      const dashboardStartTime = Date.now();
      try {
        const dashboardUrl = `/api/admin-dashboard/reservation-stats?branchId=all&startDate=${testDate}&endDate=${testDate}`;
        console.log('Dashboard API URL:', dashboardUrl);
        const dashboardResponse = await fetch(dashboardUrl);
        const dashboardResponseTime = Date.now() - dashboardStartTime;
        const dashboardData = await dashboardResponse.json();

        results.push({
          endpoint: '/api/admin-dashboard/reservation-stats (Dashboard Cards)',
          method: 'GET',
          status: dashboardResponse.status,
          responseTime: dashboardResponseTime,
          success: dashboardResponse.ok,
          data: dashboardData,
          error: !dashboardResponse.ok ? dashboardData.error : undefined
        });
      } catch (error) {
        errors.push(`Dashboard API error: ${error}`);
        results.push({
          endpoint: '/api/admin-dashboard/reservation-stats (Dashboard Cards)',
          method: 'GET',
          status: 0,
          responseTime: Date.now() - dashboardStartTime,
          success: false,
          error: `Network error: ${error}`
        });
      }

      // Test daily stats aggregation API
      const statsStartTime = Date.now();
      try {
        const statsUrl = `/api/admin-dashboard/daily-stats-aggregation?date=${testDate}&startDate=${testDate}&endDate=${testDate}&branchId=all&debug=true`;
        console.log('Daily Stats API URL:', statsUrl);
        const statsResponse = await fetch(statsUrl);
        const statsResponseTime = Date.now() - statsStartTime;
        const statsData = await statsResponse.json();

        results.push({
          endpoint: '/api/admin-dashboard/daily-stats-aggregation (Email Stats)',
          method: 'GET',
          status: statsResponse.status,
          responseTime: statsResponseTime,
          success: statsResponse.ok,
          data: statsData,
          error: !statsResponse.ok ? statsData.error : undefined
        });
      } catch (error) {
        errors.push(`Daily stats API error: ${error}`);
        results.push({
          endpoint: '/api/admin-dashboard/daily-stats-aggregation (Email Stats)',
          method: 'GET',
          status: 0,
          responseTime: Date.now() - statsStartTime,
          success: false,
          error: `Network error: ${error}`
        });
      }

      // Calculate performance metrics
      const successfulResults = results.filter(r => r.success);
      const performance: PerformanceMetrics = {
        averageResponseTime: results.length > 0
          ? results.reduce((sum, r) => sum + r.responseTime, 0) / results.length
          : 0,
        totalRequests: results.length,
        successRate: results.length > 0 ? (successfulResults.length / results.length) * 100 : 0,
        errorCount: results.filter(r => !r.success).length
      };

      setTestResults({
        endpoints: results,
        performance,
        errors
      });

    } catch (error) {
      errors.push(`Test execution error: ${error}`);
      setTestResults({
        endpoints: results,
        performance: {
          averageResponseTime: 0,
          totalRequests: results.length,
          successRate: 0,
          errorCount: results.length
        },
        errors
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runEndpointTests = async () => {
    setIsLoading(true);
    const results: EndpointTestResult[] = [];
    const errors: string[] = [];
    let createdSettingId: string | null = null;

    try {
      // Test GET /api/admin-dashboard/stats-email-settings
      const getStartTime = Date.now();
      try {
        const getResponse = await fetch('/api/admin-dashboard/stats-email-settings');
        const getResponseTime = Date.now() - getStartTime;
        const getData = await getResponse.json();
        
        results.push({
          endpoint: '/api/admin-dashboard/stats-email-settings',
          method: 'GET',
          status: getResponse.status,
          responseTime: getResponseTime,
          success: getResponse.ok,
          data: getData,
          error: !getResponse.ok ? getData.error : undefined
        });
      } catch (error) {
        errors.push(`GET settings error: ${error}`);
        results.push({
          endpoint: '/api/admin-dashboard/stats-email-settings',
          method: 'GET',
          status: 0,
          responseTime: Date.now() - getStartTime,
          success: false,
          error: `Network error: ${error}`
        });
      }

      // Test POST /api/admin-dashboard/stats-email-settings
      const postStartTime = Date.now();
      try {
        const postResponse = await fetch('/api/admin-dashboard/stats-email-settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: testEmail,
            selectedStats
          })
        });
        const postResponseTime = Date.now() - postStartTime;
        const postData = await postResponse.json();
        
        if (postResponse.ok && postData.setting?._id) {
          createdSettingId = postData.setting._id;
        }
        
        results.push({
          endpoint: '/api/admin-dashboard/stats-email-settings',
          method: 'POST',
          status: postResponse.status,
          responseTime: postResponseTime,
          success: postResponse.ok,
          data: postData,
          error: !postResponse.ok ? postData.error : undefined
        });
      } catch (error) {
        errors.push(`POST settings error: ${error}`);
        results.push({
          endpoint: '/api/admin-dashboard/stats-email-settings',
          method: 'POST',
          status: 0,
          responseTime: Date.now() - postStartTime,
          success: false,
          error: `Network error: ${error}`
        });
      }

      // Test PUT /api/admin-dashboard/stats-email-settings/[id] (if we created a setting)
      if (createdSettingId) {
        const putStartTime = Date.now();
        try {
          const putResponse = await fetch(`/api/admin-dashboard/stats-email-settings/${createdSettingId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              isActive: false,
              selectedStats: { ...selectedStats, totalSales: false }
            })
          });
          const putResponseTime = Date.now() - putStartTime;
          const putData = await putResponse.json();
          
          results.push({
            endpoint: `/api/admin-dashboard/stats-email-settings/${createdSettingId}`,
            method: 'PUT',
            status: putResponse.status,
            responseTime: putResponseTime,
            success: putResponse.ok,
            data: putData,
            error: !putResponse.ok ? putData.error : undefined
          });
        } catch (error) {
          errors.push(`PUT settings error: ${error}`);
          results.push({
            endpoint: `/api/admin-dashboard/stats-email-settings/${createdSettingId}`,
            method: 'PUT',
            status: 0,
            responseTime: Date.now() - putStartTime,
            success: false,
            error: `Network error: ${error}`
          });
        }
      }

      // Test GET /api/admin-dashboard/daily-stats-aggregation
      const statsStartTime = Date.now();
      try {
        const statsResponse = await fetch(`/api/admin-dashboard/daily-stats-aggregation?date=${testDate}&debug=true`);
        const statsResponseTime = Date.now() - statsStartTime;
        const statsData = await statsResponse.json();
        
        results.push({
          endpoint: '/api/admin-dashboard/daily-stats-aggregation',
          method: 'GET',
          status: statsResponse.status,
          responseTime: statsResponseTime,
          success: statsResponse.ok,
          data: statsData,
          error: !statsResponse.ok ? statsData.error : undefined
        });
      } catch (error) {
        errors.push(`GET stats error: ${error}`);
        results.push({
          endpoint: '/api/admin-dashboard/daily-stats-aggregation',
          method: 'GET',
          status: 0,
          responseTime: Date.now() - statsStartTime,
          success: false,
          error: `Network error: ${error}`
        });
      }

      // Test DELETE /api/admin-dashboard/stats-email-settings/[id] (cleanup)
      if (createdSettingId) {
        const deleteStartTime = Date.now();
        try {
          const deleteResponse = await fetch(`/api/admin-dashboard/stats-email-settings/${createdSettingId}`, {
            method: 'DELETE'
          });
          const deleteResponseTime = Date.now() - deleteStartTime;
          const deleteData = await deleteResponse.json();
          
          results.push({
            endpoint: `/api/admin-dashboard/stats-email-settings/${createdSettingId}`,
            method: 'DELETE',
            status: deleteResponse.status,
            responseTime: deleteResponseTime,
            success: deleteResponse.ok,
            data: deleteData,
            error: !deleteResponse.ok ? deleteData.error : undefined
          });
        } catch (error) {
          errors.push(`DELETE settings error: ${error}`);
          results.push({
            endpoint: `/api/admin-dashboard/stats-email-settings/${createdSettingId}`,
            method: 'DELETE',
            status: 0,
            responseTime: Date.now() - deleteStartTime,
            success: false,
            error: `Network error: ${error}`
          });
        }
      }

      // Calculate performance metrics
      const successfulResults = results.filter(r => r.success);
      const performance: PerformanceMetrics = {
        averageResponseTime: results.length > 0 
          ? results.reduce((sum, r) => sum + r.responseTime, 0) / results.length 
          : 0,
        totalRequests: results.length,
        successRate: results.length > 0 ? (successfulResults.length / results.length) * 100 : 0,
        errorCount: results.filter(r => !r.success).length
      };

      setTestResults({
        endpoints: results,
        performance,
        errors
      });

    } catch (error) {
      errors.push(`Test execution error: ${error}`);
      setTestResults({
        endpoints: results,
        performance: {
          averageResponseTime: 0,
          totalRequests: results.length,
          successRate: 0,
          errorCount: results.length
        },
        errors
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: number, success: boolean) => {
    if (status === 0) return <Badge variant="destructive">Network Error</Badge>;
    if (success) return <Badge variant="default" className="bg-green-500">Success</Badge>;
    if (status >= 400 && status < 500) return <Badge variant="destructive">Client Error</Badge>;
    if (status >= 500) return <Badge variant="destructive">Server Error</Badge>;
    return <Badge variant="secondary">Unknown</Badge>;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Stats Email API Debug Tool
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="comparison" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="comparison">API Comparison</TabsTrigger>
            <TabsTrigger value="config">CRUD Tests</TabsTrigger>
            <TabsTrigger value="results">Test Results</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="comparison" className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded p-4">
              <h3 className="font-medium text-blue-800 mb-2">API Data Comparison</h3>
              <p className="text-blue-700 text-sm">
                Compare the dashboard cards API with the daily stats aggregation API to identify data inconsistencies.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="comparisonDate">Test Date</Label>
                <Input
                  id="comparisonDate"
                  type="date"
                  value={testDate}
                  onChange={(e) => setTestDate(e.target.value)}
                />
              </div>
            </div>

            <Button
              onClick={runApiComparison}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Comparing APIs...' : 'Compare Dashboard vs Daily Stats APIs'}
            </Button>

            {testResults && testResults.endpoints.length === 2 && (
              <div className="mt-6 space-y-4">
                <h4 className="font-medium">Data Comparison Results</h4>
                {(() => {
                  const dashboardResult = testResults.endpoints.find(e => e.endpoint.includes('reservation-stats'));
                  const statsResult = testResults.endpoints.find(e => e.endpoint.includes('daily-stats-aggregation'));

                  if (!dashboardResult?.data || !statsResult?.data) {
                    return <p className="text-red-600">Unable to compare - one or both APIs failed</p>;
                  }

                  const dashboardData = dashboardResult.data;
                  const statsData = statsResult.data.stats;

                  return (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm">Dashboard API Results</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <div className="flex justify-between">
                            <span>Total Reservations:</span>
                            <span className="font-mono">{dashboardData.totalReservations || 0}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Total Sales:</span>
                            <span className="font-mono">${(dashboardData.totalSales || 0).toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Average Sale:</span>
                            <span className="font-mono">${(dashboardData.averageSale || 0).toFixed(2)}</span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-sm">Daily Stats API Results</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <div className="flex justify-between">
                            <span>Total Reservations:</span>
                            <span className="font-mono">{statsData.totalReservations || 0}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Total Sales:</span>
                            <span className="font-mono">${(statsData.totalSales?.amount || 0).toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Sales Count:</span>
                            <span className="font-mono">{statsData.totalSales?.count || 0}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Total Presence:</span>
                            <span className="font-mono">{statsData.totalPresence || 0}</span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="md:col-span-2">
                        <CardHeader>
                          <CardTitle className="text-sm">Differences Analysis</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2 text-sm">
                            {(() => {
                              const reservationsDiff = (statsData.totalReservations || 0) - (dashboardData.totalReservations || 0);
                              const salesDiff = (statsData.totalSales?.amount || 0) - (dashboardData.totalSales || 0);

                              return (
                                <>
                                  <div className={`flex justify-between ${reservationsDiff === 0 ? 'text-green-600' : 'text-red-600'}`}>
                                    <span>Reservations Difference:</span>
                                    <span className="font-mono">{reservationsDiff > 0 ? '+' : ''}{reservationsDiff}</span>
                                  </div>
                                  <div className={`flex justify-between ${Math.abs(salesDiff) < 0.01 ? 'text-green-600' : 'text-red-600'}`}>
                                    <span>Sales Difference:</span>
                                    <span className="font-mono">${salesDiff > 0 ? '+' : ''}${salesDiff.toFixed(2)}</span>
                                  </div>
                                  {reservationsDiff === 0 && Math.abs(salesDiff) < 0.01 ? (
                                    <div className="text-green-600 font-medium mt-2">✅ APIs are consistent!</div>
                                  ) : (
                                    <div className="text-red-600 font-medium mt-2">❌ APIs have inconsistencies</div>
                                  )}
                                </>
                              );
                            })()}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  );
                })()}
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="config" className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
              <h3 className="font-medium text-yellow-800 mb-2">CRUD API Tests</h3>
              <p className="text-yellow-700 text-sm">
                Test the Create, Read, Update, Delete operations for stats email settings.
              </p>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="testEmail">Test Email</Label>
                <Input
                  id="testEmail"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="testDate">Test Date</Label>
                <Input
                  id="testDate"
                  type="date"
                  value={testDate}
                  onChange={(e) => setTestDate(e.target.value)}
                />
              </div>
            </div>
            
            <div>
              <Label>Selected Stats (for POST test)</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {Object.entries(selectedStats).map(([key, value]) => (
                  <label key={key} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => setSelectedStats(prev => ({
                        ...prev,
                        [key]: e.target.checked
                      }))}
                    />
                    <span className="text-sm">{key}</span>
                  </label>
                ))}
              </div>
            </div>
            
            <Button 
              onClick={runEndpointTests} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Running Tests...' : 'Run API Tests'}
            </Button>
          </TabsContent>
          
          <TabsContent value="results" className="space-y-4">
            {testResults && (
              <div className="space-y-4">
                {testResults.endpoints.map((result, index) => (
                  <Card key={index}>
                    <CardContent className="pt-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <Badge variant="outline">{result.method}</Badge>
                          <span className="ml-2 font-mono text-sm">{result.endpoint}</span>
                        </div>
                        {getStatusBadge(result.status, result.success)}
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                        <span className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {result.responseTime}ms
                        </span>
                        <span>Status: {result.status}</span>
                      </div>
                      
                      {result.error && (
                        <div className="bg-red-50 border border-red-200 rounded p-2 mb-2">
                          <div className="flex items-center gap-1 text-red-700">
                            <AlertCircle className="h-4 w-4" />
                            <span className="font-medium">Error:</span>
                          </div>
                          <p className="text-red-600 text-sm mt-1">{result.error}</p>
                        </div>
                      )}
                      
                      {result.data && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-sm font-medium">Response Data</summary>
                          <pre className="bg-gray-50 p-2 rounded text-xs mt-2 overflow-auto max-h-40">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        </details>
                      )}
                    </CardContent>
                  </Card>
                ))}
                
                {testResults.errors.length > 0 && (
                  <Card>
                    <CardContent className="pt-4">
                      <h4 className="font-medium text-red-700 mb-2">Test Errors</h4>
                      <ul className="space-y-1">
                        {testResults.errors.map((error, index) => (
                          <li key={index} className="text-sm text-red-600">• {error}</li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
            
            {!testResults && (
              <div className="text-center text-gray-500 py-8">
                Run API tests to see results here
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="performance" className="space-y-4">
            {testResults && (
              <div className="grid grid-cols-2 gap-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <span className="font-medium">Success Rate</span>
                    </div>
                    <p className="text-2xl font-bold">{testResults.performance.successRate.toFixed(1)}%</p>
                    <p className="text-sm text-gray-600">
                      {testResults.performance.totalRequests - testResults.performance.errorCount} of {testResults.performance.totalRequests} requests
                    </p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Clock className="h-5 w-5 text-blue-500" />
                      <span className="font-medium">Avg Response Time</span>
                    </div>
                    <p className="text-2xl font-bold">{testResults.performance.averageResponseTime.toFixed(0)}ms</p>
                    <p className="text-sm text-gray-600">
                      Across {testResults.performance.totalRequests} requests
                    </p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="h-5 w-5 text-red-500" />
                      <span className="font-medium">Error Count</span>
                    </div>
                    <p className="text-2xl font-bold">{testResults.performance.errorCount}</p>
                    <p className="text-sm text-gray-600">
                      Failed requests
                    </p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Zap className="h-5 w-5 text-purple-500" />
                      <span className="font-medium">Total Requests</span>
                    </div>
                    <p className="text-2xl font-bold">{testResults.performance.totalRequests}</p>
                    <p className="text-sm text-gray-600">
                      API calls made
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}
            
            {!testResults && (
              <div className="text-center text-gray-500 py-8">
                Run API tests to see performance metrics here
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
