'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Eye, Mail, Download, Smartphone, Monitor, Tablet } from 'lucide-react';
import { getDailyStatsEmailTemplate, processStatsEmailTemplate, StatsEmailData } from '@/lib/utils/stats-email-templates';

interface TemplatePreviewState {
  sampleData: StatsEmailData;
  selectedStats: string[];
  previewMode: 'desktop' | 'mobile' | 'tablet';
  theme: 'default' | 'dark' | 'light';
}

interface TemplateDebugProps {
  onPreviewUpdate?: (html: string) => void;
  onTestSend?: (email: string) => void;
}

export default function StatsEmailTemplateDebug({ onPreviewUpdate, onTestSend }: TemplateDebugProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [previewHtml, setPreviewHtml] = useState<string>('');
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [sendResult, setSendResult] = useState<{ success: boolean; message: string } | null>(null);
  const [useRealData, setUseRealData] = useState(true);
  const [testDate, setTestDate] = useState(() => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    // Use local timezone to avoid date shifts
    const year = yesterday.getFullYear();
    const month = String(yesterday.getMonth() + 1).padStart(2, '0');
    const day = String(yesterday.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  });

  const [previewState, setPreviewState] = useState<TemplatePreviewState>({
    sampleData: generateSampleData(),
    selectedStats: ['totalSales', 'totalReservationsScheduled', 'totalReservationsCreated', 'totalPresence', 'salesByBranch', 'topSellers'],
    previewMode: 'desktop',
    theme: 'default'
  });

  // Only render in development environment
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // Fetch real data from the daily stats API
  const fetchRealData = async (date: string): Promise<StatsEmailData> => {
    try {
      const response = await fetch(`/api/admin-dashboard/daily-stats-aggregation?date=${date}&debug=true`);
      if (!response.ok) {
        throw new Error(`API returned ${response.status}`);
      }

      const apiData = await response.json();
      const stats = apiData.stats;

      // Transform API data to match StatsEmailData interface
      return {
        date: date,
        totalSales: stats.totalSales || { amount: 0, count: 0 },
        totalReservationsScheduled: stats.totalReservationsScheduled || 0,
        totalReservationsCreated: stats.totalReservationsCreated || 0,
        totalPresence: stats.totalPresence || 0,
        salesByBranch: stats.salesByBranch || [],
        reservationsByBranch: stats.reservationsByBranch || [],
        presenceByBranch: stats.presenceByBranch || [],
        topSellers: stats.topSellers || [],
        topPaps: stats.topPaps || [],
        selectedStats: ['totalSales', 'totalReservationsScheduled', 'totalReservationsCreated', 'totalPresence', 'salesByBranch', 'reservationsByBranch', 'presenceByBranch', 'topSellers', 'topPaps'],
        exportUrls: {
          csv: '#csv-export-placeholder',
          pdf: '#pdf-export-placeholder'
        }
      };
    } catch (error) {
      console.error('Error fetching real data:', error);
      // Fall back to sample data if API fails
      return generateSampleData();
    }
  };

  // Generate sample data for template testing
  function generateSampleData(): StatsEmailData {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    // Use local timezone to avoid date shifts
    const year = yesterday.getFullYear();
    const month = String(yesterday.getMonth() + 1).padStart(2, '0');
    const day = String(yesterday.getDate()).padStart(2, '0');
    const dateString = `${year}-${month}-${day}`;

    return {
      date: dateString,
      totalSales: {
        amount: 15420.50,
        count: 87
      },
      totalReservationsScheduled: 23,
      totalReservationsCreated: 18,
      totalPresence: 156,
      salesByBranch: [
        { branchName: 'Succursale Centre-Ville', amount: 8750.25, count: 45 },
        { branchName: 'Succursale Nord', amount: 4320.75, count: 28 },
        { branchName: 'Succursale Sud', amount: 2349.50, count: 14 }
      ],
      reservationsByBranch: [
        { branchName: 'Succursale Centre-Ville', count: 12 },
        { branchName: 'Succursale Nord', count: 7 },
        { branchName: 'Succursale Sud', count: 4 }
      ],
      presenceByBranch: [
        { branchName: 'Succursale Centre-Ville', count: 89 },
        { branchName: 'Succursale Nord', count: 42 },
        { branchName: 'Succursale Sud', count: 25 }
      ],
      topSellers: [
        { name: 'Marie Dubois', amount: 3250.00, count: 18 },
        { name: 'Jean Tremblay', amount: 2890.50, count: 15 },
        { name: 'Sophie Martin', amount: 2456.75, count: 12 },
        { name: 'Pierre Gagnon', amount: 2134.25, count: 11 },
        { name: 'Lucie Roy', amount: 1987.00, count: 9 }
      ],
      topPaps: [
        { name: 'Caroline Leblanc', count: 28 },
        { name: 'Michel Bouchard', count: 24 },
        { name: 'Isabelle Côté', count: 19 },
        { name: 'Robert Lavoie', count: 16 },
        { name: 'Nathalie Girard', count: 13 }
      ],
      selectedStats: ['totalSales', 'totalReservationsScheduled', 'totalReservationsCreated', 'totalPresence', 'salesByBranch', 'topSellers'],
      exportUrls: {
        csv: '#csv-export-placeholder',
        pdf: '#pdf-export-placeholder'
      }
    };
  }

  // Generate template preview
  const generatePreview = async () => {
    setIsLoading(true);
    try {
      let dataToUse = previewState.sampleData;

      if (useRealData) {
        dataToUse = await fetchRealData(testDate);
        // Update the preview state with real data
        setPreviewState(prev => ({
          ...prev,
          sampleData: dataToUse
        }));
      }

      const updatedData = {
        ...dataToUse,
        selectedStats: previewState.selectedStats
      };

      const template = getDailyStatsEmailTemplate(updatedData);
      const processedTemplate = processStatsEmailTemplate(template, updatedData);

      setPreviewHtml(processedTemplate);
      onPreviewUpdate?.(processedTemplate);
    } catch (error) {
      console.error('Error generating template preview:', error);
      setPreviewHtml('<p>Error generating template preview</p>');
    } finally {
      setIsLoading(false);
    }
  };

  // Send test email
  const sendTestEmail = async () => {
    if (!testEmail) {
      setSendResult({ success: false, message: 'Please enter a test email address' });
      return;
    }

    setIsLoading(true);
    setSendResult(null);

    try {
      // Get the current data (real or sample)
      let dataToUse = previewState.sampleData;
      if (useRealData) {
        dataToUse = await fetchRealData(testDate);
      }

      const response = await fetch('/api/admin-dashboard/debug/stats-email-template/send-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: testEmail,
          templateData: {
            ...dataToUse,
            selectedStats: previewState.selectedStats
          }
        })
      });

      const result = await response.json();
      setSendResult({
        success: response.ok,
        message: result.message || (response.ok ? 'Test email sent successfully!' : 'Failed to send test email')
      });
      
      onTestSend?.(testEmail);
    } catch (error) {
      console.error('Error sending test email:', error);
      setSendResult({
        success: false,
        message: 'Network error: Failed to send test email'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update selected stats
  const toggleStat = (stat: string) => {
    setPreviewState(prev => ({
      ...prev,
      selectedStats: prev.selectedStats.includes(stat)
        ? prev.selectedStats.filter(s => s !== stat)
        : [...prev.selectedStats, stat]
    }));
  };

  // Generate preview on component mount and when settings change
  useEffect(() => {
    generatePreview();
  }, [previewState.selectedStats, useRealData, testDate]);

  const getPreviewModeIcon = (mode: string) => {
    switch (mode) {
      case 'mobile': return <Smartphone className="h-4 w-4" />;
      case 'tablet': return <Tablet className="h-4 w-4" />;
      default: return <Monitor className="h-4 w-4" />;
    }
  };

  const getPreviewModeWidth = () => {
    switch (previewState.previewMode) {
      case 'mobile': return '375px';
      case 'tablet': return '768px';
      default: return '100%';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Stats Email Template Debug Tool
        </CardTitle>
        <CardDescription>
          Preview and test the daily statistics email template with sample data
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="preview">Live Preview</TabsTrigger>
            <TabsTrigger value="config">Configuration</TabsTrigger>
            <TabsTrigger value="test">Test Email</TabsTrigger>
            <TabsTrigger value="export">Export Template</TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded p-4">
              <h3 className="font-medium text-blue-800 mb-2">Template Preview</h3>
              <p className="text-blue-700 text-sm">
                Live preview of the email template with sample data. Use the Configuration tab to customize the preview.
              </p>
            </div>

            <div className="flex items-center gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Label>Preview Mode:</Label>
                <Select
                  value={previewState.previewMode}
                  onValueChange={(value: 'desktop' | 'mobile' | 'tablet') =>
                    setPreviewState(prev => ({ ...prev, previewMode: value }))
                  }
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desktop">
                      <div className="flex items-center gap-2">
                        <Monitor className="h-4 w-4" />
                        Desktop
                      </div>
                    </SelectItem>
                    <SelectItem value="tablet">
                      <div className="flex items-center gap-2">
                        <Tablet className="h-4 w-4" />
                        Tablet
                      </div>
                    </SelectItem>
                    <SelectItem value="mobile">
                      <div className="flex items-center gap-2">
                        <Smartphone className="h-4 w-4" />
                        Mobile
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button onClick={generatePreview} disabled={isLoading} variant="outline" size="sm">
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Refresh Preview
              </Button>
            </div>

            <div className="border rounded-lg overflow-hidden">
              <div 
                className="mx-auto transition-all duration-300"
                style={{ width: getPreviewModeWidth() }}
              >
                {previewHtml ? (
                  <iframe
                    srcDoc={previewHtml}
                    className="w-full h-96 border-0"
                    title="Email Template Preview"
                  />
                ) : (
                  <div className="flex items-center justify-center h-96 bg-gray-50">
                    <p className="text-gray-500">Loading preview...</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="config" className="space-y-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
              <h3 className="font-medium text-yellow-800 mb-2">Template Configuration</h3>
              <p className="text-yellow-700 text-sm">
                Customize which statistics sections to include in the email template preview.
              </p>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="dataSource">Data Source</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Checkbox
                      id="useRealData"
                      checked={useRealData}
                      onCheckedChange={(checked) => setUseRealData(checked as boolean)}
                    />
                    <Label htmlFor="useRealData" className="text-sm">Use Real API Data</Label>
                  </div>
                </div>
                <div>
                  <Label htmlFor="testDate">Test Date</Label>
                  <Input
                    id="testDate"
                    type="date"
                    value={testDate}
                    onChange={(e) => setTestDate(e.target.value)}
                    disabled={!useRealData}
                  />
                </div>
              </div>
              <div>
                <Label className="text-base font-medium">Statistics Sections</Label>
                <div className="grid grid-cols-2 gap-3 mt-2">
                  {[
                    { key: 'totalSales', label: 'Total Sales' },
                    { key: 'totalReservationsScheduled', label: 'Reservations Scheduled' },
                    { key: 'totalReservationsCreated', label: 'Reservations Created' },
                    { key: 'totalPresence', label: 'Total Presence' },
                    { key: 'salesByBranch', label: 'Sales by Branch' },
                    { key: 'reservationsByBranch', label: 'Reservations by Branch' },
                    { key: 'presenceByBranch', label: 'Presence by Branch' },
                    { key: 'topSellers', label: 'Top 5 Sellers' },
                    { key: 'topPaps', label: 'Top 5 PAPs' }
                  ].map(({ key, label }) => (
                    <div key={key} className="flex items-center space-x-2">
                      <Checkbox
                        id={key}
                        checked={previewState.selectedStats.includes(key)}
                        onCheckedChange={() => toggleStat(key)}
                      />
                      <Label htmlFor={key} className="text-sm">{label}</Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="pt-4 border-t">
                <Label className="text-base font-medium">
                  {useRealData ? 'Real API Data Summary' : 'Sample Data Summary'}
                </Label>
                <div className="grid grid-cols-2 gap-4 mt-2 text-sm">
                  <div className="space-y-1">
                    <div><strong>Source:</strong> {useRealData ? 'Live API' : 'Sample Data'}</div>
                    <div><strong>Date:</strong> {previewState.sampleData.date}</div>
                    <div><strong>Total Sales:</strong> ${previewState.sampleData.totalSales.amount.toLocaleString()}</div>
                    <div><strong>Reservations Scheduled:</strong> {previewState.sampleData.totalReservationsScheduled}</div>
                    <div><strong>Reservations Created:</strong> {previewState.sampleData.totalReservationsCreated}</div>
                    <div><strong>Presence:</strong> {previewState.sampleData.totalPresence}</div>
                  </div>
                  <div className="space-y-1">
                    <div><strong>Branches:</strong> {previewState.sampleData.salesByBranch.length}</div>
                    <div><strong>Top Sellers:</strong> {previewState.sampleData.topSellers.length}</div>
                    <div><strong>Top PAPs:</strong> {previewState.sampleData.topPaps.length}</div>
                    {useRealData && (
                      <div className="text-green-600"><strong>✓ Live Data</strong></div>
                    )}
                  </div>
                </div>
              </div>

              <Button onClick={generatePreview} disabled={isLoading} className="w-full">
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Update Preview
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="test" className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded p-4">
              <h3 className="font-medium text-green-800 mb-2">Test Email Sending</h3>
              <p className="text-green-700 text-sm">
                Send a test email with the current template configuration to verify email delivery and formatting.
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="testEmail">Test Email Address</Label>
                <Input
                  id="testEmail"
                  type="email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="space-y-2">
                <Label>Selected Statistics for Test Email</Label>
                <div className="flex flex-wrap gap-2">
                  {previewState.selectedStats.map(stat => (
                    <Badge key={stat} variant="secondary">
                      {stat.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Badge>
                  ))}
                </div>
              </div>

              <Button onClick={sendTestEmail} disabled={isLoading || !testEmail} className="w-full">
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Mail className="h-4 w-4 mr-2" />}
                Send Test Email
              </Button>

              {sendResult && (
                <Alert className={sendResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                  <AlertDescription className={sendResult.success ? "text-green-800" : "text-red-800"}>
                    {sendResult.message}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>

          <TabsContent value="export" className="space-y-4">
            <div className="bg-purple-50 border border-purple-200 rounded p-4">
              <h3 className="font-medium text-purple-800 mb-2">Export Template</h3>
              <p className="text-purple-700 text-sm">
                Download the generated HTML template for external testing or debugging.
              </p>
              <p className="text-purple-600 text-xs mt-2">
                <strong>Note:</strong> The CSV/PDF export buttons in the email template are currently placeholders and will show alerts when clicked.
              </p>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <Button
                  onClick={() => {
                    const blob = new Blob([previewHtml], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `stats-email-template-${previewState.sampleData.date}.html`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }}
                  disabled={!previewHtml}
                  variant="outline"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download HTML
                </Button>

                <Button
                  onClick={() => {
                    if (previewHtml) {
                      navigator.clipboard.writeText(previewHtml);
                    }
                  }}
                  disabled={!previewHtml}
                  variant="outline"
                >
                  📋 Copy HTML
                </Button>
              </div>

              <div className="text-sm text-gray-600">
                <p><strong>Template Size:</strong> {previewHtml ? `${(previewHtml.length / 1024).toFixed(2)} KB` : 'N/A'}</p>
                <p><strong>Selected Sections:</strong> {previewState.selectedStats.length} of 8</p>
                <p><strong>Preview Mode:</strong> {previewState.previewMode}</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
