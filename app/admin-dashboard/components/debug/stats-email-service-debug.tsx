'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Mail, 
  Clock, 
  CheckCircle, 
  XCircle, 
  BarChart3, 
  RefreshCw,
  Send,
  Eye,
  Download,
  AlertTriangle
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface EmailSendResult {
  success: boolean;
  emailAddress: string;
  messageId?: string;
  error?: string;
  timestamp: Date;
}

interface BatchEmailResult {
  totalEmails: number;
  successCount: number;
  failureCount: number;
  results: EmailSendResult[];
  executionTime: number;
  errors: string[];
}

interface EmailLogEntry {
  timestamp: Date;
  emailAddress: string;
  success: boolean;
  executionTime: number;
  templateSize: number;
  error?: string;
  retryCount?: number;
}

interface EmailServiceStats {
  totalEmails: number;
  successRate: number;
  averageExecutionTime: number;
  recentErrors: string[];
}

interface PerformanceTestResults {
  templateRenderingTime: number;
  emailSendingTime: number;
  batchProcessingTime: number;
  memoryUsage: number;
  successRate: number;
}

export default function StatsEmailServiceDebug() {
  const [isLoading, setIsLoading] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [testDate, setTestDate] = useState(() => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    // Use local timezone to avoid date shifts
    const year = yesterday.getFullYear();
    const month = String(yesterday.getMonth() + 1).padStart(2, '0');
    const day = String(yesterday.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  });
  const [batchResults, setBatchResults] = useState<BatchEmailResult | null>(null);
  const [emailLogs, setEmailLogs] = useState<EmailLogEntry[]>([]);
  const [serviceStats, setServiceStats] = useState<EmailServiceStats | null>(null);
  const [performanceResults, setPerformanceResults] = useState<PerformanceTestResults | null>(null);
  const [previewHtml, setPreviewHtml] = useState<string>('');

  // Load initial data
  useEffect(() => {
    loadEmailLogs();
    loadServiceStats();
  }, []);

  const loadEmailLogs = async () => {
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-email-service/logs');
      if (response.ok) {
        const data = await response.json();
        setEmailLogs(data.logs || []);
      }
    } catch (error) {
      console.error('Error loading email logs:', error);
    }
  };

  const loadServiceStats = async () => {
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-email-service/stats');
      if (response.ok) {
        const data = await response.json();
        setServiceStats(data.stats);
      }
    } catch (error) {
      console.error('Error loading service stats:', error);
    }
  };

  const handleSendTestEmail = async () => {
    if (!testEmail) {
      toast({
        title: 'Erreur',
        description: 'Veuillez entrer une adresse email',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-email-service/send-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: testEmail,
          date: testDate
        })
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Succès',
          description: `Email de test envoyé à ${testEmail}`
        });
        loadEmailLogs();
        loadServiceStats();
      } else {
        toast({
          title: 'Erreur',
          description: data.error || 'Erreur lors de l\'envoi de l\'email',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      toast({
        title: 'Erreur',
        description: 'Erreur lors de l\'envoi de l\'email de test',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBatchTest = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-email-service/batch-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          date: testDate
        })
      });

      const data = await response.json();

      if (data.success) {
        setBatchResults(data.results);
        toast({
          title: 'Test de lot terminé',
          description: `${data.results.successCount}/${data.results.totalEmails} emails envoyés avec succès`
        });
        loadEmailLogs();
        loadServiceStats();
      } else {
        toast({
          title: 'Erreur',
          description: data.error || 'Erreur lors du test de lot',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error running batch test:', error);
      toast({
        title: 'Erreur',
        description: 'Erreur lors du test de lot',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePerformanceTest = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-email-service/performance-test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          date: testDate
        })
      });

      const data = await response.json();

      if (data.success) {
        setPerformanceResults(data.results);
        toast({
          title: 'Test de performance terminé',
          description: 'Résultats disponibles dans l\'onglet Performance'
        });
      } else {
        toast({
          title: 'Erreur',
          description: data.error || 'Erreur lors du test de performance',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error running performance test:', error);
      toast({
        title: 'Erreur',
        description: 'Erreur lors du test de performance',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGeneratePreview = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-email-service/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          date: testDate
        })
      });

      const data = await response.json();

      if (data.success) {
        setPreviewHtml(data.html);
        toast({
          title: 'Aperçu généré',
          description: 'Template email généré avec succès'
        });
      } else {
        toast({
          title: 'Erreur',
          description: data.error || 'Erreur lors de la génération de l\'aperçu',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error generating preview:', error);
      toast({
        title: 'Erreur',
        description: 'Erreur lors de la génération de l\'aperçu',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = async () => {
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-email-service/clear-logs', {
        method: 'POST'
      });

      if (response.ok) {
        setEmailLogs([]);
        setServiceStats(null);
        toast({
          title: 'Logs effacés',
          description: 'Tous les logs ont été supprimés'
        });
      }
    } catch (error) {
      console.error('Error clearing logs:', error);
      toast({
        title: 'Erreur',
        description: 'Erreur lors de l\'effacement des logs',
        variant: 'destructive'
      });
    }
  };

  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleString('fr-CA');
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Debug - Service Email Statistiques
        </CardTitle>
        <CardDescription>
          Outils de test et de débogage pour le service d'envoi d'emails de statistiques quotidiennes
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="test" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="test">Test</TabsTrigger>
            <TabsTrigger value="preview">Aperçu</TabsTrigger>
            <TabsTrigger value="logs">Logs</TabsTrigger>
            <TabsTrigger value="stats">Statistiques</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="test" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="testEmail">Email de test</Label>
                  <Input
                    id="testEmail"
                    type="email"
                    placeholder="<EMAIL>"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="testDate">Date des statistiques</Label>
                  <Input
                    id="testDate"
                    type="date"
                    value={testDate}
                    onChange={(e) => setTestDate(e.target.value)}
                  />
                </div>
                <div className="flex gap-2">
                  <Button 
                    onClick={handleSendTestEmail} 
                    disabled={isLoading}
                    className="flex-1"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {isLoading ? 'Envoi...' : 'Envoyer Test'}
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label>Test de lot (tous les emails actifs)</Label>
                  <Button 
                    onClick={handleBatchTest} 
                    disabled={isLoading}
                    variant="outline"
                    className="w-full mt-2"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    {isLoading ? 'Test en cours...' : 'Lancer Test de Lot'}
                  </Button>
                </div>
                <div>
                  <Label>Test de performance</Label>
                  <Button 
                    onClick={handlePerformanceTest} 
                    disabled={isLoading}
                    variant="outline"
                    className="w-full mt-2"
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    {isLoading ? 'Test en cours...' : 'Test Performance'}
                  </Button>
                </div>
              </div>
            </div>

            {batchResults && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Résultats du test de lot:</strong><br />
                  Total: {batchResults.totalEmails} emails<br />
                  Succès: {batchResults.successCount}<br />
                  Échecs: {batchResults.failureCount}<br />
                  Temps d'exécution: {formatDuration(batchResults.executionTime)}
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            <div className="flex justify-between items-center">
              <Label>Aperçu du template email</Label>
              <Button 
                onClick={handleGeneratePreview} 
                disabled={isLoading}
                variant="outline"
              >
                <Eye className="h-4 w-4 mr-2" />
                {isLoading ? 'Génération...' : 'Générer Aperçu'}
              </Button>
            </div>
            
            {previewHtml && (
              <div className="border rounded-lg p-4 max-h-96 overflow-auto">
                <iframe
                  srcDoc={previewHtml}
                  className="w-full h-80 border-0"
                  title="Email Preview"
                />
              </div>
            )}
          </TabsContent>

          <TabsContent value="logs" className="space-y-4">
            <div className="flex justify-between items-center">
              <Label>Logs d'envoi d'emails ({emailLogs.length})</Label>
              <div className="flex gap-2">
                <Button onClick={loadEmailLogs} variant="outline" size="sm">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Actualiser
                </Button>
                <Button onClick={clearLogs} variant="outline" size="sm">
                  Effacer Logs
                </Button>
              </div>
            </div>
            
            <div className="space-y-2 max-h-96 overflow-auto">
              {emailLogs.map((log, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {log.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <div>
                      <div className="font-medium">{log.emailAddress}</div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(log.timestamp)}
                      </div>
                      {log.error && (
                        <div className="text-sm text-red-500">{log.error}</div>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={log.success ? 'default' : 'destructive'}>
                      {formatDuration(log.executionTime)}
                    </Badge>
                    <div className="text-sm text-muted-foreground">
                      {formatBytes(log.templateSize)}
                    </div>
                  </div>
                </div>
              ))}
              {emailLogs.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  Aucun log disponible
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="stats" className="space-y-4">
            <div className="flex justify-between items-center">
              <Label>Statistiques du service</Label>
              <Button onClick={loadServiceStats} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Actualiser
              </Button>
            </div>
            
            {serviceStats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{serviceStats.totalEmails}</div>
                    <div className="text-sm text-muted-foreground">Total Emails</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{serviceStats.successRate.toFixed(1)}%</div>
                    <div className="text-sm text-muted-foreground">Taux de Succès</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{formatDuration(serviceStats.averageExecutionTime)}</div>
                    <div className="text-sm text-muted-foreground">Temps Moyen</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{serviceStats.recentErrors.length}</div>
                    <div className="text-sm text-muted-foreground">Erreurs Récentes</div>
                  </CardContent>
                </Card>
              </div>
            )}

            {serviceStats && serviceStats.recentErrors.length > 0 && (
              <div>
                <Label>Erreurs récentes</Label>
                <div className="mt-2 space-y-2">
                  {serviceStats.recentErrors.map((error, index) => (
                    <Alert key={index} variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <Label>Résultats des tests de performance</Label>
            
            {performanceResults && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{formatDuration(performanceResults.templateRenderingTime)}</div>
                    <div className="text-sm text-muted-foreground">Rendu Template</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{formatDuration(performanceResults.emailSendingTime)}</div>
                    <div className="text-sm text-muted-foreground">Envoi Email</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{formatDuration(performanceResults.batchProcessingTime)}</div>
                    <div className="text-sm text-muted-foreground">Traitement Lot</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{formatBytes(performanceResults.memoryUsage)}</div>
                    <div className="text-sm text-muted-foreground">Utilisation Mémoire</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold">{performanceResults.successRate.toFixed(1)}%</div>
                    <div className="text-sm text-muted-foreground">Taux de Succès</div>
                  </CardContent>
                </Card>
              </div>
            )}

            {!performanceResults && (
              <div className="text-center text-muted-foreground py-8">
                Aucun test de performance exécuté. Utilisez l'onglet Test pour lancer un test.
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
