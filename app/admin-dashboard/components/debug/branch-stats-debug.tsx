'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Activity, Database, ExternalLink, Bug } from 'lucide-react';
import Link from 'next/link';
import BranchStatsQuickTest from '@/components/debug/BranchStatsQuickTest';

export default function BranchStatsDebug() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Branch Stats Debug Tools
          <Badge variant="secondary" className="ml-2">
            Task 1 & 2 Complete
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Quick Test Panel */}
          <div>
            <h4 className="font-semibold mb-3">Quick Tests</h4>
            <BranchStatsQuickTest />
          </div>

          {/* Navigation and Links */}
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-3">Debug Tools</h4>
              <div className="grid grid-cols-1 gap-3">
                <Link href="/admin-dashboard/debug">
                  <Button variant="outline" className="w-full justify-start">
                    <Bug className="h-4 w-4 mr-2" />
                    Full Debug Dashboard
                    <ExternalLink className="h-3 w-3 ml-auto" />
                  </Button>
                </Link>

                <Link href="/admin-dashboard/debug/branch-stats">
                  <Button variant="outline" className="w-full justify-start">
                    <Database className="h-4 w-4 mr-2" />
                    Branch Stats Console
                    <ExternalLink className="h-3 w-3 ml-auto" />
                  </Button>
                </Link>

                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => window.open('/api/admin-dashboard/debug/branch-stats-model', '_blank')}
                >
                  <Database className="h-4 w-4 mr-2" />
                  Model API Tests
                  <ExternalLink className="h-3 w-3 ml-auto" />
                </Button>

                <Button 
                  variant="outline" 
                  className="w-full justify-start"
                  onClick={() => window.open('/api/admin-dashboard/debug/branch-stats-aggregation', '_blank')}
                >
                  <Activity className="h-4 w-4 mr-2" />
                  Aggregation API Tests
                  <ExternalLink className="h-3 w-3 ml-auto" />
                </Button>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="font-semibold mb-2">Implementation Status</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between items-center">
                  <span>Database Schema & Models</span>
                  <Badge variant="default" className="text-xs">✅ Complete</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Aggregation Service</span>
                  <Badge variant="default" className="text-xs">✅ Complete</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Email Templates</span>
                  <Badge variant="secondary" className="text-xs">⏳ Pending</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Email Service Integration</span>
                  <Badge variant="secondary" className="text-xs">⏳ Pending</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Cron Job Scheduler</span>
                  <Badge variant="secondary" className="text-xs">⏳ Pending</Badge>
                </div>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="font-semibold mb-2">API Endpoints</h4>
              <div className="space-y-1 text-xs font-mono">
                <div className="flex justify-between">
                  <span className="text-blue-600">GET</span>
                  <span>/api/admin-dashboard/debug/branch-stats-model</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-600">POST</span>
                  <span>/api/admin-dashboard/debug/branch-stats-model</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-red-600">DELETE</span>
                  <span>/api/admin-dashboard/debug/branch-stats-model</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-600">GET</span>
                  <span>/api/admin-dashboard/debug/branch-stats-aggregation</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-600">POST</span>
                  <span>/api/admin-dashboard/debug/branch-stats-aggregation</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
