'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';

interface ValidationTestResult {
  emailValidation: boolean;
  uniqueConstraint: boolean;
  requiredFields: boolean;
  errors: string[];
}

interface PerformanceTestResult {
  queryTimes: Record<string, number>;
  indexUsage: Record<string, boolean>;
  recommendations: string[];
}

interface ModelDebugProps {
  onValidationTest?: (result: ValidationTestResult) => void;
  onPerformanceTest?: (result: PerformanceTestResult) => void;
}

export default function StatsEmailModelDebug({ onValidationTest, onPerformanceTest }: ModelDebugProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationTestResult | null>(null);
  const [performanceResult, setPerformanceResult] = useState<PerformanceTestResult | null>(null);
  const [testData, setTestData] = useState<any[]>([]);

  // Only render in development environment
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const runValidationTests = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-email-model/validation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      
      const result = await response.json();
      setValidationResult(result);
      onValidationTest?.(result);
    } catch (error) {
      console.error('Validation test failed:', error);
      setValidationResult({
        emailValidation: false,
        uniqueConstraint: false,
        requiredFields: false,
        errors: ['Failed to run validation tests']
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runPerformanceTests = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-email-model/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      
      const result = await response.json();
      setPerformanceResult(result);
      onPerformanceTest?.(result);
    } catch (error) {
      console.error('Performance test failed:', error);
      setPerformanceResult({
        queryTimes: {},
        indexUsage: {},
        recommendations: ['Failed to run performance tests']
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createTestData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-email-model/test-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      
      const result = await response.json();
      setTestData(result.data || []);
    } catch (error) {
      console.error('Failed to create test data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const cleanupTestData = async () => {
    setIsLoading(true);
    try {
      await fetch('/api/admin-dashboard/debug/stats-email-model/cleanup', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      });
      
      setTestData([]);
      setValidationResult(null);
      setPerformanceResult(null);
    } catch (error) {
      console.error('Failed to cleanup test data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>StatsEmailSettings Model Debug</CardTitle>
          <CardDescription>
            Test and validate the StatsEmailSettings model implementation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            <Button 
              onClick={runValidationTests} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Run Validation Tests
            </Button>
            
            <Button 
              onClick={runPerformanceTests} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Run Performance Tests
            </Button>
            
            <Button 
              onClick={createTestData} 
              disabled={isLoading}
              variant="outline"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Create Test Data
            </Button>
            
            <Button 
              onClick={cleanupTestData} 
              disabled={isLoading}
              variant="destructive"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Cleanup Test Data
            </Button>
          </div>
        </CardContent>
      </Card>

      {validationResult && (
        <Card>
          <CardHeader>
            <CardTitle>Validation Test Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-2">
              {getStatusIcon(validationResult.emailValidation)}
              <span>Email Validation</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(validationResult.uniqueConstraint)}
              <span>Unique Constraint</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(validationResult.requiredFields)}
              <span>Required Fields</span>
            </div>
            
            {validationResult.errors.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-1">
                    {validationResult.errors.map((error, index) => (
                      <div key={index} className="text-sm">{error}</div>
                    ))}
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {performanceResult && (
        <Card>
          <CardHeader>
            <CardTitle>Performance Test Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <h4 className="font-medium mb-2">Query Times (ms)</h4>
              <div className="space-y-1">
                {Object.entries(performanceResult.queryTimes).map(([query, time]) => (
                  <div key={query} className="flex justify-between">
                    <span className="text-sm">{query}</span>
                    <Badge variant={time > 100 ? "destructive" : "secondary"}>
                      {time}ms
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Index Usage</h4>
              <div className="space-y-1">
                {Object.entries(performanceResult.indexUsage).map(([index, used]) => (
                  <div key={index} className="flex items-center gap-2">
                    {getStatusIcon(used)}
                    <span className="text-sm">{index}</span>
                  </div>
                ))}
              </div>
            </div>
            
            {performanceResult.recommendations.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Recommendations</h4>
                <div className="space-y-1">
                  {performanceResult.recommendations.map((rec, index) => (
                    <div key={index} className="text-sm text-muted-foreground">
                      • {rec}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {testData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Data ({testData.length} records)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {testData.slice(0, 5).map((item, index) => (
                <div key={index} className="flex justify-between items-center p-2 bg-muted rounded">
                  <span className="text-sm">{item.email}</span>
                  <div className="flex gap-2">
                    <Badge variant={item.isActive ? "default" : "secondary"}>
                      {item.isActive ? "Active" : "Inactive"}
                    </Badge>
                    <Badge variant="outline">
                      {item.emailSendCount} sent
                    </Badge>
                  </div>
                </div>
              ))}
              {testData.length > 5 && (
                <div className="text-sm text-muted-foreground text-center">
                  ... and {testData.length - 5} more records
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
