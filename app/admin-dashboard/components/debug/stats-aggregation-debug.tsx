'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, CheckCircle, XCircle, AlertTriangle, BarChart3, Clock, Database, Zap } from 'lucide-react';

interface PerformanceTestResults {
  executionTimes: Record<string, number>;
  cacheMetrics: {
    hits: number;
    misses: number;
    hitRate: number;
  };
  queryMetrics: {
    totalQueries: number;
    slowQueries: QueryAnalysis[];
  };
  recommendations: string[];
}

interface ValidationTestResults {
  totalSalesAccuracy: boolean;
  branchDataConsistency: boolean;
  topPerformersValidity: boolean;
  dataIntegrityChecks: boolean;
  errors: string[];
}

interface QueryAnalysis {
  operation: string;
  executionTime: number;
  cacheHit: boolean;
  recommendation?: string;
}

interface StatsComparisonResult {
  metric: string;
  aggregationValue: number;
  directQueryValue: number;
  match: boolean;
  difference: number;
}

export default function StatsAggregationDebug() {
  const [isLoading, setIsLoading] = useState(false);
  const [performanceResults, setPerformanceResults] = useState<PerformanceTestResults | null>(null);
  const [validationResults, setValidationResults] = useState<ValidationTestResults | null>(null);
  const [comparisonResults, setComparisonResults] = useState<StatsComparisonResult[]>([]);
  const [testDate, setTestDate] = useState(() => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    // Use local timezone to avoid date shifts
    const year = yesterday.getFullYear();
    const month = String(yesterday.getMonth() + 1).padStart(2, '0');
    const day = String(yesterday.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  });

  // Only render in development environment
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const runPerformanceTest = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-aggregation/performance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ date: testDate })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // Ensure result has the expected structure
      setPerformanceResults({
        executionTimes: result.executionTimes || {},
        cacheMetrics: result.cacheMetrics || { hits: 0, misses: 0, hitRate: 0 },
        queryMetrics: result.queryMetrics || { totalQueries: 0, slowQueries: [] },
        recommendations: result.recommendations || []
      });
    } catch (error) {
      console.error('Performance test failed:', error);
      setPerformanceResults({
        executionTimes: {},
        cacheMetrics: { hits: 0, misses: 0, hitRate: 0 },
        queryMetrics: { totalQueries: 0, slowQueries: [] },
        recommendations: [`Failed to run performance tests: ${error instanceof Error ? error.message : 'Unknown error'}`]
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runValidationTest = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-aggregation/validation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ date: testDate })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      setValidationResults({
        totalSalesAccuracy: result.totalSalesAccuracy || false,
        branchDataConsistency: result.branchDataConsistency || false,
        topPerformersValidity: result.topPerformersValidity || false,
        dataIntegrityChecks: result.dataIntegrityChecks || false,
        errors: result.errors || []
      });
    } catch (error) {
      console.error('Validation test failed:', error);
      setValidationResults({
        totalSalesAccuracy: false,
        branchDataConsistency: false,
        topPerformersValidity: false,
        dataIntegrityChecks: false,
        errors: [`Failed to run validation tests: ${error instanceof Error ? error.message : 'Unknown error'}`]
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runComparisonTest = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin-dashboard/debug/stats-aggregation/comparison', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ date: testDate })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      setComparisonResults(result.comparisons || []);
    } catch (error) {
      console.error('Comparison test failed:', error);
      setComparisonResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  const clearCache = async () => {
    setIsLoading(true);
    try {
      await fetch('/api/admin-dashboard/debug/stats-aggregation/clear-cache', {
        method: 'POST'
      });
      alert('Cache cleared successfully');
    } catch (error) {
      console.error('Failed to clear cache:', error);
      alert('Failed to clear cache');
    } finally {
      setIsLoading(false);
    }
  };

  const warmCache = async () => {
    setIsLoading(true);
    try {
      // Warm cache for the last 7 days
      const dates = [];
      for (let i = 0; i < 7; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        // Use local timezone to avoid date shifts
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        dates.push(`${year}-${month}-${day}`);
      }

      await fetch('/api/admin-dashboard/debug/stats-aggregation/warm-cache', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ dates })
      });
      alert('Cache warmed successfully for last 7 days');
    } catch (error) {
      console.error('Failed to warm cache:', error);
      alert('Failed to warm cache');
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: boolean) => {
    return status ? 
      <CheckCircle className="h-4 w-4 text-green-500" /> : 
      <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getPerformanceBadge = (time: number) => {
    if (time < 500) return <Badge variant="default">Fast</Badge>;
    if (time < 2000) return <Badge variant="secondary">Good</Badge>;
    return <Badge variant="destructive">Slow</Badge>;
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          Stats Aggregation Debug Tool
        </CardTitle>
        <CardDescription>
          Test and validate the statistics aggregation service performance and accuracy
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="performance" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="validation">Validation</TabsTrigger>
            <TabsTrigger value="comparison">Comparison</TabsTrigger>
            <TabsTrigger value="cache">Cache</TabsTrigger>
          </TabsList>

          <TabsContent value="performance" className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded p-4">
              <h3 className="font-medium text-blue-800 mb-2">Performance Monitoring</h3>
              <p className="text-blue-700 text-sm">
                Monitor query execution times, cache effectiveness, and identify performance bottlenecks.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="performanceDate">Test Date</Label>
                <Input
                  id="performanceDate"
                  type="date"
                  value={testDate}
                  onChange={(e) => setTestDate(e.target.value)}
                />
              </div>
            </div>

            <Button
              onClick={runPerformanceTest}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Clock className="h-4 w-4 mr-2" />}
              Run Performance Test
            </Button>

            {performanceResults && (
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Execution Times</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {Object.entries(performanceResults.executionTimes || {}).length > 0 ? (
                        Object.entries(performanceResults.executionTimes || {}).map(([operation, time]) => (
                          <div key={operation} className="flex justify-between items-center">
                            <span className="text-sm">{operation}</span>
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-mono">{time}ms</span>
                              {getPerformanceBadge(time)}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-sm text-muted-foreground">No execution time data available</div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Cache Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{performanceResults.cacheMetrics?.hits || 0}</div>
                        <div className="text-sm text-muted-foreground">Cache Hits</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">{performanceResults.cacheMetrics?.misses || 0}</div>
                        <div className="text-sm text-muted-foreground">Cache Misses</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{(performanceResults.cacheMetrics?.hitRate || 0).toFixed(1)}%</div>
                        <div className="text-sm text-muted-foreground">Hit Rate</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {(performanceResults.recommendations || []).length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Recommendations:</strong>
                      <ul className="mt-2 list-disc list-inside">
                        {(performanceResults.recommendations || []).map((rec, index) => (
                          <li key={index} className="text-sm">{rec}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="validation" className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded p-4">
              <h3 className="font-medium text-green-800 mb-2">Data Validation</h3>
              <p className="text-green-700 text-sm">
                Cross-reference aggregated data with direct queries to ensure calculation accuracy.
              </p>
            </div>

            <Button
              onClick={runValidationTest}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Database className="h-4 w-4 mr-2" />}
              Run Validation Test
            </Button>

            {validationResults && (
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Validation Results</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(validationResults.totalSalesAccuracy)}
                        <span>Total Sales Accuracy</span>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(validationResults.branchDataConsistency)}
                        <span>Branch Data Consistency</span>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(validationResults.topPerformersValidity)}
                        <span>Top Performers Validity</span>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(validationResults.dataIntegrityChecks)}
                        <span>Data Integrity Checks</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {(validationResults.errors || []).length > 0 && (
                  <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Validation Errors:</strong>
                      <ul className="mt-2 list-disc list-inside">
                        {(validationResults.errors || []).map((error, index) => (
                          <li key={index} className="text-sm">{error}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="comparison" className="space-y-4">
            <div className="bg-purple-50 border border-purple-200 rounded p-4">
              <h3 className="font-medium text-purple-800 mb-2">Data Comparison</h3>
              <p className="text-purple-700 text-sm">
                Compare aggregation service results with individual database queries to identify discrepancies.
              </p>
            </div>

            <Button
              onClick={runComparisonTest}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <BarChart3 className="h-4 w-4 mr-2" />}
              Run Comparison Test
            </Button>

            {comparisonResults.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Comparison Results</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {comparisonResults.map((result, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(result.match)}
                          <span className="font-medium">{result.metric}</span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm">
                            Aggregation: <span className="font-mono">{result.aggregationValue}</span>
                          </div>
                          <div className="text-sm">
                            Direct Query: <span className="font-mono">{result.directQueryValue}</span>
                          </div>
                          {!result.match && (
                            <div className="text-sm text-red-600">
                              Difference: <span className="font-mono">{result.difference}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="cache" className="space-y-4">
            <div className="bg-orange-50 border border-orange-200 rounded p-4">
              <h3 className="font-medium text-orange-800 mb-2">Cache Management</h3>
              <p className="text-orange-700 text-sm">
                Monitor and manage the aggregation service cache for optimal performance.
              </p>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <Button
                onClick={clearCache}
                disabled={isLoading}
                variant="outline"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Zap className="h-4 w-4 mr-2" />}
                Clear Cache
              </Button>

              <Button
                onClick={warmCache}
                disabled={isLoading}
                variant="outline"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Database className="h-4 w-4 mr-2" />}
                Warm Cache
              </Button>

              <Button
                onClick={runPerformanceTest}
                disabled={isLoading}
                variant="outline"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Clock className="h-4 w-4 mr-2" />}
                Test Performance
              </Button>
            </div>

            {performanceResults && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Cache Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Cache Effectiveness</div>
                      <div className="text-2xl font-bold text-blue-600">
                        {(performanceResults.cacheMetrics?.hitRate || 0).toFixed(1)}%
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {performanceResults.cacheMetrics?.hits || 0} hits, {performanceResults.cacheMetrics?.misses || 0} misses
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Total Queries</div>
                      <div className="text-2xl font-bold text-green-600">
                        {performanceResults.queryMetrics?.totalQueries || 0}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Database queries executed
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Cache TTL</div>
                      <div className="text-2xl font-bold text-orange-600">
                        30m
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Cache timeout duration
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-muted rounded">
                    <div className="text-sm font-medium mb-2">Cache Performance Tips:</div>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Use "Warm Cache" to pre-load frequently accessed dates</li>
                      <li>• Cache TTL is set to 30 minutes for optimal performance</li>
                      <li>• Higher hit rates indicate better cache utilization</li>
                      <li>• Clear cache when testing new aggregation logic</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
