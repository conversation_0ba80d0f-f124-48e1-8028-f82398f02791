'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Settings, Send, Activity, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

interface CronJobDebugProps {
  onJobExecuted?: (result: JobExecutionResult) => void;
  onHealthCheck?: (result: HealthCheckResult) => void;
}

interface JobExecutionResult {
  jobName: string;
  success: boolean;
  executionTime: number;
  result?: any;
  error?: string;
  timestamp: Date;
}

interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: Record<string, boolean>;
  errors: string[];
  timestamp: Date;
}

export const CronJobDebug: React.FC<CronJobDebugProps> = ({
  onJobExecuted,
  onHealthCheck
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(() => {
    const today = new Date();
    // Use local timezone to avoid date shifts
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  });
  const [jobLogs, setJobLogs] = useState<any[]>([]);
  const [jobStats, setJobStats] = useState<any>(null);
  const [healthStatus, setHealthStatus] = useState<HealthCheckResult | null>(null);

  const executeManualJob = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/admin-dashboard/cron-job-manual', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jobName: 'daily-stats-email',
          date: selectedDate
        })
      });

      if (!response.ok) {
        throw new Error('Failed to execute job');
      }

      const result = await response.json();
      
      const executionResult: JobExecutionResult = {
        jobName: 'daily-stats-email',
        success: result.success,
        executionTime: result.executionTime || 0,
        result: result.data,
        timestamp: new Date()
      };

      if (onJobExecuted) {
        onJobExecuted(executionResult);
      }

      toast.success(`Job executed successfully: ${result.data?.successCount || 0} emails sent`);

      // Refresh logs and stats after a short delay to allow logging to complete
      setTimeout(async () => {
        await loadJobData();
      }, 1000);
      
    } catch (error) {
      const executionResult: JobExecutionResult = {
        jobName: 'daily-stats-email',
        success: false,
        executionTime: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      };

      if (onJobExecuted) {
        onJobExecuted(executionResult);
      }

      toast.error(`Job execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const executeExportCleanup = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/admin-dashboard/export-cleanup-manual', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('Export cleanup failed');
      }

      const result = await response.json();

      toast.success(`Export cleanup completed successfully`);

      // Reload job data to show updated logs
      await loadJobData();

    } catch (error) {
      console.error('Export cleanup error:', error);
      toast.error(`Export cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const performHealthCheck = async () => {
    try {
      const response = await fetch('/api/admin-dashboard/stats-email-health');

      if (!response.ok) {
        throw new Error('Health check failed');
      }

      const result = await response.json();

      const healthResult: HealthCheckResult = {
        ...result,
        timestamp: new Date()
      };

      setHealthStatus(healthResult);

      if (onHealthCheck) {
        onHealthCheck(healthResult);
      }

      const statusColor = result.status === 'healthy' ? 'success' :
                         result.status === 'degraded' ? 'warning' : 'error';

      toast[statusColor](`Health check completed: ${result.status}`);

    } catch (error) {
      const healthResult: HealthCheckResult = {
        status: 'unhealthy',
        checks: {},
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        timestamp: new Date()
      };

      setHealthStatus(healthResult);

      if (onHealthCheck) {
        onHealthCheck(healthResult);
      }

      toast.error(`Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const loadJobData = async () => {
    try {
      const [logsResponse, statsResponse] = await Promise.all([
        fetch('/api/admin-dashboard/cron-job-logs?jobName=daily-stats-email'),
        fetch('/api/admin-dashboard/cron-job-stats?jobName=daily-stats-email')
      ]);

      if (logsResponse.ok) {
        const logsData = await logsResponse.json();
        setJobLogs(logsData.logs || []);
      }

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setJobStats(statsData.stats);
      }
    } catch (error) {
      console.error('Error loading job data:', error);
    }
  };

  useEffect(() => {
    loadJobData();
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Cron Job Debug - Daily Stats Email
        </CardTitle>
        <CardDescription>
          Test and monitor the daily statistics email cron job
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Manual Execution */}
        <div className="space-y-4">
          <h3 className="font-medium">Manual Execution</h3>
          <div className="flex items-center gap-4">
            <div>
              <label className="text-sm font-medium">Date</label>
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="flex gap-2 flex-wrap">
              <Button
                onClick={executeManualJob}
                disabled={loading}
                className="flex items-center gap-2"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Send className="h-4 w-4" />
                )}
                Execute Job
              </Button>
              <Button
                onClick={performHealthCheck}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Activity className="h-4 w-4" />
                Health Check
              </Button>
              <Button
                onClick={executeExportCleanup}
                disabled={loading}
                variant="secondary"
                className="flex items-center gap-2"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                Cleanup Exports
              </Button>
            </div>
          </div>
        </div>

        {/* Health Status */}
        {healthStatus && (
          <div className="space-y-4">
            <h3 className="font-medium">Health Status</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Badge 
                  variant={
                    healthStatus.status === 'healthy' ? 'default' : 
                    healthStatus.status === 'degraded' ? 'secondary' : 'destructive'
                  }
                >
                  {healthStatus.status.toUpperCase()}
                </Badge>
                <span className="text-sm text-gray-500">
                  {healthStatus.timestamp.toLocaleString()}
                </span>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mb-2">
                {Object.entries(healthStatus.checks).map(([check, status]) => (
                  <div key={check} className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${status ? 'bg-green-500' : 'bg-red-500'}`} />
                    <span className="text-sm">{check}</span>
                  </div>
                ))}
              </div>
              {healthStatus.errors.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm font-medium text-red-600">Errors:</p>
                  <ul className="text-sm text-red-600 list-disc list-inside">
                    {healthStatus.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Job Statistics */}
        {jobStats && (
          <div className="space-y-4">
            <h3 className="font-medium">Job Statistics</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {jobStats.totalExecutions}
                </div>
                <div className="text-sm text-gray-600">Total Executions</div>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {jobStats.successRate.toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">Success Rate</div>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round(jobStats.averageExecutionTime)}ms
                </div>
                <div className="text-sm text-gray-600">Avg Time</div>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {jobStats.lastExecution 
                    ? new Date(jobStats.lastExecution).toLocaleDateString()
                    : 'Never'
                  }
                </div>
                <div className="text-sm text-gray-600">Last Run</div>
              </div>
            </div>
          </div>
        )}

        {/* Recent Logs */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium">Recent Executions</h3>
            <Button onClick={loadJobData} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {jobLogs.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No execution logs found</p>
            ) : (
              jobLogs.map((log, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    log.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant={log.success ? "default" : "destructive"}>
                        {log.success ? 'Success' : 'Failed'}
                      </Badge>
                      <span className="text-sm">
                        {new Date(log.timestamp).toLocaleString()}
                      </span>
                    </div>
                    <span className="text-sm text-gray-500">
                      {log.executionTime}ms
                    </span>
                  </div>
                  {log.error && (
                    <p className="text-sm text-red-600 mt-2">{log.error}</p>
                  )}
                  {log.totalEmails && (
                    <p className="text-sm text-gray-600 mt-2">
                      Emails: {log.successCount}/{log.totalEmails} sent successfully
                    </p>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
