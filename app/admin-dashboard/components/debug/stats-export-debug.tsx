'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Download, FileText, FileSpreadsheet, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface ExportResult {
  success: boolean;
  downloadUrl?: string;
  fileName?: string;
  expiresAt?: string;
  error?: string;
}

interface EmailSetting {
  _id: string;
  email: string;
  selectedStats: Record<string, boolean>;
}

export default function StatsExportDebug() {
  const [emailSettings, setEmailSettings] = useState<EmailSetting[]>([]);
  const [selectedEmailSetting, setSelectedEmailSetting] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(() => {
    const today = new Date();
    // Use local timezone to avoid date shifts
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  });
  const [exportResults, setExportResults] = useState<{
    csv?: ExportResult;
    pdf?: ExportResult;
  }>({});
  const [isLoading, setIsLoading] = useState(false);
  const [loadingEmailSettings, setLoadingEmailSettings] = useState(false);

  const loadEmailSettings = async () => {
    setLoadingEmailSettings(true);
    try {
      const response = await fetch('/api/admin-dashboard/stats-email-settings');
      if (response.ok) {
        const data = await response.json();
        setEmailSettings(data.settings || []);
        toast.success('Email settings loaded successfully');
      } else {
        throw new Error('Failed to load email settings');
      }
    } catch (error) {
      console.error('Error loading email settings:', error);
      toast.error('Failed to load email settings');
    } finally {
      setLoadingEmailSettings(false);
    }
  };

  const generateExport = async (format: 'csv' | 'pdf') => {
    if (!selectedEmailSetting || !selectedDate) {
      toast.error('Please select an email setting and date');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/stats-export/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailSettingId: selectedEmailSetting,
          date: selectedDate,
          format
        }),
      });

      const result = await response.json();

      if (result.success) {
        setExportResults(prev => ({
          ...prev,
          [format]: result
        }));
        toast.success(`${format.toUpperCase()} export generated successfully`);
      } else {
        throw new Error(result.error || 'Export generation failed');
      }
    } catch (error) {
      console.error(`Error generating ${format} export:`, error);
      toast.error(`Failed to generate ${format.toUpperCase()} export`);
      setExportResults(prev => ({
        ...prev,
        [format]: {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const downloadFile = (url: string, fileName: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const selectedSetting = emailSettings.find(setting => setting._id === selectedEmailSetting);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Stats Export Debug Tool
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Load Email Settings */}
          <div className="space-y-2">
            <Label>Email Settings</Label>
            <div className="flex gap-2">
              <Button
                onClick={loadEmailSettings}
                disabled={loadingEmailSettings}
                variant="outline"
              >
                {loadingEmailSettings ? 'Loading...' : 'Load Email Settings'}
              </Button>
              <Badge variant="secondary">
                {emailSettings.length} settings loaded
              </Badge>
            </div>
          </div>

          <Separator />

          {/* Export Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="emailSetting">Email Setting</Label>
              <Select value={selectedEmailSetting} onValueChange={setSelectedEmailSetting}>
                <SelectTrigger>
                  <SelectValue placeholder="Select email setting" />
                </SelectTrigger>
                <SelectContent>
                  {emailSettings.map((setting) => (
                    <SelectItem key={setting._id} value={setting._id}>
                      {setting.email}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <Input
                id="date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </div>
          </div>

          {/* Selected Stats Display */}
          {selectedSetting && (
            <div className="space-y-2">
              <Label>Selected Statistics</Label>
              <div className="flex flex-wrap gap-2">
                {Object.entries(selectedSetting.selectedStats)
                  .filter(([_, enabled]) => enabled)
                  .map(([stat, _]) => (
                    <Badge key={stat} variant="outline">
                      {stat}
                    </Badge>
                  ))}
              </div>
            </div>
          )}

          <Separator />

          {/* Export Actions */}
          <div className="space-y-4">
            <Label>Generate Exports</Label>
            <div className="flex gap-4">
              <Button
                onClick={() => generateExport('csv')}
                disabled={isLoading || !selectedEmailSetting || !selectedDate}
                className="flex items-center gap-2"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Generate CSV
              </Button>
              <Button
                onClick={() => generateExport('pdf')}
                disabled={isLoading || !selectedEmailSetting || !selectedDate}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                Generate PDF
              </Button>
            </div>
          </div>

          <Separator />

          {/* Export Results */}
          <div className="space-y-4">
            <Label>Export Results</Label>
            
            {/* CSV Results */}
            {exportResults.csv && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4" />
                    CSV Export
                    {exportResults.csv.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  {exportResults.csv.success ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        Expires: {new Date(exportResults.csv.expiresAt!).toLocaleString()}
                      </div>
                      <Button
                        size="sm"
                        onClick={() => downloadFile(exportResults.csv!.downloadUrl!, exportResults.csv!.fileName!)}
                        className="flex items-center gap-2"
                      >
                        <Download className="h-3 w-3" />
                        Download CSV
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-sm text-red-600">
                      <AlertCircle className="h-3 w-3" />
                      {exportResults.csv.error}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* PDF Results */}
            {exportResults.pdf && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    PDF Export
                    {exportResults.pdf.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  {exportResults.pdf.success ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        Expires: {new Date(exportResults.pdf.expiresAt!).toLocaleString()}
                      </div>
                      <Button
                        size="sm"
                        onClick={() => downloadFile(exportResults.pdf!.downloadUrl!, exportResults.pdf!.fileName!)}
                        className="flex items-center gap-2"
                      >
                        <Download className="h-3 w-3" />
                        Download PDF
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-sm text-red-600">
                      <AlertCircle className="h-3 w-3" />
                      {exportResults.pdf.error}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
