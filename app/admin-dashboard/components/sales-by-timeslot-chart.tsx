import React, { use<PERSON>emo, useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLanguage } from '@/lib/contexts/language-context';
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { useSalesByTimeslot, SalesByTimeslotStat } from '../hooks/useSalesByTimeslot';
import { DateRange } from 'react-day-picker';
import { dashboardFetcher } from '../utils/dashboard-fetcher';
import { BarChart3, GitCompare, Building2, Table } from 'lucide-react';

// Utility function for date formatting (using UTC to avoid timezone issues)
function formatDateLocal(date: Date | undefined): string {
  if (!date) return '';
  // Use UTC methods to avoid timezone conversion issues
  const year = date.getUTCFullYear();
  const month = (date.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = date.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

interface SalesByTimeslotChartProps {
  branchId: string | string[] | null;
  dateRange?: DateRange;
}

// Color palette for time slots
const TIMESLOT_COLORS = [
  '#3b82f6', // blue - 11:00
  '#10b981', // green - 13:00
  '#f59e0b', // amber - 15:00
  '#ef4444', // red - 17:00
  '#8b5cf6', // violet - 19:00
];

// Color palette for branches (when showing multiple branches)
const BRANCH_COLORS = [
  '#60a5fa', // blue
  '#34d399', // green
  '#fbbf24', // yellow
  '#f472b6', // pink
  '#a78bfa', // purple
  '#38bdf8', // sky
  '#f87171', // red
  '#facc15', // amber
  '#4ade80', // emerald
  '#818cf8', // indigo
];

type ViewMode = 'combined' | 'comparison' | 'individual';
type ComparisonMode = 'chart' | 'table';

export const SalesByTimeslotChart: React.FC<SalesByTimeslotChartProps> = ({ branchId, dateRange }) => {
  const { t } = useLanguage();
  
  const startDate = dateRange?.from ? formatDateLocal(dateRange.from) : undefined;
  const endDate = dateRange?.to ? formatDateLocal(dateRange.to) : undefined;

  const { stats, totals, loading, error, refresh } = useSalesByTimeslot(
    branchId,
    startDate,
    endDate
  );

  // View mode and branch selection state
  const [viewMode, setViewMode] = useState<ViewMode>('combined');
  const [comparisonMode, setComparisonMode] = useState<ComparisonMode>('chart');
  const [selectedBranchForIndividual, setSelectedBranchForIndividual] = useState<string>('');

  // Fetch branch names and data
  const [branchMap, setBranchMap] = useState<Record<string, string>>({});
  const [availableBranches, setAvailableBranches] = useState<Array<{ _id: string; name: string }>>([]);
  const isMultipleBranches = branchId === 'all' || (Array.isArray(branchId) && branchId.length > 1);
  
  useEffect(() => {
    dashboardFetcher('/api/branches')
      .then(data => {
        const map: Record<string, string> = {};
        data.forEach((b: { _id: string; name: string }) => {
          map[b._id] = b.name;
        });
        setBranchMap(map);
        setAvailableBranches(data);
      })
      .catch(err => {
        console.error('Error fetching branches for sales by timeslot chart:', err);
      });
  }, []);

  // Get available branches from the current data
  const branchesInData = useMemo(() => {
    const branchIds = new Set<string>();
    stats.forEach(day => {
      Object.values(day.timeslots).forEach(slotData => {
        if (typeof slotData === 'object' && slotData !== null) {
          Object.keys(slotData).forEach(branchId => branchIds.add(branchId));
        }
      });
    });
    return Array.from(branchIds).filter(id => branchMap[id]);
  }, [stats, branchMap]);

  // Get branches available for individual selection (all branches that are currently selected)
  const availableBranchesForIndividual = useMemo(() => {
    if (branchId === 'all') {
      return availableBranches.map(b => b._id);
    } else if (Array.isArray(branchId)) {
      return branchId;
    } else if (branchId) {
      return [branchId];
    }
    return [];
  }, [branchId, availableBranches]);

  // Set default selected branch for individual view
  useEffect(() => {
    if (availableBranchesForIndividual.length > 0 && !selectedBranchForIndividual) {
      setSelectedBranchForIndividual(availableBranchesForIndividual[0]);
    }
  }, [availableBranchesForIndividual, selectedBranchForIndividual]);

  // Auto-set view mode based on data availability
  useEffect(() => {
    if (!isMultipleBranches) {
      setViewMode('combined');
    }
  }, [isMultipleBranches]);

  // Function to handle retry button click
  const handleRetry = () => {
    refresh();
  };

  // Prepare chart data based on view mode
  const chartData = useMemo(() => {
    const timeSlots = ['11:00', '13:00', '15:00', '17:00', '19:00'];
    
    return stats.map((day: SalesByTimeslotStat) => {
      const dayData: any = {
        day: t(`common.days.${day.dayName.toLowerCase()}`) || day.dayNameFr,
        dayIndex: day.dayIndex
      };

      timeSlots.forEach(slot => {
        const slotData = day.timeslots[slot];
        
        if (viewMode === 'individual' && selectedBranchForIndividual) {
          // Show data for selected branch only
          if (typeof slotData === 'object' && slotData !== null) {
            dayData[slot] = (slotData as Record<string, number>)[selectedBranchForIndividual] || 0;
          } else if (typeof slotData === 'number') {
            // If it's a single branch selection and data is a number, use it directly
            dayData[slot] = slotData;
          } else {
            dayData[slot] = 0;
          }
        } else if (viewMode === 'comparison' && isMultipleBranches) {
          // For comparison view, group by timeslot with branches as separate series
          if (typeof slotData === 'object' && slotData !== null) {
            branchesInData.forEach(branchId => {
              const branchName = branchMap[branchId] || branchId;
              dayData[`${slot}_${branchName}`] = (slotData as Record<string, number>)[branchId] || 0;
            });
          } else {
            // If no object data, set all branches to 0 for this slot
            branchesInData.forEach(branchId => {
              const branchName = branchMap[branchId] || branchId;
              dayData[`${slot}_${branchName}`] = 0;
            });
          }
        } else {
          // Combined view - sum all branches or show single branch data
          if (typeof slotData === 'object' && slotData !== null) {
            dayData[slot] = Object.values(slotData as Record<string, number>).reduce((sum, val) => sum + val, 0);
          } else {
            dayData[slot] = typeof slotData === 'number' ? slotData : 0;
          }
        }
      });

      return dayData;
    });
  }, [stats, t, isMultipleBranches, viewMode, selectedBranchForIndividual, branchesInData, branchMap]);

  // Generate bars for the chart based on view mode
  const chartBars = useMemo(() => {
    const timeSlots = ['11:00', '13:00', '15:00', '17:00', '19:00'];
    
    if (viewMode === 'comparison' && isMultipleBranches && branchesInData.length > 0) {
      // Create bars grouped by timeslot, with each branch as a separate bar
      const bars: JSX.Element[] = [];
      
      timeSlots.forEach((slot, slotIndex) => {
        branchesInData.forEach((branchId, branchIndex) => {
          const branchName = branchMap[branchId] || branchId;
          const key = `${slot}_${branchName}`;
          const baseColor = TIMESLOT_COLORS[slotIndex];
          // Create variations of the timeslot color for different branches
          const opacity = 0.6 + (branchIndex * 0.15);
          bars.push(
            <Bar
              key={key}
              dataKey={key}
              fill={baseColor}
              fillOpacity={opacity}
              name={`${slot} - ${branchName}`}
              radius={[2, 2, 0, 0]}
            />
          );
        });
      });
      return bars;
    } else {
      // Standard timeslot bars for combined and individual views
      return timeSlots.map((slot, index) => (
        <Bar
          key={slot}
          dataKey={slot}
          fill={TIMESLOT_COLORS[index]}
          name={slot}
          radius={[2, 2, 0, 0]}
        />
      ));
    }
  }, [viewMode, isMultipleBranches, branchesInData, branchMap]);

  // Prepare comparison table data
  const comparisonTableData = useMemo(() => {
    if (viewMode !== 'comparison' || !isMultipleBranches) return [];
    
    const timeSlots = ['11:00', '13:00', '15:00', '17:00', '19:00'];
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    
    return stats.map((day: SalesByTimeslotStat) => {
      const dayData: any = {
        day: t(`common.days.${day.dayName.toLowerCase()}`) || day.dayNameFr,
        dayIndex: day.dayIndex
      };
      
      timeSlots.forEach(slot => {
        const slotData = day.timeslots[slot];
        if (typeof slotData === 'object' && slotData !== null) {
          branchesInData.forEach(branchId => {
            const branchName = branchMap[branchId] || branchId;
            dayData[`${slot}_${branchName}`] = (slotData as Record<string, number>)[branchId] || 0;
          });
        }
      });
      
      return dayData;
    });
  }, [stats, viewMode, isMultipleBranches, branchesInData, branchMap, t]);

  // Custom tooltip for the bar chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow border border-gray-200 dark:border-gray-700 text-sm">
          <p className="font-semibold mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded" 
                style={{ backgroundColor: entry.color }}
              />
              <span>{entry.name || entry.dataKey}: {entry.value} sales</span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  // Calculate totals for individual branch view
  const individualBranchTotals = useMemo(() => {
    if (viewMode !== 'individual' || !selectedBranchForIndividual) {
      return { totalSales: 0, totalAmount: 0 };
    }

    let totalSales = 0;
    stats.forEach(day => {
      Object.values(day.timeslots).forEach(slotData => {
        if (typeof slotData === 'object' && slotData !== null) {
          totalSales += (slotData as Record<string, number>)[selectedBranchForIndividual] || 0;
        } else if (typeof slotData === 'number') {
          totalSales += slotData;
        }
      });
    });

    return { totalSales, totalAmount: 0 }; // Amount calculation would need API enhancement
  }, [stats, viewMode, selectedBranchForIndividual]);

  const displayTotals = viewMode === 'individual' ? individualBranchTotals : totals;

  // Render comparison table
  const renderComparisonTable = () => {
    const timeSlots = ['11:00', '13:00', '15:00', '17:00', '19:00'];
    
    return (
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b border-emerald-200 dark:border-emerald-800">
              <th className="text-left p-2 font-semibold">Day</th>
              {timeSlots.map(slot => (
                <th key={slot} className="text-center p-2 font-semibold" colSpan={branchesInData.length}>
                  {slot}
                </th>
              ))}
            </tr>
            <tr className="border-b border-emerald-200 dark:border-emerald-800">
              <th></th>
              {timeSlots.map(slot => 
                branchesInData.map(branchId => (
                  <th key={`${slot}_${branchId}`} className="text-center p-1 text-xs">
                    {branchMap[branchId] || branchId}
                  </th>
                ))
              )}
            </tr>
          </thead>
          <tbody>
            {comparisonTableData.map((day, dayIndex) => (
              <tr key={dayIndex} className="border-b border-emerald-100 dark:border-emerald-900 hover:bg-emerald-50 dark:hover:bg-emerald-950">
                <td className="p-2 font-medium">{day.day}</td>
                {timeSlots.map(slot => 
                  branchesInData.map((branchId, branchIndex) => {
                    const branchName = branchMap[branchId] || branchId;
                    const value = day[`${slot}_${branchName}`] || 0;
                    const color = BRANCH_COLORS[branchIndex % BRANCH_COLORS.length];
                    return (
                      <td key={`${slot}_${branchId}`} className="text-center p-1">
                        <span 
                          className="inline-block px-2 py-1 rounded text-xs font-medium"
                          style={{ 
                            backgroundColor: `${color}20`,
                            color: color,
                            border: `1px solid ${color}40`
                          }}
                        >
                          {value}
                        </span>
                      </td>
                    );
                  })
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <Card className="bg-gradient-to-br from-emerald-100 to-emerald-300 dark:from-emerald-900 dark:to-emerald-700 text-emerald-900 dark:text-emerald-100 shadow-lg border-0 relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
            />
          </svg>
          <span>Sales by Time Slot</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* View Mode Controls - Only show if multiple branches */}
        {isMultipleBranches && (
          <div className="mb-4 space-y-3">
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as ViewMode)} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="combined" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  <span className="hidden sm:inline">Combined</span>
                </TabsTrigger>
                <TabsTrigger value="comparison" className="flex items-center gap-2">
                  <GitCompare className="h-4 w-4" />
                  <span className="hidden sm:inline">Compare</span>
                </TabsTrigger>
                <TabsTrigger value="individual" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  <span className="hidden sm:inline">Individual</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Comparison mode toggle */}
            {viewMode === 'comparison' && (
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">View:</span>
                <Tabs value={comparisonMode} onValueChange={(value) => setComparisonMode(value as ComparisonMode)} className="w-auto">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="chart" className="flex items-center gap-2">
                      <BarChart3 className="h-3 w-3" />
                      <span>Chart</span>
                    </TabsTrigger>
                    <TabsTrigger value="table" className="flex items-center gap-2">
                      <Table className="h-3 w-3" />
                      <span>Table</span>
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            )}

            {/* Branch selector for individual view */}
            {viewMode === 'individual' && (
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Branch:</span>
                <Select value={selectedBranchForIndividual} onValueChange={setSelectedBranchForIndividual}>
                  <SelectTrigger className="w-[200px] bg-white/20 border-white/30">
                    <SelectValue placeholder="Select branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableBranchesForIndividual.map((branchId) => (
                      <SelectItem key={branchId} value={branchId}>
                        {branchMap[branchId] || branchId}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Branch legend for comparison view */}
            {viewMode === 'comparison' && branchesInData.length > 0 && (
              <div className="flex flex-wrap gap-2">
                <span className="text-sm font-medium">Branches:</span>
                {branchesInData.map((branchId, index) => (
                  <Badge 
                    key={branchId} 
                    variant="secondary" 
                    className="text-xs"
                    style={{ 
                      backgroundColor: `${BRANCH_COLORS[index % BRANCH_COLORS.length]}20`,
                      borderColor: BRANCH_COLORS[index % BRANCH_COLORS.length],
                      color: BRANCH_COLORS[index % BRANCH_COLORS.length]
                    }}
                  >
                    {branchMap[branchId] || branchId}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center h-[400px]">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-emerald-500"></div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-[400px]">
            <span className="text-red-600 dark:text-red-400 mb-2">{error}</span>
            <Button variant="outline" size="sm" onClick={handleRetry}>
              Retry
            </Button>
          </div>
        ) : chartData.length === 0 ? (
          <div className="flex items-center justify-center h-[400px]">
            <span className="text-gray-500 dark:text-gray-400">No data available</span>
          </div>
        ) : (
          <>
            {/* Render chart or table based on comparison mode */}
            {viewMode === 'comparison' && comparisonMode === 'table' ? (
              <div className="min-h-[400px] bg-white/10 dark:bg-black/10 rounded-lg p-4">
                {renderComparisonTable()}
              </div>
            ) : (
              <div className="h-[400px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={chartData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#d1d5db" />
                    <XAxis 
                      dataKey="day"
                      tick={{ fill: 'currentColor', fontSize: 12 }}
                      tickMargin={10}
                    />
                    <YAxis 
                      tick={{ fill: 'currentColor', fontSize: 12 }}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    {chartBars}
                  </BarChart>
                </ResponsiveContainer>
              </div>
            )}
            
            {/* Summary stats */}
            <div className="flex flex-col sm:flex-row justify-around mt-4 gap-2">
              <div className="flex flex-col items-center p-2 rounded-lg bg-white/30 dark:bg-black/20">
                <span className="text-sm font-medium">
                  {viewMode === 'individual' && selectedBranchForIndividual 
                    ? `${branchMap[selectedBranchForIndividual]} - Total Sales`
                    : 'Total Sales'
                  }
                </span>
                <span className="text-xl font-bold">{displayTotals.totalSales}</span>
              </div>
              {viewMode !== 'individual' && (
                <div className="flex flex-col items-center p-2 rounded-lg bg-white/30 dark:bg-black/20">
                  <span className="text-sm font-medium">Total Amount</span>
                  <span className="text-xl font-bold">${displayTotals.totalAmount.toLocaleString()}</span>
                </div>
              )}
            </div>
          </>
        )}
        
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 p-2 bg-emerald-50 dark:bg-emerald-950 text-xs rounded border border-emerald-200 dark:border-emerald-800">
            <strong>DEBUG:</strong> 
            <br />
            <div>Date range: {startDate || 'not set'} to {endDate || 'not set'}</div>
            <div>Branch ID: {Array.isArray(branchId) ? branchId.join(', ') : branchId}</div>
            <div>View Mode: {viewMode}</div>
            <div>Comparison Mode: {comparisonMode}</div>
            <div>Selected Branch: {selectedBranchForIndividual}</div>
            <div>Available Branches for Individual: {availableBranchesForIndividual.join(', ')}</div>
            <div>Branches in Data: {branchesInData.join(', ')}</div>
            <div>Is Multiple Branches: {isMultipleBranches.toString()}</div>
            <div>Data points: {stats.length}</div>
            <div>Total sales: {displayTotals.totalSales}, Total amount: ${displayTotals.totalAmount}</div>
            <div>Chart Bars Count: {chartBars.length}</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};