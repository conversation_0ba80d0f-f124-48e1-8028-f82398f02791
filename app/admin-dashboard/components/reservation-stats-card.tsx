import React, { useMemo, useRef, useEffect, useState } from 'react';
import { useReservationStats, ReservationStat, useReservationStatsAll } from '../hooks/useReservationStats';
import { format, parse } from 'date-fns';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useLanguage } from '@/lib/contexts/language-context';
import { useReservationSummaryStats } from '../hooks/useReservationSummaryStats';
import { dashboardFetcher } from '../utils/dashboard-fetcher';
import { Badge } from '@/components/ui/badge';

interface ReservationStatsCardProps {
  branchId: string | string[] | null;
}

const TIME_SLOTS = [
  //'09:00-11:00', // removed as no longer in use
  '11:00-13:00',
  '13:00-15:00',
  '15:00-17:00',
  '17:00-19:00',
  '19:00-21:00',
];

function getCellColor(count: number) {
  if (count >= 15) return 'bg-red-300';
  if (count >= 8) return 'bg-yellow-300';
  if (count > 0) return 'bg-green-200';
  return '';
}

// Color palette for branches
const BRANCH_COLORS = [
  'bg-blue-200 dark:bg-blue-700 text-blue-900 dark:text-blue-100',
  'bg-green-200 dark:bg-green-700 text-green-900 dark:text-green-100',
  'bg-yellow-200 dark:bg-yellow-700 text-yellow-900 dark:text-yellow-100',
  'bg-pink-200 dark:bg-pink-700 text-pink-900 dark:text-pink-100',
  'bg-purple-200 dark:bg-purple-700 text-purple-900 dark:text-purple-100',
  'bg-sky-200 dark:bg-sky-700 text-sky-900 dark:text-sky-100',
  'bg-red-200 dark:bg-red-700 text-red-900 dark:text-red-100',
  'bg-amber-200 dark:bg-amber-700 text-amber-900 dark:text-amber-100',
  'bg-emerald-200 dark:bg-emerald-700 text-emerald-900 dark:text-emerald-100',
  'bg-indigo-200 dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100',
];

const cardStyle =
  'bg-gradient-to-br from-teal-100 to-teal-300 dark:from-teal-900 dark:to-teal-700 text-teal-900 dark:text-teal-100 shadow-lg border-0 relative overflow-hidden';

// Map a startHour (e.g. '09:00') to a slot (e.g. '09:00-11:00')
function getSlotForStartHour(startHour: string): string | undefined {
  const hour = parseInt(startHour.split(':')[0], 10);
  if (hour >= 9 && hour < 11) return '09:00-11:00';
  if (hour >= 11 && hour < 13) return '11:00-13:00';
  if (hour >= 13 && hour < 15) return '13:00-15:00';
  if (hour >= 15 && hour < 17) return '15:00-17:00';
  if (hour >= 17 && hour < 19) return '17:00-19:00';
  if (hour >= 19 && hour < 21) return '19:00-21:00';
  return undefined;
}

export const ReservationStatsCard: React.FC<ReservationStatsCardProps> = ({ branchId }) => {
  const {
    stats,
    loading,
    error,
    loadMore,
    hasMore,
    isLoadingMore,
  } = useReservationStats(branchId, 10);
  const { t } = useLanguage();
  const summary = useReservationSummaryStats(branchId);

  // Mobile detection
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // New: Fetch all stats for slot totals in the sticky footer
  const {
    stats: allStats,
    loading: allLoading,
    error: allError,
  } = useReservationStatsAll(branchId);

  // Fetch branch names for legend if all branches
  const [branchMap, setBranchMap] = useState<Record<string, string>>({});
  useEffect(() => {
    if (branchId === 'all') {
      dashboardFetcher('/api/branches')
        .then(data => {
          const map: Record<string, string> = {};
          data.forEach((b: { _id: string; name: string }) => {
            map[b._id] = b.name;
          });
          setBranchMap(map);
        })
        .catch(err => {
          console.error('Error fetching branches for reservation stats:', err);
        });
    }
  }, [branchId]);

  // Infinite scroll: observe the sentinel div
  const sentinelRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    if (!hasMore || isLoadingMore) return;
    const observer = new window.IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { root: null, rootMargin: '0px', threshold: 1.0 }
    );
    if (sentinelRef.current) {
      observer.observe(sentinelRef.current);
    }
    return () => {
      if (sentinelRef.current) {
        observer.unobserve(sentinelRef.current);
      }
    };
  }, [hasMore, isLoadingMore, loadMore]);

  // Use only real stats
  const effectiveStats = stats;

  // Get all branchIds present in the data (for color assignment)
  const branchIds = useMemo(() => {
    if (branchId !== 'all') return [];
    const ids = new Set<string>();
    effectiveStats.forEach(s => { if (s._id.branchId) ids.add(s._id.branchId); });
    return Array.from(ids);
  }, [effectiveStats, branchId]);

  // Transform stats into a table: { [date]: { [time]: { [branchId]: count } } }
  const { table, dates, totals, slotTotals, grandTotal } = useMemo(() => {
    const table: Record<string, Record<string, Record<string, number>>> = {};
    const totals: Record<string, number> = {};
    const slotTotals: Record<string, number> = {};
    let grandTotal = 0;
    const datesSet = new Set<string>();
    if (effectiveStats) {
      for (const s of effectiveStats) {
        const date = s._id.date;
        const startHour = s._id.time;
        const slot = getSlotForStartHour(startHour);
        if (!slot) continue;
        const bId = s._id.branchId || 'default';
        datesSet.add(date);
        if (!table[date]) table[date] = {};
        if (!table[date][slot]) table[date][slot] = {};
        table[date][slot][bId] = (table[date][slot][bId] || 0) + s.count;
        totals[date] = (totals[date] || 0) + s.count;
        slotTotals[slot] = (slotTotals[slot] || 0) + s.count;
        grandTotal += s.count;
      }
    }
    // Sort dates descending (newest first)
    const dates = Array.from(datesSet).sort((a, b) => {
      // Parse as yyyy-MM-dd or dd-MMM-yyyy
      const parseDate = (d: string) => {
        // Try ISO first, fallback to d-MMM-yyyy
        const iso = Date.parse(d);
        if (!isNaN(iso)) return iso;
        try {
          return parse(d, 'dd-MMM-yyyy', new Date()).getTime();
        } catch {
          return 0;
        }
      };
      return parseDate(b) - parseDate(a);
    });
    return { table, dates, totals, slotTotals, grandTotal };
  }, [effectiveStats]);

  // Calculate slot totals for all data (for sticky footer)
  const allSlotTotals = React.useMemo(() => {
    const slotTotals: Record<string, number> = {};
    if (allStats) {
      for (const s of allStats) {
        const slot = getSlotForStartHour(s._id.time);
        if (!slot) continue;
        slotTotals[slot] = (slotTotals[slot] || 0) + s.count;
      }
    }
    return slotTotals;
  }, [allStats]);

  // 1. Calculate per-day, per-branch sales/presence for the first column
  const dayBranchStats = useMemo(() => {
    const stats: Record<string, Record<string, { sales: number; presence: number }>> = {};
    if (effectiveStats) {
      for (const s of effectiveStats) {
        const date = s._id.date;
        const bId = s._id.branchId || 'default';
        if (!stats[date]) stats[date] = {};
        if (!stats[date][bId]) stats[date][bId] = { sales: 0, presence: 0 };
        stats[date][bId].sales += s.salesCount || 0;
        stats[date][bId].presence += s.presenceCount || 0;
      }
    }
    return stats;
  }, [effectiveStats]);

  // 2. Calculate all-slot totals for sticky footer, by branch and slot
  const allSlotBranchTotals = useMemo(() => {
    const totals: Record<string, Record<string, { count: number; sales: number; presence: number }>> = {};
    if (allStats) {
      for (const s of allStats) {
        const slot = getSlotForStartHour(s._id.time);
        if (!slot) continue;
        const bId = s._id.branchId || 'default';
        if (!totals[slot]) totals[slot] = {};
        if (!totals[slot][bId]) totals[slot][bId] = { count: 0, sales: 0, presence: 0 };
        totals[slot][bId].count += s.count;
        totals[slot][bId].sales += s.salesCount || 0;
        totals[slot][bId].presence += s.presenceCount || 0;
      }
    }
    return totals;
  }, [allStats]);

  if (loading) {
    return (
      <Card className={cardStyle + ' min-h-[120px]'}>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-center">{t('dashboard.reservations')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center min-h-[80px]">
            <span className="text-gray-500 dark:text-gray-300 animate-pulse">{t('common.loading')}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cardStyle + ' min-h-[120px]'}>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-center text-red-200">{t('common.error')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center min-h-[80px]">
            <span className="text-red-500 dark:text-red-200">{error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!effectiveStats || effectiveStats.length === 0) {
    return (
      <Card className={cardStyle + ' min-h-[120px]'}>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-center">{t('dashboard.reservations')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center min-h-[80px]">
            <span className="text-gray-400 dark:text-gray-200">{t('common.noData')}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cardStyle + ' min-h-[120px]'}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-center flex items-center gap-2">
          <svg className="w-7 h-7" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a4 4 0 00-3-3.87M9 20H4v-2a4 4 0 013-3.87m9-7a4 4 0 11-8 0 4 4 0 018 0zm6 8v2a2 2 0 01-2 2h-4a2 2 0 01-2-2v-2a2 2 0 012-2h4a2 2 0 012 2zM7 20v-2a2 2 0 012-2h4a2 2 0 012 2v2" />
          </svg>
          {t('dashboard.reservationsDayTimeslot')}
        </CardTitle>
        {branchId === 'all' && branchIds.length > 0 && (
          <div className="flex flex-wrap gap-2 justify-center mt-2">
            {branchIds.map((bId, idx) => (
              <span
                key={bId}
                className={`inline-flex items-center gap-1 rounded px-2 py-1 text-xs font-semibold ${BRANCH_COLORS[idx % BRANCH_COLORS.length]}`}
              >
                <span className="w-3 h-3 rounded-full inline-block" style={{ backgroundColor: 'currentColor' }}></span>
                <span>{branchMap[bId] || bId}</span>
              </span>
            ))}
          </div>
        )}
      </CardHeader>
      <CardContent>
        {isMobile ? (
          // Mobile card view
          <div className="space-y-3 max-h-[500px] overflow-y-auto">
            {dates.map(date => {
              const dateTotal = totals[date] || 0;
              if (dateTotal === 0) return null;

              return (
                <Card key={date} className="p-3 bg-white/50 dark:bg-white/5">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-semibold text-sm">
                      {(() => {
                        let parsedDate: Date | null = null;
                        const iso = Date.parse(date);
                        if (!isNaN(iso)) parsedDate = new Date(iso);
                        else {
                          try { parsedDate = parse(date, 'dd-MMM-yyyy', new Date()); } catch { parsedDate = null; }
                        }
                        return parsedDate && !isNaN(parsedDate.getTime()) ? format(parsedDate, 'dd MMM yyyy') : date;
                      })()}
                    </h4>
                    <Badge variant="secondary" className="text-xs">
                      Total: {dateTotal}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    {TIME_SLOTS.map(slot => {
                      const slotData = table[date]?.[slot];
                      if (!slotData) return null;

                      if (branchId === 'all') {
                        const slotBranches = slotData || {};
                        const hasData = Object.values(slotBranches).some(count => count > 0);
                        if (!hasData) return null;

                        return (
                          <div key={slot} className="p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs">
                            <div className="font-medium mb-1">{slot}</div>
                            <div className="space-y-1">
                              {branchIds.map((bId, idx) => {
                                const count = slotBranches[bId] || 0;
                                if (!count) return null;
                                const stat = effectiveStats.find(s => s._id.date === date && getSlotForStartHour(s._id.time) === slot && s._id.branchId === bId);
                                const sales = stat?.salesCount || 0;
                                const presence = stat?.presenceCount || 0;
                                return (
                                  <div key={bId} className="flex justify-between items-center">
                                    <span className="truncate">{branchMap[bId] || bId}</span>
                                    <div className="flex gap-1 text-xs">
                                      <span className="bg-blue-100 dark:bg-blue-900 px-1 rounded">{count}</span>
                                      <span className="bg-green-100 dark:bg-green-900 px-1 rounded">S:{sales}</span>
                                      <span className="bg-orange-100 dark:bg-orange-900 px-1 rounded">P:{presence}</span>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        );
                      } else {
                        const count = slotData[Object.keys(slotData)[0]] || 0;
                        if (!count) return null;
                        const stat = effectiveStats.find(s => s._id.date === date && getSlotForStartHour(s._id.time) === slot);
                        const sales = stat?.salesCount || 0;
                        const presence = stat?.presenceCount || 0;

                        return (
                          <div key={slot} className="p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs">
                            <div className="font-medium">{slot}</div>
                            <div className="flex justify-between items-center mt-1">
                              <span>Count: {count}</span>
                              <div className="flex gap-1">
                                <span className="bg-green-100 dark:bg-green-900 px-1 rounded">S:{sales}</span>
                                <span className="bg-orange-100 dark:bg-orange-900 px-1 rounded">P:{presence}</span>
                              </div>
                            </div>
                          </div>
                        );
                      }
                    })}
                  </div>
                </Card>
              );
            })}

            {/* Infinite scroll sentinel for mobile */}
            {hasMore && (
              <div ref={sentinelRef} className="flex justify-center mt-4">
                {isLoadingMore && <span className="text-gray-500">{t('common.loading')}</span>}
              </div>
            )}
          </div>
        ) : (
          // Desktop table view
          <div className="overflow-x-auto max-h-[500px] overflow-y-auto rounded-lg bg-white/70 dark:bg-white/10 p-2">
            <table className="min-w-max w-full border-collapse text-center">
            <thead className="sticky top-0 z-10 bg-white/80 dark:bg-gray-900/80">
              <tr>
                <th className="border px-2 py-1">Date de RDV</th>
                {TIME_SLOTS.map(slot => (
                  <th key={slot} className="border px-2 py-1">{slot}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {dates.map(date => (
                <tr key={date}>
                  <td className="border px-2 py-1 font-semibold">
                    {(() => {
                      let parsedDate: Date | null = null;
                      const iso = Date.parse(date);
                      if (!isNaN(iso)) parsedDate = new Date(iso);
                      else {
                        try { parsedDate = parse(date, 'dd-MMM-yyyy', new Date()); } catch { parsedDate = null; }
                      }
                      const formatted = parsedDate && !isNaN(parsedDate.getTime()) ? format(parsedDate, 'dd MMM yyyy') : date;
                      return (
                        <>
                          <span>{formatted}</span>
                          <span className="ml-2 text-xs text-blue-700 dark:text-blue-200 font-bold bg-blue-100 dark:bg-blue-900 rounded px-2 py-0.5 align-middle">{totals[date]}</span>
                          {branchId === 'all' && branchIds.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-1">
                              {branchIds.map((bId, idx) => {
                                const stats = dayBranchStats[date]?.[bId];
                                if (!stats) return null;
                                return (
                                  <span key={bId} className={`inline-flex items-center gap-1 rounded px-2 py-0.5 text-xxs font-semibold ${BRANCH_COLORS[idx % BRANCH_COLORS.length]}`}
                                    title={`${branchMap[bId] || bId}: ${t('dashboard.sales')}: ${stats.sales}, ${t('dashboard.presence')}: ${stats.presence}`}
                                  >
                                    <span className="w-2 h-2 rounded-full inline-block" style={{ backgroundColor: 'currentColor' }}></span>
                                    <span>{stats.sales} <span className="opacity-60">S</span> / {stats.presence} <span className="opacity-60">P</span></span>
                                  </span>
                                );
                              })}
                            </div>
                          )}
                          {branchId !== 'all' && (
                            <span className="ml-2 text-xs text-green-700 dark:text-green-200 font-bold bg-green-100 dark:bg-green-900 rounded px-2 py-0.5 align-middle">
                              S: {dayBranchStats[date]?.[Object.keys(dayBranchStats[date] || {})[0]]?.sales || 0} / P: {dayBranchStats[date]?.[Object.keys(dayBranchStats[date] || {})[0]]?.presence || 0}
                            </span>
                          )}
                        </>
                      );
                    })()}
                  </td>
                  {TIME_SLOTS.map(slot => {
                    // If all branches, show color chips for each branch
                    if (branchId === 'all') {
                      const slotBranches = table[date]?.[slot] || {};
                      const branchChips = branchIds.map((bId, idx) => {
                        const count = slotBranches[bId] || 0;
                        if (!count) return null;
                        // Find the stat for this date/slot/branch
                        const stat = effectiveStats.find(s => s._id.date === date && getSlotForStartHour(s._id.time) === slot && s._id.branchId === bId);
                        const sales = stat?.salesCount || 0;
                        const presence = stat?.presenceCount || 0;
                        return (
                          <span
                            key={bId}
                            className={`inline-flex items-center justify-center text-xs min-w-[24px] h-6 rounded-full px-1 mx-0.5 ${BRANCH_COLORS[idx % BRANCH_COLORS.length]}`}
                            title={`${branchMap[bId] || bId}: ${t('dashboard.sales')}: ${sales}, ${t('dashboard.presence')}: ${presence}`}
                          >
                            {count}
                          </span>
                        );
                      }).filter(Boolean);
                      return (
                        <td key={slot} className="border px-2 py-1">
                          <div className="flex flex-wrap justify-center gap-1">
                            {branchChips.length > 0 ? branchChips : ''}
                          </div>
                        </td>
                      );
                    }
                    // Single branch mode
                    const count = table[date]?.[slot]?.[Object.keys(table[date]?.[slot] || {})[0]] || 0;
                    // Find the stat for this date/slot/branch
                    const stat = effectiveStats.find(s => s._id.date === date && getSlotForStartHour(s._id.time) === slot);
                    const sales = stat?.salesCount || 0;
                    const presence = stat?.presenceCount || 0;
                    return (
                      <td key={slot} className={`border px-2 py-1 ${getCellColor(count)}`}
                        title={`${t('dashboard.sales')}: ${sales}, ${t('dashboard.presence')}: ${presence}`}
                      >
                        {count > 0 ? count : ''}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="sticky bottom-0 bg-white dark:bg-gray-900 z-10">
                <td className="border px-2 py-1 font-bold">Total</td>
                {TIME_SLOTS.map(slot => (
                  <td key={slot} className="border px-2 py-1 font-bold">
                    {allLoading ? (
                      <span className="text-gray-400 animate-pulse">{t('common.loading')}</span>
                    ) : allError ? (
                      <span className="text-red-500">{t('common.error')}</span>
                    ) : branchId === 'all' && branchIds.length > 0 ? (
                      <div className="flex flex-col gap-1 items-center">
                        {branchIds.map((bId, idx) => {
                          const stats = allSlotBranchTotals[slot]?.[bId];
                          if (!stats) return null;
                          return (
                            <span key={bId} className={`inline-flex items-center gap-1 rounded px-2 py-0.5 text-xxs font-semibold ${BRANCH_COLORS[idx % BRANCH_COLORS.length]}`}
                              title={`${branchMap[bId] || bId}: ${t('dashboard.sales')}: ${stats.sales}, ${t('dashboard.presence')}: ${stats.presence}`}
                            >
                              <span className="w-2 h-2 rounded-full inline-block" style={{ backgroundColor: 'currentColor' }}></span>
                              <span>{stats.count} <span className="opacity-60">T</span> / {stats.sales} <span className="opacity-60">S</span> / {stats.presence} <span className="opacity-60">P</span></span>
                            </span>
                          );
                        })}
                      </div>
                    ) : (
                      <span>
                        {allSlotBranchTotals[slot]?.[Object.keys(allSlotBranchTotals[slot] || {})[0]]?.count || 0}
                        <span className="ml-1 text-green-700 dark:text-green-200 font-bold bg-green-100 dark:bg-green-900 rounded px-1 py-0.5 align-middle">
                          S: {allSlotBranchTotals[slot]?.[Object.keys(allSlotBranchTotals[slot] || {})[0]]?.sales || 0} / P: {allSlotBranchTotals[slot]?.[Object.keys(allSlotBranchTotals[slot] || {})[0]]?.presence || 0}
                        </span>
                      </span>
                    )}
                  </td>
                ))}
              </tr>
            </tfoot>
          </table>
          {/* Infinite scroll sentinel */}
          {hasMore && (
            <div ref={sentinelRef} className="flex justify-center mt-4">
              {isLoadingMore && <span className="text-gray-500">{t('common.loading')}</span>}
            </div>
          )}
          </div>
        )}
      </CardContent>
      {process.env.NODE_ENV === 'development' && (
        <div className="p-2 bg-blue-50 dark:bg-blue-900 text-xs rounded mt-2 border border-blue-200 dark:border-blue-700">
          <strong>DEBUG Infinite Scroll:</strong>
          <ul className="list-disc ml-4">
            <li>stats.length: {stats.length}</li>
            <li>hasMore: {String(hasMore)}</li>
            <li>isLoadingMore: {String(isLoadingMore)}</li>
            <li>branchId: {String(branchId)}</li>
            {/* Add more debug info if needed */}
          </ul>
        </div>
      )}
    </Card>
  );
}; 