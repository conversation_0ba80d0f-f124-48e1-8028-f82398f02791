import React, { useState, useRef, useEffect } from 'react';
import { Check, ChevronDown, X, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';

interface Branch {
  value: string;
  label: string;
  name: string;
  city: string;
  province: string;
}

interface MultiSelectBranchesProps {
  options: Branch[];
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  loading?: boolean;
  maxSelections?: number;
}

export const MultiSelectBranches: React.FC<MultiSelectBranchesProps> = ({
  options,
  value,
  onChange,
  placeholder = "Sélectionner...",
  loading = false,
  maxSelections = 10
}) => {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const selectedBranches = options.filter(option => value.includes(option.value));

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    option.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
    option.province.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (branchValue: string) => {
    if (value.includes(branchValue)) {
      onChange(value.filter(v => v !== branchValue));
    } else if (value.length < maxSelections) {
      onChange([...value, branchValue]);
    }
  };

  const handleRemove = (branchValue: string) => {
    onChange(value.filter(v => v !== branchValue));
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };

    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [open]);

  return (
    <div className="space-y-2 relative">
      <Button
        ref={buttonRef}
        variant="outline"
        role="combobox"
        aria-expanded={open}
        className="w-full justify-between"
        disabled={loading}
        onClick={() => setOpen(!open)}
      >
        {loading ? (
          "Chargement..."
        ) : value.length === 0 ? (
          placeholder
        ) : (
          `${value.length} succursale${value.length > 1 ? 's' : ''} sélectionnée${value.length > 1 ? 's' : ''}`
        )}
        <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </Button>

      {/* Custom dropdown */}
      {open && (
        <div
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg"
        >
          {/* Search input */}
          <div className="p-2 border-b">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Rechercher une succursale..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {/* Options list */}
          <ScrollArea className="max-h-64">
            <div className="p-1">
              {filteredOptions.length === 0 ? (
                <div className="px-3 py-2 text-sm text-gray-500">
                  Aucune succursale trouvée.
                </div>
              ) : (
                filteredOptions.map((branch) => (
                  <div
                    key={branch.value}
                    className="flex items-center px-3 py-2 cursor-pointer hover:bg-gray-100 rounded-sm"
                    onClick={() => handleSelect(branch.value)}
                  >
                    <Check
                      className={`mr-2 h-4 w-4 ${
                        value.includes(branch.value) ? "opacity-100" : "opacity-0"
                      }`}
                    />
                    <div className="flex-1">
                      <div className="font-medium text-sm">{branch.name}</div>
                      <div className="text-xs text-gray-500">{branch.city}, {branch.province}</div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </div>
      )}

      {/* Selected branches display */}
      {selectedBranches.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedBranches.map((branch) => (
            <Badge key={branch.value} variant="secondary" className="flex items-center gap-1">
              {branch.name}
              <X
                className="h-3 w-3 cursor-pointer hover:text-red-500"
                onClick={() => handleRemove(branch.value)}
              />
            </Badge>
          ))}
        </div>
      )}

      {value.length >= maxSelections && (
        <p className="text-sm text-amber-600">
          Maximum {maxSelections} succursales peuvent être sélectionnées
        </p>
      )}
    </div>
  );
};
