'use client';

import * as React from 'react';
import { useState, useEffect, Suspense } from 'react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { Building2, Globe, Users, ChevronDown, ChevronRight, CalendarX, Search, ChevronLeft, Mail } from 'lucide-react';
import { useLanguage } from '@/lib/contexts/language-context';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Baby } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { DateSwitcher } from '../reservations/components/date-switcher';
import { fr } from 'date-fns/locale';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Switch } from "@/components/ui/switch";
import { useRouter } from 'next/navigation';
import { isMobileOrTablet } from '@/lib/utils/device';
import { usePermissions } from '@/hooks/use-permissions';
import { canUserAccessAffectations } from '@/lib/utils/permissions-utils';
import { useAppSelector } from '@/lib/redux/hooks';
import { StatusConfirmationDialog } from "@/app/reservations/components/status-confirmation-dialog";
import type { ReservationStatus, UpdateData } from "@/app/reservations/components/status-select";
import { SellDialog } from "@/app/reservations/components/sell-dialog";
import { ConfirmUserDialog } from "@/app/reservations/components/confirm-user-dialog";

import { getStatusBadge } from "./utils/reservation-status-badge";
import socket, { useSocket } from '@/lib/socket';
import { useSession } from 'next-auth/react';

/**
 * This component extracts URL parameters using useSearchParams() hook
 * It must be wrapped in Suspense to handle the suspense boundary required by Next.js
 */
function UrlParamsHandler({ onParamsReady }: { 
  onParamsReady: (startDate: string | null, endDate: string | null, showAllDates: boolean, branchId: string, appointmentId: string | null) => void 
}) {
  const searchParams = useSearchParams();
  
  // Get URL parameters for redirection support
  // Handle backward compatibility with "date" parameter
  const legacyDate = searchParams?.get('date');
  const initialStartDate = searchParams?.get('startDate') || legacyDate || format(new Date(), 'yyyy-MM-dd');
  const initialEndDate = searchParams?.get('endDate') || legacyDate || format(new Date(), 'yyyy-MM-dd');
  const initialShowAllDates = searchParams?.get('showAllDates') === 'true';
  const initialBranchId = searchParams?.get('branchId') || '';
  let initialAppointmentId = searchParams?.get('appointmentId') || null;
  
  // Handle 'all' time slots parameter
  if (initialAppointmentId === 'all') {
    initialAppointmentId = 'all';
  }
  
  // Call the callback as soon as we have the params
  React.useEffect(() => {
    onParamsReady(initialStartDate, initialEndDate, initialShowAllDates, initialBranchId, initialAppointmentId);
  }, [initialStartDate, initialEndDate, initialShowAllDates, initialBranchId, initialAppointmentId, onParamsReady]);
  
  return null; // This component doesn't render anything
}

/**
 * Affectations Page
 * 
 * This page can be accessed directly or through redirects from other pages.
 * 
 * URL Parameters supported:
 * - startDate: YYYY-MM-DD format (e.g., 2023-12-31)
 * - endDate: YYYY-MM-DD format (e.g., 2023-12-31)
 * - showAllDates: 'true' or 'false'
 * - branchId: The ID of the branch to pre-select
 * - appointmentId: The ID of a specific appointment to show
 * 
 * Example usage from other pages:
 * ```
 * import { useRouter } from 'next/navigation';
 * 
 * const router = useRouter();
 * 
 * // Redirect to affectations page with specific parameters
 * router.push(`/affectations?startDate=2023-12-25&endDate=2023-12-25&branchId=${branchId}&appointmentId=${appointmentId}`);
 * ```
 */

interface UserStat {
  userId: string;
  name: string;
  dailyAssignmentCount: number;
  globalAssignmentCount: number;
}

// Define Stats interface
interface Stats {
  totalAdults: number;
  totalChildren: number;
  presentAdults: number;
  presentChildren: number;
}

// Calculate statistics for displayed reservations
const calculateStats = (reservations: Reservation[]) => {
  try {
    // First, ensure we have an array with valid reservations
    if (!Array.isArray(reservations) || reservations.length === 0) {
      return { totalAdults: 0, totalChildren: 0, presentAdults: 0, presentChildren: 0 };
    }
    
    // Get total counts by directly iterating
    let totalAdults = 0;
    let totalChildren = 0;
    let presentAdults = 0;
    let presentChildren = 0;
    
    for (const res of reservations) {
      // Calculate adult count based on client2Name existence instead of hasCompanion
      // 1 for primary client + 1 if client2Name exists and is not empty
      let adultCount = 1; // Start with primary client
      if (res.customerInfo?.client2Name && res.customerInfo.client2Name.trim() !== '') {
        adultCount += 1; // Add companion if client2Name exists
      }
      
      // Calculate children count from preferences.childrenAges
      let childCount = 0;
      
      if (res.preferences?.hasChildren && res.preferences?.childrenAges) {
        // Count teenagers (13-17) as adults
        const teenCount = res.preferences.childrenAges.age13to17 || 0;
        adultCount += teenCount; // Add teenagers to adult count
        
        // Only count younger children as children
        childCount = 
          (res.preferences.childrenAges.age0to5 || 0) + 
          (res.preferences.childrenAges.age6to12 || 0);
      }
      
      // Add to totals
      totalAdults += adultCount;
      totalChildren += childCount;
      
      // Add to present counts if status is 'present' or 'assigned'
      if (res.status === 'present' || res.status === 'assigned') {
        presentAdults += adultCount;
        presentChildren += childCount;
      }
    }
    
    const result = { totalAdults, totalChildren, presentAdults, presentChildren };
    return result;
  } catch (error) {
    console.error('Error calculating stats:', error);
    return { totalAdults: 0, totalChildren: 0, presentAdults: 0, presentChildren: 0 };
  }
};

interface Branch {
  _id: string;
  name: string;
}

interface Seller {
  _id: string;
  name: string;
  email?: string;
  phone?: string;
  type: 'branch' | 'online' | 'home' | 'family';
}

interface Reservation {
  _id: string;
  appointmentId: string;
  partnerId: string;
  assigned_user_id?: string;
  type: 'branch' | 'online' | 'home' | 'family';
  status: 'pending' | 'confirmed' | 'cancelled' | 'assigned' | 'present';
  source?: 'invitation' | 'direct' | 'other' | 'amq_website'; // Added for tracking reservation source
  partner?: { // Added for populated partner information when source is invitation
    id: string;
    name: string;
    email: string;
  };
  customerInfo: {
    client1Name: string;
    hasCompanion: boolean;
    client2Name?: string;
    city: string;
    postalCode: string;
    phone: string;
    phone2?: string;
    email: string;
    adultCount: number;
    childCount: number;
  };
  preferences: any;
  createdAt: string;
  updatedAt: string;
  // Define the assignedUser field more explicitly
  assignedUser?: {
    _id: string;
    name: string;
    email?: string;
    phone?: string;
    type?: string;
  };
}

interface Appointment {
  _id: string;
  branchId: string;
  date: string;
  startHour: string;
  endHour: string;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
  reservationCounts?: {
    branch: number;
    online: number;
    family: number;
    home: number;
  };
}

// Helper function to get a consistent color based on user ID
const getUserColor = (userId: string): string => {
  // Generate a deterministic color based on userId
  const colors = [
    'blue', 'green', 'purple', 'amber', 'pink', 'teal', 
    'indigo', 'cyan', 'orange', 'lime', 'emerald', 'fuchsia'
  ];
  
  // Simple hash function to pick a color based on userId
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }
  const index = Math.abs(hash) % colors.length;
  
  return colors[index];
};

// Helper function to get CSS values for a user color
const getUserColorStyles = (userId: string): { bg: string, border: string, text: string } => {
  const color = getUserColor(userId);
  
  const colorMap: Record<string, { bg: string, border: string, text: string }> = {
    blue: { bg: 'bg-blue-100', border: 'border-blue-300', text: 'text-blue-700' },
    green: { bg: 'bg-green-100', border: 'border-green-300', text: 'text-green-700' },
    purple: { bg: 'bg-purple-100', border: 'border-purple-300', text: 'text-purple-700' },
    amber: { bg: 'bg-amber-100', border: 'border-amber-300', text: 'text-amber-700' },
    pink: { bg: 'bg-pink-100', border: 'border-pink-300', text: 'text-pink-700' },
    teal: { bg: 'bg-teal-100', border: 'border-teal-300', text: 'text-teal-700' },
    indigo: { bg: 'bg-indigo-100', border: 'border-indigo-300', text: 'text-indigo-700' },
    cyan: { bg: 'bg-cyan-100', border: 'border-cyan-300', text: 'text-cyan-700' },
    orange: { bg: 'bg-orange-100', border: 'border-orange-300', text: 'text-orange-700' },
    lime: { bg: 'bg-lime-100', border: 'border-lime-300', text: 'text-lime-700' },
    emerald: { bg: 'bg-emerald-100', border: 'border-emerald-300', text: 'text-emerald-700' },
    fuchsia: { bg: 'bg-fuchsia-100', border: 'border-fuchsia-300', text: 'text-fuchsia-700' }
  };
  
  return colorMap[color];
};

export default function AffectationsPage() {
  const { toast } = useToast();
  const { t } = useLanguage();
  const router = useRouter();
  const { isLoading: permissionsLoading, error: permissionsError } = usePermissions();
  const permissions = useAppSelector((state: any) => state.permissions);

  // All hooks moved to the top before any conditional returns
  const [startDate, setStartDate] = useState<string | null>(null);
  const [endDate, setEndDate] = useState<string | null>(null);
  const [showAllDates, setShowAllDates] = useState<boolean>(false);
  const [branchId, setBranchId] = useState<string>('');
  const [selectedAppointmentId, setSelectedAppointmentId] = useState<string | 'all' | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [sellers, setSellers] = useState<Seller[]>([]);
  const [stats, setStats] = useState<Stats>({ totalAdults: 0, totalChildren: 0, presentAdults: 0, presentChildren: 0 });
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [displayedReservations, setDisplayedReservations] = useState<Reservation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingAppointment, setIsLoadingAppointment] = useState(false);
  const [userStats, setUserStats] = useState<UserStat[]>([]);
  const [branchSellers, setBranchSellers] = useState<Record<string, Seller[]>>({});
  const [collapsedGroups, setCollapsedGroups] = useState<Record<string, boolean>>({});
  const [userSearchTerm, setUserSearchTerm] = useState<string>('');
  const [reservationSearchTerm, setReservationSearchTerm] = useState<string>('');
  const [branchSearchTerms, setBranchSearchTerms] = useState<Record<string, string>>({});
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [pendingAssignment, setPendingAssignment] = useState<{ sellerId: string, reservationId: string, sellerName: string, assignmentCount: number } | null>(null);
  const [showUnavailableUsers, setShowUnavailableUsers] = useState<boolean>(true);
  const timeSlotsRef = React.useRef<HTMLDivElement>(null);
  const [scrollIndicators, setScrollIndicators] = useState<{ left: number; right: number }>({ left: 0, right: 0 });
  // Track if branchId was set from URL params
  const branchIdFromUrl = React.useRef(false);
  // New state for redirect spinner
  const [isRedirecting, setIsRedirecting] = useState(false);
  // Add state for reservation statuses and dialog
  const [reservationStatuses, setReservationStatuses] = useState<ReservationStatus[]>([]);
  const [statusLoadingId, setStatusLoadingId] = useState<string | null>(null);
  const [statusDialog, setStatusDialog] = useState<{ open: boolean; reservation: Reservation | null }>({ open: false, reservation: null });
  // Dialog state for status changes
  const [isSellDialogOpen, setSellDialogOpen] = useState(false);
  const [isConfirmUserDialogOpen, setConfirmUserDialogOpen] = useState(false);

  const [pendingStatusChange, setPendingStatusChange] = useState<{ reservation: Reservation, statusId: string, statusCode: string } | null>(null);
  // Add state to track recently updated reservation for visual indicator
  const [recentlyUpdatedReservationId, setRecentlyUpdatedReservationId] = useState<string | null>(null);

  // Permission check
  const hasAffectationsAccess = React.useMemo(() => canUserAccessAffectations(permissions), [permissions]);
/*
  // Device detection and redirect (do not return early)
  useEffect(() => {
    if (typeof window !== 'undefined' && isMobileOrTablet()) {
      setIsRedirecting(true);
      router.replace('/affectations/dev/');
    }
  }, [router]);
*/

const {socket} = useSocket();
const { data: session } = useSession();

// Handler to update reservation status from socket event
type StatusChangeData = { reservationId: string; status: Reservation["status"]; userId: string };
const handleSocketStatusChange = React.useCallback((data: StatusChangeData) => {
  setReservations(prevReservations =>
    prevReservations.map(reservation =>
      reservation._id === data.reservationId
        ? { ...reservation, status: data.status }
        : reservation
    )
  );
  setDisplayedReservations(prevDisplayed =>
    prevDisplayed.map(reservation =>
      reservation._id === data.reservationId
        ? { ...reservation, status: data.status }
        : reservation
    )
  );
  setRecentlyUpdatedReservationId(data.reservationId);
  // Remove the highlight after 3 seconds
  setTimeout(() => setRecentlyUpdatedReservationId(null), 3000);

}, [toast, t]);

useEffect(() => {
  if(!socket) return;
  const handleStatusChange = (data: UpdateData) => {
    console.log("data", data);
    const reservation=reservations.find(reservation=>reservation._id===data.reservationId);
    // Only proceed if reservationId, status, and userId are valid strings
    if(
      reservation &&
      typeof data.userId === 'string' &&
      typeof data.reservationId === 'string' &&
      data.userId !== session?.user?.id
    ){
      console.log("status_change", data);
      handleSocketStatusChange({ reservationId: data.reservationId, status: data.status.status as Reservation["status"], userId: data.userId });
    }
  };

  socket.on('status_change', handleStatusChange);

  return () => {
    socket.off('status_change', handleStatusChange);
  };
}, [socket, reservations, session?.user?.id, handleSocketStatusChange]);
  // URL params handler
  const handleUrlParams = React.useCallback((
    startDateParam: string | null, 
    endDateParam: string | null, 
    showAllDatesParam: boolean, 
    branchIdParam: string, 
    appointmentId: string | null
  ) => {
    setStartDate(startDateParam);
    setEndDate(endDateParam);
    setShowAllDates(showAllDatesParam);
    if (branchIdParam) {
      setBranchId(branchIdParam);
      branchIdFromUrl.current = true;
    }
    setSelectedAppointmentId(appointmentId);
  }, []);
  
  // Handle date range change
  const handleDateRangeChange = React.useCallback((start: string | null, end: string | null) => {
    setStartDate(start);
    setEndDate(end);
  }, []);
  
  // Handle show all dates change
  const handleShowAllDatesChange = React.useCallback((showAll: boolean) => {
    setShowAllDates(showAll);
  }, []);

  // Calculate stats whenever displayedReservations changes
  useEffect(() => {
    if (displayedReservations) {
      setStats(calculateStats(displayedReservations));
    }
  }, [displayedReservations]);
  
  // Update scroll indicators when timeslots content changes
  useEffect(() => {
    updateScrollIndicators();
    
    // Also add a window resize listener to update indicators
    const handleResize = () => {
      updateScrollIndicators();
    };
    
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [appointments, isLoading]);
  
  // Reference to keep track of current API calls
  const abortControllersRef = React.useRef<{
    requestId?: string;
    sellers?: AbortController;
    reservations?: AbortController;
  }>({});

  // Function to abort previous API calls
  const abortPreviousCalls = (type: 'sellers' | 'reservations' | 'all') => {
    if (type === 'sellers' || type === 'all') {
      if (abortControllersRef.current.sellers) {
        abortControllersRef.current.sellers.abort();
      }
      if (type === 'sellers') {
        abortControllersRef.current.sellers = new AbortController();
      }
    }
    
    if (type === 'reservations' || type === 'all') {
      if (abortControllersRef.current.reservations) {
        abortControllersRef.current.reservations.abort();
      }
      if (type === 'reservations') {
        abortControllersRef.current.reservations = new AbortController();
      }
    }
    
    if (type === 'all') {
      // Generate a unique request ID to identify this specific request sequence
      const requestId = Math.random().toString(36).substring(2, 9);
      abortControllersRef.current = {
        requestId,
        sellers: new AbortController(),
        reservations: new AbortController(),
      };
      return requestId;
    }
  };

  // Fetch branches
  useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await fetch('/api/branches');
        if (!response.ok) throw new Error('Failed to fetch branches');
        const data = await response.json();
        setBranches(data);
        // Only set default branch if branchId is not set from URL and still empty
        if (data.length > 0 && !branchId && !branchIdFromUrl.current) {
          setBranchId(data[0]._id);
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
        toast({
          title: 'Error',
          description: 'Failed to load branches',
          variant: 'destructive',
        });
      }
    };

    fetchBranches();
  }, [toast, branchId]);

  // Fetch appointments for selected date range and branch
  useEffect(() => {
    const fetchAppointments = async () => {
      if (!branchId) return;
      
      setIsLoading(true);
      
      try {
        let url = `/api/affectations/appointments?branchId=${branchId}`;
        
        // Add date parameters only if not showing all dates
        if (!showAllDates && startDate) {
          url += `&startDate=${startDate}`;
          if (endDate) {
            url += `&endDate=${endDate}`;
          }
        } else {
          url += `&allDates=true`;
        }
        
        const response = await fetch(url);
        if (!response.ok) throw new Error('Failed to fetch appointments');
        const data = await response.json();
        
        // Clear selected appointment if no appointments are found
        if (!data.appointments || data.appointments.length === 0) {
          setSelectedAppointmentId(null);
          setReservations([]);
          setSellers([]);
        }
        
        setAppointments(data.appointments || []);
        
        // Check if we should keep the initialAppointmentId or select the first appointment
        if (selectedAppointmentId) {
          // Special case for 'all' - always keep it
          if (selectedAppointmentId === 'all') {
            // Don't change the selection if it's 'all'
            return;
          }
          
          // Verify if the selected appointment exists in the fetched appointments
          const appointmentExists = data.appointments?.some(
            (apt: Appointment) => apt._id === selectedAppointmentId
          );
          
          // If not, select the first appointment instead
          if (!appointmentExists && data.appointments && data.appointments.length > 0) {
            setSelectedAppointmentId(data.appointments[0]._id);
          }
        } else if (data.appointments && data.appointments.length > 0) {
          setSelectedAppointmentId(data.appointments[0]._id);
        }
      } catch (error) {
        console.error('Error fetching appointments:', error);
        toast({
          title: 'Error',
          description: 'Failed to load appointments',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAppointments();
  }, [startDate, endDate, showAllDates, branchId, toast, selectedAppointmentId]);

  // Fetch sellers and reservations when appointment is selected
  useEffect(() => {
    const fetchData = async () => {
      if (!selectedAppointmentId) return;

      // Handle the case when we want to show all time slots
      let selectedSlots = [];
      
      if (selectedAppointmentId === 'all') {
        // For 'all' time slots, we'll use all appointments
        selectedSlots = appointments;
        if (selectedSlots.length === 0) {
          // Clear reservations when no appointments are found
          setReservations([]);
          setSellers([]);
          setDisplayedReservations([]);
          return;
        }
      } else {
        // Regular case - find the specific appointment
        const selectedSlot = appointments.find(apt => apt._id === selectedAppointmentId);
        if (!selectedSlot) {
          // Clear reservations when no appointments are found
          setReservations([]);
          setSellers([]);
          setDisplayedReservations([]);
          return;
        }
        selectedSlots = [selectedSlot];
      }

      // Set loading state immediately when appointment changes
      setIsLoadingAppointment(true);
      
      // Clear previous data while loading
      setReservations([]);
      setSellers([]);
      setDisplayedReservations([]);
      
      // Abort any ongoing API calls and get a new request ID
      const requestId = abortPreviousCalls('all');
      
      try {
        // Fetch reservations first
        let appointmentIdParam;
        if (selectedAppointmentId === 'all') {
          // Join all appointment IDs with commas
          appointmentIdParam = selectedSlots.map(slot => slot._id).join(',');
        } else {
          appointmentIdParam = selectedAppointmentId;
        }
        
        const reservationsResponse = await fetch(
          `/api/affectations/reservations?appointmentId=${appointmentIdParam}`,
          { signal: abortControllersRef.current.reservations?.signal }
        );
        
        // Check if we were aborted by comparing request IDs
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        if (!reservationsResponse.ok) throw new Error('Failed to fetch reservations');
        const reservationsData = await reservationsResponse.json();
        const reservationsList = reservationsData.reservations || [];
        
        // For all time slots case, we need to fetch sellers for all appointments
        let availableSellers = [];
        let userStats = [];
        
        if (selectedAppointmentId === 'all') {
          // When showing all slots, we need to fetch all sellers that are available for any of the slots
          // We'll use the first slot as a reference for the API call for simplicity
          const referenceSlot = selectedSlots[0];
          
          // Fetch available sellers using the updated API
          const response = await fetch(
            `/api/affectations/sellers?branchId=${branchId}&appointmentDate=${referenceSlot.date}&appointmentStartTime=${referenceSlot.startHour}&appointmentEndTime=${referenceSlot.endHour}`,
            { signal: abortControllersRef.current.sellers?.signal }
          );
          
          // Check if we were aborted by comparing request IDs
          if (abortControllersRef.current.requestId !== requestId) {
            return; // This request was aborted, a new one has started
          }
          
          if (!response.ok) throw new Error('Failed to fetch sellers');
          const { users = [] } = await response.json();
          availableSellers = users;
          
          // Fetch user assignment statistics for all slots
          const statsPromises = selectedSlots.map(slot => 
            fetch(`/api/affectations/user-stats?appointmentId=${slot._id}`)
              .then(res => res.json())
              .then(data => data.userStats || [])
              .catch(err => {
                console.error('Failed to fetch user stats for appointment:', slot._id, err);
                return [];
              })
          );
          
          const allStatsArrays = await Promise.all(statsPromises);
          
          // Check if we were aborted by comparing request IDs
          if (abortControllersRef.current.requestId !== requestId) {
            return; // This request was aborted, a new one has started
          }
          
          // Merge all user stats arrays
          userStats = allStatsArrays.flat();
          
          // Aggregate the stats by user ID
          const aggregatedStats = userStats.reduce((acc: UserStat[], stat: UserStat) => {
            const existingStat = acc.find(s => s.userId === stat.userId);
            if (existingStat) {
              existingStat.dailyAssignmentCount += stat.dailyAssignmentCount;
              existingStat.globalAssignmentCount += stat.globalAssignmentCount;
            } else {
              acc.push({ ...stat });
            }
            return acc;
          }, []);
          
          setUserStats(aggregatedStats);
        } else {
          // Regular case - single appointment
          const selectedSlot = selectedSlots[0];
          
          // Fetch available sellers using the updated API
          const response = await fetch(
            `/api/affectations/sellers?branchId=${branchId}&appointmentDate=${selectedSlot.date}&appointmentStartTime=${selectedSlot.startHour}&appointmentEndTime=${selectedSlot.endHour}`,
            { signal: abortControllersRef.current.sellers?.signal }
          );
          
          // Check if we were aborted by comparing request IDs
          if (abortControllersRef.current.requestId !== requestId) {
            return; // This request was aborted, a new one has started
          }
          
          if (!response.ok) throw new Error('Failed to fetch sellers');
          const { users = [] } = await response.json();
          availableSellers = users;
          
          // Fetch user assignment statistics
          const statsResponse = await fetch(`/api/affectations/user-stats?appointmentId=${selectedAppointmentId}`);
          
          // Check if we were aborted by comparing request IDs
          if (abortControllersRef.current.requestId !== requestId) {
            return; // This request was aborted, a new one has started
          }
          
          if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            setUserStats(statsData.userStats || []);
          } else {
            console.error('Failed to fetch user stats');
            setUserStats([]);
          }
        }
        
        // Fetch all sellers for each branch
        const branchData: Record<string, Seller[]> = {};
        
        // Get current branch sellers
        const branchResponse = await fetch(`/api/users/branch-sellers?branchId=${branchId}`);
        
        // Check if we were aborted by comparing request IDs
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        if (branchResponse.ok) {
          const data = await branchResponse.json();
          branchData[branchId] = data.sellers || [];
        }
        
        setBranchSellers(branchData);
          
        // Add assignedUser to each reservation
        const reservationsWithAssignedUsers = reservationsList.map((reservation: Reservation) => {
          if (reservation.assigned_user_id) {
            // If the reservation already has assignedUser data from the API response, use it
            if (reservation.assignedUser && reservation.assignedUser._id === reservation.assigned_user_id) {
              return reservation;
            }
            
            // First try to find the user in available sellers list
            let assignedUser = availableSellers.find((user: Seller) => user._id === reservation.assigned_user_id);
            
            // If not found in available sellers, try the branch sellers for all branches
            if (!assignedUser) {
              for (const branchSellersArray of Object.values(branchData)) {
                assignedUser = branchSellersArray.find((user: Seller) => user._id === reservation.assigned_user_id);
                if (assignedUser) break;
              }
            }
            
            // If still not found, check if we already have this user in other reservations
            if (!assignedUser) {
              // Search the original list being mapped, not the list being created
              const existingReservation = reservationsList.find((r: Reservation) => 
                r.assigned_user_id === reservation.assigned_user_id && r.assignedUser
              );
              
              if (existingReservation && existingReservation.assignedUser) {
                assignedUser = existingReservation.assignedUser;
              } else {
                 // Final attempt - check the current state reservations
                const existingInAllReservations = reservations.find((r: Reservation) => 
                  r.assigned_user_id === reservation.assigned_user_id && r.assignedUser
                );
                
                if (existingInAllReservations && existingInAllReservations.assignedUser) {
                  assignedUser = existingInAllReservations.assignedUser;
                }
              }
            }
            
            // If found, add the assignedUser to the reservation
            if (assignedUser) {
              return { ...reservation, assignedUser };
            }
          }
          return reservation;
        });
        
        // Final check before updating state - use requestId for stability
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        // Log some debug info about the reservations
        console.log(`Loaded ${reservationsWithAssignedUsers.length} reservations: `, {
          withAssignedUserId: reservationsWithAssignedUsers.filter((r: Reservation) => r.assigned_user_id).length,
          withAssignedUser: reservationsWithAssignedUsers.filter((r: Reservation) => r.assignedUser).length,
          mismatchedAssignment: reservationsWithAssignedUsers.filter((r: Reservation) => r.assigned_user_id && !r.assignedUser).length
        });

        if (reservationsWithAssignedUsers.some((r: Reservation) => r.assigned_user_id && !r.assignedUser)) {
          console.warn('Found reservations with assigned_user_id but missing assignedUser:', 
            reservationsWithAssignedUsers.filter((r: Reservation) => r.assigned_user_id && !r.assignedUser)
          );
        }
        
        // Always update both lists, even if empty
        setSellers(availableSellers);
        setReservations(reservationsWithAssignedUsers);
        // Update displayed reservations to calculate stats
        setDisplayedReservations(reservationsWithAssignedUsers);
      } catch (error) {
        // Ignore abort errors as they are expected when cancelling requests
        if ((error as Error).name !== 'AbortError') {
          console.error('Error fetching data:', error);
          toast({
            title: 'Error',
            description: 'Failed to load data',
            variant: 'destructive',
          });
          // Clear the lists on error
          setSellers([]);
          setReservations([]);
          setDisplayedReservations([]);
        }
      } finally {
        // Only set loading to false if this is still the current request by checking requestId
        if (abortControllersRef.current.requestId === requestId) {
          setIsLoadingAppointment(false);
        }
      }
    };

    fetchData();
    
    // Cleanup function to abort API calls when component unmounts or dependency changes
    return () => {
      abortPreviousCalls('all');
    };
  }, [selectedAppointmentId, appointments, branchId, toast]);

  // Fetch reservation statuses on mount
  useEffect(() => {
    fetch("/api/reservation-statuses")
      .then(res => res.json())
      .then(data => setReservationStatuses(data))
      .catch(() => setReservationStatuses([]));
  }, []);

  const handleDrop = async (sellerId: string, reservationId: string) => {
    // Find the seller and reservation
    const seller = sellers.find((s: Seller) => s._id === sellerId);
    const reservation = reservations.find((r: Reservation) => r._id === reservationId);
    
    if (!seller || !reservation) {
      console.error('Seller or reservation not found');
      return;
    }

    // Count the current assignments for this user
    const currentAssignmentCount = reservations.filter(
      (r: Reservation) => r.assigned_user_id === sellerId
    ).length;

    // If the seller already has 3 or more assignments, show confirmation dialog
    if (currentAssignmentCount >= 3) {
      setPendingAssignment({
        sellerId,
        reservationId,
        sellerName: seller.name,
        assignmentCount: currentAssignmentCount
      });
      setConfirmationDialogOpen(true);
      return;
    }

    // Otherwise proceed with the assignment
    await processAssignment(sellerId, reservationId);
  };

  // Helper function to get the latest user info from all possible sources
  const getFullUserInfo = async (userId: string): Promise<Seller | null> => {
    if (!userId) return null;
    
    // First try to find in available sellers
    let user = sellers.find((s: Seller) => s._id === userId);
    if (user) return user;
    
    // Then check in branch sellers across all branches
    for (const branchSellersArr of Object.values(branchSellers)) {
      user = branchSellersArr.find((s: Seller) => s._id === userId);
      if (user) return user;
    }
    
    // If still not found, try to find in existing reservations
    const reservationWithUser = reservations.find(
      (r: Reservation) => r.assigned_user_id === userId && r.assignedUser
    );
    if (reservationWithUser?.assignedUser) {
      // Convert to a valid Seller type with default branch type if missing
      const user = { ...reservationWithUser.assignedUser };
      return {
        _id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        type: user.type as 'branch' | 'online' | 'home' | 'family' || 'branch'
      };
    }
    
    // As a last resort, fetch from API
    try {
      const response = await fetch(`/api/users/${userId}`);
      if (response.ok) {
        const data = await response.json();
        if (data.user) return data.user;
      }
    } catch (error) {
      console.error(`Failed to fetch user info for ID ${userId}:`, error);
    }
    
    return null;
  };

  // Enhance assignment process to ensure user data is complete
  const processAssignment = async (sellerId: string, reservationId: string) => {
    try {
      // Get the most complete seller info possible
      const seller = await getFullUserInfo(sellerId);
      const reservation = reservations.find((r: Reservation) => r._id === reservationId);
      
      if (!seller || !reservation) {
        console.error('Seller or reservation not found');
        return;
      }

      // Ensure we have complete seller information
      const sellerInfo: Seller = {
        _id: seller._id,
        name: seller.name,
        email: seller.email || '',
        phone: seller.phone || '',
        // Default to 'branch' type if not specified
        type: (seller.type as 'branch' | 'online' | 'home' | 'family') || 'branch'
      };

      // Update the reservation with the assigned user
      const updatedReservations = reservations.map((r: Reservation) => {
        if (r._id === reservationId) {
          // Store complete seller information to ensure it's available even after page refresh
          return {
            ...r,
            assigned_user_id: sellerId,
            assignedUser: sellerInfo,
            status: 'confirmed' as const
          };
        }
        return r;
      });
      
      // Update state immediately - but keep the seller in the available list
      setReservations(updatedReservations);
      // Update displayed reservations for stats
      setDisplayedReservations(updatedReservations);

      // Make the API call
      const response = await fetch('/api/affectations/assign', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          sellerId,
          reservationId,
          branchId,
          // Send complete seller info to ensure it's saved server-side
          sellerInfo
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to assign seller');
      }

      // Get the updated reservation with assigned user data from the API response
      const result = await response.json();
      if (result.success && result.reservation) {
        // Update the reservations list with the server's version of the reservation
        const serverUpdatedReservations = reservations.map((r: Reservation) => {
          if (r._id === reservationId) {
            return {
              ...r,
              ...result.reservation,
              // Ensure assignedUser is properly set - fallback to our local copy if missing
              assignedUser: result.reservation.assignedUser || sellerInfo
            };
          }
          return r;
        });
        
        // Update state with the server's data
        setReservations(serverUpdatedReservations);
        setDisplayedReservations(serverUpdatedReservations);
      }

      toast({
        title: 'Success',
        description: 'Seller assigned successfully',
      });

      // Refresh user stats after assignment
      refreshUserStats();
    } catch (error) {
      console.error('Error assigning seller:', error);
      toast({
        title: 'Error',
        description: 'Failed to assign seller',
        variant: 'destructive',
      });
      
      // Revert optimistic updates on error by refreshing data
      refreshData();
    }
  };

  const handleUnassign = async (reservationId: string) => {
    try {
      // Find the reservation
      const reservation = reservations.find((r: Reservation) => r._id === reservationId);
      
      if (!reservation || !reservation.assignedUser) {
        console.error('Reservation or assigned user not found');
        return;
      }

      // Update the reservation to remove assigned user
      const updatedReservations = reservations.map((r: Reservation) => {
        if (r._id === reservationId) {
          const { assignedUser, assigned_user_id, ...rest } = r;
          return {
            ...rest,
            status: 'pending' as const
          };
        }
        return r;
      });
      
      // Update state immediately - seller remains in the available list
      setReservations(updatedReservations);
      // Update displayed reservations for stats
      setDisplayedReservations(updatedReservations);

      // Make the API call
      const response = await fetch('/api/affectations/unassign', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reservationId }),
      });

      if (!response.ok) {
        throw new Error('Failed to unassign seller');
      }

      toast({
        title: 'Success',
        description: 'Seller unassigned successfully',
      });

      // Refresh user stats after unassignment
      refreshUserStats();
    } catch (error) {
      console.error('Error unassigning seller:', error);
      toast({
        title: 'Error',
        description: 'Failed to unassign seller',
        variant: 'destructive',
      });
      
      // Revert optimistic updates on error
      refreshData();
    }
  };

  // Function to refresh only user statistics
  const refreshUserStats = async () => {
    if (!selectedAppointmentId) return;
    
    try {
      // Fetch user assignment statistics
      const statsResponse = await fetch(`/api/affectations/user-stats?appointmentId=${selectedAppointmentId}`);
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setUserStats(statsData.userStats || []);
      } else {
        console.error('Failed to fetch user stats');
        setUserStats([]);
      }
    } catch (error) {
      console.error('Error refreshing user stats:', error);
    }
  };

  // Function to refresh both sellers and reservations
  const refreshData = async () => {
    if (!selectedAppointmentId) return;
    
    setIsLoadingAppointment(true);
    
    // Abort any ongoing API calls and get a new request ID
    const requestId = abortPreviousCalls('all');
    
    try {
      // If we're viewing all time slots
      if (selectedAppointmentId === 'all') {
        if (appointments.length === 0) return;
        
        // Join all appointment IDs for the API call
        const appointmentIdParam = appointments.map(slot => slot._id).join(',');
        
        // Fetch all reservations for all appointments
        const reservationsResponse = await fetch(
          `/api/affectations/reservations?appointmentId=${appointmentIdParam}`,
          { signal: abortControllersRef.current.reservations?.signal }
        );
        
        // Check if we were aborted by comparing request IDs
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        if (!reservationsResponse.ok) throw new Error('Failed to fetch reservations');
        const reservationsData = await reservationsResponse.json();
        const reservationsList = reservationsData.reservations || [];
        
        // Collect user IDs that need to be fetched (assigned_user_id without assignedUser)
        const userIdsToFetch = reservationsList
          .filter((r: Reservation) => r.assigned_user_id && !r.assignedUser)
          .map((r: Reservation) => r.assigned_user_id);
        
        // Fetch missing user data if needed
        let additionalUserData: Record<string, Seller> = {};
        if (userIdsToFetch.length > 0) {
          try {
            // Fetch in batches or all at once depending on your API
            const userDataResponse = await fetch(`/api/users/batch?userIds=${userIdsToFetch.join(',')}`);
            if (userDataResponse.ok) {
              const userData = await userDataResponse.json();
              additionalUserData = userData.users.reduce((acc: Record<string, Seller>, user: Seller) => {
                acc[user._id] = user;
                return acc;
              }, {});
            }
          } catch (error) {
            console.error('Failed to fetch additional user data:', error);
          }
        }
        
        // Use the first appointment as reference for sellers API
        const referenceSlot = appointments[0];
        
        // Fetch available sellers using the updated API
        const response = await fetch(
          `/api/affectations/sellers?branchId=${branchId}&appointmentDate=${referenceSlot.date}&appointmentStartTime=${referenceSlot.startHour}&appointmentEndTime=${referenceSlot.endHour}`,
          { signal: abortControllersRef.current.sellers?.signal }
        );
        
        // Check if we were aborted by comparing request IDs
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        if (!response.ok) throw new Error('Failed to fetch sellers');
        const { users = [] } = await response.json();
        let availableSellers = users;
        
        // Fetch user stats for all appointments
        const statsPromises = appointments.map(slot => 
          fetch(`/api/affectations/user-stats?appointmentId=${slot._id}`)
            .then(res => res.json())
            .then(data => data.userStats || [])
            .catch(err => {
              console.error('Failed to fetch user stats for appointment:', slot._id, err);
              return [];
            })
        );
        
        const allStatsArrays = await Promise.all(statsPromises);
        
        // Check if we were aborted by comparing request IDs
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        // Merge all user stats arrays
        const mergedStats = allStatsArrays.flat();
        
        // Aggregate the stats by user ID
        const aggregatedStats = mergedStats.reduce((acc: UserStat[], stat: UserStat) => {
          const existingStat = acc.find(s => s.userId === stat.userId);
          if (existingStat) {
            existingStat.dailyAssignmentCount += stat.dailyAssignmentCount;
            existingStat.globalAssignmentCount += stat.globalAssignmentCount;
          } else {
            acc.push({ ...stat });
          }
          return acc;
        }, []);
        
        // Check if we were aborted by comparing request IDs
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        setUserStats(aggregatedStats);
        
        // Add assignedUser to each reservation
        const reservationsWithAssignedUsers = reservationsList.map((reservation: Reservation) => {
          if (reservation.assigned_user_id) {
            // If the reservation already has assignedUser data from the API response, use it
            if (reservation.assignedUser && reservation.assignedUser._id === reservation.assigned_user_id) {
              return reservation;
            }
            
            // Check if we fetched this user's data in our batch request
            if (additionalUserData[reservation.assigned_user_id]) {
              return {
                ...reservation,
                assignedUser: additionalUserData[reservation.assigned_user_id]
              };
            }
            
            // Otherwise continue with existing logic...
            // First try to find the user in available sellers list
            let assignedUser = availableSellers.find((user: Seller) => user._id === reservation.assigned_user_id);
            
            // If not found in available sellers, look in all branch sellers
            if (!assignedUser) {
              for (const branchSellersArray of Object.values(branchSellers)) {
                assignedUser = branchSellersArray.find((user: Seller) => user._id === reservation.assigned_user_id);
                if (assignedUser) break;
              }
            }
            
            // If still not found, check if we already have this user in other reservations
            if (!assignedUser) {
              // Search the original list being mapped, not the list being created
              const existingReservation = reservationsList.find((r: Reservation) => 
                r.assigned_user_id === reservation.assigned_user_id && r.assignedUser
              );
              
              if (existingReservation && existingReservation.assignedUser) {
                assignedUser = existingReservation.assignedUser;
              } else {
                 // Final attempt - check the current state reservations
                const existingInAllReservations = reservations.find((r: Reservation) => 
                  r.assigned_user_id === reservation.assigned_user_id && r.assignedUser
                );
                
                if (existingInAllReservations && existingInAllReservations.assignedUser) {
                  assignedUser = existingInAllReservations.assignedUser;
                }
              }
            }
            
            // If found, add the assignedUser to the reservation
            if (assignedUser) {
              return { ...reservation, assignedUser };
            }
          }
          return reservation;
        });
        
        // Final check before updating state - use requestId for stability
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        setSellers(availableSellers);
        setReservations(reservationsWithAssignedUsers);
        setDisplayedReservations(reservationsWithAssignedUsers);
      } else {
        // Original refresh logic for a single appointment
        const selectedSlot = appointments.find(apt => apt._id === selectedAppointmentId);
        if (!selectedSlot) return;
        
        // Fetch reservations first
        const reservationsResponse = await fetch(
          `/api/affectations/reservations?appointmentId=${selectedAppointmentId}`,
          { signal: abortControllersRef.current.reservations?.signal }
        );
        
        // Check if we were aborted by comparing request IDs
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        if (!reservationsResponse.ok) throw new Error('Failed to fetch reservations');
        const reservationsData = await reservationsResponse.json();
        const reservationsList = reservationsData.reservations || [];
        
        // Collect user IDs that need to be fetched (assigned_user_id without assignedUser)
        const userIdsToFetch = reservationsList
          .filter((r: Reservation) => r.assigned_user_id && !r.assignedUser)
          .map((r: Reservation) => r.assigned_user_id);
        
        // Fetch missing user data if needed
        let additionalUserData: Record<string, Seller> = {};
        if (userIdsToFetch.length > 0) {
          try {
            // Fetch in batches or all at once depending on your API
            const userDataResponse = await fetch(`/api/users/batch?userIds=${userIdsToFetch.join(',')}`);
            if (userDataResponse.ok) {
              const userData = await userDataResponse.json();
              additionalUserData = userData.users.reduce((acc: Record<string, Seller>, user: Seller) => {
                acc[user._id] = user;
                return acc;
              }, {});
            }
          } catch (error) {
            console.error('Failed to fetch additional user data:', error);
          }
        }
        
        // Fetch available sellers
        const response = await fetch(
          `/api/affectations/sellers?branchId=${branchId}&appointmentDate=${selectedSlot.date}&appointmentStartTime=${selectedSlot.startHour}&appointmentEndTime=${selectedSlot.endHour}`,
          { signal: abortControllersRef.current.sellers?.signal }
        );
        
        // Check if we were aborted by comparing request IDs
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        if (!response.ok) throw new Error('Failed to fetch sellers');
        const { users = [] } = await response.json();
        let availableSellers = users;
        
        // Fetch user assignment statistics
        const statsResponse = await fetch(`/api/affectations/user-stats?appointmentId=${selectedAppointmentId}`);
        
        // Check if we were aborted by comparing request IDs
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setUserStats(statsData.userStats || []);
        }
        
        // Add assignedUser to each reservation
        const reservationsWithAssignedUsers = reservationsList.map((reservation: Reservation) => {
          if (reservation.assigned_user_id) {
            // If the reservation already has assignedUser data from the API response, use it
            if (reservation.assignedUser && reservation.assignedUser._id === reservation.assigned_user_id) {
              return reservation;
            }
            
            // Check if we fetched this user's data in our batch request
            if (additionalUserData[reservation.assigned_user_id]) {
              return {
                ...reservation,
                assignedUser: additionalUserData[reservation.assigned_user_id]
              };
            }
            
            // Otherwise continue with existing logic...
            let assignedUser = availableSellers.find((user: Seller) => user._id === reservation.assigned_user_id);
            
            if (!assignedUser) {
              for (const branchSellersArray of Object.values(branchSellers)) {
                assignedUser = branchSellersArray.find((user: Seller) => user._id === reservation.assigned_user_id);
                if (assignedUser) break;
              }
            }
            
            if (!assignedUser) {
              // Search the original list being mapped, not the list being created
              const existingReservation = reservationsList.find((r: Reservation) => 
                r.assigned_user_id === reservation.assigned_user_id && r.assignedUser
              );
              
              if (existingReservation && existingReservation.assignedUser) {
                assignedUser = existingReservation.assignedUser;
              } else {
                // Final attempt - check the current state reservations
                const existingInAllReservations = reservations.find((r: Reservation) => 
                  r.assigned_user_id === reservation.assigned_user_id && r.assignedUser
                );
                
                if (existingInAllReservations && existingInAllReservations.assignedUser) {
                  assignedUser = existingInAllReservations.assignedUser;
                }
              }
            }
            
            if (assignedUser) {
              return { ...reservation, assignedUser };
            }
          }
          return reservation;
        });
        
        // Final check before updating state - use requestId for stability
        if (abortControllersRef.current.requestId !== requestId) {
          return; // This request was aborted, a new one has started
        }
        
        setSellers(availableSellers);
        setReservations(reservationsWithAssignedUsers);
        setDisplayedReservations(reservationsWithAssignedUsers);
      }
    } catch (error) {
      if ((error as Error).name !== 'AbortError') {
        console.error('Error refreshing data:', error);
        toast({
          title: 'Error',
          description: 'Failed to refresh data',
          variant: 'destructive',
        });
      }
    } finally {
      // Only set loading to false if this is still the current request by checking requestId
      if (abortControllersRef.current.requestId === requestId) {
        setIsLoadingAppointment(false);
      }
    }
  };

  // Helper function to get initials from a name
  const getInitials = (name: string): string => {
    return name.split(' ').map((part: string) => part[0]).join('').toUpperCase().substring(0, 2);
  };

  // Function to toggle collapse state for a branch group
  const toggleGroupCollapse = (branchName: string) => {
    setCollapsedGroups(prev => ({
      ...prev,
      [branchName]: !prev[branchName]
    }));
  };

  // Helper function to group reservations by branch
  const groupReservationsByBranch = (reservations: Reservation[]): Record<string, Reservation[]> => {
    // Create a map to store reservations grouped by branch name
    const groupedReservations: Record<string, Reservation[]> = {};
    
    // For reservations without branch info, use this category
    const unknownBranchName = "Autres";
    
    // Group the reservations
    reservations.forEach(reservation => {
      // Get branch name from the reservation
      let branchName = unknownBranchName;
      
      if (reservation.type === 'branch') {
        // For branch type reservations, use assigned branch name
        const branch = branches.find(b => b._id === branchId);
        if (branch) {
          branchName = branch.name;
        }
      } else if (reservation.type === 'online') {
        branchName = "En ligne";
      } else if (reservation.type === 'home') {
        branchName = "À domicile";
      } else if (reservation.type === 'family') {
        branchName = "Famille";
      }
      
      // Initialize array if this branch is not in the map yet
      if (!groupedReservations[branchName]) {
        groupedReservations[branchName] = [];
      }
      
      // Add reservation to the appropriate group
      groupedReservations[branchName].push(reservation);
    });
    
    return groupedReservations;
  };

  // Function to check if a seller is available (based on calendar)
  const isSellerAvailable = (sellerId: string): boolean => {
    if (!sellerId) return false;
    
    // Check if the seller is in the sellers list from API (which checks calendar availability)
    return sellers.some(seller => seller._id === sellerId);
  };

  // Function to get all sellers for a specific branch or category
  const getAllSellersForBranch = (branchName: string): Seller[] => {
    if (branchName === "En ligne" || branchName === "À domicile" || branchName === "Famille" || branchName === "Autres") {
      // For non-branch types, show all available sellers
      // In the future, you might want to filter by type
      return sellers;
    }
    
    // Find the branch ID by name
    const branch = branches.find(b => b.name === branchName);
    if (!branch) return [];
    
    // Return all sellers for this branch
    return branchSellers[branch._id] || [];
  };

  // Function to filter sellers including availability status if needed
  const getFilteredSellers = (sellers: Seller[], searchTerm: string, showUnavailable: boolean): Seller[] => {
    let filteredSellers = sellers;
    
    // First apply search term filter
    if (searchTerm.trim()) {
      const lowerCaseSearchTerm = searchTerm.toLowerCase();
      filteredSellers = filteredSellers.filter((seller: Seller) => 
        seller.name.toLowerCase().includes(lowerCaseSearchTerm) || 
        (seller.phone && seller.phone.includes(searchTerm)) ||
        (seller.email && seller.email.toLowerCase().includes(lowerCaseSearchTerm))
      );
    }
    
    // Then filter by availability if needed, but always include sellers who have current assignments
    if (!showUnavailable) {
      filteredSellers = filteredSellers.filter((seller: Seller) => 
        isSellerAvailable(seller._id) || 
        // Always include sellers who have current assignments in displayed reservations
        reservations.some((reservation: Reservation) => reservation.assigned_user_id === seller._id)
      );
    }
    
    return filteredSellers;
  };

  // Function to filter reservations based on search term
  const filterReservations = (reservations: Reservation[], searchTerm: string): Reservation[] => {
    if (!searchTerm.trim()) return reservations;
    
    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    return reservations.filter((reservation: Reservation) => {
      const customerInfo = reservation.customerInfo || {};
      
      return (
        (customerInfo.client1Name && customerInfo.client1Name.toLowerCase().includes(lowerCaseSearchTerm)) ||
        (customerInfo.client2Name && customerInfo.client2Name.toLowerCase().includes(lowerCaseSearchTerm)) ||
        (customerInfo.email && customerInfo.email.toLowerCase().includes(lowerCaseSearchTerm)) ||
        (customerInfo.phone && customerInfo.phone.includes(searchTerm)) ||
        (customerInfo.phone2 && customerInfo.phone2.includes(searchTerm)) ||
        (customerInfo.city && customerInfo.city.toLowerCase().includes(lowerCaseSearchTerm)) ||
        (customerInfo.postalCode && customerInfo.postalCode.includes(searchTerm))
      );
    });
  };

  // Function to handle branch search term changes
  const handleBranchSearchChange = (branchName: string, value: string) => {
    setBranchSearchTerms(prev => ({
      ...prev,
      [branchName]: value
    }));
  };

  // Function to clear branch search term
  const clearBranchSearch = (branchName: string) => {
    setBranchSearchTerms(prev => ({
      ...prev,
      [branchName]: ''
    }));
  };

  // Helper function to group reservations by branch with search filtering
  const groupReservationsByBranchWithSearch = (reservations: Reservation[]): Record<string, Reservation[]> => {
    // First group by branch (unchanged)
    const grouped = groupReservationsByBranch(reservations);

    // Then filter each branch group by its branch-specific search term and sort
    return Object.entries(grouped).reduce((acc, [branchName, branchReservations]) => {
      const searchTerm = branchSearchTerms[branchName] || '';
      let filteredReservations: Reservation[];

      if (!searchTerm.trim()) {
        // No search term for this branch, keep all reservations
        filteredReservations = branchReservations;
      } else {
        // Filter this branch's reservations by its specific search term
        filteredReservations = filterReservations(branchReservations, searchTerm);
      }

      // Sort reservations so that those with status "present" appear first
      const sortedReservations = filteredReservations.sort((a, b) => {
        // If one has "present" status and the other doesn't, prioritize "present"
        if (a.status === 'present' && b.status !== 'present') return -1;
        if (a.status !== 'present' && b.status === 'present') return 1;

        // If both have the same priority (both "present" or both not "present"), maintain original order
        return 0;
      });

      acc[branchName] = sortedReservations;
      return acc;
    }, {} as Record<string, Reservation[]>);
  };

  const updateScrollIndicators = () => {
    if (timeSlotsRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = timeSlotsRef.current;
      const left = scrollLeft;
      const right = scrollWidth - scrollLeft - clientWidth;
      setScrollIndicators({ left, right });
    }
  };

 

  // PATCH status helper
  const patchReservationStatus = async (reservation: Reservation, statusId: string, statusCode: string, confirmationType?: string) => {
    setStatusLoadingId(reservation._id);
    try {
      const body: any = { statusId };
      if (confirmationType) body.confirmationType = confirmationType;
      const response = await fetch(`/api/reservations/${reservation._id}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update status');
      }

      // Refetch statuses to ensure select options are up to date
      await fetch("/api/reservation-statuses")
        .then(res => res.json())
        .then(data => setReservationStatuses(data));

      // Update local state
      setReservations(prev => prev.map(r => r._id === reservation._id ? { ...r, status: toReservationStatus(statusCode) } : r));
      setDisplayedReservations(prev => prev.map(r => r._id === reservation._id ? { ...r, status: toReservationStatus(statusCode) } : r));
      toast({ title: t('common.success'), description: t('reservations.statusChangeSuccess') || 'Status updated' });
    } catch (error: any) {
      toast({ title: t('common.error'), description: error.message || t('reservations.statusChangeError') || 'Failed to update status', variant: 'destructive' });
    } finally {
      setStatusLoadingId(null);
      setStatusDialog({ open: false, reservation: null });
    }
  };

  // Helper for type-safe status assignment (move outside component)
  const allowedStatuses: Reservation["status"][] = ["pending", "confirmed", "cancelled", "assigned", "present"];
  function toReservationStatus(status: string): Reservation["status"] {
    return allowedStatuses.includes(status as Reservation["status"]) ? (status as Reservation["status"]) : "pending";
  }

  // Helper to map sellers to User type for dialogs
  function mapSellerToUser(seller: Seller): any {
    return {
      ...seller,
      isActive: true, // or infer from your data if available
      roles: [{ _id: '', name: seller.type }],
    };
  }

  // Only do conditional rendering after all hooks
  if (isRedirecting) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        <span className="ml-4 text-lg">Redirection...</span>
      </div>
    );
  }

  if (permissionsLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        <span className="ml-4 text-lg">{t('common.loading')}</span>
      </div>
    );
  }

  if (!hasAffectationsAccess) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">{t('accessDenied.title') || 'Accès refusé'}</h2>
          <p className="text-muted-foreground">{t('accessDenied.description') || 'Vous n\'avez pas la permission d\'accéder à cette page.'}</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col h-full">
      {/* Wrap the useSearchParams call in a Suspense boundary */}
      <Suspense fallback={null}>
        <UrlParamsHandler onParamsReady={handleUrlParams} />
      </Suspense>
      
      {/* Single row header with all elements aligned horizontally */}
      <div className="border-b bg-slate-50">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between p-4 gap-4 overflow-x-hidden">
          {/* Left section: Stats and time slots */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full md:w-auto md:flex-1 overflow-x-hidden">
            {/* Stats section */}
            <div className="flex gap-3 flex-shrink-0">
              <div className="flex items-center space-x-2 p-2 bg-gray-100 rounded-lg shadow-sm">
                <Users className="h-5 w-5 text-blue-600 flex-shrink-0" />
                <div>
                  <p className="text-xs font-medium text-gray-500">Adultes</p>
                  <p className="text-sm font-semibold">
                    {isLoading || isLoadingAppointment ? (
                      <span className="w-10 h-4 bg-slate-200 rounded inline-block animate-pulse"></span>
                    ) : (
                      `${stats.presentAdults}/${stats.totalAdults}`
                    )}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2 p-2 bg-gray-100 rounded-lg shadow-sm">
                <Baby className="h-5 w-5 text-green-600 flex-shrink-0" />
                <div>
                  <p className="text-xs font-medium text-gray-500">Enfants</p>
                  <p className="text-sm font-semibold">
                    {isLoading || isLoadingAppointment ? (
                      <span className="w-10 h-4 bg-slate-200 rounded inline-block animate-pulse"></span>
                    ) : (
                      `${stats.presentChildren}/${stats.totalChildren}`
                    )}
                  </p>
                </div>
              </div>
            </div>
            
            {/* Time slots in-line - now in the same row */}
            <div className="relative flex flex-col flex-1 min-w-0">
              <div className="flex overflow-x-auto gap-2 ml-2 mt-3 mr-3 scrollbar-thin" ref={timeSlotsRef} onScroll={updateScrollIndicators}>
                {isLoading ? (
                  // Loading skeleton for time slots
                  Array(3).fill(0).map((_, index) => (
                    <div key={index} className="flex-shrink-0 px-4 py-2 rounded-md border bg-slate-100 animate-pulse">
                      <div className="flex flex-col items-center">
                        <div className="w-16 h-3 bg-slate-200 rounded mb-1"></div>
                        <div className="w-20 h-4 bg-slate-200 rounded mb-1"></div>
                        <div className="w-28 h-3 bg-slate-200 rounded"></div>
                      </div>
                    </div>
                  ))
                ) : (
                  <>
                    {/* All Time Slots filter card */}
                    <button
                      onClick={() => {
                        if (selectedAppointmentId === 'all') {
                          setSelectedAppointmentId(appointments.length > 0 ? appointments[0]._id : null);
                        } else {
                          setSelectedAppointmentId('all');
                        }
                      }}
                      className={cn(
                        "flex-shrink-0 px-4 py-2 rounded-md border bg-white hover:bg-slate-100 transition-colors",
                        selectedAppointmentId === 'all' && "border-primary border-2"
                      )}
                    >
                      <div className="flex flex-col items-center">
                        {(showAllDates || (startDate !== endDate)) && (
                          <div className="text-xs text-muted-foreground mb-1">
                            {showAllDates ? 'Toutes dates' : 'Plage de dates'}
                          </div>
                        )}
                        <div className={cn(
                          "text-sm font-medium",
                          selectedAppointmentId === 'all' && "text-primary"
                        )}>
                          Tous les créneaux
                        </div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <span className="flex items-center">
                            <Building2 className="h-3 w-3 mr-1" />
                            {appointments.reduce((sum, apt) => sum + (apt.reservationCounts?.branch || 0), 0)}
                          </span>
                          <span className="flex items-center">
                            <Users className="h-3 w-3 mr-1" />
                            {appointments.reduce((sum, apt) => sum + (apt.reservationCounts?.family || 0), 0)}
                          </span>
                          <span className="flex items-center">
                            <Globe className="h-3 w-3 mr-1" />
                            {appointments.reduce((sum, apt) => sum + (apt.reservationCounts?.online || 0), 0)}
                          </span>
                        </div>
                      </div>
                    </button>
                    
                    {/* Individual time slots */}
                    {appointments.map((appointment) => (
                      <button
                        key={appointment._id}
                        onClick={() => setSelectedAppointmentId(appointment._id)}
                        className={cn(
                          "flex-shrink-0 px-4 py-2 rounded-md border bg-white hover:bg-slate-100 transition-colors",
                          selectedAppointmentId === appointment._id && "border-primary border-2"
                        )}
                      >
                        <div className="flex flex-col items-center">
                          {/* Add date display when showing date range or all dates */}
                          {(showAllDates || (startDate !== endDate)) && (
                            <div className="text-xs text-muted-foreground mb-1">
                              {format(new Date(appointment.date), 'dd MMM', { locale: fr })}
                            </div>
                          )}
                          <div className={cn(
                            "text-sm font-medium",
                            selectedAppointmentId === appointment._id && "text-primary"
                          )}>
                            {appointment.startHour}-{appointment.endHour}
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span className="flex items-center">
                              <Building2 className="h-3 w-3 mr-1" />
                              {appointment.reservationCounts?.branch || 0}/{appointment.capacity}
                            </span>
                            <span className="flex items-center">
                              <Users className="h-3 w-3 mr-1" />
                              {appointment.reservationCounts?.family || 0}/{appointment.max_capacity_family}
                            </span>
                            <span className="flex items-center">
                              <Globe className="h-3 w-3 mr-1" />
                              {appointment.reservationCounts?.online || 0}/{appointment.online}
                            </span>
                          </div>
                        </div>
                      </button>
                    ))}
                  </>
                )}
              </div>
              
              {/* Scroll indicators */}
              {scrollIndicators.left > 10 && (
                <button 
                  className="absolute left-0 top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full bg-slate-200/80 text-slate-700 flex items-center justify-center shadow-md hover:bg-slate-300/80 transition-colors z-10"
                  onClick={() => {
                    if (timeSlotsRef.current) {
                      timeSlotsRef.current.scrollBy({ left: -200, behavior: 'smooth' });
                    }
                  }}
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
              )}
              
              {scrollIndicators.right > 10 && (
                <button 
                  className="absolute right-0 top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full bg-slate-200/80 text-slate-700 flex items-center justify-center shadow-md hover:bg-slate-300/80 transition-colors z-10"
                  onClick={() => {
                    if (timeSlotsRef.current) {
                      timeSlotsRef.current.scrollBy({ left: 200, behavior: 'smooth' });
                    }
                  }}
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
          
          {/* Right section: branch/date picker */}
          <div className="flex flex-col xs:flex-row items-start xs:items-center gap-3 w-full md:w-auto flex-shrink-0 md:ml-2">
            <Select value={branchId} onValueChange={setBranchId}>
              <SelectTrigger className="w-full xs:w-[180px]">
                <SelectValue placeholder={t('events.selectBranch')} />
              </SelectTrigger>
              <SelectContent>
                {branches.map((branch) => (
                  <SelectItem key={branch._id} value={branch._id}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="w-full xs:w-auto">
              <DateSwitcher 
                startDate={startDate} 
                endDate={endDate} 
                showAllDates={showAllDates}
                onDateRangeChange={handleDateRangeChange}
                onShowAllDatesChange={handleShowAllDatesChange}
                mode="single"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        {/* Content area */}
        <div className="p-4 h-full">
          {selectedAppointmentId && (
            <div className="h-full">
              {/* Reservations grouped by branch */}
              <div className="border rounded-lg shadow-sm overflow-hidden flex flex-col h-[calc(100vh-230px)]">
                <div className="p-3 border-b flex-shrink-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold">
                      Réservations ({reservations?.length || 0})
                    </h3>
                    <div className="flex items-center gap-2 text-xs text-slate-600">
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 rounded-full bg-blue-700/20 flex items-center justify-center text-[9px] text-blue-800">J</div>
                        <span>Affectations du jour</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className="w-3 h-3 rounded-full bg-slate-300 flex items-center justify-center text-[9px] text-slate-800">T</div>
                        <span>Affectations totales</span>
                      </div>
                    </div>
                  </div>
                </div>
                <ScrollArea className="flex-grow overflow-auto scrollbar-thin">
                  <div className="p-3">
                    {isLoading ? (
                      <div className="flex items-center justify-center p-4 text-sm text-muted-foreground">
                        <div className="flex flex-col w-full gap-4">
                          <div className="h-6 w-48 bg-slate-200 rounded animate-pulse"></div>
                          <div className="flex flex-col gap-3">
                            {[1, 2, 3, 4].map((i) => (
                              <div key={i} className="p-4 border rounded-lg bg-slate-100 animate-pulse">
                                <div className="flex flex-col gap-2">
                                  <div className="h-4 w-40 bg-slate-200 rounded"></div>
                                  <div className="h-3 w-32 bg-slate-200 rounded"></div>
                                  <div className="h-3 w-48 bg-slate-200 rounded"></div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ) : isLoadingAppointment ? (
                      <div className="flex items-center justify-center p-4 text-sm text-muted-foreground">
                        <div className="flex flex-col w-full gap-4">
                          <div className="h-6 w-48 bg-slate-200 rounded animate-pulse"></div>
                          <div className="flex flex-col gap-3">
                            {[1, 2, 3].map((i) => (
                              <div key={i} className="p-4 border rounded-lg bg-slate-100 animate-pulse">
                                <div className="flex flex-col gap-2">
                                  <div className="h-4 w-40 bg-slate-200 rounded"></div>
                                  <div className="h-3 w-32 bg-slate-200 rounded"></div>
                                  <div className="h-3 w-48 bg-slate-200 rounded"></div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ) : reservations?.length === 0 ? (
                      <div className="flex flex-col items-center justify-center p-8 text-sm text-muted-foreground">
                        <CalendarX className="h-12 w-12 text-slate-300 mb-2" />
                        <p>Aucune réservation trouvée</p>
                      </div>
                    ) : (
                      Object.entries(groupReservationsByBranchWithSearch(reservations)).map(([branchName, branchReservations]) => (
                        <div key={branchName} className="mb-6">
                          <div 
                            className={cn(
                              "font-medium text-sm bg-slate-100 p-2 flex items-center cursor-pointer hover:bg-slate-200 transition-colors",
                              collapsedGroups[branchName] ? "rounded-md" : "rounded-t-md"
                            )}
                            onClick={() => toggleGroupCollapse(branchName)}
                          >
                            {collapsedGroups[branchName] ? (
                              <ChevronRight className="h-4 w-4 mr-2 text-slate-600" />
                            ) : (
                              <ChevronDown className="h-4 w-4 mr-2 text-slate-600" />
                            )}
                            <Building2 className="h-4 w-4 mr-2 text-slate-600" />
                            <span>{branchName}</span>
                            <span className="ml-2 text-xs text-slate-500">
                              ({branchReservations.length} {branchReservations.length === 1 ? 'réservation' : 'réservations'})
                            </span>
                          </div>
                          
                          {!collapsedGroups[branchName] && (
                            <div className="border-x border-b rounded-b-md flex h-[600px]">
                              {/* Users section on the left */}
                              <div className="w-72 border-r bg-slate-50 flex flex-col">
                                <div className="p-2 border-b bg-slate-100 flex-shrink-0">
                                  <div className="flex justify-between items-center mb-2">
                                    <h4 className="text-xs font-medium text-slate-700">
                                      Utilisateurs ({getAllSellersForBranch(branchName).filter(seller => isSellerAvailable(seller._id)).length}/{getAllSellersForBranch(branchName).length})
                                    </h4>
                                    <div className="flex items-center gap-2">
                                      <span className="text-xs text-slate-600">Absents</span>
                                      <Switch 
                                        checked={showUnavailableUsers} 
                                        onCheckedChange={setShowUnavailableUsers}
                                      />
                                    </div>
                                  </div>
                                  <div className="flex gap-2 mb-2 text-xs text-slate-600">
                                    <div className="flex items-center">
                                      <div className="w-3 h-3 rounded-full bg-blue-100 mr-1"></div>
                                      <span>Disponible</span>
                                    </div>
                                    <div className="flex items-center">
                                      <div className="w-3 h-3 rounded-full bg-amber-100 mr-1"></div>
                                      <span>Indisponible mais affecté</span>
                                    </div>
                                    <div className="flex items-center">
                                      <div className="w-3 h-3 rounded-full bg-red-100 mr-1"></div>
                                      <span>Indisponible</span>
                                    </div>
                                  </div>
                                  <div className="relative">
                                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
                                    <Input
                                      placeholder="Rechercher..."
                                      className="pl-7 py-1 h-7 text-xs"
                                      value={userSearchTerm}
                                      onChange={(e) => setUserSearchTerm(e.target.value)}
                                    />
                                    {userSearchTerm && (
                                      <button
                                        onClick={() => setUserSearchTerm('')}
                                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-slate-400 hover:text-slate-600"
                                      >
                                        ✕
                                      </button>
                                    )}
                                  </div>
                                </div>
                                <div className="p-2 space-y-2 overflow-y-auto scrollbar-thin h-full">
                                  {getAllSellersForBranch(branchName).length === 0 ? (
                                    <div className="text-xs text-center text-slate-500 p-2">
                                      Aucun utilisateur disponible
                                    </div>
                                  ) : getFilteredSellers(getAllSellersForBranch(branchName), userSearchTerm, showUnavailableUsers).length === 0 ? (
                                    <div className="text-xs text-center text-slate-500 p-2">
                                      {!showUnavailableUsers 
                                        ? "Aucun utilisateur disponible" 
                                        : `Aucun résultat pour "${userSearchTerm}"`}
                                    </div>
                                  ) : (
                                    [...getFilteredSellers(getAllSellersForBranch(branchName), userSearchTerm, showUnavailableUsers)]
                                      .sort((a, b) => {
                                        // First sort by availability
                                        const aAvailable = isSellerAvailable(a._id);
                                        const bAvailable = isSellerAvailable(b._id);
                                        
                                        if (aAvailable && !bAvailable) return -1;
                                        if (!aAvailable && bAvailable) return 1;
                                        
                                        // Then by daily assignment count
                                        const aCount = userStats.find(stat => stat.userId === a._id)?.dailyAssignmentCount || 0;
                                        const bCount = userStats.find(stat => stat.userId === b._id)?.dailyAssignmentCount || 0;
                                        return aCount - bCount;
                                      })
                                      .map((seller) => {
                                        // Find assignment counts for this user
                                        const userStat = userStats.find(stat => stat.userId === seller._id);
                                        const dailyCount = userStat?.dailyAssignmentCount || 0;
                                        const globalCount = userStat?.globalAssignmentCount || 0;
                                        
                                        // Count the current assignments for this user in visible reservations
                                        const currentAssignmentCount = reservations.filter(
                                          (r: Reservation) => r.assigned_user_id === seller._id
                                        ).length;
                                        
                                        // Determine if the seller has too many assignments
                                        const hasManyAssignments = currentAssignmentCount >= 3;
                                        
                                        // Check if seller is available based on calendar
                                        const available = isSellerAvailable(seller._id);
                                        
                                        // Get color for this user
                                        const userColorStyles = getUserColorStyles(seller._id);
                                        
                                        return (
                                          <div
                                            key={seller._id}
                                            className={cn(
                                              "p-2 border-2 rounded-lg text-sm",
                                              available 
                                                ? "bg-white hover:bg-slate-50 transition-colors cursor-grab" 
                                                : reservations.some(r => r.assigned_user_id === seller._id)
                                                  ? "bg-amber-50 border-amber-200 hover:bg-amber-100 transition-colors cursor-grab"
                                                  : "bg-red-50 text-red-800 opacity-70 cursor-not-allowed",
                                              currentAssignmentCount > 0 && available && userColorStyles.border,
                                              hasManyAssignments && "border-yellow-300"
                                            )}
                                            draggable={available || reservations.some(r => r.assigned_user_id === seller._id)}
                                            onDragStart={(e) => {
                                              e.dataTransfer.setData('sellerId', seller._id);
                                              // Show warning toast for sellers with many assignments
                                              if (hasManyAssignments) {
                                                toast({
                                                  title: "Attention",
                                                  description: `${seller.name} a déjà ${currentAssignmentCount} affectations`,
                                                  variant: "default",
                                                });
                                              }
                                              // If user isn't available but has assignments, show warning
                                              if (!available && reservations.some(r => r.assigned_user_id === seller._id)) {
                                                toast({
                                                  title: "Attention",
                                                  description: `${seller.name} est marqué(e) comme indisponible`,
                                                  variant: "destructive",
                                                });
                                              }
                                            }}
                                          >
                                            <div className="flex items-center justify-between gap-1">
                                              <div className="flex items-center gap-2 min-w-0">
                                                <div className={cn(
                                                  "flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium",
                                                  available 
                                                    ? (currentAssignmentCount > 0 ? userColorStyles.bg : "bg-blue-100") 
                                                    : reservations.some(r => r.assigned_user_id === seller._id)
                                                      ? "bg-amber-100" 
                                                      : "bg-red-100",
                                                  available 
                                                    ? (currentAssignmentCount > 0 ? userColorStyles.text : "text-blue-600") 
                                                    : reservations.some(r => r.assigned_user_id === seller._id)
                                                      ? "text-amber-700"
                                                      : "text-red-600",
                                                  hasManyAssignments && "bg-yellow-100 text-yellow-700"
                                                )}>
                                                  {getInitials(seller.name)}
                                                </div>
                                                <div className="font-medium truncate">
                                                  {seller.name}
                                                  {currentAssignmentCount > 0 && (
                                                    <span className={cn(
                                                      "ml-1 text-xs font-normal",
                                                      hasManyAssignments ? "text-yellow-600" : "text-blue-600"
                                                    )}>
                                                      ({currentAssignmentCount})
                                                    </span>
                                                  )}
                                                  {!available && reservations.some(r => r.assigned_user_id === seller._id) && (
                                                    <span className="ml-1 text-xs text-amber-600 font-normal">
                                                      (indisponible)
                                                    </span>
                                                  )}
                                                </div>
                                              </div>
                                              <div className="flex items-center gap-1">
                                                {dailyCount > 0 && (
                                                  <div className={cn(
                                                    "text-xs px-1.5 py-0.5 rounded-full flex-shrink-0 flex items-center gap-0.5",
                                                    available
                                                      ? "bg-blue-100 text-blue-700"
                                                      : "bg-red-100 text-red-700"
                                                  )}
                                                  title="Affectations pour aujourd'hui"
                                                  >
                                                    <span className="w-3 h-3 rounded-full bg-blue-700/20 flex items-center justify-center text-[9px] text-blue-800">J</span>
                                                    <span>{dailyCount}</span>
                                                  </div>
                                                )}
                                                {globalCount > 0 && (
                                                  <div className={cn(
                                                    "text-xs px-1.5 py-0.5 rounded-full flex-shrink-0 flex items-center gap-0.5",
                                                    available
                                                      ? "bg-slate-100 text-slate-700"
                                                      : "bg-red-50 text-red-400"
                                                  )}
                                                  title="Affectations totales (tous les jours)"
                                                  >
                                                    <span className="w-3 h-3 rounded-full bg-slate-300 flex items-center justify-center text-[9px] text-slate-800">T</span>
                                                    <span>{globalCount}</span>
                                                  </div>
                                                )}
                                              </div>
                                            </div>
                                            {seller.phone && (
                                              <div className="text-xs text-slate-500 mt-1 truncate">
                                                📱 {seller.phone}
                                              </div>
                                            )}
                                          </div>
                                        );
                                      })
                                  )}
                                </div>
                              </div>
                              
                              {/* Reservations section on the right with search at top */}
                              <div className="flex-1 flex flex-col">
                                {/* Search input for branch reservations */}
                                <div className="p-2 border-b bg-slate-50">
                                  <div className="relative">
                                    <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
                                    <Input
                                      placeholder={`Rechercher dans ${branchName}...`}
                                      className="pl-7 py-1 h-7 text-xs"
                                      value={branchSearchTerms[branchName] || ''}
                                      onChange={(e) => handleBranchSearchChange(branchName, e.target.value)}
                                    />
                                    {(branchSearchTerms[branchName] || '').length > 0 && (
                                      <button
                                        onClick={() => clearBranchSearch(branchName)}
                                        className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-slate-400 hover:text-slate-600"
                                      >
                                        ✕
                                      </button>
                                    )}
                                  </div>
                                </div>
                                
                                {/* Reservations area */}
                                <div className="flex-1 p-3 overflow-y-auto scrollbar-thin">
                                  {branchReservations.length === 0 ? (
                                    <div className="text-xs text-center text-slate-500 p-4">
                                      Aucune réservation trouvée pour cette recherche
                                    </div>
                                  ) : (
                                    <div className="grid grid-cols-1 gap-3">
                                      {branchReservations.map((reservation) => (
                                        <div
                                          key={reservation._id}
                                          className="p-2 border rounded-lg bg-white"
                                          onDragOver={(e) => {
                                            if (!reservation.assigned_user_id) {
                                              e.preventDefault();
                                              e.currentTarget.classList.add('ring-2', 'ring-primary');
                                            }
                                          }}
                                          onDragLeave={(e) => {
                                            e.currentTarget.classList.remove('ring-2', 'ring-primary');
                                          }}
                                          onDrop={(e) => {
                                            e.preventDefault();
                                            e.currentTarget.classList.remove('ring-2', 'ring-primary');
                                            if (!reservation.assigned_user_id) {
                                              const sellerId = e.dataTransfer.getData('sellerId');
                                              handleDrop(sellerId, reservation._id);
                                            }
                                          }}
                                        >
                                          <div className="flex items-start justify-between">
                                            <div>
                                              <div className="font-medium">
                                                {reservation.customerInfo?.client1Name || 'Client Sans Nom'}
                                                {reservation.customerInfo?.hasCompanion && reservation.customerInfo?.client2Name && 
                                                  ` & ${reservation.customerInfo.client2Name}`
                                                }
                                              </div>
                                              <div className="flex gap-4">
                                                <div className="text-xs text-muted-foreground flex items-center gap-1">
                                                  <span>📧</span> {reservation.customerInfo?.email || 'Pas d\'email'}
                                                </div>
                                                <div className="text-xs text-muted-foreground flex items-center gap-1">
                                                  <span>📱</span> {reservation.customerInfo?.phone || 'Pas de téléphone'}
                                                  {reservation.customerInfo?.phone2 && ` / ${reservation.customerInfo.phone2}`}
                                                </div>
                                              </div>
                                              <div className="text-xs text-muted-foreground mt-1">
                                                {reservation.customerInfo?.city || 'Ville inconnue'}, {reservation.customerInfo?.postalCode || 'Code postal inconnu'}
                                              </div>
                                            </div>
                                            <div className="flex flex-col items-end gap-1">
                                              <div className="flex items-center gap-3">
                                                {/* Invitation source indicator - positioned to the left */}
                                                {reservation.source === 'invitation' && reservation.partner && (
                                                  <TooltipProvider>
                                                    <Tooltip>
                                                      <TooltipTrigger asChild>
                                                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600">
                                                          <Mail className="h-5 w-5" />
                                                        </div>
                                                      </TooltipTrigger>
                                                      <TooltipContent>
                                                        <p className="text-sm">
                                                          <strong>Invitation de:</strong><br />
                                                          {reservation.partner.name}<br />
                                                          {reservation.partner.email}
                                                        </p>
                                                      </TooltipContent>
                                                    </Tooltip>
                                                  </TooltipProvider>
                                                )}

                                                <Badge variant="outline" className={cn(
                                                  "capitalize text-xs px-2 py-0.5",
                                                  reservation.type === 'branch' && "bg-blue-500/10 text-blue-500 border-blue-500/20",
                                                  reservation.type === 'online' && "bg-green-500/10 text-green-500 border-green-500/20",
                                                  reservation.type === 'home' && "bg-orange-500/10 text-orange-500 border-orange-500/20",
                                                  reservation.type === 'family' && "bg-purple-500/10 text-purple-500 border-purple-500/20"
                                                )}>
                                                  {reservation.type}
                                                </Badge>
                                              </div>
                                              
                                              {/* Show assignment badge based on whether assignedUser exists or just assigned_user_id */}
                                              {reservation.assignedUser ? (
                                                <div 
                                                  className={cn(
                                                    "flex items-center mt-1 rounded-full px-2 py-0.5 relative group transition-all duration-300",
                                                    isSellerAvailable(reservation.assignedUser._id)
                                                      ? getUserColorStyles(reservation.assignedUser._id).bg
                                                      : "bg-amber-100",
                                                    reservation._id === recentlyUpdatedReservationId &&
                                                      "ring-4 ring-yellow-400 ring-offset-2 shadow-lg scale-105 animate-pulse"
                                                  )}
                                                >
                                                  <div className={cn(
                                                    "flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium mr-1",
                                                    isSellerAvailable(reservation.assignedUser._id)
                                                      ? getUserColorStyles(reservation.assignedUser._id).bg
                                                      : "bg-amber-100",
                                                    isSellerAvailable(reservation.assignedUser._id)
                                                      ? getUserColorStyles(reservation.assignedUser._id).text
                                                      : "text-amber-700"
                                                  )}>
                                                    {getInitials(reservation.assignedUser.name)}
                                                  </div>
                                                  <span className={cn(
                                                    "text-xs",
                                                    isSellerAvailable(reservation.assignedUser._id)
                                                      ? getUserColorStyles(reservation.assignedUser._id).text
                                                      : "text-amber-700"
                                                  )}>
                                                    {reservation.assignedUser.name}
                                                    {!isSellerAvailable(reservation.assignedUser._id) && (
                                                      <span className="ml-1 text-amber-600">(indisponible)</span>
                                                    )}
                                                  </span>
                                                  <button
                                                    className="absolute -right-1 -top-1 w-4 h-4 rounded-full bg-red-500 text-white flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      handleUnassign(reservation._id);
                                                    }}
                                                    title="Unassign"
                                                  >
                                                    ×
                                                  </button>
                                                  {/* Strong visual indicator for status change by another user */}
                                                  {reservation._id === recentlyUpdatedReservationId && (
                                                    <span className="ml-2 text-yellow-700 text-xs font-bold animate-bounce">
                                                      {t('common.updated') || 'Updated'}
                                                    </span>
                                                  )}
                                                </div>
                                              ) : reservation.assigned_user_id ? (
                                                <div 
                                                  className="flex items-center mt-1 rounded-full px-2 py-0.5 relative group bg-amber-100"
                                                  onClick={() => {
                                                    console.log('Assigned reservation without assignedUser data:', reservation);
                                                    // Try to refresh this specific reservation
                                                    refreshData();
                                                  }}
                                                >
                                                  <div className="flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium mr-1 bg-amber-100 text-amber-700">
                                                    ?
                                                  </div>
                                                  <span className="text-xs text-amber-700">
                                                    Utilisateur affecté
                                                    <span className="ml-1 text-amber-600">(données manquantes)</span>
                                                  </span>
                                                  <button
                                                    className="absolute -right-1 -top-1 w-4 h-4 rounded-full bg-red-500 text-white flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      handleUnassign(reservation._id);
                                                    }}
                                                    title="Unassign"
                                                  >
                                                    ×
                                                  </button>
                                                </div>
                                              ) : null}
                                              {/* Debug button to help troubleshoot */}
                                              {reservation.assigned_user_id && !reservation.assignedUser && (
                                                <div 
                                                  className="flex items-center mt-1 rounded-full px-2 py-0.5 relative group bg-red-100"
                                                  onClick={() => console.log('Missing assignedUser for reservation with assigned_user_id:', reservation)}
                                                >
                                                  <span className="text-xs text-red-700">
                                                    Assigned but missing user info
                                                  </span>
                                                </div>
                                              )}
                                              <div className="mt-2">
                                                {getStatusBadge(reservation.status, reservationStatuses)}
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Dialog for multiple assignments */}
      <AlertDialog open={confirmationDialogOpen} onOpenChange={setConfirmationDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmer l'affectation multiple</AlertDialogTitle>
            <AlertDialogDescription>
              {pendingAssignment && (
                <>
                  <span className="font-medium">{pendingAssignment.sellerName}</span> a déjà {pendingAssignment.assignmentCount} affectations.
                  <br/>
                  Voulez-vous vraiment affecter cet utilisateur à une réservation supplémentaire?
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annuler</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (pendingAssignment) {
                  processAssignment(pendingAssignment.sellerId, pendingAssignment.reservationId);
                  setPendingAssignment(null);
                }
              }}
              className="bg-yellow-600 hover:bg-yellow-700"
            >
              Confirmer
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      {/* Status confirmation dialog for 'present' status */}
      {statusDialog.open && statusDialog.reservation && (
        <StatusConfirmationDialog
          isOpen={statusDialog.open}
          onClose={() => setStatusDialog({ open: false, reservation: null })}
          isLoading={statusLoadingId === statusDialog.reservation?._id}
          pendingReservation={statusDialog.reservation ? { reservationId: statusDialog.reservation._id, statusId: reservationStatuses.find(s => s.code === 'present')?._id || '' } : null}
          onConfirm={async (confirmationType) => {
            if (!statusDialog.reservation) return;
            const presentStatus = reservationStatuses.find(s => s.code === 'present');
            if (!presentStatus) return;
            await patchReservationStatus(statusDialog.reservation, presentStatus._id, presentStatus.code, confirmationType);
          }}
        />
      )}
      {/* Sell Dialog for 'sales' status */}
      {isSellDialogOpen && pendingStatusChange && (
        <SellDialog
          isOpen={isSellDialogOpen}
          onClose={() => { setSellDialogOpen(false); setPendingStatusChange(null); }}
          isLoading={statusLoadingId === pendingStatusChange.reservation._id}
          pendingReservation={{ reservationId: pendingStatusChange.reservation._id, statusId: pendingStatusChange.statusId, hasAssignedUser: !!pendingStatusChange.reservation.assigned_user_id }}
          users={sellers.map(mapSellerToUser)}
          onConfirm={async (amount: number, userId?: string) => {
            if (!userId) return;
            await patchReservationStatus(pendingStatusChange.reservation, pendingStatusChange.statusId, 'sales', userId);
            setSellDialogOpen(false);
            setPendingStatusChange(null);
          }}
        />
      )}
      {/* Confirm User Dialog for 'confirmed' status (if no assigned user) */}
      {isConfirmUserDialogOpen && pendingStatusChange && (
        <ConfirmUserDialog
          isOpen={isConfirmUserDialogOpen}
          onClose={() => { setConfirmUserDialogOpen(false); setPendingStatusChange(null); }}
          isLoading={statusLoadingId === pendingStatusChange.reservation._id}
          pendingReservation={{ reservationId: pendingStatusChange.reservation._id, statusId: pendingStatusChange.statusId }}
          users={sellers.map(mapSellerToUser)}
          onConfirm={async (userId: string) => {
            setStatusLoadingId(pendingStatusChange.reservation._id);
            try {
              const assignResponse = await fetch(`/api/affectations/assign`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ sellerId: userId, reservationId: pendingStatusChange.reservation._id }),
              });
              if (!assignResponse.ok) {
                const errorData = await assignResponse.json();
                throw new Error(errorData.error || 'Failed to assign user');
              }
              await patchReservationStatus(pendingStatusChange.reservation, pendingStatusChange.statusId, 'confirmed');
              setReservations(prev => prev.map(r => r._id === pendingStatusChange.reservation._id ? { ...r, assigned_user_id: userId } : r));
              setDisplayedReservations(prev => prev.map(r => r._id === pendingStatusChange.reservation._id ? { ...r, assigned_user_id: userId } : r));
            } catch (error: any) {
              toast({ title: t('common.error'), description: error.message || 'Failed to assign user and change status', variant: 'destructive' });
            } finally {
              setStatusLoadingId(null);
              setConfirmUserDialogOpen(false);
              setPendingStatusChange(null);
            }
          }}
        />
      )}

    </div>
  );
}
// End of AffectationsPage component
