import React from 'react';
import { Badge } from '@/components/ui/badge';
import type { ReservationStatus } from '@/app/reservations/components/status-select';

// Utility to ensure good text contrast
function getTextColor(hexColor: string): string {
  if (!hexColor) return '#000000';
  const color = hexColor.charAt(0) === '#' ? hexColor.substring(1) : hexColor;
  const r = parseInt(color.substring(0, 2), 16);
  const g = parseInt(color.substring(2, 4), 16);
  const b = parseInt(color.substring(4, 6), 16);
  const darkenFactor = 0.7;
  const darkR = Math.floor(r * darkenFactor);
  const darkG = Math.floor(g * darkenFactor);
  const darkB = Math.floor(b * darkenFactor);
  return `#${darkR.toString(16).padStart(2, '0')}${darkG.toString(16).padStart(2, '0')}${darkB.toString(16).padStart(2, '0')}`;
}

/**
 * Returns a readonly status badge for a reservation.
 * @param status The reservation status code or object
 * @param statuses The list of ReservationStatus objects
 */
export function getStatusBadge(
  status: string | ReservationStatus,
  statuses: ReservationStatus[]
): React.ReactNode {
  let statusObj: ReservationStatus | undefined;
  if (typeof status === 'string') {
    statusObj = statuses.find(s => s.code === status);
  } else if (status && typeof status === 'object') {
    statusObj = status;
  }

  if (statusObj) {
    const hexColor = statusObj.color || '#888888';
    const textColor = getTextColor(hexColor);
    return (
      <Badge
        className="capitalize max-w-[120px] truncate"
        style={{
          backgroundColor: `${hexColor}30`, // ~20% opacity
          borderColor: `${hexColor}40`, // ~25% opacity
          color: textColor,
        }}
      >
        {statusObj.name}
      </Badge>
    );
  }

  // Fallback for unknown status
  return <Badge variant="outline">{typeof status === 'string' ? status : 'Unknown'}</Badge>;
} 