import React, { useEffect, useState, useMemo } from "react";
import { useDrop } from "react-dnd";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { CalendarX, X } from "lucide-react";
import { useLanguage } from "@/lib/contexts/language-context";
import { useAssignments, Reservation, Seller } from "./AffectationsContext";
import { DragTypes, getSellerColor } from "./DragDropUtils";
import { useToast } from "@/hooks/use-toast";

interface AffectationsReservationsProps {
  branchId: string | null;
  appointmentId: string | null;
}

export const AffectationsReservations: React.FC<AffectationsReservationsProps> = ({ branchId, appointmentId }) => {
  const { t } = useLanguage();
  const { 
    getReservationAssignment, 
    addAssignment, 
    removeAssignment, 
    isLoading: isAssignmentLoading,
    reservations, 
    setReservations,
    sellers,
    getInitials
  } = useAssignments();
  
  const [filteredReservations, setFilteredReservations] = useState<Reservation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Fetch reservations
  useEffect(() => {
    if (!branchId || !appointmentId) {
      setReservations([]);
      return;
    }
    
    setLoading(true);
    setError(null);
    
    fetch(`/api/affectations/reservations?branchId=${branchId}&appointmentId=${appointmentId}`)
      .then(async (res) => {
        if (!res.ok) throw new Error("Failed to fetch reservations");
        const data = await res.json();
        
        // Prepare reservations - add initials to assignedUser if exists
        const preparedReservations = data.reservations.map((res: Reservation) => {
          if (res.assignedUser && res.assignedUser.name) {
            return {
              ...res,
              assignedUser: {
                ...res.assignedUser,
                initials: res.assignedUser.initials || getInitials(res.assignedUser.name)
              }
            };
          }
          return res;
        });
        
        setReservations(preparedReservations || []);
      })
      .catch((e) => setError(e.message || "Failed to fetch reservations"))
      .finally(() => setLoading(false));
  }, [branchId, appointmentId, setReservations, getInitials]);

  // Process reservations to include assignment information from context using useMemo
  const processedReservations = useMemo(() => {
    return reservations.map(reservation => {
      let updatedReservation = { ...reservation };
      
      // Get assignment from context
      const assignment = getReservationAssignment(reservation._id);
      
      // Case 1: Assignment exists in the context
      if (assignment) {
        // If the user_id from assignment doesn't match the reservation's assigned_user_id
        // or if the reservation has no assigned_user_id
        if (!reservation.assigned_user_id || reservation.assigned_user_id !== assignment.sellerId) {
          // Get seller details from sellers
          const seller = sellers.find(s => s._id === assignment.sellerId);
          
          if (seller) {
            updatedReservation = {
              ...updatedReservation,
              assigned_user_id: assignment.sellerId,
              assignedUser: {
                _id: seller._id,
                name: seller.name,
                initials: seller.initials || getInitials(seller.name),
                email: seller.email,
                phone: seller.phone,
                type: seller.userType
              }
            };
          }
        }
        // If reservation has an assignedUser but missing initials, add them
        else if (reservation.assignedUser && !reservation.assignedUser.initials && reservation.assignedUser.name) {
          updatedReservation = {
            ...updatedReservation,
            assignedUser: {
              ...reservation.assignedUser,
              initials: getInitials(reservation.assignedUser.name)
            }
          };
        }
      } 
      // Case 2: Assignment doesn't exist in context but reservation has assigned_user_id
      else if (reservation.assigned_user_id) {
        // This block will only execute if the assignment was removed in context
        // OR if the reservation has assigned_user_id but there's no corresponding assignment
        
        // Check if we have the seller in our sellers list
        const seller = sellers.find(s => s._id === reservation.assigned_user_id);
        
        if (seller && !reservation.assignedUser) {
          // If the reservation has assigned_user_id and we have the seller, but assignedUser is missing
          // This is likely data that came from the server that needs to be enriched
          updatedReservation = {
            ...updatedReservation,
            assignedUser: {
              _id: seller._id,
              name: seller.name,
              initials: seller.initials || getInitials(seller.name),
              email: seller.email,
              phone: seller.phone,
              type: seller.userType
            }
          };
        } else if (!seller && !getReservationAssignment(reservation._id)) {
          // If assignment was removed in context and we can't find the seller
          if (process.env.NODE_ENV === 'development') {
            console.log('Removing assignment for reservation:', reservation._id);
          }
          
          updatedReservation = {
            ...updatedReservation,
            assigned_user_id: undefined,
            assignedUser: undefined
          };
        }
      }
      
      return updatedReservation;
    });
  }, [reservations, getReservationAssignment, sellers, getInitials]);

  // Filter reservations based on search term and sort by status
  useEffect(() => {
    let reservationsToProcess = processedReservations;

    // Apply search filter if there's a search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      reservationsToProcess = processedReservations.filter(reservation => {
        const client1Name = reservation.customerInfo?.client1Name?.toLowerCase() || '';
        const client2Name = reservation.customerInfo?.client2Name?.toLowerCase() || '';
        const email = reservation.customerInfo?.email?.toLowerCase() || '';
        const phone = reservation.customerInfo?.phone || '';
        const phone2 = reservation.customerInfo?.phone2 || '';
        const city = reservation.customerInfo?.city?.toLowerCase() || '';

        return client1Name.includes(term) ||
               client2Name.includes(term) ||
               email.includes(term) ||
               phone.includes(term) ||
               phone2.includes(term) ||
               city.includes(term);
      });
    }

    // Sort reservations so that those with status "present" appear first
    const sortedReservations = reservationsToProcess.sort((a, b) => {
      // If one has "present" status and the other doesn't, prioritize "present"
      if (a.status === 'present' && b.status !== 'present') return -1;
      if (a.status !== 'present' && b.status === 'present') return 1;

      // If both have the same priority (both "present" or both not "present"), maintain original order
      return 0;
    });

    setFilteredReservations(sortedReservations);
  }, [processedReservations, searchTerm]);

  const reservationsCount = filteredReservations.length;
  const totalReservations = processedReservations.length;

  return (
    <section className="h-full w-full flex flex-col">
      <div className="py-1 px-2 bg-slate-50 border-b flex items-center justify-between">
        <div className="font-medium text-xs">{t("Réservations")} ({reservationsCount}/{totalReservations})</div>
      </div>

      {/* Search box */}
      <div className="px-1 py-0.5 border-b">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>
          </div>
          <Input
            placeholder={t("Rechercher...")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-6 pl-7 text-xs py-0 px-2"
          />
        </div>
      </div>
      
      {/* Reservations list - explicitly scrollable */}
      <div className="flex-1 overflow-y-auto p-2">
        {loading || isAssignmentLoading ? (
          <div className="space-y-1">
            {[...Array(3)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full rounded" />
            ))}
          </div>
        ) : error ? (
          <div className="text-red-500 text-xs">{error}</div>
        ) : !branchId || !appointmentId ? (
          <div className="text-muted-foreground text-xs">Sélectionnez une branche et un créneau.</div>
        ) : filteredReservations.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-4 text-xs text-muted-foreground">
            <CalendarX className="h-8 w-8 text-slate-300 mb-1" />
            <p>{searchTerm ? t("Aucune réservation ne correspond à votre recherche") : t("Aucune réservation trouvée")}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-2">
            {filteredReservations.map((reservation) => (
              <DroppableReservation 
                key={reservation._id} 
                reservation={reservation}
                onAssign={addAssignment}
                onUnassign={removeAssignment}
              />
            ))}
          </div>
        )}
      </div>
      
      {/* Debug display in development - compact */}
      {process.env.NODE_ENV === 'development' && (
        <div className="py-0.5 px-1 border-t bg-slate-100 text-[10px] flex flex-wrap gap-x-2">
          <span>Results: {filteredReservations.length}/{processedReservations.length}</span>
          <span>Assigned: {processedReservations.filter(r => r.assignedUser).length}</span>
          <span>Sellers: {sellers.length}</span>
        </div>
      )}
    </section>
  );
};

interface DroppableReservationProps {
  reservation: Reservation;
  onAssign: (sellerId: string, reservationId: string) => Promise<boolean>;
  onUnassign: (reservationId: string) => Promise<boolean>;
}

const DroppableReservation: React.FC<DroppableReservationProps> = ({ 
  reservation, 
  onAssign,
  onUnassign
}) => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const [isAssigning, setIsAssigning] = useState(false);
  const [isUnassigning, setIsUnassigning] = useState(false);

  // Set up drop functionality
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: DragTypes.SELLER,
    drop: (item: any) => handleDrop(item),
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
      canDrop: !!monitor.canDrop()
    })
  }));

  const handleDrop = async (item: any) => {
    if (isAssigning) return;
    
    try {
      setIsAssigning(true);
      const success = await onAssign(item.sellerId, reservation._id);
      
      if (!success) {
        toast({
          title: t("Affectation échouée"),
          description: t("Impossible d'affecter ce vendeur à cette réservation."),
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error assigning seller:", error);
      toast({
        title: t("Erreur"),
        description: t("Une erreur est survenue lors de l'affectation."),
        variant: "destructive"
      });
    } finally {
      setIsAssigning(false);
    }
  };

  const handleUnassign = async () => {
    if (isUnassigning || !reservation.assignedUser) return;
    
    try {
      setIsUnassigning(true);
      const success = await onUnassign(reservation._id);
      
      if (!success) {
        toast({
          title: t("Désaffectation échouée"),
          description: t("Impossible de retirer ce vendeur de cette réservation."),
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error unassigning seller:", error);
      toast({
        title: t("Erreur"),
        description: t("Une erreur est survenue lors de la désaffectation."),
        variant: "destructive"
      });
    } finally {
      setIsUnassigning(false);
    }
  };

  // Background color when hovered for drag and drop
  let bgColor = 'bg-white';
  
  if (isAssigning || isUnassigning) {
    bgColor = 'bg-blue-50';
  } else if (isOver && canDrop) {
    bgColor = 'bg-green-50';
  } else if (reservation.assignedUser) {
    // Use the seller color for assigned reservations
    bgColor = getSellerColor(reservation.assignedUser._id);
  }

  return (
    <div
      ref={drop}
      className={cn(
        "border rounded p-2 shadow-sm transition-colors",
        bgColor,
        (isOver && canDrop) && "ring-2 ring-green-500",
        (isAssigning || isUnassigning) && "opacity-70"
      )}
    >
      <div className="flex items-start justify-between gap-2">
        {/* Customer info */}
        <div className="flex-1">
          <div className="flex items-baseline gap-1">
            <h3 className="font-medium text-xs">
              {reservation.customerInfo?.client1Name || t("Client non spécifié")}
            </h3>
            {reservation.customerInfo?.client2Name && (
              <span className="text-[10px] text-gray-500">& {reservation.customerInfo.client2Name}</span>
            )}
          </div>
          
          {reservation.customerInfo?.phone && (
            <div className="text-[10px] text-gray-600 truncate">{reservation.customerInfo.phone}</div>
          )}
          
          {/* Type badge */}
          <div className="flex items-center gap-1 mt-1">
            <Badge variant="outline" className="px-1 py-0 h-5 text-[10px]">
              {reservation.type || "Standard"}
            </Badge>
            {reservation.customerInfo?.city && (
              <Badge variant="secondary" className="px-1 py-0 h-5 text-[10px]">
                {reservation.customerInfo.city}
              </Badge>
            )}
          </div>
        </div>
        
        {/* Assigned user */}
        {reservation.assignedUser ? (
          <div className="flex flex-col items-end">
            <div className="flex items-center gap-1">
              <div className="flex flex-col items-end text-right">
                <span className="text-xs font-medium truncate">
                  {reservation.assignedUser.name}
                </span>
                {reservation.assignedUser.phone && (
                  <span className="text-[10px] text-gray-500 truncate">{reservation.assignedUser.phone}</span>
                )}
              </div>
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center font-medium text-xs">
                {reservation.assignedUser.initials || '??'}
              </div>
            </div>
            {/* Unassign button */}
            <button 
              className="mt-1 text-xs text-red-600 hover:text-red-800 flex items-center gap-0.5"
              onClick={handleUnassign}
              disabled={isUnassigning}
            >
              <X size={12} />
              <span>{t("Retirer")}</span>
            </button>
          </div>
        ) : (
          <div className="flex flex-col items-end justify-center border border-dashed border-gray-300 rounded p-1 h-full">
            <div className="text-[10px] text-muted-foreground">{t("Déposez ici")}</div>
          </div>
        )}
      </div>
    </div>
  );
}; 