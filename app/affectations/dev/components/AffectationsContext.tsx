"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";

// Types
export interface Assignment {
  sellerId: string;
  reservationId: string;
  timestamp: number;
}

// Seller type
export interface Seller {
  _id: string;
  name: string;
  email?: string;
  phone?: string;
  userType?: string;
  available: boolean;
  dailyAssignmentCount: number;
  globalAssignmentCount: number;
  initials?: string;
}

// Reservation type
export interface Reservation {
  _id: string;
  appointmentId: string;
  partnerId: string;
  assigned_user_id?: string;
  type: 'branch' | 'online' | 'home' | 'family';
  status: 'pending' | 'confirmed' | 'cancelled' | 'assigned' | 'present';
  customerInfo: {
    client1Name: string;
    hasCompanion: boolean;
    client2Name?: string;
    city: string;
    postalCode: string;
    phone: string;
    phone2?: string;
    email: string;
    adultCount: number;
    childCount: number;
  };
  preferences: any;
  createdAt: string;
  updatedAt: string;
  assignedUser?: {
    _id: string;
    name: string;
    email?: string;
    phone?: string;
    type?: string;
    initials?: string;
  };
}

interface AssignmentContextType {
  // Assignments management
  assignments: Assignment[];
  addAssignment: (sellerId: string, reservationId: string) => Promise<boolean>;
  removeAssignment: (reservationId: string) => Promise<boolean>;
  getSellerAssignments: (sellerId: string) => Assignment[];
  getReservationAssignment: (reservationId: string) => Assignment | undefined;
  getSellerAssignmentCount: (sellerId: string) => number;
  
  // Reservation data management
  reservations: Reservation[];
  setReservations: React.Dispatch<React.SetStateAction<Reservation[]>>;
  
  // Seller data management
  sellers: Seller[];
  setSellers: React.Dispatch<React.SetStateAction<Seller[]>>;
  getSeller: (sellerId: string) => Seller | undefined;
  
  // Helper function
  getInitials: (name: string) => string;
  
  // Loading and error states
  isLoading: boolean;
  error: string | null;
}

// Create the context
const AssignmentContext = createContext<AssignmentContextType | undefined>(undefined);

// Helper function to get initials from a name
const getInitialsFromName = (name: string): string => {
  return name
    .split(" ")
    .map(part => part[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

// Assignment context provider
export const AssignmentProvider: React.FC<{
  children: ReactNode;
  branchId: string | null;
  appointmentId: string | null;
}> = ({ children, branchId, appointmentId }) => {
  const [assignments, setAssignments] = useState<Assignment[]>([]);
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [sellers, setSellers] = useState<Seller[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Process existing reservations to extract assignments when reservations data changes
  useEffect(() => {
    // Create assignments from reservations that have assigned_user_id
    const extractedAssignments = reservations
      .filter(r => r.assigned_user_id)
      .map(r => ({
        sellerId: r.assigned_user_id as string,
        reservationId: r._id,
        timestamp: new Date(r.updatedAt || Date.now()).getTime()
      }));
    
    // Only update if there are differences to avoid re-renders
    if (JSON.stringify(extractedAssignments) !== JSON.stringify(assignments)) {
      setAssignments(extractedAssignments);
    }
  }, [reservations]);

  // Process reservations to ensure all assigned_user_id have corresponding assignedUser info
  useEffect(() => {
    if (sellers.length === 0 || reservations.length === 0) return;

    let needsUpdate = false;
    const updatedReservations = reservations.map(reservation => {
      // If reservation has assigned_user_id but no assignedUser
      if (reservation.assigned_user_id && !reservation.assignedUser) {
        const seller = sellers.find(s => s._id === reservation.assigned_user_id);
        if (seller) {
          needsUpdate = true;
          return {
            ...reservation,
            assignedUser: {
              _id: seller._id,
              name: seller.name,
              initials: seller.initials || getInitialsFromName(seller.name),
              email: seller.email,
              phone: seller.phone,
              type: seller.userType
            }
          };
        }
      }
      // If reservation has assignedUser but missing initials
      else if (reservation.assignedUser && reservation.assignedUser.name && !reservation.assignedUser.initials) {
        needsUpdate = true;
        return {
          ...reservation,
          assignedUser: {
            ...reservation.assignedUser,
            initials: getInitialsFromName(reservation.assignedUser.name)
          }
        };
      }
      return reservation;
    });

    if (needsUpdate) {
      setReservations(updatedReservations);
    }
  }, [sellers, reservations]);

  // Add a new assignment
  const addAssignment = async (sellerId: string, reservationId: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // First remove any existing assignment for this reservation
      const existingAssignment = assignments.find(a => a.reservationId === reservationId);
      if (existingAssignment) {
        // Update local state immediately for better UX
        setAssignments(prev => prev.filter(a => a.reservationId !== reservationId));
      }
      
      // Call the API to assign
      const response = await fetch("/api/affectations/assign", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          sellerId,
          reservationId,
          appointmentId,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to assign seller");
      }

      // Update local state immediately
      const newAssignment: Assignment = {
        sellerId,
        reservationId,
        timestamp: Date.now(),
      };

      // Get seller details
      const seller = sellers.find(s => s._id === sellerId);

      // Update assignments state
      setAssignments(prev => [...prev.filter(a => a.reservationId !== reservationId), newAssignment]);
      
      // Update the reservation in the reservations array 
      setReservations(prev => 
        prev.map(reservation => 
          reservation._id === reservationId
            ? { 
                ...reservation, 
                assigned_user_id: sellerId,
                assignedUser: seller ? {
                  _id: seller._id,
                  name: seller.name,
                  initials: seller.initials || getInitialsFromName(seller.name),
                  email: seller.email,
                  phone: seller.phone,
                  type: seller.userType
                } : undefined
              }
            : reservation
        )
      );
      
      // Update the seller's assignment count
      setSellers(prev => 
        prev.map(s => 
          s._id === sellerId
            ? { ...s, dailyAssignmentCount: s.dailyAssignmentCount + 1 }
            : s
        )
      );
      
      setError(null);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error adding assignment:", err);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Remove an assignment
  const removeAssignment = async (reservationId: string): Promise<boolean> => {
    try {
      // Update local state immediately for better UX
      const assignmentToRemove = assignments.find(a => a.reservationId === reservationId);
      if (assignmentToRemove) {
        const sellerId = assignmentToRemove.sellerId;
        
        setAssignments(prev => prev.filter(a => a.reservationId !== reservationId));
        
        // Also update the reservation in the reservations array
        setReservations(prev => 
          prev.map(reservation => 
            reservation._id === reservationId
              ? { 
                  ...reservation, 
                  assigned_user_id: undefined,
                  assignedUser: undefined 
                }
              : reservation
          )
        );
        
        // Update the seller's assignment count
        setSellers(prev => 
          prev.map(s => 
            s._id === sellerId
              ? { ...s, dailyAssignmentCount: Math.max(0, s.dailyAssignmentCount - 1) }
              : s
          )
        );
      } else {
        // No assignment to remove
        return true;
      }
      
      setIsLoading(true);
      
      // Call the API to unassign
      const response = await fetch("/api/affectations/unassign", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          reservationId,
        }),
      });

      if (!response.ok) {
        // Revert local state if API call fails
        if (assignmentToRemove) {
          const sellerId = assignmentToRemove.sellerId;
          setAssignments(prev => [...prev, assignmentToRemove]);
          
          // Get seller details
          const seller = sellers.find(s => s._id === sellerId);
          
          // Revert the reservation change
          setReservations(prev => {
            return prev.map(r => 
              r._id === reservationId
                ? { 
                    ...r, 
                    assigned_user_id: sellerId,
                    assignedUser: seller ? {
                      _id: seller._id,
                      name: seller.name,
                      initials: seller.initials || getInitialsFromName(seller.name),
                      email: seller.email,
                      phone: seller.phone,
                      type: seller.userType
                    } : undefined
                  }
                : r
            );
          });
          
          // Revert the seller's assignment count
          setSellers(prev => 
            prev.map(s => 
              s._id === sellerId
                ? { ...s, dailyAssignmentCount: s.dailyAssignmentCount + 1 }
                : s
            )
          );
        }
        throw new Error("Failed to unassign seller");
      }

      setError(null);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error removing assignment:", err);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Get all assignments for a specific seller
  const getSellerAssignments = (sellerId: string) => {
    return assignments.filter(a => a.sellerId === sellerId);
  };

  // Get the seller assignment for a specific reservation
  const getReservationAssignment = (reservationId: string) => {
    return assignments.find(a => a.reservationId === reservationId);
  };

  // Get the number of assignments for a seller
  const getSellerAssignmentCount = (sellerId: string) => {
    return assignments.filter(a => a.sellerId === sellerId).length;
  };
  
  // Get a seller by ID
  const getSeller = (sellerId: string) => {
    return sellers.find(s => s._id === sellerId);
  };

  return (
    <AssignmentContext.Provider
      value={{
        assignments,
        addAssignment,
        removeAssignment,
        getSellerAssignments,
        getReservationAssignment,
        getSellerAssignmentCount,
        reservations,
        setReservations,
        sellers,
        setSellers,
        getSeller,
        getInitials: getInitialsFromName,
        isLoading,
        error,
      }}
    >
      {children}
    </AssignmentContext.Provider>
  );
};

// Custom hook to use the assignment context
export const useAssignments = () => {
  const context = useContext(AssignmentContext);
  if (context === undefined) {
    throw new Error("useAssignments must be used within an AssignmentProvider");
  }
  return context;
}; 