// Drag types
export const DragTypes = {
  SELLER: 'seller',
  RESERVATION: 'reservation'
};

// Colors for assigned items
export const AssignmentColors = [
  'bg-blue-100 border-blue-200 text-blue-800',
  'bg-green-100 border-green-200 text-green-800',
  'bg-purple-100 border-purple-200 text-purple-800',
  'bg-orange-100 border-orange-200 text-orange-800',
  'bg-pink-100 border-pink-200 text-pink-800',
  'bg-cyan-100 border-cyan-200 text-cyan-800',
  'bg-amber-100 border-amber-200 text-amber-800',
  'bg-lime-100 border-lime-200 text-lime-800',
  'bg-indigo-100 border-indigo-200 text-indigo-800',
  'bg-rose-100 border-rose-200 text-rose-800',
  'bg-teal-100 border-teal-200 text-teal-800',
  'bg-emerald-100 border-emerald-200 text-emerald-800',
];

// Get a color based on seller ID (consistent color for the same seller)
export const getSellerColor = (sellerId: string): string => {
  // Create a simple hash from the seller ID
  const hash = sellerId.split('').reduce((acc, char) => {
    return acc + char.charCodeAt(0);
  }, 0);
  
  // Get a color from the array using the hash
  return AssignmentColors[hash % AssignmentColors.length];
};

// Drag item interfaces
export interface DragSellerItem {
  type: typeof DragTypes.SELLER;
  sellerId: string;
  sellerName: string;
  initials: string;
}

export interface DragReservationItem {
  type: typeof DragTypes.RESERVATION;
  reservationId: string;
  clientName: string;
} 