import React from "react";
import { useAssignments } from "./AffectationsContext";
import { useLanguage } from "@/lib/contexts/language-context";

export const AffectationsStats: React.FC = () => {
  const { t } = useLanguage();
  const { assignments, reservations, sellers } = useAssignments();
  
  // Calculate basic stats
  const totalReservations = reservations.length;
  const assignedReservations = reservations.filter(r => r.assignedUser).length;
  const assignmentRate = totalReservations > 0 
    ? Math.round((assignedReservations / totalReservations) * 100) 
    : 0;
  
  const availableSellers = sellers.filter(s => s.available).length;
  
  return (
    <div className="flex flex-wrap items-center justify-between gap-2 text-xs">
      <div className="flex items-center gap-2">
        <span className="font-medium">{t("Affectations")}: {assignedReservations}/{totalReservations}</span>
        <div className="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className="h-full bg-blue-500 rounded-full" 
            style={{ width: `${assignmentRate}%` }}
          ></div>
        </div>
        <span className="text-gray-500">{assignmentRate}%</span>
      </div>
      <div className="flex items-center gap-2">
        <span className="text-gray-500">{t("Vendeurs disponibles")}: {availableSellers}/{sellers.length}</span>
      </div>
    </div>
  );
}; 