import React from "react";
import { useDragLayer } from "react-dnd";
import { DragTypes, getSellerColor, DragSellerItem } from "./DragDropUtils";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/lib/contexts/language-context";

function getItemStyles(initialOffset: any, currentOffset: any): React.CSSProperties {
  if (!initialOffset || !currentOffset) {
    return { display: 'none' };
  }
  const { x, y } = currentOffset;
  return {
    position: 'fixed',
    pointerEvents: 'none' as 'none',
    left: 0,
    top: 0,
    transform: `translate(${x}px, ${y}px)`,
    zIndex: 1000,
    width: 260,
    opacity: 1,
  };
}

export const CustomDragLayer: React.FC = () => {
  const { t } = useLanguage();
  const {
    itemType,
    isDragging,
    item,
    initialOffset,
    currentOffset,
  } = useDragLayer((monitor) => ({
    item: monitor.getItem(),
    itemType: monitor.getItemType(),
    initialOffset: monitor.getInitialSourceClientOffset(),
    currentOffset: monitor.getSourceClientOffset(),
    isDragging: monitor.isDragging(),
  }));

  if (!isDragging || itemType !== DragTypes.SELLER || !item) return null;
  const seller = item as DragSellerItem;
  const bgColor = getSellerColor(seller.sellerId);

  return (
    <div style={getItemStyles(initialOffset, currentOffset)}
      className={process.env.NODE_ENV === 'development' ? 'border-2 border-dashed border-yellow-400' : ''}
    >
      <div className={`p-3 border rounded-lg ${bgColor} flex items-center gap-3 shadow-xl`}
        style={{ opacity: 0.95 }}
      >
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center font-medium text-sm">
          {seller.initials || 'NA'}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <span className="font-medium text-sm truncate">{seller.sellerName}</span>
          </div>
          <div className="mt-1">
            <Badge className="px-2.5 py-1 bg-blue-50 border-blue-200 text-blue-700 flex items-center gap-1.5">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-400">
                <path d="M14 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
                <path d="M14 16a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
                <path d="M8 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
                <path d="M8 16a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
                <path d="M22 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
                <path d="M22 16a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
              </svg>
              <span className="font-medium text-blue-500">{t("En cours d'affectation")}</span>
            </Badge>
          </div>
        </div>
      </div>
    </div>
  );
}; 