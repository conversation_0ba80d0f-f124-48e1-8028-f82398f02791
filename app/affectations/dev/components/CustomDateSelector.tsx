import React, { useState, useEffect, useCallback } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { format, parse, addDays } from "date-fns";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { CalendarIcon } from "lucide-react";

interface Appointment {
  _id: string;
  startHour: string;
  endHour: string;
  label: string;
  date?: string;
}

interface CustomDateSelectorProps {
  value?: { date: Date | null; appointment: Appointment | null };
  onChange?: (value: { date: Date | null; appointment: Appointment | null }) => void;
  branchId?: string | null;
}

export const CustomDateSelector: React.FC<CustomDateSelectorProps> = ({ value, onChange, branchId }) => {
  const [open, setOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(value?.date || null);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(value?.appointment || null);
  const [loading, setLoading] = useState(false);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [error, setError] = useState<string | null>(null);

  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  // Helper to update URL params
  const updateUrlParams = useCallback((date: Date | null, appointment: Appointment | null) => {
    const params = new URLSearchParams(searchParams?.toString() || "");
    
    if (date) {
      // Format date in ISO format (YYYY-MM-DD)
      params.set("date", format(date, "yyyy-MM-dd"));
    } else {
      params.delete("date");
    }
    
    if (appointment) {
      params.set("appointmentId", appointment._id);
      // Add time slot to URL params
      params.set("timeSlot", `${appointment.startHour}-${appointment.endHour}`);
    } else {
      params.delete("appointmentId");
      params.delete("timeSlot");
    }
    
    if (branchId) {
      params.set("branchId", branchId);
    }
    
    router.replace(`${pathname}?${params.toString()}`);
  }, [searchParams, router, pathname, branchId]);

  // Fetch appointments from API
  const fetchAppointments = useCallback(async (date: Date, branchId: string) => {
    setLoading(true);
    setError(null);
    setAppointments([]);
    try {
      const dateStr = format(date, "yyyy-MM-dd");
      const url = `/api/affectations/appointments?branchId=${branchId}&date=${dateStr}`;
      const res = await fetch(url);
      if (!res.ok) throw new Error("Failed to fetch appointments");
      const data = await res.json();
      // Map to Appointment[]
      const appts = (data.appointments || []).map((apt: any) => ({
        _id: apt._id,
        startHour: apt.startHour,
        endHour: apt.endHour,
        label: apt.label || `${apt.startHour} - ${apt.endHour}`,
        date: apt.date,
      }));
      setAppointments(appts);
    } catch (e: any) {
      setError(e.message || "Failed to load appointments");
    } finally {
      setLoading(false);
    }
  }, []);

  // On mount: initialize from URL params
  useEffect(() => {
    const urlDate = searchParams?.get("date");
    const urlAppointmentId = searchParams?.get("appointmentId");
    const urlTimeSlot = searchParams?.get("timeSlot");
    
    if (urlDate) {
      try {
        // Parse the date directly without adjusting - fixing the -1 day bug
        const dateObj = parse(urlDate, "yyyy-MM-dd", new Date());
        
        if (process.env.NODE_ENV === 'development') {
          console.log("Parsing date from URL:", urlDate, "->", dateObj);
        }
        
        setSelectedDate(dateObj);
        if (onChange) onChange({ date: dateObj, appointment: null });
        if (branchId) fetchAppointments(dateObj, branchId);
      } catch (error) {
        console.error("Error parsing date from URL:", error);
      }
    }
    
    // We'll use the appointment ID and time slot to select the appointment later
    if (urlAppointmentId && urlTimeSlot) {
      // Store the appointment ID for later use when appointments are loaded
      setSelectedAppointment((prev) => prev && prev._id === urlAppointmentId ? prev : null);
    }
  }, [branchId, fetchAppointments, onChange, searchParams]);

  // When appointments are fetched, select the one from URL if present
  useEffect(() => {
    const urlAppointmentId = searchParams?.get("appointmentId");
    const urlTimeSlot = searchParams?.get("timeSlot");
    
    if (urlAppointmentId && appointments.length > 0) {
      const found = appointments.find((a) => a._id === urlAppointmentId);
      
      // If we found the appointment by ID
      if (found) {
        setSelectedAppointment(found);
        if (onChange) onChange({ date: selectedDate, appointment: found });
      } 
      // If not found by ID but we have the time slot
      else if (urlTimeSlot && appointments.length > 0) {
        const [startHour, endHour] = urlTimeSlot.split('-');
        const foundByTime = appointments.find(
          (a) => a.startHour === startHour && a.endHour === endHour
        );
        
        if (foundByTime) {
          setSelectedAppointment(foundByTime);
          if (onChange) onChange({ date: selectedDate, appointment: foundByTime });
          // Update URL with the correct appointment ID
          updateUrlParams(selectedDate, foundByTime);
        }
      }
    }
  }, [appointments, onChange, searchParams, selectedDate, updateUrlParams]);

  // When date changes, fetch appointments
  useEffect(() => {
    if (selectedDate && branchId) {
      fetchAppointments(selectedDate, branchId);
    }
  }, [selectedDate, branchId, fetchAppointments]);

  const handleDateSelect = (date: Date | undefined) => {
    if (!date) return;
    setSelectedDate(date);
    setSelectedAppointment(null);
    if (onChange) onChange({ date, appointment: null });
    updateUrlParams(date, null);
    if (branchId) fetchAppointments(date, branchId);
  };

  const handleAppointmentSelect = (apt: Appointment) => {
    setSelectedAppointment(apt);
    if (onChange) onChange({ date: selectedDate, appointment: apt });
    updateUrlParams(selectedDate, apt);
    setOpen(false);
  };

  // More tablet-friendly popover width
  const popoverWidth = 422; // Reduced width for better tablet usage

  // Show more compact date display for tablet
  const getDisplayText = () => {
    if (!selectedDate) return "Select date";
    
    // Format date to be more compact (e.g., "Jan 01")
    const dateText = format(selectedDate, "MMM dd");
    
    if (selectedAppointment) {
      return `${dateText} · ${selectedAppointment.startHour}-${selectedAppointment.endHour}`;
    }
    
    return dateText;
  };

  return (
    <div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="h-8 text-sm px-2 gap-1">
            <CalendarIcon className="h-3.5 w-3.5" />
            {getDisplayText()}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="p-0" style={{ width: `${popoverWidth}px` }}>
          <div className="flex flex-row gap-1 p-2 w-full">
            {/* Calendar */}
            <div style={{ width: 260 }}>
              <Calendar
                mode="single"
                selected={selectedDate || undefined}
                onSelect={handleDateSelect}
                className="mb-0 p-2"
              />
            </div>
            {/* Appointments List */}
            <div style={{ width: 140 }} className="pl-1 border-l">
              {loading ? (
                <div className="flex flex-col gap-1 p-1">
                  <Skeleton className="h-7 w-full" />
                  <Skeleton className="h-7 w-full" />
                  <Skeleton className="h-7 w-full" />
                </div>
              ) : error ? (
                <div className="text-red-500 text-xs p-1">{error}</div>
              ) : selectedDate ? (
                appointments.length === 0 ? (
                  <div className="text-muted-foreground text-xs p-1">No appointments</div>
                ) : (
                  <ul className="flex flex-col gap-1 p-1 overflow-y-auto max-h-[220px]">
                    {appointments.map((apt) => (
                      <li key={apt._id}>
                        <Button
                          variant={selectedAppointment?._id === apt._id ? "default" : "outline"}
                          size="sm"
                          className={cn(
                            "w-full h-7 justify-start text-xs py-0",
                            selectedAppointment?._id === apt._id && "ring-1 ring-primary"
                          )}
                          onClick={() => handleAppointmentSelect(apt)}
                        >
                          {apt.label}
                        </Button>
                      </li>
                    ))}
                  </ul>
                )
              ) : (
                <div className="text-muted-foreground text-xs p-1">Select a date</div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}; 