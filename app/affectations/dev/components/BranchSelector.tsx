'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useLanguage } from '@/lib/contexts/language-context';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';

interface Branch {
  _id: string;
  name: string;
  phone: string;
  responsible: string[];
  agents: string[];
  sellers: string[];
}

interface BranchSelectorProps {
  onChange: (branchId: string) => void;
  currentBranchId: string | null;
}

export function BranchSelector({ onChange, currentBranchId }: BranchSelectorProps) {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { data: session } = useSession();
  const { t } = useLanguage();
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  // Check if user is SuperAdmin
  const isSuperAdmin = session?.user?.roles?.some(
    (role: any) => role._id === '67add3214badd3283e873329'
  );

  // Update URL when branch changes
  const handleBranchChange = (branchId: string) => {
    const params = new URLSearchParams(searchParams?.toString() || "");
    params.set("branchId", branchId);
    router.replace(`${pathname}?${params.toString()}`);
    onChange(branchId);
  };

  useEffect(() => {
    async function fetchBranches() {
      try {
        setLoading(true);
        
        // Fetch branches
        const response = await fetch('/api/branches');
        
        if (!response.ok) {
          throw new Error('Failed to fetch branches');
        }
        
        const data = await response.json();
        setBranches(data);
        
        // Check for branch ID in URL params
        const urlBranchId = searchParams?.get("branchId");
        
        // If we have a branch ID in URL params and it exists in our branches
        if (urlBranchId && data.some((branch: Branch) => branch._id === urlBranchId)) {
          onChange(urlBranchId);
        }
        // If no valid branch ID in URL or no current branch ID
        else if (data.length > 0 && !currentBranchId) {
          // For regular users, find their branch
          if (!isSuperAdmin) {
            const userBranch = data.find((branch: Branch) => {
              const userId = session?.user?.id || '';
              return branch.responsible?.some((user: any) => user._id === userId) || 
                     branch.agents?.some((user: any) => user._id === userId) ||
                     branch.sellers?.some((user: any) => user._id === userId);
            });
            
            if (userBranch) {
              handleBranchChange(userBranch._id);
            } else if (data[0]) {
              // Fallback to first branch if user's branch not found
              handleBranchChange(data[0]._id);
            }
          } else if (data[0]) {
            // For SuperAdmin, set first branch as default
            handleBranchChange(data[0]._id);
          }
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
        setError(error instanceof Error ? error.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    }
    
    fetchBranches();
  }, [session, onChange, currentBranchId, isSuperAdmin, searchParams]);

  if (loading) {
    return <Skeleton className="h-8 w-[130px]" />;
  }

  if (error) {
    return <div className="text-red-500 text-xs">{error}</div>;
  }

  // If user is not SuperAdmin and we have branches, show selector only if user is in multiple branches
  if (!isSuperAdmin) {
    const userId = session?.user?.id || '';
    const userBranches = branches.filter((branch) =>
      (branch.responsible?.some((user: any) => user._id === userId)) ||
      (branch.agents?.some((user: any) => user._id === userId)) ||
      (branch.sellers?.some((user: any) => user._id === userId))
    );
    
    if (userBranches.length === 1) {
      return (
        <div className="text-xs text-gray-500 flex items-center gap-1">
          <span>{t('common.branch')}:</span>
          <span className="font-medium">{userBranches[0].name}</span>
        </div>
      );
    } else if (userBranches.length > 1) {
      return (
        <div className="flex items-center gap-1">
          <span className="text-xs text-gray-500">{t('common.branch')}:</span>
          <Select
            value={currentBranchId || undefined}
            onValueChange={handleBranchChange}
          >
            <SelectTrigger className="h-8 w-[130px] text-sm">
              <SelectValue placeholder={t('conversations.selectBranch')} />
            </SelectTrigger>
            <SelectContent>
              {userBranches.map((branch) => (
                <SelectItem key={branch._id} value={branch._id}>
                  {branch.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );
    }
    // If user is not in any branch, show nothing
    return null;
  }

  return (
    <div className="flex items-center gap-1">
      <span className="text-xs text-gray-500">{t('common.branch')}:</span>
      <Select
        value={currentBranchId || undefined}
        onValueChange={handleBranchChange}
      >
        <SelectTrigger className="h-8 w-[130px] text-sm">
          <SelectValue placeholder={t('conversations.selectBranch')} />
        </SelectTrigger>
        <SelectContent>
          {branches.map((branch) => (
            <SelectItem key={branch._id} value={branch._id}>
              {branch.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
} 