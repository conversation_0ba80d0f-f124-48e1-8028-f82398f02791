import React from "react";
import { BranchSelector } from "./BranchSelector";
import { CustomDateSelector } from "./CustomDateSelector";

interface AffectationsHeaderProps {
  branchId: string | null;
  setBranchId: (id: string | null) => void;
  dateSelectorValue: { date: Date | null; appointment: any | null };
  setDateSelectorValue: (v: { date: Date | null; appointment: any | null }) => void;
}

export const AffectationsHeader: React.FC<AffectationsHeaderProps> = ({
  branchId,
  setBranchId,
  dateSelectorValue,
  setDateSelectorValue,
}) => {
  return (
    <header className="w-full py-1 px-2 bg-white border-b shadow-xs flex items-center justify-between gap-2">
      {/* Branch Selector */}
      <BranchSelector currentBranchId={branchId} onChange={setBranchId} />
      {/* Custom Date Selector */}
      <CustomDateSelector value={dateSelectorValue} onChange={setDateSelectorValue} branchId={branchId} />
    </header>
  );
}; 