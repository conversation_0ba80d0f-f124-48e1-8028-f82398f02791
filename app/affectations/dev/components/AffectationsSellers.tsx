import React, { useEffect, useState, useMemo, useRef } from "react";
import { useDrag } from "react-dnd";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { useLanguage } from "@/lib/contexts/language-context";
import { useAssignments, Seller } from "./AffectationsContext";
import { DragTypes, getSellerColor } from "./DragDropUtils";

interface AffectationsSellersProps {
  branchId: string | null;
  appointmentId: string | null;
  appointmentDate: string | null;
  appointmentStartTime: string | null;
  appointmentEndTime: string | null;
}

export const AffectationsSellers: React.FC<AffectationsSellersProps> = ({ 
  branchId, 
  appointmentId, 
  appointmentDate, 
  appointmentStartTime, 
  appointmentEndTime 
}) => {
  const { t } = useLanguage();
  const { 
    getSellerAssignmentCount, 
    assignments, 
    sellers: contextSellers, 
    setSellers,
    getInitials
  } = useAssignments();
  const [filteredSellers, setFilteredSellers] = useState<Seller[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showOnlyAvailable, setShowOnlyAvailable] = useState(false);
  const [showTooltip, setShowTooltip] = useState(true);
  const firstDragHandleRef = useRef<HTMLDivElement | null>(null);
  const tooltipRef = useRef<HTMLDivElement | null>(null);

  // Position tooltip correctly relative to the first drag handle
  useEffect(() => {
    if (showTooltip && firstDragHandleRef.current && tooltipRef.current) {
      const positionTooltip = () => {
        const handleRect = firstDragHandleRef.current?.getBoundingClientRect();
        const tooltipElem = tooltipRef.current;
        
        if (handleRect && tooltipElem) {
          // Position the tooltip slightly to the right of the handle
          tooltipElem.style.position = 'fixed';
          tooltipElem.style.top = `${handleRect.top + handleRect.height/2 - tooltipElem.offsetHeight/2}px`;
          tooltipElem.style.left = `${handleRect.right + 10}px`;
          tooltipElem.style.opacity = '1';
        }
      };
      
      // Position immediately and then on window resize
      positionTooltip();
      window.addEventListener('resize', positionTooltip);
      
      // Clean up
      return () => window.removeEventListener('resize', positionTooltip);
    }
  }, [showTooltip, filteredSellers.length]);

  // Hide tooltip after 8 seconds or on interaction
  useEffect(() => {
    if (showTooltip) {
      const timer = setTimeout(() => {
        setShowTooltip(false);
      }, 8000);
      
      const handleInteraction = () => {
        setShowTooltip(false);
      };
      
      window.addEventListener('click', handleInteraction);
      window.addEventListener('touchstart', handleInteraction);
      window.addEventListener('scroll', handleInteraction);
      
      return () => {
        clearTimeout(timer);
        window.removeEventListener('click', handleInteraction);
        window.removeEventListener('touchstart', handleInteraction);
        window.removeEventListener('scroll', handleInteraction);
      };
    }
  }, [showTooltip]);

  // Fetch sellers for this appointment
  useEffect(() => {
    if (!branchId || !appointmentId || !appointmentDate || !appointmentStartTime || !appointmentEndTime) {
      return;
    }
    setLoading(true);
    setError(null);
    fetch(`/api/affectations/sellers-list?branchId=${branchId}&appointmentId=${appointmentId}&appointmentDate=${appointmentDate}&appointmentStartTime=${appointmentStartTime}&appointmentEndTime=${appointmentEndTime}`)
      .then(async (res) => {
        if (!res.ok) throw new Error("Failed to fetch sellers");
        const data = await res.json();
        const sellersWithInitials = (data.sellers || []).map((seller: Seller) => ({
          ...seller,
          initials: seller.initials || getInitials(seller.name)
        }));
        setSellers(sellersWithInitials);
      })
      .catch((e) => setError(e.message || "Failed to fetch sellers"))
      .finally(() => setLoading(false));
  }, [branchId, appointmentId, appointmentDate, appointmentStartTime, appointmentEndTime, setSellers, getInitials]);

  // Process sellers with current assignment counts using useMemo
  const processedSellers = useMemo(() => {
    return contextSellers.map(seller => {
      const currentAssignmentCount = getSellerAssignmentCount(seller._id);
      if (currentAssignmentCount !== seller.dailyAssignmentCount) {
        return {
          ...seller,
          dailyAssignmentCount: currentAssignmentCount
        };
      }
      return seller;
    });
  }, [contextSellers, assignments, getSellerAssignmentCount]);
  
  // Filter sellers based on search term and availability toggle
  useEffect(() => {
    let filtered = [...processedSellers];
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(seller => 
        seller.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (seller.phone && seller.phone.includes(searchTerm))
      );
    }
    
    // Apply availability filter if enabled
    if (showOnlyAvailable) {
      filtered = filtered.filter(seller => seller.available);
    }
    
    // Sort sellers: available first, then by assignment count (ascending)
    filtered.sort((a, b) => {
      // First group by availability (available first)
      if (a.available && !b.available) return -1;
      if (!a.available && b.available) return 1;
      
      // Then sort by assignment count (ascending)
      return a.dailyAssignmentCount - b.dailyAssignmentCount;
    });
    
    setFilteredSellers(filtered);
  }, [processedSellers, searchTerm, showOnlyAvailable]);

  const sellersCount = filteredSellers.length;
  const totalSellers = processedSellers.length;

  return (
    <aside className="h-full w-full flex flex-col">
      <div className="p-1 bg-slate-50 border-b flex flex-col gap-0.5">
        <div className="flex items-center justify-between">
          <div className="font-medium text-xs">{t("Utilisateurs")} ({sellersCount}/{totalSellers})</div>
          <div className="flex items-center gap-1">
            <div className="text-[10px] text-gray-600">{t("Disponible")}</div>
            <Switch 
              checked={showOnlyAvailable}
              onCheckedChange={setShowOnlyAvailable}
              className="scale-65"
            />
          </div>
        </div>
        <div className="flex items-center text-[10px] text-gray-500 gap-2">
          <div className="flex items-center gap-0.5">
            <div className="w-1.5 h-1.5 rounded-full bg-blue-500"></div>
            <span>{t("Jour")}</span>
          </div>
          <div className="flex items-center gap-0.5">
            <div className="w-1.5 h-1.5 rounded-full bg-green-500"></div>
            <span>{t("Total")}</span>
          </div>
        </div>
      </div>
      
      {/* Search box */}
      <div className="px-1 py-0.5 border-b">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>
          </div>
          <Input
            placeholder={t("Rechercher...")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-6 pl-7 text-xs py-0 px-2"
          />
        </div>
      </div>
      
      {/* Sellers list - explicitly scrollable div */}
      <div className="flex-1 overflow-y-auto p-1 relative">
        {loading ? (
          <div className="space-y-1">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-10 w-full rounded" />
            ))}
          </div>
        ) : error ? (
          <div className="text-red-500 text-xs p-1">{error}</div>
        ) : !branchId || !appointmentId || !appointmentDate ? (
          <div className="text-muted-foreground text-xs p-1">{t("Sélectionnez une branche et un créneau.")}</div>
        ) : filteredSellers.length === 0 ? (
          <div className="text-muted-foreground text-xs p-1">{t("Aucun vendeur trouvé")}</div>
        ) : (
          <div className="flex flex-col gap-1 relative">
            {filteredSellers.map((seller, index) => (
              <DraggableSeller 
                key={seller._id} 
                seller={seller}
                dragHandleRef={index === 0 ? firstDragHandleRef : null}
                showTooltip={index === 0 && showTooltip} 
              />
            ))}
          </div>
        )}
      </div>
      
      {/* Tooltip - rendered at the document level for proper positioning */}
      {showTooltip && filteredSellers.length > 0 && (
        <div 
          ref={tooltipRef}
          className="w-44 bg-blue-600 text-white text-xs p-2.5 rounded shadow-lg z-50 opacity-0 pointer-events-none"
          style={{ position: 'fixed' }}
        >
          {t("Glissez depuis cette zone pour affecter un utilisateur")}
          <div className="absolute -left-3 top-1/2 -translate-y-1/2 w-0 h-0 border-y-8 border-r-8 border-l-0 border-y-transparent border-r-blue-600"></div>
        </div>
      )}
      
      {/* Debug display in development - collapsed to a single line */}
      {process.env.NODE_ENV === 'development' && (
        <div className="py-0.5 px-1 border-t bg-slate-100 text-[10px] flex items-center gap-2 flex-wrap">
          <span>Filter: {showOnlyAvailable ? '✓' : '✗'}</span>
          <span>Results: {filteredSellers.length}/{contextSellers.length}</span>
        </div>
      )}
    </aside>
  );
};

// Draggable seller component
interface DraggableSellerProps {
  seller: Seller;
  showTooltip?: boolean;
  dragHandleRef?: React.RefObject<HTMLDivElement> | null;
}

const DraggableSeller: React.FC<DraggableSellerProps> = ({ 
  seller, 
  showTooltip = false,
  dragHandleRef = null
}) => {
  const { t } = useLanguage();
  const { getSellerAssignmentCount, getSellerAssignments } = useAssignments();
  const appointmentAssignmentCount = seller.dailyAssignmentCount;
  const sellerAssignments = getSellerAssignments(seller._id);
  
  // Background color based on availability and assignment status
  let bgColor = 'bg-white';
  if (!seller.available) {
    bgColor = seller.dailyAssignmentCount > 0 ? 'bg-amber-50' : 'bg-red-50';
  } else if (sellerAssignments.length > 0) {
    // Use the seller color for assigned sellers, but with a light variant
    bgColor = getSellerColor(seller._id);
  }

  // Set up drag functionality only for the drag handle
  const [{ isDragging }, drag] = useDrag(() => ({
    type: DragTypes.SELLER,
    item: {
      type: DragTypes.SELLER,
      sellerId: seller._id,
      sellerName: seller.name,
      initials: seller.initials || 'NA'
    },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
    canDrag: seller.available || seller.dailyAssignmentCount > 0
  }));

  // Combine drag ref with the tooltip reference if needed
  const setRefs = (element: HTMLDivElement | null) => {
    drag(element);
    if (dragHandleRef) {
      // Use the function form of ref to avoid the read-only property error
      (dragHandleRef as React.MutableRefObject<HTMLDivElement | null>).current = element;
    }
  };

  return (
    <div className={`flex border rounded ${bgColor} ${isDragging ? 'opacity-50' : ''} min-w-[200px]`}>
      {/* Main content area - fixed width to ensure consistent handle positioning */}
      <div className="w-[calc(100%-50px)] py-1.5 px-2">
        <div className="flex items-center gap-1.5">
          {/* Avatar with initials */}
          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 text-blue-800 flex items-center justify-center font-medium text-xs">
            {seller.initials || 'NA'}
          </div>
          
          <div className="flex-1 min-w-0">
            {/* Name and badge */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="font-medium text-xs truncate">{seller.name}</span>
                {appointmentAssignmentCount > 0 && (
                  <span className="ml-1 px-1 py-0.5 text-[10px] font-medium rounded-full bg-blue-500 text-white">
                    {appointmentAssignmentCount}
                  </span>
                )}
              </div>
              <div className="flex items-center gap-1">
                {seller.dailyAssignmentCount > 0 && (
                  <span className="px-0.5 text-[10px] text-blue-700 flex items-center">
                    <span className="inline-block w-1.5 h-1.5 rounded-full bg-blue-500 mr-0.5"></span>
                    {seller.dailyAssignmentCount}
                  </span>
                )}
                {seller.globalAssignmentCount > 0 && (
                  <span className="px-0.5 text-[10px] text-green-700 flex items-center">
                    <span className="inline-block w-1.5 h-1.5 rounded-full bg-green-500 mr-0.5"></span>
                    {seller.globalAssignmentCount}
                  </span>
                )}
              </div>
            </div>
            
            {/* Contact info */}
            <div className="text-[10px] text-gray-500 mt-0.5">
              <div className="truncate">
                {seller.phone || seller.email || t("Aucun contact")}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Drag handle - fixed width for consistent position */}
      {(seller.available || seller.dailyAssignmentCount > 0) ? (
        <div 
          ref={setRefs}
          className={`w-[50px] flex items-center justify-center border-l bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 cursor-grab hover:bg-blue-100 transition-colors duration-150 relative ${showTooltip ? 'ring-2 ring-blue-400' : ''}`}
          title={t("Glisser pour affecter")}
        >
          <div className="flex flex-col items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-400 mb-1">
              <path d="M14 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
              <path d="M14 16a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
              <path d="M8 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
              <path d="M8 16a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
              <path d="M22 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
              <path d="M22 16a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
            </svg>
            <span className="text-[10px] font-medium text-blue-500">{t("Affecter")}</span>
          </div>
        </div>
      ) : (
        <div className="w-[50px] flex items-center justify-center border-l bg-gray-50 border-gray-200 text-gray-400 cursor-not-allowed">
          <div className="flex flex-col items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400 mb-1">
              <circle cx="12" cy="12" r="10"/>
              <path d="m15 9-6 6"/>
              <path d="m9 9 6 6"/>
            </svg>
            <span className="text-[10px] font-medium">{t("Indisponible")}</span>
          </div>
        </div>
      )}
    </div>
  );
}; 