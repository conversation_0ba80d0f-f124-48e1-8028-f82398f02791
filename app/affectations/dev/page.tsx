"use client";

import React, { Suspense, useState, useEffect } from "react";
import { DndProvider as MultiBackendProvider } from "react-dnd-multi-backend";
import { HTML5toTouch } from "rdndmb-html5-to-touch";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/lib/contexts/language-context";
import { useSearchParams } from "next/navigation";
import { parse } from "date-fns";
import { PanelLeft, PanelLeftClose, X } from "lucide-react";
import { Button } from "@/components/ui/button";

// Placeholder imports for modular components (to be implemented)
import { AffectationsHeader } from "./components/AffectationsHeader";
import { AffectationsStats } from "./components/AffectationsStats";
import { AffectationsSellers } from "./components/AffectationsSellers";
import { AffectationsReservations } from "./components/AffectationsReservations";

// Context
import { AssignmentProvider } from "./components/AffectationsContext";
import { CustomDragLayer } from "./components/CustomDragLayer";

// Debug utility to detect backend (for development)
const AffectationsBackendDebug: React.FC = () => {
  const [isTouch, setIsTouch] = useState(false);
  useEffect(() => {
    const check = () => setIsTouch('ontouchstart' in window || navigator.maxTouchPoints > 0);
    check();
    window.addEventListener('resize', check);
    return () => window.removeEventListener('resize', check);
  }, []);
  return (
    <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded p-1 mb-1 text-xs">
      <b>DnD:</b> {isTouch ? 'Touch' : 'Mouse'}
    </div>
  );
};

const AffectationsDevPage = () => {
  return (
    <Suspense fallback={<div className="text-xs text-center p-1">Loading...</div>}>
      <AffectationsDevPageContent />
    </Suspense>
  );
};

const AffectationsDevPageContent = () => {
  const searchParams = useSearchParams();
  const { t } = useLanguage();
  const [branchId, setBranchId] = useState<string | null>(null);
  const [dateSelectorValue, setDateSelectorValue] = useState<{ date: Date | null; appointment: any | null }>({ date: null, appointment: null });
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(false);

  // Extract appointmentId and slot info from dateSelectorValue
  const appointmentId = dateSelectorValue.appointment?._id || null;
  const appointmentDate = dateSelectorValue.date ? dateSelectorValue.date.toISOString().slice(0, 10) : null;
  const appointmentStartTime = dateSelectorValue.appointment?.startHour || null;
  const appointmentEndTime = dateSelectorValue.appointment?.endHour || null;

  // Initialize both branchId and date from URL params on initial load
  useEffect(() => {
    if (initialLoadComplete) return;
    
    const urlBranchId = searchParams?.get("branchId");
    const urlDate = searchParams?.get("date");
    const urlAppointmentId = searchParams?.get("appointmentId");
    
    let shouldUpdateState = false;
    
    // Initialize branchId from URL if available
    if (urlBranchId && !branchId) {
      setBranchId(urlBranchId);
      shouldUpdateState = true;
    }
    
    // Initialize date from URL if available
    if (urlDate) {
      try {
        // Parse the date in a timezone-safe way
        const dateObj = parse(urlDate, "yyyy-MM-dd", new Date());
        
        // Update state only if there's a change
        if (!dateSelectorValue.date || 
            dateSelectorValue.date.toISOString().slice(0, 10) !== dateObj.toISOString().slice(0, 10)) {
          setDateSelectorValue(prev => ({ ...prev, date: dateObj }));
          shouldUpdateState = true;
        }
      } catch (error) {
        console.error("Error parsing date from URL:", error);
      }
    }
    
    if (shouldUpdateState) {
      // Don't mark as complete yet, as we need to wait for components to render with the updated state
      setTimeout(() => {
        setInitialLoadComplete(true);
      }, 500); // Small delay to ensure components have time to react to the state changes
    } else {
      setInitialLoadComplete(true);
    }
  }, [searchParams, branchId, dateSelectorValue.date, initialLoadComplete]);

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    setSidebarVisible(prev => !prev);
    
    // Update body class based on sidebar visibility
    if (sidebarVisible) {
      document.body.classList.add('hide-toolbar');
      document.body.classList.remove('show-sidebar-only');
    } else {
      document.body.classList.remove('hide-toolbar');
      document.body.classList.add('show-sidebar-only');
    }
  };

  // Hide toolbar and sidebar when mounted
  useEffect(() => {
    // Force a small delay to ensure all layout is ready
    const timer = setTimeout(() => {
      // Add a class to hide the toolbar/sidebar for this page
      document.body.classList.add('hide-toolbar');
      document.body.classList.remove('show-sidebar-only');
    }, 100);
    
    // Clean up on unmount
    return () => {
      clearTimeout(timer);
      document.body.classList.remove('hide-toolbar');
      document.body.classList.remove('show-sidebar-only');
    };
  }, []);

  return (
    <MultiBackendProvider options={HTML5toTouch}>
      <CustomDragLayer />
      <AssignmentProvider branchId={branchId} appointmentId={appointmentId}>
        <div className="fixed inset-0 flex flex-col bg-white z-50">
          {/* Debug info - condensed to single line */}
          {process.env.NODE_ENV === 'development' && (
            <div className="bg-slate-50 text-[10px] py-0.5 px-1 border-b flex flex-wrap gap-x-2">
              <span>Load: {initialLoadComplete ? '✓' : '⟳'}</span>
              <span>Branch: {branchId || 'None'}</span>
              <span>Date: {appointmentDate || 'None'}</span>
              <span>Appt: {appointmentId?.substring(0, 6) || 'None'}</span>
              <AffectationsBackendDebug />
            </div>
          )}
          
          {/* Header: stats, selectors, etc. */}
          <div className="z-10">
            <Suspense fallback={<div className="text-xs text-center p-1">Loading...</div>}>
              <AffectationsHeader
                branchId={branchId}
                setBranchId={setBranchId}
                dateSelectorValue={dateSelectorValue}
                setDateSelectorValue={setDateSelectorValue}
              />
            </Suspense>
          </div>

          {/* Main content area - optimized for tablet */}
          <div className="flex-1 flex overflow-hidden relative">
            {/* Toggle Button - Now fixed in top left corner and not absolute */}
            <Button
              variant="outline"
              size="icon"
              className={cn(
                "h-7 w-7 rounded-full shadow-md bg-white z-50",
                sidebarVisible 
                  ? "fixed top-4 left-4 ml-[60px]" // When sidebar is visible, position it next to the sidebar
                  : "fixed top-4 left-4" // When sidebar is hidden, position it in the top left
              )}
              onClick={toggleSidebar}
              aria-label={sidebarVisible ? "Hide sidebar" : "Show sidebar"}
            >
              {sidebarVisible ? <PanelLeftClose className="h-4 w-4" /> : <PanelLeft className="h-4 w-4" />}
            </Button>

            {/* Sidebar from the app when it's toggled visible */}
            <div className={cn(
              "fixed inset-y-0 left-0 z-40 w-[60px] bg-white shadow-md transition-transform duration-200",
              sidebarVisible ? "translate-x-0" : "-translate-x-full"
            )}>
              {/* Sidebar content should be empty as we're using the app's sidebar */}
            </div>

            {/* Sellers list (left) - narrower on tablet, only this scrolls */}
            <div className={cn(
              "flex-shrink-0 overflow-hidden border-r transition-all duration-200",
              sidebarVisible ? "ml-[60px] w-1/3" : "w-1/3" // Adjust width based on sidebar visibility
            )}>
              <AffectationsSellers 
                branchId={branchId} 
                appointmentId={appointmentId}
                appointmentDate={appointmentDate}
                appointmentStartTime={appointmentStartTime}
                appointmentEndTime={appointmentEndTime}
              />
            </div>
            
            {/* Reservations list (right) - larger area for drop targets, only this scrolls */}
            <div className="flex-1 overflow-hidden">
              <AffectationsReservations branchId={branchId} appointmentId={appointmentId} />
            </div>
          </div>

          {/* Stats bar at the bottom - made compact */}
          <div className="border-t bg-slate-50 py-1 px-2 text-sm z-10">
            <AffectationsStats />
          </div>
        </div>
      </AssignmentProvider>
    </MultiBackendProvider>
  );
};

export default AffectationsDevPage; 