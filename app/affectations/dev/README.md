# Affectations Dev Page (Modular Redesign)

## Context
This directory contains the modular, responsive rebuild of the Affectations page for the AMQ Partners project. The goal is to split the original monolithic page into reusable, maintainable components, following best practices for UI/UX, state management, and code organization. This work is based on a detailed analysis and refactor plan, with a focus on developer experience and future extensibility.

## Project Goals
- Modularize the Affectations page UI and logic
- Ensure responsive design and accessibility
- Use shadcn/ui and Tailwind for modern UI/UX
- Integrate shared components (e.g., BranchSelector)
- Provide clear feedback, loading, and error states
- Document all new components and utilities

## Main Components

### `AffectationsHeader`
**Signature:**
```tsx
export const AffectationsHeader: React.FC = () => {...}
```
**Description:**
Header for the page. Contains the branch selector and a custom date/time slot selector. Manages local state for selected branch and date/appointment.

### `CustomDateSelector`
**Signature:**
```tsx
export const CustomDateSelector: React.FC<CustomDateSelectorProps>
// Props:
//   value?: { date: Date | null; appointment: Appointment | null }
//   onChange?: (value: { date: Date | null; appointment: Appointment | null }) => void
```
**Description:**
A custom date and time slot selector. Clicking opens a popover with a calendar and a list of appointments for the selected date. Selecting a date fetches and displays time slots; selecting a slot closes the popover and updates the value. The trigger button automatically sizes to fit its content when closed, and the popover expands to a wider fixed width (~472px) when open to accommodate both the calendar and time slots side by side.

### `AffectationsSellers`

A component that displays a list of sellers for the affectations page with a simplified interface. It shows all sellers by default with visual indicators for different availability states.

**Features:**
- Simple toggle to filter between all sellers and only available sellers
- Search sellers by name or phone number
- Display sellers with avatar initials, name, phone number, and assignment count badges
- Color-coding for availability states:
  - White: Available sellers
  - Amber/Yellow: Unavailable but assigned sellers
  - Red: Unavailable and not assigned sellers
- Sorted list: available sellers first, then sorted by daily assignment count (ascending)
- Responsive design with proper loading, error, and empty states
- Partial drag handle with visual indicator to improve scrolling on touch devices
- Assignment counter badge showing current assignment count for the selected time slot

**Props:**
```typescript
interface AffectationsSellersProps {
  branchId: string | null;
  appointmentId: string | null;
  appointmentDate: string | null;
  appointmentStartTime: string | null;
  appointmentEndTime: string | null;
}
```

**State:**
- `sellers`: Array of seller objects with availability and assignment data
- `filteredSellers`: Filtered array based on search term and availability toggle
- `searchTerm`: Current search input value
- `showOnlyAvailable`: Toggle state for filtering only available sellers

**API:**
Fetches data from `/api/affectations/sellers-list` with query parameters for branch, appointment, date and time.

**DraggableSeller Component:**
A subcomponent that renders an individual seller card with:
- Visual styling based on availability status
- Contact information display
- Assignment count indicators
- A dedicated draggable handle with visual indicator for touch devices
- "Indisponible" indicator for sellers who cannot be assigned

### `AffectationsReservations`
**Signature:**
```tsx
export const AffectationsReservations: React.FC<AffectationsReservationsProps> = ({ branchId, appointmentId }) => {...}
```
**Description:**
A component that displays the list of reservations for a selected branch and appointment. Features include:
- Search functionality to filter reservations by client name, phone, email, or city
- Display of client information including contact details, location, and party size
- Visual indicators for reservation type and status
- Information about assigned users when applicable
- Responsive layout with proper loading, error, and empty states
- Reservation count indicator showing filtered/total results
- Drop zones for seller assignment via drag and drop
- Color-coded reservations based on assigned seller (consistent colors per seller)
- Ability to unassign sellers via a button click

### `AssignmentContext`
A React context that manages the assignments between sellers and reservations. It provides the following:

**Features:**
- Load existing assignments from API when branch or appointment changes
- Add new assignments with API integration
- Remove assignments with API integration
- Track loading states and errors
- Get assignments for a specific seller or reservation
- Count assignments per seller

**API Integration:**
- Fetch assignments: `GET /api/affectations/appointments/${appointmentId}/affectations`
- Assign seller: `POST /api/affectations/assign`
- Unassign seller: `POST /api/affectations/unassign`

### `AffectationsStats`
**Signature:**
```tsx
export const AffectationsStats: React.FC = () => {...}
```
**Description:**
Placeholder for the stats bar (adults/children present/total, etc.).

### `AffectationsDebug`
**Signature:**
```tsx
export const AffectationsDebug: React.FC = () => {...}
```
**Description:**
Debug info and tools, only rendered in development mode.

### CustomDragLayer
`CustomDragLayer: React.FC`
- Renders a custom drag preview for seller cards during drag-and-drop operations using react-dnd. Ensures the dragged card visually follows the user's finger/mouse, especially on touch devices. Only visible while dragging.

## Design Decisions
- **Date/Time Selector:** The trigger button fits its content when closed, while the popover expands to a wider fixed width when open to accommodate both the calendar and time slot list side by side, ensuring a clean UX with no overflow.
- **State Management:** Local state is used for selectors; can be lifted up as needed.
- **Responsiveness:** Layouts use flex and responsive classes for best experience on all devices.
- **Extensibility:** All components are designed to be easily extended and reused.
- **Drag and Drop:** Uses react-dnd for intuitive seller-to-reservation assignments with visual feedback.
- **Draggable Handle:** Only a portion of the seller card is draggable with a clear visual indicator, allowing the rest of the card to be used for scrolling on touch devices.
- **Color Coding:** Consistent color scheme makes it easy to track which sellers are assigned to which reservations.

## Next Steps
- Connect selectors to data fetching and filtering for sellers/reservations
- Implement modular logic for assignment, drag-and-drop, and stats
- Add tests and further documentation as new components/utilities are added
- Add additional features for bulk assignment and statistics

## Relevant APIs (for reference)
- `/api/branches`
- `/api/affectations/appointments`
- `/api/affectations/reservations`
- `/api/affectations/sellers`
- `/api/affectations/user-stats`
- `/api/users/branch-sellers`
- `/api/affectations/assign`
- `/api/affectations/unassign`
- `/api/affectations/appointments/${appointmentId}/affectations`