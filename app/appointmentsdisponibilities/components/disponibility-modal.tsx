'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { Label } from '@/components/ui/label';
import { useLanguage } from '@/lib/contexts/language-context';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { useSession } from 'next-auth/react';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';

interface User {
  _id: string;
  name: string;
  email: string;
}

interface DisponibilityModalProps {
  disponibility: any;
  onSave: (disponibility: any) => void;
  onClose: () => void;
}

export function DisponibilityModal({ disponibility, onSave, onClose }: DisponibilityModalProps) {
  const { t } = useLanguage();
  const { toast } = useToast();
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      toast({
        title: 'Error',
        description: 'You must be logged in.',
        variant: 'destructive',
      });
    },
  });
  

  const [localDisponibility, setLocalDisponibility] = useState(disponibility);
  const [hourSelections, setHourSelections] = useState<Record<string, boolean>>(
    disponibility.opened_hours || {}
  );
  const [users, setUsers] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(true);

  // Get initial user ID from disponibility or session
  const [selectedUserId, setSelectedUserId] = useState<string>(() => {
    // If disponibility has a user_id, use it
    if (disponibility.user_id) {
      return typeof disponibility.user_id === 'object' 
        ? disponibility.user_id._id 
        : disponibility.user_id;
    }
    // Otherwise, use session user id
    return '';
  });

  // Only set selectedUserId from session if it's empty and no disponibility.user_id exists
  useEffect(() => {
    if (!selectedUserId && !disponibility.user_id && status === 'authenticated' && session?.user?.id) {
      setSelectedUserId(session.user.id);
    }
  }, [status, session?.user, selectedUserId, disponibility.user_id]);

  // Generate time slots from 9 AM to 8 PM
  const timeSlots = Array.from({ length: 12 }, (_, i) => {
    const hour = i + 9;
    return `${hour.toString().padStart(2, '0')}:00`;
  });

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoadingUsers(true);
        const response = await fetch('/api/users');
        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }
        const data = await response.json();
        setUsers(data.users || []);
      } catch (error) {
        console.error('Error fetching users:', error);
        toast({
          title: 'Error',
          description: 'Failed to load users',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingUsers(false);
      }
    };

    fetchUsers();
  }, [toast]);

  const handleToggleHour = (time: string) => {
    setHourSelections(prev => ({
      ...prev,
      [time]: !prev[time]
    }));
  };

  const handleSubmit = async () => {
    // Wait for session to be ready
    if (status === 'loading') {
      return;
    }

    // Ensure we have a valid session
    if (!session?.user) {
      toast({
        title: 'Error',
        description: 'Please log in to continue.',
        variant: 'destructive',
      });
      return;
    }

    console.log('Submit attempt with:', {
      fullSession: session,
      sessionUser: session.user,
      selectedUserId,
      disponibility: localDisponibility,
      hourSelections
    });

    // Ensure we have a selected user
    if (!selectedUserId) {
      console.log('No user selected');
      toast({
        title: 'Error',
        description: 'Please select a user',
        variant: 'destructive',
      });
      return;
    }

    try {
      const updatedDisponibility = {
        ...localDisponibility,
        user_id: selectedUserId,
        opened_hours: hourSelections,
        created_by: selectedUserId, // Use the selected user ID for created_by
        date: disponibility.date
      };

      console.log('Saving disponibility:', updatedDisponibility);
      onSave(updatedDisponibility);
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      toast({
        title: 'Error',
        description: 'Failed to save disponibility',
        variant: 'destructive',
      });
    }
  };

  // Show loading state while session is loading
  if (status === 'loading') {
    return (
      <Dialog open={true} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <div className="flex items-center justify-center p-8">
            <Skeleton className="h-32 w-full" />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  console.log('Render state:', {
    status,
    session,
    selectedUserId,
    hourSelections
  });

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {t('appointmentsDisponibilities.editTitle')}: {format(new Date(disponibility.date), 'EEEE, MMMM d, yyyy')}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div>
            <label className="text-sm font-medium mb-2 block">
              {t('appointmentsDisponibilities.selectUser')}
            </label>
            {isLoadingUsers ? (
              <Skeleton className="h-10 w-full" />
            ) : (
              <Select
                value={selectedUserId}
                onValueChange={setSelectedUserId}
                disabled={!!disponibility.user_id}
              >
                <SelectTrigger className={cn(
                  disponibility.user_id && "opacity-50 cursor-not-allowed"
                )}>
                  <SelectValue placeholder={t('appointmentsDisponibilities.selectUserPlaceholder')} />
                </SelectTrigger>
                <SelectContent>
                  {users.map((user) => (
                    <SelectItem key={user._id} value={user._id}>
                      {user.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            {timeSlots.map((time) => (
              <div
                key={time}
                className={cn(
                  "flex items-center justify-between p-3 rounded-lg",
                  hourSelections[time] ? "bg-gray-100" : "bg-gray-50"
                )}
              >
                <Label htmlFor={`time-${time}`} className="font-medium">
                  {time}
                </Label>
                <Switch
                  id={`time-${time}`}
                  checked={!!hourSelections[time]}
                  onCheckedChange={() => handleToggleHour(time)}
                />
              </div>
            ))}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {t('common.cancel')}
          </Button>
          <Button onClick={handleSubmit}>
            {t('common.save')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 