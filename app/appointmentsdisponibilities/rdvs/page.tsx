'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Plus, TableIcon, CalendarIcon } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/lib/contexts/language-context';
import { useSession } from 'next-auth/react';
import type { RDV } from '@/app/types/rdv';
import { RDVStatus } from '@/app/types/rdv';
import { RDVsTable } from '@/app/components/rdvs-table';
import { RDVForm } from '@/app/components/rdv-form';
import { RDVsCalendar } from './components/rdvs-calendar';

export default function RDVsPage() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const { t } = useLanguage();
  
  const [rdvs, setRdvs] = useState<RDV[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [showDeleted, setShowDeleted] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [selectedRDV, setSelectedRDV] = useState<Partial<RDV> | null>(null);
  const [view, setView] = useState<'table' | 'calendar'>('table');

  useEffect(() => {
    const storedView = localStorage.getItem('rdvsView') as 'table' | 'calendar';
    if (storedView) {
      setView(storedView);
    }
  }, []);

  const handleViewChange = (newView: 'table' | 'calendar') => {
    setView(newView);
    localStorage.setItem('rdvsView', newView);
  };

  const fetchRDVs = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/rdvs?showDeleted=${showDeleted}`);
      if (!response.ok) {
        throw new Error('Failed to fetch RDVs');
      }
      const data = await response.json();
      setRdvs(data);
      setRefreshKey(prev => prev + 1);
    } catch (error) {
      console.error('Failed to fetch RDVs:', error);
      toast({
        title: 'Error',
        description: t('rdvs.messages.fetchError'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRDVs();
  }, [showDeleted]); // Refetch when showDeleted changes

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">{t('rdvs.title')}</h1>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 border rounded-md p-1">
              <Button
                variant={view === 'table' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setView('table')}
                className="gap-2"
              >
                <TableIcon className="h-4 w-4" />
                Table
              </Button>
              <Button
                variant={view === 'calendar' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleViewChange('calendar')}
                className="gap-2"
              >
                <CalendarIcon className="h-4 w-4" />
                Calendar
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="show-deleted"
                checked={showDeleted}
                onCheckedChange={setShowDeleted}
              />
              <Label htmlFor="show-deleted">{t('rdvs.showDeleted')}</Label>
            </div>
          </div>
          {!showDeleted && (
            <Button
              onClick={() => setIsAddDialogOpen(true)}
              className="bg-[#e81217] hover:bg-[#e81217]/90 text-white"
            >
              <Plus className="mr-2 h-4 w-4" />
              {t('rdvs.addRdv')}
            </Button>
          )}
        </div>
      </div>

      {view === 'table' ? (
        <RDVsTable
          key={refreshKey}
          rdvs={rdvs}
          isLoading={isLoading}
          onDelete={fetchRDVs}
          onRestore={fetchRDVs}
          showDeleted={showDeleted}
        />
      ) : (
        <RDVsCalendar
          rdvs={rdvs}
          isLoading={isLoading}
          onDateClick={(date) => {
            setIsAddDialogOpen(true);
            // Pre-fill the date in the form
            const formattedDate = format(date, 'yyyy-MM-dd');
            setSelectedRDV({
              firstName: '',
              lastName: '',
              address: '',
              postalCode: '',
              phone: '',
              status: RDVStatus.PENDING,
              createdBy: session?.user?.id || '',
              date_rdv: formattedDate,
              hour: '9:00' // Default to first available slot
            });
          }}
          onRdvClick={(rdv) => {
            setSelectedRDV(rdv);
            setIsAddDialogOpen(true);
          }}
        />
      )}

      <Dialog
        open={isAddDialogOpen}
        onOpenChange={(open) => {
          setIsAddDialogOpen(open);
          if (!open) setSelectedRDV(null);
        }}
      >
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {selectedRDV?._id ? t('rdvs.editRdv') : t('rdvs.addRdv')}
            </DialogTitle>
          </DialogHeader>
          <RDVForm
            initialData={selectedRDV || undefined}
            onSuccess={() => {
              setIsAddDialogOpen(false);
              setSelectedRDV(null);
              fetchRDVs();
              toast({
                title: 'Success',
                description: selectedRDV?._id ? t('rdvs.messages.updated') : t('rdvs.messages.created'),
              });
            }}
            onCancel={() => {
              setIsAddDialogOpen(false);
              setSelectedRDV(null);
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}