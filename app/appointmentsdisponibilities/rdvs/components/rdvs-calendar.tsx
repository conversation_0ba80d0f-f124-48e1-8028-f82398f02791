'use client';

import { useState } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, addMonths, subMonths, isSameMonth, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Loader2, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { RDV } from '@/app/types/rdv';
import { useLanguage } from '@/lib/contexts/language-context';

interface RDVsCalendarProps {
  rdvs: RDV[];
  isLoading: boolean;
  onDateClick: (date: Date) => void;
  onRdvClick: (rdv: RDV) => void;
}

export function RDVsCalendar({ rdvs, isLoading, onDateClick, onRdvClick }: RDVsCalendarProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const { t, language } = useLanguage();
  const locale = language === 'fr' ? fr : undefined;

  const handlePreviousMonth = () => setCurrentMonth(subMonths(currentMonth, 1));
  const handleNextMonth = () => setCurrentMonth(addMonths(currentMonth, 1));

  const days = eachDayOfInterval({
    start: startOfMonth(currentMonth),
    end: endOfMonth(currentMonth)
  });

  // Create a map of dates to RDVs, sorted by time
  const rdvMap = rdvs.reduce((acc, rdv) => {
    const date = rdv.date_rdv;
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(rdv);
    // Sort by hour
    acc[date].sort((a, b) => {
      return parseInt(a.hour) - parseInt(b.hour);
    });
    return acc;
  }, {} as Record<string, RDV[]>);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <Button 
          variant="outline" 
          size="icon" 
          onClick={handlePreviousMonth}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-xl font-semibold">
          {format(currentMonth, 'MMMM yyyy', { locale })}
        </h2>
        <Button 
          variant="outline" 
          size="icon" 
          onClick={handleNextMonth}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-7 gap-1">
        {['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'].map((day) => (
          <div key={day} className="p-2 text-center font-medium border-b">
            {t(`common.days.short.${day}`)}
          </div>
        ))}
        
        {days.map((day) => {
          const formattedDay = format(day, 'yyyy-MM-dd');
          const dayRdvs = rdvMap[formattedDay] || [];
          
          return (
            <div
              key={day.toString()}
              className={cn(
                "p-2 border min-h-[100px] relative cursor-pointer",
                !isSameMonth(day, currentMonth)
                  ? "bg-muted/50 opacity-50"
                  : "hover:bg-muted/50",
                dayRdvs.length > 0 && "bg-primary/5"
              )}
              onClick={() => isSameMonth(day, currentMonth) && onDateClick(day)}
            >
              <div className="flex justify-between items-start">
                <span className={cn(
                  "font-medium",
                  !isSameMonth(day, currentMonth) && "text-muted-foreground"
                )}>
                  {format(day, 'd')}
                </span>
                <div className="flex items-center gap-1">
                  {dayRdvs.length > 0 && (
                    <Calendar className="h-4 w-4 text-primary" />
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 hover:bg-primary/20"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDateClick(day);
                    }}
                  >
                    <span className="text-xs font-bold">+</span>
                  </Button>
                </div>
              </div>
              
              {dayRdvs.length > 0 && (
                <div className="mt-1">
                  {dayRdvs.map((rdv) => (
                    <div
                      key={rdv._id}
                      className="text-xs bg-primary/10 text-primary p-1 mb-1 rounded truncate cursor-pointer hover:bg-primary/20"
                      title={`${rdv.firstName} ${rdv.lastName} - ${rdv.hour}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        onRdvClick(rdv);
                      }}
                    >
                      {rdv.hour} - {rdv.firstName} {rdv.lastName}
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}