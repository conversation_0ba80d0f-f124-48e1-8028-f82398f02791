'use client';

import { useState, useEffect, useCallback } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, addMonths, subMonths, isSameMonth } from 'date-fns';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useSession } from 'next-auth/react';
import { ChevronLeft, ChevronRight, Plus, Loader2, Calendar } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { OverbookingModal } from './components/overbooking-modal';
import { Overbooking, Branch } from './types';

export default function OverbookingPage() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [overbookings, setOverbookings] = useState<Overbooking[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedOverbooking, setSelectedOverbooking] = useState<Overbooking | null>(null);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoadingBranches, setIsLoadingBranches] = useState(true);
  const [selectedBranchId, setSelectedBranchId] = useState<string>('');

  // Function to fetch branches
  const fetchBranches = useCallback(async () => {
    try {
      setIsLoadingBranches(true);
      const response = await fetch('/api/branches');
      if (!response.ok) throw new Error(`Failed to fetch branches: ${response.status}`);
      const data = await response.json();
      setBranches(data);
      if (data.length > 0 && !selectedBranchId) {
        setSelectedBranchId(data[0]._id);
      }
    } catch (error) {
      console.error('Error fetching branches:', error);
      toast({
        title: 'Error',
        description: 'Failed to load branches. Please refresh the page.',
        variant: 'destructive',
      });
      setBranches([]);
    } finally {
      setIsLoadingBranches(false);
    }
  }, [toast, selectedBranchId]);

  // Function to fetch overbookings
  const fetchOverbookings = useCallback(async () => {
    if (!selectedBranchId) return;

    setIsLoading(true);
    try {
      const start = format(startOfMonth(currentMonth), 'yyyy-MM-dd');
      const end = format(endOfMonth(currentMonth), 'yyyy-MM-dd');
      
      const url = `/api/overbooking?branch_id=${selectedBranchId}&date=${start}&endDate=${end}`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch overbookings: ${response.status}`);
      }
      
      const data = await response.json();
      setOverbookings(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error in fetchOverbookings:', error);
      toast({
        title: "Error",
        description: "Failed to load overbookings. Please try again.",
        variant: "destructive",
      });
      setOverbookings([]);
    } finally {
      setIsLoading(false);
    }
  }, [selectedBranchId, currentMonth, toast]);

  useEffect(() => {
    fetchBranches();
  }, [fetchBranches]);

  useEffect(() => {
    if (selectedBranchId) {
      fetchOverbookings();
    }
  }, [fetchOverbookings, selectedBranchId]);

  const handlePreviousMonth = () => setCurrentMonth(subMonths(currentMonth, 1));
  const handleNextMonth = () => setCurrentMonth(addMonths(currentMonth, 1));

  const handleDateClick = (date: Date) => {
    if (!isSameMonth(date, currentMonth)) return;
    
    const formattedDate = format(date, 'yyyy-MM-dd');
    setSelectedDate(formattedDate);
    
    const existingOverbooking = overbookings.find(
      over => over.date === formattedDate
    );
    
    setSelectedOverbooking(existingOverbooking || {
      branch_id: selectedBranchId,
      date: formattedDate,
      opened_hours: {}
    } as Overbooking);
    
    setIsModalOpen(true);
  };

  const handleSaveOverbooking = async (data: Overbooking) => {
    try {
      if (!selectedBranchId) {
        toast({
          title: "Error",
          description: "Please select a branch first",
          variant: "destructive",
        });
        return;
      }
      
      const method = data._id ? 'PUT' : 'POST';
      const url = data._id
        ? `/api/overbooking/${data._id}`
        : '/api/overbooking';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...data, branch_id: selectedBranchId }),
      });
      
      const responseData = await response.json();
      
      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to save overbooking');
      }
      
      setIsModalOpen(false);
      fetchOverbookings();
      
      toast({
        title: "Success",
        description: "Overbooking saved successfully",
      });
    } catch (error: any) {
      console.error('Error saving overbooking:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to save overbooking",
        variant: "destructive",
      });
    }
  };

  const days = eachDayOfInterval({
    start: startOfMonth(currentMonth),
    end: endOfMonth(currentMonth)
  });

  const overbookingMap = overbookings.reduce((acc, over) => {
    acc[over.date] = over;
    return acc;
  }, {} as Record<string, Overbooking>);

  return (
    <div className="flex h-[calc(100vh-4rem)]">
      {/* Branches Sidebar - Desktop Only */}
      <div className="hidden md:block w-64 border-r bg-gray-50/50 overflow-y-auto">
        <div className="p-4">
          <h2 className="text-lg font-semibold mb-4">Branches</h2>
          {isLoadingBranches ? (
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {branches.map((branch) => (
                <button
                  key={branch._id}
                  onClick={() => setSelectedBranchId(branch._id)}
                  className={cn(
                    "w-full px-4 py-3 text-left transition-colors relative",
                    selectedBranchId === branch._id
                      ? "bg-gray-100"
                      : "hover:bg-gray-50"
                  )}
                >
                  <div className="font-medium text-gray-900">{branch.name}</div>
                  {selectedBranchId === branch._id && (
                    <div className="absolute left-0 top-0 bottom-0 w-0.5 bg-red-500" />
                  )}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Calendar View */}
      <div className="flex-1 p-6 overflow-y-auto">
        <div className="max-w-5xl mx-auto">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 space-y-4 sm:space-y-0">
            <h1 className="text-2xl font-bold">Branch Overbooking</h1>

            {/* Mobile Branch Select */}
            <div className="block md:hidden w-full sm:w-auto mb-4 sm:mb-0">
              {isLoadingBranches ? (
                <Skeleton className="h-10 w-full sm:w-[200px]" />
              ) : (
                <Select
                  value={selectedBranchId}
                  onValueChange={setSelectedBranchId}
                >
                  <SelectTrigger className="w-full sm:w-[200px]">
                    <SelectValue placeholder="Select a branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {branches.map((branch) => (
                      <SelectItem key={branch._id} value={branch._id}>
                        <div className="font-medium">{branch.name}</div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            <div className="flex items-center space-x-4">
              <Button 
                variant="outline" 
                size="icon" 
                onClick={handlePreviousMonth}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-xl font-semibold min-w-[200px] text-center">
                {format(currentMonth, 'MMMM yyyy')}
              </h2>
              <Button 
                variant="outline" 
                size="icon" 
                onClick={handleNextMonth}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="grid grid-cols-7 gap-1">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <div key={day} className="p-2 text-center font-medium border-b">
                  {day}
                </div>
              ))}
              
              {days.map((day) => {
                const formattedDay = format(day, 'yyyy-MM-dd');
                const overbooking = overbookingMap[formattedDay];
                const hasOverbooking = !!overbooking;
                
                const availableHours = hasOverbooking ? 
                  Object.entries(overbooking.opened_hours)
                    .filter(([_, isAvailable]) => isAvailable)
                    .map(([hour]) => hour)
                    .sort() : [];
                
                return (
                  <div 
                    key={day.toString()}
                    className={cn(
                      "p-2 border min-h-[100px] relative",
                      !isSameMonth(day, currentMonth)
                        ? "bg-muted/50 opacity-50"
                        : "cursor-pointer hover:bg-muted/50",
                      hasOverbooking && "bg-primary/5"
                    )}
                    onClick={() => handleDateClick(day)}
                  >
                    <div className="flex justify-between items-start">
                      <span className={cn(
                        "font-medium",
                        !isSameMonth(day, currentMonth) && "text-muted-foreground"
                      )}>
                        {format(day, 'd')}
                      </span>
                      {hasOverbooking && (
                        <Calendar className="h-4 w-4 text-primary" />
                      )}
                    </div>
                    
                    {hasOverbooking && Object.keys(overbooking.opened_hours).length > 0 && (
                      <div className="mt-2 text-xs space-y-1">
                        <div className="flex flex-wrap gap-1">
                          {Object.entries(overbooking.opened_hours)
                            .filter(([_, count]) => count > 0)
                            .map(([hour, count]) => (
                              <span key={hour} className="inline-block px-1.5 py-0.5 bg-primary/10 text-primary rounded-sm">
                                {hour} ({count})
                              </span>
                            ))}
                        </div>
                      </div>
                    )}
                    
                    <Button 
                      size="icon" 
                      variant="ghost" 
                      className="absolute bottom-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDateClick(day);
                      }}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      {isModalOpen && selectedOverbooking && (
        <OverbookingModal
          overbooking={selectedOverbooking}
          onSave={handleSaveOverbooking}
          onClose={() => setIsModalOpen(false)}
        />
      )}
    </div>
  );
}