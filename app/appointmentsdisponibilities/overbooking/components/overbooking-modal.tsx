'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface Overbooking {
  _id?: string;
  branch_id: string;
  date: string;
  opened_hours: {
    [key: string]: number;
  };
}

interface OverbookingModalProps {
  overbooking: Overbooking;
  onSave: (overbooking: Overbooking) => Promise<void>;
  onClose: () => void;
}

export function OverbookingModal({ overbooking, onSave, onClose }: OverbookingModalProps) {
  const hours = Array.from({ length: 13 }, (_, i) => `${i + 8}:00`);
  const [pendingHours, setPendingHours] = useState<Overbooking['opened_hours']>(overbooking.opened_hours || {});
  const [isSaving, setIsSaving] = useState(false);

  const incrementHour = (hour: string) => {
    setPendingHours(prev => ({
      ...prev,
      [hour]: (prev[hour] || 0) + 1
    }));
  };

  const decrementHour = (hour: string) => {
    setPendingHours(prev => {
      const count = prev[hour] || 0;
      if (count > 0) {
        return {
          ...prev,
          [hour]: count - 1
        };
      }
      return prev;
    });
  };


  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave({
        ...overbooking,
        opened_hours: pendingHours,
      });
      onClose();
    } catch (error) {
      console.error('Failed to save overbooking:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open onOpenChange={() => onClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            Overbooking for {format(new Date(overbooking.date), 'MMMM d, yyyy')}
          </DialogTitle>
        </DialogHeader>
        <div className="grid grid-cols-3 gap-2 py-4">
          {hours.map((hour) => (
            <div key={hour} className="flex flex-col items-center">
              <Button
                onClick={() => incrementHour(hour)}
                variant={pendingHours[hour] && pendingHours[hour] > 0 ? 'default' : 'outline'}
                className={cn(
                  'w-full',
                  pendingHours[hour] && pendingHours[hour] > 0 && 'bg-primary text-primary-foreground'
                )}
              >
                {hour} ({pendingHours[hour] || 0})
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => decrementHour(hour)}
                disabled={!(pendingHours[hour] && pendingHours[hour] > 0)}
              >
                -
              </Button>
            </div>
          ))}
        </div>
        <div className="flex justify-end space-x-2 mt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving}
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}