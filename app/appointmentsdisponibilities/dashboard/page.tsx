'use client';

import { Suspense } from 'react';
import AvailabilityCalendar from '@/components/dashboard/availability-calendar';
import { Skeleton } from '@/components/ui/skeleton';

export default function AppointmentsDashboardPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <Suspense 
        fallback={
          <div className="w-full h-[600px] rounded-lg border">
            <Skeleton className="w-full h-full" />
          </div>
        }
      >
        <AvailabilityCalendar />
      </Suspense>
    </div>
  );
}