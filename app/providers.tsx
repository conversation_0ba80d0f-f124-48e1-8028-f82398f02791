'use client';

import { ReduxProvider } from '@/lib/redux/provider';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from '@/components/theme-provider';
import { SubMenuProvider } from '@/components/dynamic-sub-menu/sub-menu-provider';
import { LanguageProvider } from '@/lib/contexts/language-context';
import { NotificationProvider } from '@/lib/contexts/notification-context';
import { TooltipProvider } from '@/components/ui/tooltip';
import { InvitationProgressProvider } from '@/lib/contexts/invitation-progress-context';
import { PermissionsInitializer } from '@/components/permissions-initializer';
import { usePathname } from 'next/navigation';

function ConditionalThemeProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const isInvoiceSigningPage = pathname?.startsWith('/invoice-signing');

  if (isInvoiceSigningPage) {
    return (
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        forcedTheme="light"
        enableSystem={false}
        disableTransitionOnChange
      >
        {children}
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      {children}
    </ThemeProvider>
  );
}

export function AppProviders({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <LanguageProvider>
        <ConditionalThemeProvider>
          <SubMenuProvider>
            <ReduxProvider>
              <PermissionsInitializer>
                <TooltipProvider>
                  <NotificationProvider>
                    <InvitationProgressProvider>
                      {children}
                    </InvitationProgressProvider>
                  </NotificationProvider>
                </TooltipProvider>
              </PermissionsInitializer>
            </ReduxProvider>
          </SubMenuProvider>
        </ConditionalThemeProvider>
      </LanguageProvider>
    </SessionProvider>
  );
}