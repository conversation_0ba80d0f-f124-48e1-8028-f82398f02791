'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Pencil, Trash2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import { Allergy } from '@/types/allergy';
import { useLanguage } from '@/lib/contexts/language-context';
import { ALLERGY_PERMISSIONS } from '@/types/permission-codes';
import { PermissionGuard } from '@/components/permission-guard';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { useSession } from 'next-auth/react';

export default function AllergiesPage() {
  const { data: session, status: sessionStatus } = useSession();
  const [searchTerm, setSearchTerm] = useState('');
  const [allergies, setAllergies] = useState<Allergy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedAllergy, setSelectedAllergy] = useState<Allergy | null>(null);
  const [formData, setFormData] = useState({ name: '', name_en: '' });
  const { toast } = useToast();
  const { t } = useLanguage();

  const fetchAllergies = async () => {
    try {
      setIsLoading(true);
      setIsError(false);
      setErrorMessage('');
      
      const response = await fetch('/api/allergies');
      
      if (!response.ok) {
        let errorMsg;
        try {
          const errorData = await response.json();
          errorMsg = errorData?.error || `HTTP error ${response.status}`;
        } catch (parseError) {
          // If JSON parsing fails, use status text
          errorMsg = `HTTP error ${response.status} (${response.statusText})`;
        }
        
        throw new Error(errorMsg);
      }
      
      const data = await response.json();
      setAllergies(data);
    } catch (error) {
      console.error('Failed to fetch allergies:', error);
      setIsError(true);
      setErrorMessage(error instanceof Error ? error.message : 'Failed to fetch allergies');
      toast({
        title: 'Error',
        description: t('allergies.error'),
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch if session is authenticated
    if (sessionStatus === 'authenticated') {
      fetchAllergies();
    }
  }, [sessionStatus]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const url = selectedAllergy 
        ? `/api/allergies/${selectedAllergy._id}` 
        : '/api/allergies';
      const method = selectedAllergy ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.error || 'Failed to save allergy');
      }

      toast({
        description: t(selectedAllergy ? 'allergies.updateSuccess' : 'allergies.createSuccess'),
      });

      setIsAddDialogOpen(false);
      setIsEditDialogOpen(false);
      setSelectedAllergy(null);
      setFormData({ name: '', name_en: '' });
      fetchAllergies();
    } catch (error) {
      console.error('Save allergy error:', error);
      toast({
        title: 'Error',
        description: t('allergies.saveError'),
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async () => {
    if (!selectedAllergy) return;

    try {
      const response = await fetch(`/api/allergies/${selectedAllergy._id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.error || 'Failed to delete allergy');
      }

      toast({
        description: t('allergies.deleteSuccess'),
      });

      setIsDeleteDialogOpen(false);
      setSelectedAllergy(null);
      fetchAllergies();
    } catch (error) {
      console.error('Delete allergy error:', error);
      toast({
        title: 'Error',
        description: t('allergies.deleteError'),
        variant: 'destructive',
      });
    }
  };

  const filteredAllergies = allergies.filter(allergy =>
    allergy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    allergy.name_en.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Add a retry button in the error state
  const ErrorDisplay = () => (
    <TableRow>
      <TableCell colSpan={3} className="text-center">
        <div className="flex flex-col items-center py-4">
          <p className="text-destructive mb-4">{t('allergies.errorLoading')}: {errorMessage}</p>
          <Button 
            variant="outline" 
            onClick={() => fetchAllergies()}
            className="w-auto"
          >
            {t('common.retry')}
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );

  // Show loading state for the entire page during session loading
  if (sessionStatus === 'loading') {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-between items-center mb-6">
          <Skeleton className="h-8 w-[200px]" />
          <Skeleton className="h-10 w-[150px]" />
        </div>
        <Skeleton className="h-10 w-[300px] mb-4" />
        <div className="border rounded-lg">
          <div className="p-4">
            <Skeleton className="h-8 w-full mb-4" />
            <Skeleton className="h-16 w-full mb-2" />
            <Skeleton className="h-16 w-full mb-2" />
            <Skeleton className="h-16 w-full" />
          </div>
        </div>
      </div>
    );
  }

  // Check permission after session is loaded
  const hasViewPermission = session?.user?.permissions?.includes(ALLERGY_PERMISSIONS.VIEW_ALLERGIES);
  
  if (!hasViewPermission) {
    return (
      <div className="container mx-auto py-10">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-destructive">
            {t('common.accessDenied')}
          </h1>
          <p className="text-muted-foreground">
            {t('common.noPermission')}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('allergies.title')}</h1>
        {session?.user?.permissions?.includes(ALLERGY_PERMISSIONS.CREATE_ALLERGIES) && (
          <Button onClick={() => {
            setFormData({ name: '', name_en: '' });
            setIsAddDialogOpen(true);
          }}>
            <Plus className="w-4 h-4 mr-2" />
            {t('allergies.addAllergy')}
          </Button>
        )}
      </div>

      <div className="mb-4">
        <Input
          placeholder={t('table.search')}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>{t('allergies.name')}</TableHead>
              <TableHead>{t('allergies.name_en')}</TableHead>
              <TableHead className="w-[100px]">{t('table.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={3} className="text-center">
                  {t('allergies.loading')}
                </TableCell>
              </TableRow>
            ) : isError ? (
              <ErrorDisplay />
            ) : filteredAllergies.length === 0 ? (
              <TableRow>
                <TableCell colSpan={3} className="text-center">
                  {t('allergies.noAllergies')}
                </TableCell>
              </TableRow>
            ) : (
              filteredAllergies.map((allergy) => (
                <TableRow key={allergy._id}>
                  <TableCell>{allergy.name}</TableCell>
                  <TableCell>{allergy.name_en}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      {session?.user?.permissions?.includes(ALLERGY_PERMISSIONS.EDIT_ALLERGIES) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedAllergy(allergy);
                            setFormData({
                              name: allergy.name,
                              name_en: allergy.name_en,
                            });
                            setIsEditDialogOpen(true);
                          }}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                      )}
                      {session?.user?.permissions?.includes(ALLERGY_PERMISSIONS.DELETE_ALLERGIES) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedAllergy(allergy);
                            setIsDeleteDialogOpen(true);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Add/Edit Dialog */}
      <Dialog 
        open={isAddDialogOpen || isEditDialogOpen} 
        onOpenChange={(open) => {
          if (!open) {
            setIsAddDialogOpen(false);
            setIsEditDialogOpen(false);
            setSelectedAllergy(null);
            setFormData({ name: '', name_en: '' });
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {isEditDialogOpen ? t('allergies.editAllergy') : t('allergies.addAllergy')}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <label htmlFor="name">{t('allergies.name')}</label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="name_en">{t('allergies.name_en')}</label>
                <Input
                  id="name_en"
                  value={formData.name_en}
                  onChange={(e) => setFormData({ ...formData, name_en: e.target.value })}
                  required
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit">
                {isEditDialogOpen ? t('allergies.update') : t('allergies.add')}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('allergies.confirmDelete')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('allergies.confirmDeleteText')}
              <strong>{selectedAllergy?.name}</strong>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {t('common.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 