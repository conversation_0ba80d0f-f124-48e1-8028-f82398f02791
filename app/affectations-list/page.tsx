'use client';

import * as React from 'react';
import { useState, useEffect } from 'react';
import { format, addDays, subDays } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { Search, Download, Filter, ChevronLeft, ChevronRight, Plus, Mail } from 'lucide-react';
import { useLanguage } from '@/lib/contexts/language-context';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Branch {
  _id: string;
  name: string;
}

interface Seller {
  _id: string;
  name: string;
  email?: string;
  phone?: string;
  type: 'branch' | 'online' | 'home' | 'family';
}

interface Reservation {
  _id: string;
  appointmentId: string;
  partnerId: string;
  assigned_user_id?: string;
  type: 'branch' | 'online' | 'home' | 'family';
  status: 'pending' | 'confirmed' | 'cancelled' | 'assigned';
  source?: 'invitation' | 'direct' | 'other' | 'amq_website'; // Added for tracking reservation source
  partner?: { // Added for populated partner information when source is invitation
    id: string;
    name: string;
    email: string;
  };
  customerInfo: {
    client1Name: string;
    hasCompanion: boolean;
    client2Name?: string;
    city: string;
    postalCode: string;
    phone: string;
    phone2?: string;
    email: string;
  };
  preferences: any;
  createdAt: string;
  updatedAt: string;
  appointmentDate?: string;
  appointmentTime?: string;
  assignedUser?: Seller;
}

interface Appointment {
  _id: string;
  branchId: string;
  date: string;
  startHour: string;
  endHour: string;
  capacity: number;
  online: number;
  home: number;
  max_capacity_family: number;
  reservationCounts?: {
    branch: number;
    online: number;
    family: number;
    home: number;
  };
}

export default function AffectationsListPage() {
  const { toast } = useToast();
  const { t } = useLanguage();
  const [date, setDate] = useState<string>(format(new Date(), 'yyyy-MM-dd'));
  const [branchId, setBranchId] = useState<string>('');
  const [branches, setBranches] = useState<Branch[]>([]);
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [showDeleted, setShowDeleted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [agentFilter, setAgentFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);
  
  // Fetch branches
  useEffect(() => {
    const fetchBranches = async () => {
      try {
        const response = await fetch('/api/branches');
        if (!response.ok) throw new Error('Failed to fetch branches');
        const data = await response.json();
        setBranches(data);
        if (data.length > 0 && !branchId) {
          setBranchId(data[0]._id);
        }
      } catch (error) {
        console.error('Error fetching branches:', error);
        toast({
          title: 'Error',
          description: 'Failed to load branches',
          variant: 'destructive',
        });
      }
    };

    fetchBranches();
  }, [toast, branchId]);

  // Fetch all reservations for the selected date and branch
  useEffect(() => {
    const fetchReservations = async () => {
      if (!branchId) return;
      
      setIsLoading(true);
      
      try {
        const response = await fetch(`/api/affectations/all-reservations?date=${date}&branchId=${branchId}`);
        if (!response.ok) throw new Error('Failed to fetch reservations');
        const data = await response.json();
        
        // Transform the data to include appointment date and time
        const transformedReservations = data.reservations.map((reservation: any) => {
          const appointment = data.appointments.find((app: any) => app._id === reservation.appointmentId);
          return {
            ...reservation,
            appointmentDate: appointment?.date,
            appointmentTime: appointment ? `${appointment.startHour}-${appointment.endHour}` : '',
            assignedUser: data.users.find((user: any) => user._id === reservation.assigned_user_id)
          };
        });
        
        setReservations(transformedReservations || []);
      } catch (error) {
        console.error('Error fetching reservations:', error);
        toast({
          title: 'Error',
          description: 'Failed to load reservations',
          variant: 'destructive',
        });
        setReservations([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchReservations();
  }, [date, branchId, toast]);

  const handlePreviousDay = () => {
    const currentDate = new Date(date);
    const newDate = subDays(currentDate, 1);
    setDate(format(newDate, 'yyyy-MM-dd'));
  };

  const handleNextDay = () => {
    const currentDate = new Date(date);
    const newDate = addDays(currentDate, 1);
    setDate(format(newDate, 'yyyy-MM-dd'));
  };

  // Filter reservations based on search, status, and agent filters
  const filteredReservations = reservations.filter(reservation => {
    // Search filter
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = searchTerm === '' ||
      reservation.customerInfo.client1Name.toLowerCase().includes(searchLower) ||
      (reservation.customerInfo.client2Name && reservation.customerInfo.client2Name.toLowerCase().includes(searchLower)) ||
      reservation.customerInfo.phone.includes(searchTerm) ||
      reservation.customerInfo.email.toLowerCase().includes(searchLower);
    
    // Status filter
    const matchesStatus = statusFilter === 'all' || reservation.status === statusFilter;
    
    // Agent filter
    const matchesAgent = agentFilter === 'all' || 
      (agentFilter === 'assigned' && reservation.assigned_user_id) ||
      (agentFilter === 'unassigned' && !reservation.assigned_user_id);
    
    return matchesSearch && matchesStatus && matchesAgent;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, 'yyyy-MM-dd');
  };

  // Get status badge style
  const getStatusBadge = (status: string) => {
    switch(status) {
      case 'assigned':
        return <Badge className="bg-green-500/10 text-green-500 border-green-500/20">Assigned</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20">Pending</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-500/10 text-red-500 border-red-500/20">Cancelled</Badge>;
      case 'confirmed':
        return <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/20">Confirmed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get type badge style
  const getTypeBadge = (type: string) => {
    switch(type) {
      case 'branch':
        return <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/20">Branch</Badge>;
      case 'online':
        return <Badge className="bg-green-500/10 text-green-500 border-green-500/20">Online</Badge>;
      case 'home':
        return <Badge className="bg-orange-500/10 text-orange-500 border-orange-500/20">Home</Badge>;
      case 'family':
        return <Badge className="bg-purple-500/10 text-purple-500 border-purple-500/20">Family</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-4 border-b">
        <h1 className="text-2xl font-bold">Affectations List</h1>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Select value={branchId} onValueChange={setBranchId}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select Branch" />
              </SelectTrigger>
              <SelectContent>
                {branches.map((branch) => (
                  <SelectItem key={branch._id} value={branch._id}>
                    {branch.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="icon"
                onClick={handlePreviousDay}
                title="Previous Day"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Input
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                className="w-[180px]"
              />
              <Button 
                variant="outline" 
                size="icon"
                onClick={handleNextDay}
                title="Next Day"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <Button variant="default">
            <Plus className="mr-2 h-4 w-4" />
            Create Reservation
          </Button>
        </div>
      </div>

      <div className="p-4 flex flex-col gap-4">
        {/* Filters */}
        <div className="flex flex-wrap items-center gap-4 justify-between">
          <div className="flex-1 max-w-md relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by phone..." 
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="flex items-center gap-3">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="assigned">Assigned</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={agentFilter} onValueChange={setAgentFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Agents" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Agents</SelectItem>
                <SelectItem value="assigned">Assigned</SelectItem>
                <SelectItem value="unassigned">Unassigned</SelectItem>
              </SelectContent>
            </Select>
            
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              More Filters
            </Button>
            
            <Button variant="outline" className="gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Checkbox 
            id="show-deleted" 
            checked={showDeleted} 
            onCheckedChange={(checked) => setShowDeleted(checked as boolean)} 
          />
          <label htmlFor="show-deleted" className="text-sm cursor-pointer">
            Show Deleted
          </label>
        </div>
        
        {/* Table */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <Checkbox />
                </TableHead>
                <TableHead>Created At</TableHead>
                <TableHead>Reference</TableHead>
                <TableHead>Client Name</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Agent</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array(5).fill(0).map((_, index) => (
                  <TableRow key={`loading-${index}`}>
                    <TableCell colSpan={11} className="h-16 text-center text-muted-foreground">
                      Loading...
                    </TableCell>
                  </TableRow>
                ))
              ) : filteredReservations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={11} className="h-24 text-center text-muted-foreground">
                    No reservations found.
                  </TableCell>
                </TableRow>
              ) : (
                filteredReservations.map((reservation) => {
                  const isInvitationSource = reservation.source === 'invitation';

                  return (
                  <TableRow
                    key={reservation._id}
                    className={cn(
                      isInvitationSource && "bg-blue-50/50 border-l-4 border-l-blue-400"
                    )}
                  >
                    <TableCell>
                      <Checkbox />
                    </TableCell>
                    <TableCell className="text-sm whitespace-nowrap">
                      {format(new Date(reservation.createdAt), "yyyy-MM-dd HH:mm")}
                    </TableCell>
                    <TableCell className="font-medium">{reservation._id.substring(0, 10)}</TableCell>
                    <TableCell>
                      {reservation.customerInfo.client1Name}
                      {reservation.customerInfo.hasCompanion && reservation.customerInfo.client2Name && 
                        ` & ${reservation.customerInfo.client2Name}`
                      }
                    </TableCell>
                    <TableCell>{reservation.customerInfo.phone}</TableCell>
                    <TableCell>{reservation.appointmentDate}</TableCell>
                    <TableCell>{reservation.appointmentTime}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {/* Invitation source indicator - positioned to the left */}
                        {isInvitationSource && reservation.partner && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600">
                                  <Mail className="h-5 w-5" />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="text-sm">
                                  <strong>Invitation de:</strong><br />
                                  {reservation.partner.name}<br />
                                  {reservation.partner.email}
                                </p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        {getTypeBadge(reservation.type)}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(reservation.status)}</TableCell>
                    <TableCell>
                      {reservation.assignedUser ? (
                        <div className="flex items-center gap-2">
                          <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-xs font-medium">
                            {reservation.assignedUser.name.split(' ').map(part => part[0]).join('').toUpperCase().substring(0, 2)}
                          </div>
                          <span className="text-sm">{reservation.assignedUser.name}</span>
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">Unassigned</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-4 w-4">
                          <path d="M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                        </svg>
                      </Button>
                    </TableCell>
                  </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Pagination */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing <span className="font-medium">{filteredReservations.length}</span> of <span className="font-medium">{reservations.length}</span> reservations
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" disabled={isLoading}>
              Previous
            </Button>
            <Button variant="outline" size="sm" disabled={isLoading}>
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
} 