import { useState, useEffect, useCallback } from 'react';
import { 
  loadBillingTableState, 
  saveBillingTableParameter,
  type BillingTableState 
} from '../utils/localStorage';

interface UseBillingTableStateReturn {
  // View mode
  viewMode: 'list' | 'kanban';
  setViewMode: (mode: 'list' | 'kanban') => void;
  
  // Search and filters
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  status: string | null;
  setStatus: (status: string | null) => void;
  signed: string | null;
  setSigned: (signed: string | null) => void;
  startDate: Date | null;
  setStartDate: (date: Date | null) => void;
  endDate: Date | null;
  setEndDate: (date: Date | null) => void;
  showArchived: boolean;
  setShowArchived: (show: boolean) => void;
  showDeleted: boolean;
  setShowDeleted: (show: boolean) => void;
  
  // Sorting
  sort: { field: string; direction: 'asc' | 'desc' };
  setSort: (sort: { field: string; direction: 'asc' | 'desc' }) => void;
  
  // Pagination
  currentPage: number;
  setCurrentPage: (page: number) => void;
  pageSize: number;
  setPageSize: (size: number) => void;
  
  // Selection
  selectedInvoices: string[];
  setSelectedInvoices: (invoices: string[]) => void;
  
  // Utility functions
  resetFilters: () => void;
  isHydrated: boolean;
}

export function useBillingTableState(): UseBillingTableStateReturn {
  const [isHydrated, setIsHydrated] = useState(false);
  
  // Initialize state from localStorage or defaults
  const [viewMode, setViewModeState] = useState<'list' | 'kanban'>('list');
  const [searchTerm, setSearchTermState] = useState('');
  const [status, setStatusState] = useState<string | null>(null);
  const [signed, setSignedState] = useState<string | null>(null);
  const [startDate, setStartDateState] = useState<Date | null>(null);
  const [endDate, setEndDateState] = useState<Date | null>(null);
  const [showArchived, setShowArchivedState] = useState(false);
  const [showDeleted, setShowDeletedState] = useState(false);
  const [sort, setSortState] = useState<{ field: string; direction: 'asc' | 'desc' }>({
    field: 'createdAt',
    direction: 'desc'
  });
  const [currentPage, setCurrentPageState] = useState(1);
  const [pageSize, setPageSizeState] = useState(10);
  const [selectedInvoices, setSelectedInvoicesState] = useState<string[]>([]);

  // Load state from localStorage on mount
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    try {
      const savedState = loadBillingTableState();
      
      if (savedState.viewMode) setViewModeState(savedState.viewMode);
      if (savedState.searchTerm !== undefined) setSearchTermState(savedState.searchTerm);
      if (savedState.status !== undefined) setStatusState(savedState.status);
      if (savedState.signed !== undefined) setSignedState(savedState.signed);
      if (savedState.startDate) setStartDateState(new Date(savedState.startDate));
      if (savedState.endDate) setEndDateState(new Date(savedState.endDate));
      if (savedState.showArchived !== undefined) setShowArchivedState(savedState.showArchived);
      if (savedState.showDeleted !== undefined) setShowDeletedState(savedState.showDeleted);
      if (savedState.sort) setSortState(savedState.sort);
      if (savedState.currentPage !== undefined) setCurrentPageState(savedState.currentPage);
      if (savedState.pageSize !== undefined) setPageSizeState(savedState.pageSize);
      if (savedState.selectedInvoices) setSelectedInvoicesState(savedState.selectedInvoices);
      
    } catch (error) {
      console.error('Error loading billing table state from localStorage:', error);
    } finally {
      setIsHydrated(true);
    }
  }, []);

  // Create wrapper functions that save to localStorage
  const setViewMode = useCallback((mode: 'list' | 'kanban') => {
    setViewModeState(mode);
    if (isHydrated) saveBillingTableParameter('viewMode', mode);
  }, [isHydrated]);

  const setSearchTerm = useCallback((term: string) => {
    setSearchTermState(term);
    if (isHydrated) saveBillingTableParameter('searchTerm', term);
  }, [isHydrated]);

  const setStatus = useCallback((statusValue: string | null) => {
    setStatusState(statusValue);
    if (isHydrated) saveBillingTableParameter('status', statusValue);
  }, [isHydrated]);

  const setSigned = useCallback((signedValue: string | null) => {
    setSignedState(signedValue);
    if (isHydrated) saveBillingTableParameter('signed', signedValue);
  }, [isHydrated]);

  const setStartDate = useCallback((date: Date | null) => {
    setStartDateState(date);
    if (isHydrated) saveBillingTableParameter('startDate', date?.toISOString() || null);
  }, [isHydrated]);

  const setEndDate = useCallback((date: Date | null) => {
    setEndDateState(date);
    if (isHydrated) saveBillingTableParameter('endDate', date?.toISOString() || null);
  }, [isHydrated]);

  const setShowArchived = useCallback((show: boolean) => {
    setShowArchivedState(show);
    if (isHydrated) saveBillingTableParameter('showArchived', show);
  }, [isHydrated]);

  const setShowDeleted = useCallback((show: boolean) => {
    setShowDeletedState(show);
    if (isHydrated) saveBillingTableParameter('showDeleted', show);
  }, [isHydrated]);

  const setSort = useCallback((sortValue: { field: string; direction: 'asc' | 'desc' }) => {
    setSortState(sortValue);
    if (isHydrated) saveBillingTableParameter('sort', sortValue);
  }, [isHydrated]);

  const setCurrentPage = useCallback((page: number) => {
    setCurrentPageState(page);
    if (isHydrated) saveBillingTableParameter('currentPage', page);
  }, [isHydrated]);

  const setPageSize = useCallback((size: number) => {
    setPageSizeState(size);
    if (isHydrated) saveBillingTableParameter('pageSize', size);
  }, [isHydrated]);

  const setSelectedInvoices = useCallback((invoices: string[]) => {
    setSelectedInvoicesState(invoices);
    if (isHydrated) saveBillingTableParameter('selectedInvoices', invoices);
  }, [isHydrated]);

  // Reset filters function
  const resetFilters = useCallback(() => {
    setSearchTerm('');
    setStatus(null);
    setSigned(null);
    setStartDate(null);
    setEndDate(null);
    setShowArchived(false);
    setShowDeleted(false);
    setCurrentPage(1);
    setSelectedInvoices([]);
  }, [setSearchTerm, setStatus, setSigned, setStartDate, setEndDate, setShowArchived, setShowDeleted, setCurrentPage, setSelectedInvoices]);

  return {
    viewMode,
    setViewMode,
    searchTerm,
    setSearchTerm,
    status,
    setStatus,
    signed,
    setSigned,
    startDate,
    setStartDate,
    endDate,
    setEndDate,
    showArchived,
    setShowArchived,
    showDeleted,
    setShowDeleted,
    sort,
    setSort,
    currentPage,
    setCurrentPage,
    pageSize,
    setPageSize,
    selectedInvoices,
    setSelectedInvoices,
    resetFilters,
    isHydrated,
  };
}
