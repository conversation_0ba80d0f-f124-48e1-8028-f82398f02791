/**
 * Utilities for managing billing table state in localStorage
 */

export interface BillingTableState {
  // View mode
  viewMode?: 'list' | 'kanban';
  
  // Search and filters
  searchTerm?: string;
  status?: string | null;
  signed?: string | null;
  startDate?: string | null; // ISO string
  endDate?: string | null; // ISO string
  showArchived?: boolean;
  showDeleted?: boolean;
  
  // Sorting
  sort?: {
    field: string;
    direction: 'asc' | 'desc';
  };
  
  // Pagination
  currentPage?: number;
  pageSize?: number;
  
  // Selection
  selectedInvoices?: string[];
}

const BILLING_TABLE_KEY = 'billing-table-state';

/**
 * Save billing table state to localStorage
 * @param state The table state to save
 */
export function saveBillingTableState(state: Partial<BillingTableState>): void {
  if (typeof window === 'undefined') return;
  
  try {
    // Get existing state
    const existingState = loadBillingTableState();
    
    // Merge with new state
    const mergedState = { ...existingState, ...state };
    
    // Save to localStorage
    localStorage.setItem(BILLING_TABLE_KEY, JSON.stringify(mergedState));
  } catch (error) {
    console.error('Error saving billing table state to localStorage:', error);
  }
}

/**
 * Load billing table state from localStorage
 * @returns The table state or empty object if not found
 */
export function loadBillingTableState(): BillingTableState {
  if (typeof window === 'undefined') return {};
  
  try {
    const savedState = localStorage.getItem(BILLING_TABLE_KEY);
    if (savedState === null) return {};
    
    const parsed = JSON.parse(savedState) as BillingTableState;
    
    // Convert date strings back to Date objects if they exist
    if (parsed.startDate) {
      // Keep as string for now, will be converted to Date in the hook
    }
    if (parsed.endDate) {
      // Keep as string for now, will be converted to Date in the hook
    }
    
    return parsed;
  } catch (error) {
    console.error('Error loading billing table state from localStorage:', error);
    return {};
  }
}

/**
 * Clear billing table state from localStorage
 */
export function clearBillingTableState(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(BILLING_TABLE_KEY);
    console.log('Cleared billing table state from localStorage');
  } catch (error) {
    console.error('Error clearing billing table state from localStorage:', error);
  }
}

/**
 * Save individual billing table parameter to localStorage
 * @param parameter The parameter name
 * @param value The value to save
 */
export function saveBillingTableParameter<K extends keyof BillingTableState>(
  parameter: K,
  value: BillingTableState[K]
): void {
  if (typeof window === 'undefined') return;
  
  try {
    const existingState = loadBillingTableState();
    const newState = { ...existingState, [parameter]: value };
    localStorage.setItem(BILLING_TABLE_KEY, JSON.stringify(newState));
  } catch (error) {
    console.error(`Error saving billing table parameter ${parameter} to localStorage:`, error);
  }
}

/**
 * Load individual billing table parameter from localStorage
 * @param parameter The parameter name
 * @param defaultValue The default value if not found
 * @returns The parameter value or default
 */
export function loadBillingTableParameter<K extends keyof BillingTableState>(
  parameter: K,
  defaultValue: BillingTableState[K]
): BillingTableState[K] {
  if (typeof window === 'undefined') return defaultValue;
  
  try {
    const state = loadBillingTableState();
    return state[parameter] !== undefined ? state[parameter] : defaultValue;
  } catch (error) {
    console.error(`Error loading billing table parameter ${parameter} from localStorage:`, error);
    return defaultValue;
  }
}
