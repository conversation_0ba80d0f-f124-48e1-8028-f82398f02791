# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/your_main_database

# MongoDB History Database Configuration (for collection history tracking)
# Option 1: Use separate database (recommended for production)
# MONGODB_HISTORY_URI=mongodb://localhost:27017/your_history_database
# MONGODB_HISTORY_DB_NAME=history_db

# Option 2: Use same database with different collection naming (for development)
# Just set the database name, will use MONGODB_URI connection
# MONGODB_HISTORY_DB_NAME=collection_history

# If neither is set, defaults to '{collection_name}_history' collections in main database
# Example: 'reservations_history', 'users_history', 'orders_history', etc.

# LocationIQ API Configuration (for address autocomplete)
LOCATIONIQ_API_KEY=your_locationiq_api_key_here

# Other environment variables...


